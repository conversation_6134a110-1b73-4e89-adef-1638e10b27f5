import 'dart:async';
import 'package:connect/common/constants.dart';
import 'package:connect/driver/driver_home_route.dart';
import 'package:connect/driver/provider/driver_progress_model.dart';
import 'package:connect/provider/connect_locale.dart';
import 'package:connect/provider/history_model.dart';
import 'package:connect/provider/order_sold_out_model.dart';
import 'package:connect/provider/order_status_provider.dart';
import 'package:connect/provider/orders_details_model.dart';
import 'package:connect/provider/orders_details_refresh_provider.dart';
import 'package:connect/provider/orders_model.dart';
import 'package:connect/provider/pre_order_model.dart';
import 'package:connect/provider/report_model.dart';
import 'package:connect/provider/rest_menu_model.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/provider/search_model.dart';
import 'package:connect/provider/switch_role_model.dart';
import 'package:connect/ui/home/<USER>';
import 'package:connect/ui/home/<USER>/help_center_route.dart';
import 'package:connect/ui/home/<USER>/help_feedback_route.dart';
import 'package:connect/ui/home/<USER>/multi_language_route.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/ondeman_ddelivery_route.dart';
import 'package:connect/ui/home/<USER>/switch_role.dart';
import 'package:connect/ui/home/<USER>/update_pwd_route.dart';
import 'package:connect/ui/home/<USER>/orders_details_route.dart';
import 'package:connect/ui/home/<USER>/orders_details_split_screen_route.dart';
import 'package:connect/ui/home/<USER>/orders_setting.dart';
import 'package:connect/ui/home/<USER>/daily_report.dart';
import 'package:connect/ui/login/login_route.dart';
import 'package:connect/ui/search/search_route.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/flutter_device_type.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/print_manager.dart';
import 'package:connect/utils/roles_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sp_util/sp_util.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'common/global.dart';
import 'driver/delivery/driver_at_dropoff_map_route.dart';
import 'driver/delivery/driver_completed_dropoff_upload_route.dart';
import 'driver/delivery/driver_heat_map_web.dart';
import 'driver/driver_report_route.dart';
import 'driver/provider/driver_assigns_model.dart';
import 'driver/provider/driver_dropoff_model.dart';
import 'driver/provider/driver_feedback_model.dart';
import 'driver/provider/driver_heat_model.dart';
import 'driver/provider/driver_model.dart';
import 'driver/provider/driver_online.dart';
import 'driver/provider/driver_passcode.dart';
import 'driver/provider/driver_region_announcement_model.dart';
import 'driver/provider/driver_report_model.dart';
import 'driver/provider/driver_shifts_model.dart';
import 'driver/provider/driver_support_model.dart';
import 'driver/provider/driver_tickets.dart';
import 'driver/ui/pickup_complete/driver_pickup_complete_route.dart';
import 'driver/ui/pickup_failed/pickup_failed_des_route.dart';
import 'driver/widget/driver_one_dialog.dart';
import 'generated/l10n.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:timezone/data/latest.dart' as tz;

import 'ui/home/<USER>/daily_food_report.dart';

Future<void> main() async {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    await Firebase.initializeApp();

    await SpUtil.getInstance();

    await GlobalConfig.deviceId();

    //init persistent log
    LogManager.instance;

    if (GlobalConfig.isRelease) {
      //sentry init
      await SentryFlutter.init((options) => options.dsn = Constants.SENTRY_DSN);
    }

    if (TrackingUtils.instance?.amplitude != null) {
      // await TrackingUtils.instance!.amplitude.init(Constants.AMP_API_KEY);
    }

    if (kDebugMode) {
      // Force disable Crashlytics collection while doing every day development.
      // Temporarily toggle this to true if you want to test crash reporting in your app.
      LogUtil.init(isDebug: true);
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(false);
    }

    //set only portrait screen
    var isTablet = Device.get().isTablet == true;
    LogUtil.v(
        "isTablet::$isTablet,devicePixelRatio::${Device.devicePixelRatio},width::${Device.size.width},height::${Device.size.height}");
    if (!isTablet) {
      await SystemChrome.setPreferredOrientations(
          [DeviceOrientation.portraitUp]);
    }

    var isSunmi = await PrintManager.instance.isSunmi();
    LogUtil.v("isSunmi::$isSunmi");
    GlobalConfig.isSunmi = isSunmi;

    runApp(MultiProvider(
      providers: [
        ChangeNotifierProvider<OrdersViewModel>(
          create: (_) => OrdersViewModel(),
        ),
        ChangeNotifierProvider<OrderSoldOutModel>(
          create: (_) => OrderSoldOutModel(),
        ),
        ChangeNotifierProvider<SearchViewModel>(
          create: (_) => SearchViewModel(),
        ),
        ChangeNotifierProvider<HistoryViewModel>(
          create: (_) => HistoryViewModel(),
        ),
        ChangeNotifierProvider<OrderStatusProvider>(
          create: (_) => OrderStatusProvider(),
        ),
        ChangeNotifierProvider<PreOrderViewModel>(
          create: (_) => PreOrderViewModel(),
        ),
        ChangeNotifierProvider<ConnectLocale>(
          create: (_) => ConnectLocale(),
        ),
        ChangeNotifierProvider<ReportModel>(
          create: (_) => ReportModel(),
        ),
        ChangeNotifierProvider<RestMenuModel>(
          create: (_) => RestMenuModel(),
        ),
        ChangeNotifierProvider<RestModel>(
          create: (_) => RestModel(),
        ),
        ChangeNotifierProvider<DriverModel>(
          create: (_) => DriverModel(),
        ),
        ChangeNotifierProvider<DriverPassCode>(
          create: (_) => DriverPassCode(),
        ),
        ChangeNotifierProvider<DriverOnline>(
          create: (_) => DriverOnline(),
        ),
        ChangeNotifierProvider<DriverReportModel>(
          create: (_) => DriverReportModel(),
        ),
        ChangeNotifierProvider<OrdersDetailsRefreshProvider>(
          create: (_) => OrdersDetailsRefreshProvider(),
        ),
        ChangeNotifierProvider<DriverShiftsModel>(
          create: (_) => DriverShiftsModel(),
        ),
        ChangeNotifierProvider<DriverRegionAnnouncementModel>(
          create: (_) => DriverRegionAnnouncementModel(),
        ),
        ChangeNotifierProvider<DriverDroppoffModel>(
          create: (_) => DriverDroppoffModel(),
        ),
        ChangeNotifierProvider<DriverHeatModel>(
          create: (_) => DriverHeatModel(),
        ),
        ChangeNotifierProvider<DriverAssignsModel>(
          create: (_) => DriverAssignsModel(),
        ),
        ChangeNotifierProvider<DriverTickets>(
          create: (_) => DriverTickets(),
        ),
        ChangeNotifierProvider<DriverFeedbackModel>(
          create: (_) => DriverFeedbackModel(),
        ),
        ChangeNotifierProvider<DriverSupportModel>(
          create: (_) => DriverSupportModel(),
        ),
        ChangeNotifierProvider<OrdersDetailsModel>(
          create: (_) => OrdersDetailsModel(),
        ),
        ChangeNotifierProvider<SwitchRoleModel>(
          create: (_) => SwitchRoleModel(),
        ),
        ChangeNotifierProvider<DriverProgressModel>(
          create: (_) => DriverProgressModel(),
        )
      ],
      child: MyApp(),
    ));
  }, (exception, stackTrace) {
    LogUtil.v(
        "global exception::${exception.toString()},${stackTrace.toString()}");
    //via photo permission failed
    if (Constants.PERMISSION_PHOTO_EX == exception.toString()) {
      DriverOneDialog.show(
          GlobalConfig.navigatorKey.currentContext!,
          S
              .of(GlobalConfig.navigatorKey.currentContext!)
              .connect_permission_photo, callback: () {
        //open system setting
        openAppSettings();
      });
    } else {
      if (!exception.toString().contains(Constants.NULL_CHECK)) {
        Sentry.captureException(
            "global exception::${exception.toString()},${stackTrace.toString()}");
      }
    }
  });
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => new _MyAppState();
}

class _MyAppState extends State<MyApp> {
  // This widget is the root of your application.
  final routes = <String, WidgetBuilder>{
    LoginRoute.tag: (context) => LoginRoute(),
    HomeRoute.tag: (context) => HomeRoute(),
    SearchRoute.tag: (context) => SearchRoute(),
    OrdersSetting.tag: (context) => OrdersSetting(),
    UpdatePwdRoute.tag: (context) => UpdatePwdRoute(),
    MultiLanguageRoute.tag: (context) => MultiLanguageRoute(),
    DriverHomeRoute.tag: (context) => DriverHomeRoute(),
    DriverPickupCompleteRoute.tag: (context) => DriverPickupCompleteRoute(),
    PickupFailedDesRoute.tag: (context) => PickupFailedDesRoute(),
    OrdersDetailsRoute.tag: (context) => OrdersDetailsRoute(),
    OrdersDetailsSplitScreenRoute.tag: (context) =>
        OrdersDetailsSplitScreenRoute(),
    DriverCompletedDropOffUploadRoute.tag: (context) =>
        DriverCompletedDropOffUploadRoute(),
    DriverAtDropoffMapRoute.tag: (context) => DriverAtDropoffMapRoute(),
    DriverHeatMapWeb.tag: (context) => DriverHeatMapWeb(),
    HelpCenterRoute.tag: (context) => HelpCenterRoute(),
    HelpFeedbackRoute.tag: (context) => HelpFeedbackRoute(),
    DriverHeatMapWeb.tag: (context) => DriverHeatMapWeb(),
    DailyReport.tag: (context) => DailyReport(),
    DriverReportRoute.tag: (context) => DriverReportRoute(),
    DailyFoodReport.tag: (context) => DailyFoodReport(),
    OnDemandDeliveryRoute.tag: (context) => OnDemandDeliveryRoute(),
    SwitchRole.tag: (context) => SwitchRole(),
  };

  @override
  void initState() {
    super.initState();
    //sentry log user
    if (GlobalConfig.isRelease) {
      var user = ConnectCache.getUser();
      if (user != null) {
        Sentry.configureScope((scope) {
          scope.setTag("connectUserId", user.userId ?? "");
          scope.setUser(SentryUser(id: user.userId, email: user.email));
          scope.setContexts("connect_user", ConnectCache.getMapUser());
        });
      }
    }
    //oneSingle init
    GlobalConfig.initFirebase();

    //keep awake
    WakelockPlus.enable();

    // initialize timezones
    tz.initializeTimeZones();
  }

  @override
  void dispose() {
    super.dispose();
    LogUtil.v("main dispose");
    //disable awake
    WakelockPlus.disable();
  }

  @override
  Widget build(BuildContext context) {
    //remove translucent
    // LogUtil.v("rolesName::${ConnectCache.getCurrentRole().name}");
    // LogUtil.v("token::${ConnectCache.getToken()}");
    if (Theme.of(context).platform == TargetPlatform.android) {
      SystemUiOverlayStyle _style =
          SystemUiOverlayStyle(statusBarColor: Colors.transparent);
      SystemChrome.setSystemUIOverlayStyle(_style);
    }
    return Consumer<ConnectLocale>(builder: (context, currentLocale, child) {
      return RefreshConfiguration(
        headerTriggerDistance: 80.0,
        footerTriggerDistance: 120.0,
        maxOverScrollExtent: 80,
        enableBallisticLoad: false,
        child: MaterialApp(
          locale: currentLocale.value,
          navigatorKey: GlobalConfig.navigatorKey,
          localizationsDelegates: const [
            S.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate
          ],
          supportedLocales: S.delegate.supportedLocales,
          title: 'Connect',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primarySwatch: Colors.deepOrange,
            visualDensity: VisualDensity.adaptivePlatformDensity,
          ),
          home: ConnectCache.getToken()!.isEmpty
              ? LoginRoute()
              : RolesManager.isDriverRole()
                  ? DriverHomeRoute()
                  : HomeRoute(),
          builder: (BuildContext context, Widget? child) {
            return MediaQuery(
              //set text size does not change with system settings
              data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
              child: FlutterSmartDialog(child: child),
            );
          },
          routes: routes,
        ),
      );
    });
  }
}
