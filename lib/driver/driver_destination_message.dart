import 'package:connect/common/MyStyles.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/object_util.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import 'bean/destination_entity.dart';

class DriverDestinationMessage extends StatefulWidget {
  DriverDestinationMessage(this.entity, {Key? key}) : super(key: key);

  final DestinationEntity entity;

  @override
  _DriverDestinationMessageState createState() =>
      _DriverDestinationMessageState();
}

class _DriverDestinationMessageState extends State<DriverDestinationMessage> {
  //time formatted
  String _printDuration(Duration duration) {
    // LogUtil.v("duration::${duration.inMinutes}");
    var inMinutes = duration.inMinutes;
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(inMinutes.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes";
  }

  String? _time() {
    try {
      var entity = widget.entity;
      var next = entity.next;

      var arriveAt = next?.arriveAt;

      if (ObjectUtil.isEmpty(arriveAt)) return null;

      var difference = DateUtil.getDateTime(arriveAt!)!
          .toLocal()
          .difference(DateTime.now())
          .inMilliseconds;
      LogUtil.v("driverDate difference::$difference");

      if (difference <= 0) {
        //show current date if diff less than 0
        return DateUtil.formatDate(DateTime.now(), format: "HH:mm");
      }
      var mile = next?.distance;

      if (ObjectUtil.isEmpty(mile)) return null;

      LogUtil.v("driver message mile::$mile");
      if (mile! <= 1) {
        return null;
      }
      num currentDistance = 1 / mile;
      LogUtil.v("driverDate currentDistance::$currentDistance");

      String driverDate = _printDuration(
          Duration(milliseconds: (currentDistance * difference).toInt()));
      LogUtil.v("driverDate::$driverDate");
      return driverDate;
    } catch (e) {
      LogUtil.e("driverDate::${e.toString()}");
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    var time = _time();
    return time == null
        ? EmptyContainer()
        : Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                      child: Text(
                    sprintf(
                        S.of(context).connect_lock_driver_message, ["$time"]),
                    style: MyStyles.m13.copyWith(
                      color: MyColors.portal,
                    ),
                  )),
                ],
              ),
              SizedBox(
                height: 5,
              )
            ],
          );
  }
}
