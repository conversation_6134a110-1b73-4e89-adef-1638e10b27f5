import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/widget/driver_more_dialog.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';

import '../common/MyStyles.dart';
import 'ui/pickup_failed/driver_pickup_failed_sheet.dart';

class DriverMoreOrders extends StatefulWidget {
  DriverMoreOrders(this.routeToPickUp, {Key? key}) : super(key: key);

  final bool routeToPickUp;
  @override
  _DriverMoreOrdersState createState() => _DriverMoreOrdersState();
}

class _DriverMoreOrdersState extends State<DriverMoreOrders> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 32),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  side: BorderSide(
                    width: 1.0,
                    color: MyColors.ccc,
                  ),
                  backgroundColor: MyColors.W,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  fixedSize: Size(double.infinity, 32),
                ),
                child: Text(
                  S.of(context).driver_not_pickup_complete,
                  style: MyStyles.m13.copyWith(color: MyColors.color999),
                  textAlign: TextAlign.center,
                ),
                onPressed: () {
                  DriverPickupFailedSheet.show(context, widget.routeToPickUp);
                },
              ),
            ),
            SizedBox(
              width: 35,
            ),
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: MyColors.portal,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  fixedSize: Size(double.infinity, 32),
                ),
                child: Text(
                  S.of(context).driver_get_more,
                  style: MyStyles.m13.copyWith(color: MyColors.W),
                  textAlign: TextAlign.center,
                ),
                onPressed: () {
                  TrackingUtils.instance!
                      .tracking(TConstants.D_DELIVERY_GET_MORE_CLICK);
                  showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return DriverMoreDialog();
                      });
                },
              ),
            ),
          ],
        ),
        SizedBox(
          height: Dimens.dp_30,
        ),
      ],
    );
  }
}
