import 'package:connect/common/MyStyles.dart';
import 'package:connect/driver/provider/driver_report_model.dart';
import 'package:connect/driver/report/date/driver_report_date_group.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/tz_date.dart';
import 'package:connect/utils/log_util.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:connect/generated/l10n.dart' as L10n;

import '../gen/assets.gen.dart';
import '../ui/home/<USER>';
import '../utils/format_utils.dart';
import 'report/driver_report_adjustment.dart';
import 'report/driver_report_category.dart';
import 'report/driver_report_route.dart';
import 'report/driver_report_summary.dart';

class DriverReportRoute extends StatefulWidget {
  static String tag = "driver_report_Route";
  @override
  DriverReportRouteState createState() => DriverReportRouteState();
}

class DriverReportRouteState extends State<DriverReportRoute>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  String from = "";
  String to = "";

  List<Tab> tabs = [
    Tab(
      text: L10n.S.current.dailyReport_title_distribution,
    ),
    Tab(
      text: L10n.S.current.driver_report_segment_regular,
    ),
    Tab(
      text: L10n.S.current.dailyReport_title_orders,
    ),
  ];

  void _onRefresh({bool isLoading = false}) async {
    if (isLoading) LoadingUtils.show();
    var reportModel = context.read<DriverReportModel>();
    await reportModel.driverReport(from, to);
    if (isLoading) LoadingUtils.dismiss();
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
  }

  String restDateformate(dt, DateFormatType type, [String? tz]) {
    var timezone = tz ?? FormatUtils.format(dt);

    var result = "";
    switch (type) {
      case DateFormatType.startOfDay:
        result = formatDate(
                dt, [yyyy, '-', mm, '-', dd, 'T', '00', ':', '00', ':', '00']) +
            timezone;
        break;
      case DateFormatType.endOfDay:
        result = formatDate(
                dt, [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
            timezone;
        break;
      default:
    }

    return result;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final args = ModalRoute.of(context)!.settings.arguments
        as DriverReportRouteV2Arguments;
    if (from.isEmpty) {
      from = restDateformate(args.from, DateFormatType.startOfDay);
    }
    if (to.isEmpty) {
      to = restDateformate(args.to, DateFormatType.endOfDay);
    }

    return DefaultTabController(
      length: tabs.length,
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          centerTitle: false,
          leadingWidth: 0,
          automaticallyImplyLeading: false,
          title: Row(
            children: [
              InkWell(
                child: Assets.images.icons.leftArrow.image(width: 30),
                onTap: () {
                  Navigator.pop(context);
                },
              ),
              DriverReportDateGroup(args.from, args.to, (f, t) {
                setState(() {
                  from = f;
                  to = t;
                });
                _onRefresh(isLoading: true);
              }),
            ],
          ),
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(40),
            child: Align(
              alignment: Alignment.centerLeft,
              child: TabBar(
                labelStyle: MyStyles.m12.copyWith(color: MyColors.color666),
                labelColor: MyColors.portal,
                indicatorColor: MyColors.portal,
                unselectedLabelColor: MyColors.color666,
                tabs: tabs,
                indicatorSize: TabBarIndicatorSize.label,
                indicatorWeight: 2,
              ),
            ),
          ),
        ),
        body: TabBarView(
          children: [
            DriverReportSummary(from, to),
            DriverReportCategory(from, to),
            DriverReportAdjustment(from, to),
          ],
        ),
      ),
    );
  }
}
