import 'package:connect/common/MyStyles.dart';
import 'package:connect/driver/bean/driver_assigns_entity.dart';
import 'package:connect/driver/provider/driver_assigns_model.dart';
import 'package:connect/gen/assets.gen.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sprintf/sprintf.dart';

import 'driver_assign_item.dart';
import 'package:provider/provider.dart';

class DriverAssignRoute extends StatefulWidget {
  DriverAssignRoute({Key? key}) : super(key: key);

  @override
  _DriverAssignRouteState createState() => _DriverAssignRouteState();
}

class _DriverAssignRouteState extends State<DriverAssignRoute>
    with AutomaticKeepAliveClientMixin {
  late RefreshController _refreshController;

  void _onRefresh() {
    context
        .read<DriverAssignsModel>()
        .driverAssigns()
        .whenComplete(() => _refreshController.refreshCompleted());
  }

  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: true);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var items = context.watch<DriverAssignsModel>().responseList ?? [];
    //find delivered order
    var completedList =
        items.where((element) => element.delivery?.time != null).toList();
    //find bundle
    var bundleList = items.where((element) => element.bundle != null).toList();

    List<String?> responseListID = [];
    List<DriverAssignsEntity> responseList = [];

    items.forEach((element) {
      if (element.bundle == null) {
        responseList.add(element);
      } else if (!responseListID.contains(element.bundle?.combineId)) {
        responseListID.add(element.bundle?.combineId);
        responseList.add(element);
      }
    });

    List<String> titleItems = [];
    var orderInfo = S.of(context).driver_orders_dynamic_title1;
    orderInfo.split("@1").forEach((element) {
      var secondItems = element.split("@2");
      if (titleItems.length > 0) titleItems.add("@1");
      titleItems.add(secondItems[0]);
      if (secondItems.length > 1) {
        titleItems.add("@2");
        titleItems.add(secondItems[1]);
      }
    });
    return Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          title: Text(
            S.of(context).title_orders,
            style: MyStyles.m20.copyWith(color: MyColors.color333),
          ),
        ),
        backgroundColor: MyColors.bg,
        resizeToAvoidBottomInset: false,
        body: Column(
          children: [
            Container(
              color: MyColors.W,
              height: 36,
              child: Row(
                children: [
                  SizedBox(width: 16),
                  Assets.images.order.crown.image(width: 20),
                  SizedBox(width: 4),
                  _buildInfoRichText(
                      context,
                      titleItems,
                      "${items.length - completedList.length}",
                      "${completedList.length}")
                ],
              ),
            ),
            Expanded(
              child: SmartRefresher(
                controller: _refreshController,
                physics: AlwaysScrollableScrollPhysics(),
                onRefresh: _onRefresh,
                child: items.isEmpty
                    ? EmptyView(S.of(context).no_data)
                    : ListView.builder(
                        itemBuilder: (context, index) {
                          return DriverAssignItem(
                              responseList[index], bundleList);
                        },
                        itemCount: responseList.length),
              ),
            ),
          ],
        ));
  }

  @override
  bool get wantKeepAlive => true;

  Widget _buildInfoRichText(BuildContext context, List<String> items,
      String keyword1, String keyword2) {
    List<TextSpan> spans = [];
    items.forEach((element) {
      var text = element;
      var color = MyColors.color666;
      var style = MyStyles.r12;
      if (element == "@1") {
        text = keyword1;
        color = MyColors.MY;
        style = MyStyles.m13;
      } else if (element == "@2") {
        text = keyword2;
        color = MyColors.portal;
        style = MyStyles.m13;
      }
      var s = TextSpan(
        text: text,
        style: style.copyWith(color: color),
      );
      spans.add(s);
    });
    return RichText(
      text: TextSpan(children: spans),
    );
  }
}
