import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:url_launcher/url_launcher.dart';

///phone actionSheet
class DriverAssignPhone {
  static const String TAG = "DriverAssignPhone";

  static show(BuildContext context, String restaurantPhone,
      String customerPhone, bool isCompletedPickup) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(
              context, restaurantPhone, customerPhone, isCompletedPickup);
        },
        context: context);
  }

  static Widget buildBottomSheetWidget(BuildContext context,
      String restaurantPhone, String customerPhone, bool isCompletedPickup) {
    return Container(
      height: isCompletedPickup ? Dimens.dp_120 : Dimens.dp_170,
      child: Column(
        children: [
          buildItem(
              context, S.of(context).connect_contact_restaurant, //google maps
              onTap: () async {
            LogManager.instance!
                .log(TAG, "", "assign_restaurant_phone::$restaurantPhone");
            if (restaurantPhone.isNotEmpty) {
              var params = Map<String, dynamic>();
              params["phone"] = "$restaurantPhone";
              TrackingUtils.instance!.tracking(
                  TConstants.D_DELIVERY_ORDER_RESTAURANT_CALL_CLICK,
                  value: params);

              await launch("tel:$restaurantPhone");
            } else {
              ToastUtils.show(S
                  .of(GlobalConfig.navigatorKey.currentContext!)
                  .connect_no_phone_found);
              LogManager.instance!.log(TAG, "", "restaurant phone is empty");
            }
          }),
          !isCompletedPickup
              ? Divider(
                  height: Dimens.dp_1,
                )
              : EmptyContainer(),
          !isCompletedPickup
              ? buildItem(context, S.of(context).connect_contact_customer,
                  onTap: () async {
                  LogManager.instance!
                      .log(TAG, "", "assign_customer_phone::$customerPhone");
                  if (customerPhone.isNotEmpty) {
                    var params = Map<String, dynamic>();
                    params["phone"] = "$customerPhone";
                    TrackingUtils.instance!.tracking(
                        TConstants.D_DELIVERY_ORDER_CUSTOMER_CALL_CLICK,
                        value: params);

                    await launch("tel:$customerPhone");
                  } else {
                    ToastUtils.show(S
                        .of(GlobalConfig.navigatorKey.currentContext!)
                        .connect_no_phone_found);
                    LogManager.instance!
                        .log(TAG, "", "customer phone is empty");
                  }
                })
              : EmptyContainer(), //apple maps
          Divider(
            color: Colors.grey[200],
            thickness: Dimens.dp_10,
          ),
          buildItem(context, S.of(context).cancel), //cancel
        ],
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        await Future.delayed(Duration(milliseconds: 300));
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_50,
        child: Text(
          title,
          style: TextStyle(
              color: S.of(context).cancel == title
                  ? Colors.black87
                  : MyColors.portal,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
