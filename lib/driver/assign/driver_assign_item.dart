import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/constants.dart';
import 'package:connect/driver/assign/driver_assign_phone.dart';
import 'package:connect/driver/bean/driver_assigns_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:sprintf/sprintf.dart';

class DriverAssignItem extends StatefulWidget {
  DriverAssignItem(this.item, this.bundleList, {Key? key}) : super(key: key);

  final DriverAssignsEntity item;
  final List<DriverAssignsEntity> bundleList;

  @override
  _DriverAssignItemState createState() => _DriverAssignItemState();
}

class _DriverAssignItemState extends State<DriverAssignItem> {
  @override
  Widget build(BuildContext context) {
    var item = widget.item;

    bool hasBundle = false;
    List<DriverAssignsEntity> combineList = [];
    if (item.bundle != null) {
      hasBundle = true;
      combineList = widget.bundleList
          .where((element) =>
              element.sId != item.sId &&
              element.bundle!.combineId == item.bundle!.combineId)
          .toList();
      combineList.insert(0, item);
    }

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.white,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: hasBundle
            ? ListView.separated(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return _buildRow(combineList[index], true);
                },
                separatorBuilder: (context, index) {
                  return SizedBox(
                    height: Dimens.dp_15,
                  );
                },
                itemCount: combineList.length)
            : _buildRow(widget.item, false),
      ),
    );
  }

  Widget _buildRow(DriverAssignsEntity item, bool isBundle) {
    var completePickupTime = "";
    bool isCompletedPickup = false;
    var completedAt;
    String? stops = "";

    isCompletedPickup = item.delivery?.status == Constants.PICKUP_COMPLETED;
    if (isCompletedPickup) {
      var confirmedAt = item.confirmedAt;
      completedAt = item.delivery?.stats?.pickupCompleted;
      if (completedAt != null && confirmedAt != null) {
        var dateTime = DateUtil.getDateTime(confirmedAt)!
            .toLocal()
            .add(Duration(minutes: completedAt.toInt()));
        completePickupTime = DateUtil.formatDate(dateTime, format: "HH:mm");
        stops = sprintf(
            S.of(context).driver_time_complete_pickup, ["$completePickupTime"]);
      }
    } else if (item.delivery!.status == Constants.EN_ROUTE_TO_DROPOFF) {
      stops = S.of(context).driver_en_route_to_drop_off;
    } else if (item.delivery!.status == Constants.AT_DROPOFF) {
      stops = S.of(context).driver_at_drop_off;
    } else if (item.delivery!.status == Constants.COMPLETED) {
      stops = S.of(context).driver_delivered;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      isBundle
                          ? Icon(
                              Icons.merge_type_rounded,
                              color: Colors.orange,
                            )
                          : EmptyContainer(),
                      isBundle
                          ? SizedBox(
                              width: 5,
                            )
                          : EmptyContainer(),
                      Text(
                        "#${item.passcode}",
                        style: MyStyles.m15.copyWith(
                          color: MyColors.color333,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    stops,
                    style: MyStyles.m15.copyWith(
                      color: MyColors.color333,
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: Dimens.dp_3,
              ),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      "${ServerMultiLan.multiAdapt2(item.restaurant!.name)}",
                      style: MyStyles.r13.copyWith(color: MyColors.color666),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      if (item.restaurant != null) {
                        if (item.customer == null) {
                          DriverAssignPhone.show(context,
                              item.restaurant!.phone, "", isCompletedPickup);
                        } else {
                          DriverAssignPhone.show(
                              context,
                              item.restaurant!.phone,
                              item.customer!.phone,
                              isCompletedPickup);
                        }
                      } else {
                        LogManager.instance!
                            .log("DriverAssignItem", "", "restaurant is null");
                      }
                    },
                    child: Container(
                      width: 36,
                      height: 36,
                      child: Icon(
                        Icons.phone,
                        size: 20,
                        color: MyColors.portal,
                      ),
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
