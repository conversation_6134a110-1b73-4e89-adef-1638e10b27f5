import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_heat_model.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/provider/driver_online.dart';
import 'package:connect/driver/provider/driver_region_announcement_model.dart';
import 'package:connect/driver/widget/driver_one_dialog.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'bean/driver_orders_entity.dart';
import 'bean/driver_region_entity.dart';
import 'delivery/driver_heat_map_web.dart';

class DriverOrder extends StatefulWidget {
  DriverOrder(this.entity, this.noOrder, this.newOrder, {Key? key})
      : super(key: key);

  final DriverOrdersEntity? entity;
  final bool noOrder;
  final bool newOrder;

  @override
  _DriverOrderState createState() => _DriverOrderState();
}

class _DriverOrderState extends State<DriverOrder> {
  String orderText = "";
  Color? color;

  @override
  void initState() {
    super.initState();
    context.read<DriverHeatModel>().heatEntity = null;
    context.read<DriverRegionAnnouncementModel>().regionEntity = null;
    WidgetsBinding.instance!.addPostFrameCallback((_) {
      if (mounted)
        context.read<DriverRegionAnnouncementModel>().regionAnnouncement();
    });
  }

  @override
  Widget build(BuildContext context) {
    // LogUtil.v("DriverOrder::build");
    if (widget.noOrder) {
      orderText = S.of(context).driver_no_order;
      color = Colors.grey;
    } else if (widget.newOrder) {
      orderText = S.of(context).driver_new_order;
      color = Colors.blue;
    }

    DriverRegionEntity? region = context.watch<DriverRegionAnnouncementModel>().regionEntity;

    // 如果有新订单且有tron 有 rejectReasons
    if (widget.newOrder && region != null && region.tron != null &&
        region.tron!.options != null && region.tron!.options!.rejectReasons != null &&
        region.tron!.options!.rejectReasons!.isNotEmpty) {
      LogUtil.v("DriverOrder::build - Condition met, showing order details page");
      return _buildOrderDetailsPage(context);
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () async {
            if (widget.newOrder) {
              TrackingUtils.instance!
                  .tracking(TConstants.D_DELIVERY_NEW_ORDER_CLICK);

              LoadingUtils.show();
              var driverModel = context.read<DriverModel>();
              var onCall = await driverModel.checkOnline();

              if (!onCall) {
                LoadingUtils.dismiss();
                context.read<DriverOnline>().setOnlineStatus(false);
                DriverOneDialog.show(
                    context, S.of(context).driver_online_receive);
                return;
              }

              await driverModel.createOrder();
              LoadingUtils.dismiss();
            }
          },
          child: Text(
            orderText,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: color,
                fontSize: Dimens.sp_15,
                fontWeight: FontWeight.bold),
          ),
        ),
        widget.noOrder
            ? Container(
                margin: const EdgeInsets.only(top: Dimens.dp_15),
                child: ElevatedButton(
                  style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(Colors.blue),
                      shape: MaterialStateProperty.all(RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(Dimens.dp_20)))),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.whatshot_outlined,
                        color: Colors.white,
                      ),
                      SizedBox(
                        width: Dimens.dp_5,
                      ),
                      Text(S.of(context).driver_check_heat_map,
                          style: TextStyle(color: Colors.white)),
                    ],
                  ),
                  onPressed: () async {
                    var pickups =
                        await context.read<DriverHeatModel>().heatPickups();
                    if (pickups != null) {
                      Navigator.of(context)
                          .pushNamed(DriverHeatMapWeb.tag, arguments: pickups);
                    }
                  },
                ),
              )
            : EmptyContainer(),
      ],
    );
  }

  Widget _buildOrderDetailsPage(BuildContext context) {
    final route = widget.entity!.route![0];
    final order = route.order;
    
    // 获取餐馆名称
    String restaurantName = '';
    if (order?.restaurant?.name != null) {
      restaurantName = order!.restaurant!.name!.zhCn ?? 
                      order.restaurant!.name!.enUs ?? 
                      order.restaurant!.name!.zhHk ?? '';
    }
    
    // 获取餐馆地址
    String restaurantAddress = route.address?.formatted ?? '';
    
    // 获取顾客地址
    String customerAddress = order?.delivery?.address?.formatted ?? '';
    
    // 获取预计收益
    String estimatedEarnings = order?.delivery?.driverPay != null 
        ? '€${order!.delivery!.driverPay!.toStringAsFixed(2)}' 
        : '';

    return Container(
      child: Column(
        children: [
          // 顶部标题
          // Container(
          //   padding: EdgeInsets.all(Dimens.dp_20),
          //   child: Text(
          //     S.of(context).driver_tap_to_stop_delivery,
          //     style: TextStyle(
          //       color: Colors.red,
          //       fontSize: Dimens.sp_18,
          //       fontWeight: FontWeight.bold,
          //     ),
          //   ),
          // ),
          
          // 订单详情
          Container(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: Dimens.dp_30),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // 餐馆名称
                  _buildInfoSection(S.of(context).driver_restaurant_name, restaurantName),
                  SizedBox(height: Dimens.dp_30),
                  
                  // 餐馆地址
                  _buildInfoSection(S.of(context).driver_restaurant_address, restaurantAddress),
                  SizedBox(height: Dimens.dp_30),
                  
                  // 顾客地址
                  _buildInfoSection(S.of(context).driver_customer_address, customerAddress),
                  SizedBox(height: Dimens.dp_30),
                  
                  // 预计收益
                  _buildInfoSection(S.of(context).driver_estimated_earnings, estimatedEarnings),
                ],
              ),
            ),
          ),
          
          // 底部按钮
          Container(
            padding: EdgeInsets.all(Dimens.dp_20),
            child: Row(
              children: [
                // Tap to Start 按钮
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(right: Dimens.dp_10),
                    child: ElevatedButton(
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(Colors.blue),
                        padding: MaterialStateProperty.all(
                          EdgeInsets.symmetric(vertical: Dimens.dp_15),
                        ),
                        shape: MaterialStateProperty.all(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(Dimens.dp_25),
                          ),
                        ),
                      ),
                      onPressed: () async {
                        if (widget.newOrder) {
                          TrackingUtils.instance!
                              .tracking(TConstants.D_DELIVERY_NEW_ORDER_CLICK);

                          LoadingUtils.show();
                          var driverModel = context.read<DriverModel>();
                          var onCall = await driverModel.checkOnline();

                          if (!onCall) {
                            LoadingUtils.dismiss();
                            context.read<DriverOnline>().setOnlineStatus(false);
                            DriverOneDialog.show(
                                context, S.of(context).driver_online_receive);
                            return;
                          }

                          await driverModel.createOrder();
                          LoadingUtils.dismiss();
                        }
                      },
                      child: Text(
                        S.of(context).driver_tap_to_start,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: Dimens.sp_16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
                
                // Reject 按钮
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(left: Dimens.dp_10),
                    child: ElevatedButton(
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(Colors.red),
                        padding: MaterialStateProperty.all(
                          EdgeInsets.symmetric(vertical: Dimens.dp_15),
                        ),
                        shape: MaterialStateProperty.all(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(Dimens.dp_25),
                          ),
                        ),
                      ),
                      onPressed: () {
                        _showRejectReasonBottomSheet(context);
                      },
                      child: Text(
                        S.of(context).driver_reject,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: Dimens.sp_16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.black45,
            fontSize: Dimens.sp_14,
          ),
        ),
        SizedBox(height: Dimens.dp_8),
        Text(
          value.isNotEmpty ? value : "N/A",
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.black87,
            fontSize: Dimens.sp_16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  void _showRejectReasonBottomSheet(BuildContext context) {
    DriverRegionEntity? region = context.read<DriverRegionAnnouncementModel>().regionEntity;
    List<String> rejectReasons = region?.tron?.options?.rejectReasons ?? [];
    
    if (rejectReasons.isEmpty) {
      // 如果没有拒绝原因，则不显示
      return ;
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(Dimens.dp_20),
              topRight: Radius.circular(Dimens.dp_20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Container(
                padding: EdgeInsets.all(Dimens.dp_20),
                child: Text(
                  S.of(context).driver_pick_failed_reason,
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: Dimens.sp_16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              
              // 分隔线
              Container(
                height: 1,
                color: Colors.white24,
                margin: EdgeInsets.symmetric(horizontal: Dimens.dp_20),
              ),
              
              // 选项列表
              ...rejectReasons.map((reason) => _buildReasonOption(context, reason)).toList(),
              
              // 分隔线
              Container(
                height: 8,
                color: Colors.black26,
              ),
              
              // 取消按钮
              Container(
                width: double.infinity,
                child: TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: Dimens.dp_20),
                  ),
                  child: Text(
                    S.of(context).cancel,
                    style: TextStyle(
                      color: Colors.blue,
                      fontSize: Dimens.sp_18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              // 底部安全区域
              SizedBox(height: MediaQuery.of(context).padding.bottom),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReasonOption(BuildContext context, String reason) {
    return Container(
      width: double.infinity,
      child: TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          _rejectOrder(context, reason);
        },
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: Dimens.dp_20),
        ),
        child: Text(
          reason,
          style: TextStyle(
            color: Colors.blue,
            fontSize: Dimens.sp_18,
            fontWeight: FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Future<void> _rejectOrder(BuildContext context, String reason) async {
    try {
      LoadingUtils.show();
      
      // 获取必要的参数
      DriverRegionEntity? region = context.read<DriverRegionAnnouncementModel>().regionEntity;
      String? regionId = region?.regionId;
      String? orderId = widget.entity?.route?.first.order?.sId;
      
      if (regionId == null || orderId == null) {
        LoadingUtils.dismiss();
        return;
      }
      
      // 调用driver model中的reject API
      var driverModel = context.read<DriverModel>();
      await driverModel.rejectOrder(regionId, orderId, reason);
      
      // 刷新订单数据
      await driverModel.driverOrders();
      
      LoadingUtils.dismiss();
      
    } catch (e) {
      LoadingUtils.dismiss();
      LogUtil.e("Reject order error: $e");
    }
  }
}
