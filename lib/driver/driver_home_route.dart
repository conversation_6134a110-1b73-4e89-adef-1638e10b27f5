import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/repository/login_repository.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/shifts/driver_shifts_route.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/gen/assets.gen.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>';
import 'package:connect/utils/tracking_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'assign/driver_assign_route.dart';
import 'bean/location_timer.dart';
import 'driver_route.dart';
import 'driver_support_route.dart';
import 'report/driver_report_route.dart';

class DriverHomeRoute extends StatefulWidget {
  static String tag = "driver_home_route";

  DriverHomeRoute({Key? key}) : super(key: key);

  @override
  _DriverHomeRouteState createState() => _DriverHomeRouteState();
}

class _DriverHomeRouteState extends State<DriverHomeRoute>
    with WidgetsBindingObserver {
  int _selectedIndex = 0;
  late List<Widget> _pages;
  var _pageController = PageController();

  List<BottomNavigationBarItem> _tabs = [];

  void _pageChanged(int index) {
    setState(() {
      if (_selectedIndex != index) _selectedIndex = index;
    });
  }

  void buildTabs() {
    _tabs = <BottomNavigationBarItem>[
      BottomNavigationBarItem(
        icon: Assets.images.driverTab.icoDeliveryDefault.svg(),
        activeIcon: Assets.images.driverTab.icoDeliverySelect.svg(),
        label: S.of(context).driver,
      ),
      BottomNavigationBarItem(
        icon: Assets.images.driverTab.icoOrderDefault.svg(),
        activeIcon: Assets.images.driverTab.icoOrderSelect.svg(),
        label: S.of(context).title_orders,
      ),
      BottomNavigationBarItem(
        icon: Assets.images.driverTab.icoReportDefault.svg(),
        activeIcon: Assets.images.driverTab.icoReportSelect.svg(),
        label: S.of(context).title_report,
      ),
      BottomNavigationBarItem(
        icon: Assets.images.driverTab.icoScheduleDefault.svg(),
        activeIcon: Assets.images.driverTab.icoScheduleSelect.svg(),
        label: S.of(context).driver_shifts,
      ),
      BottomNavigationBarItem(
        icon: Assets.images.driverTab.icoServiceDefault.svg(),
        activeIcon: Assets.images.driverTab.icoServiceSelect.svg(),
        label: S.of(context).title_support,
      ),
      BottomNavigationBarItem(
        icon: Assets.images.driverTab.icoMoreDefault.svg(),
        activeIcon: Assets.images.driverTab.icoMoreSelect.svg(),
        label: S.of(context).title_more,
      ),
    ];
  }

  void _onTabTapped(int index) {
    _pageController.jumpToPage(index);
    if (index == 0) {
      context.read<DriverModel>().driverOrders();
    }

    //tracking
    if (index == 0) {
      TrackingUtils.instance!.tracking(TConstants.D_DELIVERY_TAB_LICK);
    } else if (index == 2) {
      TrackingUtils.instance!.tracking(TConstants.D_ORDERS_TAB_CLICK);
    } else if (index == 3) {
      TrackingUtils.instance!.tracking(TConstants.D_REPORT_TAB_CLICK);
    } else if (index == 4) {
      TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_TAB_CLICK);
    } else if (index == 5) {
      TrackingUtils.instance!.tracking(TConstants.D_SUPPORT_TAB_CLICK);
    } else if (index == 6) {
      TrackingUtils.instance!.tracking(TConstants.D_MORE_TAB_CLICK);
    }
  }

  @override
  void initState() {
    super.initState();
    LogUtil.v("DriverHomeRoute::initState");
    LoginRepository.renewToken(context);
    WidgetsBinding.instance!.addObserver(this);
    _pages = [
      DriverRoute(),
      DriverAssignRoute(),
      DriverReportRouteV2(),
      DriverShiftsRoute(),
      DriverSupportRoute(),
      MoreRoute(),
    ];
    //tracking
    TrackingUtils.instance!.tracking(TConstants.D_DELIVERY_HOME_ENTER);
  }

  bool paused = false;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LogUtil.v("-didChangeAppLifecycleState-" + state.toString());
    switch (state) {
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.resumed:
        GlobalConfig.isBackground = false;
        // startTime();
        if (paused) {
          //check whether driver online
          GlobalConfig.eventBus.fire(CheckDriverOnline());
          TrackingUtils.instance!.tracking(TConstants.D_SWITCH_TO_FOREGROUND);
        }
        paused = false;

        if (GlobalConfig.paymentRefresh) {
          GlobalConfig.eventBus.fire(DriverSetupPaymentRefresh());
        }
        break;
      case AppLifecycleState.paused:
        paused = true;
        GlobalConfig.isBackground = true;
        cancelTime();
        GlobalConfig.eventBus.fire(SwitchBackground());

        TrackingUtils.instance!.tracking(TConstants.D_SWITCH_TO_BACKGROUND);
        break;
      case AppLifecycleState.detached:
        break;
      case AppLifecycleState.hidden:
       break;
    }
  }

  void startTime() {
    LocationTimer.startSubmit(context);
  }

  void cancelTime() {
    LocationTimer.cancelSubmit();
  }

  @override
  void dispose() {
    super.dispose();
    LogUtil.v("driver::dispose");
    cancelTime();
    _pageController.dispose();
    WidgetsBinding.instance!.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    buildTabs();
    return Scaffold(
      backgroundColor: Colors.grey[200],
      resizeToAvoidBottomInset: true,
      appBar: PreferredSize(
          child: AppBar(
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle.light,
            backgroundColor: Colors.white,
          ),
          preferredSize: Size.fromHeight(0.0)),
      body: PageView.builder(
        itemBuilder: (context, index) => _pages[index],
        controller: _pageController,
        physics: NeverScrollableScrollPhysics(),
        itemCount: _pages.length,
        onPageChanged: _pageChanged,
      ),
      bottomNavigationBar: BottomNavigationBar(
        selectedFontSize: Dimens.sp_12,
        unselectedFontSize: Dimens.sp_12,
        currentIndex: _selectedIndex,
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        fixedColor: RColors.r_orange,
        items: _tabs,
      ),
    );
  }
}
