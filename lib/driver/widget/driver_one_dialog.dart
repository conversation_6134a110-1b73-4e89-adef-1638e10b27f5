import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:flutter/material.dart';

class DriverOneDialog {
  static show(
      BuildContext context, String? content, {VoidCallback? callback}) async {
    await showDialog(
        barrierDismissible: false,
        context: context,
        builder: (context) {
          return AlertDialog(
            content: Text(content!),
            actions: [
              TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    if(callback!=null){
                      Future.delayed(Duration(milliseconds: 300))
                          .then((value) => callback());
                    }
                  },
                  child: Text(
                    S.of(context).ok,
                    style: TextStyle(
                        color: RColors.r_orange, fontWeight: FontWeight.bold),
                  ))
            ],
          );
        });
  }
}
