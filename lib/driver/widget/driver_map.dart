import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/bean/destination_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:map_launcher/map_launcher.dart';

///map actionSheet
class DriverMap {
  static show(BuildContext context, DestinationEntity? entity, String from) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context, entity, from);
        },
        context: context);
  }

  static launchWaze(String lat, String log) async {
    var url = 'waze://?ll=$lat%2C$log';
    var fallbackUrl = 'https://waze.com/ul?ll=$lat%2C$log&navigate=yes';
    try {
      bool launched =
          await launch(url, forceSafariVC: false, forceWebView: false);
      LogUtil.v("launched::$launched");
      if (!launched) {
        await launch(fallbackUrl, forceSafariVC: false, forceWebView: false);
      }
    } catch (e) {
      await launch(fallbackUrl, forceSafariVC: false, forceWebView: false);
    }
  }

  static tracking(
      String type, String? address, DestinationEntity entity, String from) {
    var params = Map<String, dynamic>();
    params["type"] = type;
    params["system"] = "${Platform.operatingSystem}";
    params["address"] = address;
    params["lat"] = "${entity.lat}";
    params["log"] = "${entity.log}";
    if ("pickup" == from) {
      TrackingUtils.instance!
          .tracking(TConstants.D_DELIVERY_PICKUP_MAP_CLICK, value: params);
    } else if ("dropoff" == from) {
      TrackingUtils.instance!
          .tracking(TConstants.D_DELIVERY_DROPOFF_MAP_CLICK, value: params);
    } else if ("shifts" == from) {
      TrackingUtils.instance!
          .tracking(TConstants.D_SHIFTS_LOCATION_CLICK, value: params);
    }

    // var sentryEvent = SentryEvent(extra: params, throwable: "map");
    // Sentry.captureEvent(sentryEvent);
  }

  static Widget buildBottomSheetWidget(
      BuildContext context, DestinationEntity? entity, String from) {
    return Container(
      height: Platform.isIOS ? Dimens.dp_220 : Dimens.dp_170,
      child: Column(
        children: [
          buildItem(context, S.of(context).driver_google_maps, //google maps
              onTap: () async {
            if (entity!.formatted == null) {
              return;
            }
            tracking("google_map", entity.formatted, entity, from);
            bool? available = await MapLauncher.isMapAvailable(MapType.google);
            if (available == null || !available) {
              ToastUtils.show(S.of(GlobalConfig.context()).driver_google_map);
              await Future.delayed(Duration(seconds: 1));
              var lat = entity.lat.toString();
              var log = entity.log.toString();
              launch(
                  "https://www.google.com/maps/search/?api=1&query=${lat}%2C${log}");
              return;
            }

            await MapLauncher.showMarker(
                mapType: MapType.google,
                coords: Coords(entity.lat!, entity.log!),
                title: entity.formatted ?? "");
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, S.of(context).driver_waze_maps, //waze maps
              onTap: () async {
            if (entity?.formatted != null) {
              tracking("waze_map", entity!.formatted, entity, from);
              launchWaze(
                entity.lat.toString(),
                entity.log.toString(),
              ); //if the address is invalid ,then ios can not forward to safari browser
            }
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          Platform.isIOS
              ? buildItem(context, S.of(context).driver_app_maps,
                  onTap: () async {
                  //app map
                  try {
                    if (entity!.formatted != null) {
                      String formatted = entity.formatted!.replaceAll("#", " ");

                      tracking("app_map", entity.formatted, entity, from);

                      if ("pickup" == from) {
                        formatted = entity.resName!;
                      }

                      // LogUtil.v("formatted::$formatted,lat::${entity.lat},log::${entity.log}");
                      MethodChannel _channel =
                          const MethodChannel('com.connect/print');
                      var arguments = Map();
                      arguments["lat"] = "${entity.lat}";
                      arguments["log"] = "${entity.log}";
                      arguments["address"] = formatted;
                      arguments["from"] = entity.from;
                      await _channel.invokeMethod('appleMap', arguments);
                    }
                  } catch (e) {
                    LogUtil.v("ios::${e.toString()}");
                  }
                })
              : EmptyContainer(), //apple maps
          Divider(
            color: Colors.grey[200],
            thickness: Dimens.dp_10,
          ),
          buildItem(context, S.of(context).cancel), //cancel
        ],
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        await Future.delayed(Duration(milliseconds: 300));
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_50,
        child: Text(
          title,
          style: TextStyle(
              color: S.of(context).cancel == title
                  ? Colors.black87
                  : MyColors.portal,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
