import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:flutter/material.dart';

class DriverDialog {
  static show(
      BuildContext context, String content, VoidCallback callback,{ValueChanged<String>? valueChanged}) async {
    await showDialog(
        barrierDismissible: false,
        context: context,
        builder: (context) {
          return AlertDialog(
            content: Text(content),
            actions: [
              TextButton(
                  onPressed: () {
                    if (valueChanged != null) valueChanged("N");
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    S.of(context).cancel,
                    style: TextStyle(
                        color: Colors.black87, fontWeight: FontWeight.bold),
                  )),
              TextButton(
                  onPressed: () {
                    if (valueChanged != null) valueChanged("Y");
                    Navigator.of(context).pop();
                    Future.delayed(Duration(milliseconds: 300))
                        .then((value) => callback());
                  },
                  child: Text(
                    S.of(context).confirm,
                    style: TextStyle(
                        color: RColors.r_orange, fontWeight: FontWeight.bold),
                  ))
            ],
          );
        });
  }
}
