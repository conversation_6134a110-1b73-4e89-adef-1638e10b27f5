import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';

class DriverAnnouncementDialog extends Dialog {
  DriverAnnouncementDialog(this.callback, this.motd, {Key? key})
      : super(key: key);

  final VoidCallback callback;
  final List<MultiNameEntity> motd;

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: Dimens.dp_420,
          child: Container(
            margin: const EdgeInsets.all(Dimens.dp_15),
            padding: const EdgeInsets.fromLTRB(
                Dimens.dp_15, Dimens.dp_0, Dimens.dp_15, Dimens.dp_15),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(Dimens.dp_15),
            ),
            child: Column(
              children: [
                Container(
                  color: Colors.white,
                  alignment: Alignment.center,
                  height: Dimens.dp_50,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Text(
                                S.of(context).cancel,
                                style: TextStyle(
                                    color: Colors.black87,
                                    fontSize: Dimens.sp_15),
                              ),
                            ),
                            Text(S.of(context).driver_announcements,
                                style: TextStyle(
                                    color: Colors.black87,
                                    fontSize: Dimens.sp_16,
                                    fontWeight: FontWeight.bold)),
                            Container(
                              width: Dimens.dp_50,
                              height: Dimens.dp_30,
                            ),
                          ],
                        ),
                      ),
                      Divider(
                        height: Dimens.dp_1,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.separated(
                      itemBuilder: (context, index) {
                        return Column(
                          children: [
                            SizedBox(
                              height: Dimens.dp_10,
                            ),
                            Text("${index + 1}.${ServerMultiLan.multiAdapt(motd[index])}${motd[index].enUs}"),
                          ],
                        );
                      },
                      separatorBuilder: (context, index) {
                        return SizedBox(
                          height: Dimens.dp_10,
                        );
                      },
                      itemCount: motd.length),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
