import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class DriverStar extends StatefulWidget {
  DriverStar(this.factor, this.reason, {Key? key}) : super(key: key);

  final num? factor;
  final List<String>? reason;

  @override
  _DriverStarState createState() => _DriverStarState();
}

class _DriverStarState extends State<DriverStar> {
  String reasonStr = "";

  String _buildReason(List<String> reasons) {
    reasonStr = "";
    reasons.forEach((element) {
      reasonStr = reasonStr + "$element ";
    });
    return reasonStr;
  }

  @override
  Widget build(BuildContext context) {
    _buildReason(widget.reason!);
    return Column(
      children: [
        SizedBox(
          height: Dimens.dp_3,
        ),
        Row(
          children: _getGradeStar(widget.factor!.toDouble(), 5),
        ),
        SizedBox(
          height: Dimens.dp_3,
        ),
        Row(
          children: [
            Expanded(child: Text("$reasonStr")),
          ],
        )
      ],
    );
  }

  List<Widget> _getGradeStar(double score, int total) {
    List<Widget> _list = [];
    for (var i = 0; i < total; i++) {
      double factor = (score - i);
      if (factor >= 1) {
        factor = 1.0;
      } else if (factor < 0) {
        factor = 0;
      }
      Stack _st = Stack(
        children: <Widget>[
          Icon(
            Icons.star,
            color: Colors.grey,
            size: Dimens.dp_16,
          ),
          ClipRect(
              child: Align(
            alignment: Alignment.topLeft,
            widthFactor: factor,
            child: Icon(
              Icons.star,
              color: Colors.blue,
              size: Dimens.dp_16,
            ),
          ))
        ],
      );
      _list.add(_st);
    }
    return _list;
  }
}
