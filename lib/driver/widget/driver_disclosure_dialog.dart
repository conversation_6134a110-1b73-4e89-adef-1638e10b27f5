import 'package:connect/common/constants.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:connect/generated/l10n.dart';

class DriverDisclosureDialog extends Dialog {
  DriverDisclosureDialog(this.callback, {Key? key}) : super(key: key);

  final VoidCallback callback;

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: SizedBox(
          width: Dimens.dp_300,
          height: Dimens.dp_420,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: Dimens.dp_15),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(Dimens.dp_15),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Icon(
                  Icons.location_on_outlined,
                  color: Colors.blue,
                ),
                SizedBox(
                  height: Dimens.dp_5,
                ),
                Text(
                  S.of(context).driver_use_location_title,
                  style: TextStyle(
                      fontSize: Dimens.sp_17,
                      color: Colors.black,
                      fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  height: Dimens.dp_15,
                ),
                Text(
                  S.of(context).driver_location_disclosure,
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: Dimens.dp_10,
                ),
                Text(
                  S.of(context).driver_location_access_description,
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: Dimens.dp_30,
                ),
                Image.asset(
                  'assets/images/maps.png',
                  width: Dimens.dp_100,
                  fit: BoxFit.cover,
                  height: Dimens.dp_100,
                ),
                SizedBox(
                  height: Dimens.dp_30,
                ),
                Container(
                  padding:
                      EdgeInsets.only(left: Dimens.dp_15, right: Dimens.dp_15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      TextButton(
                        onPressed: () {
                          //tracking
                          TrackingUtils.instance!.tracking(TConstants.D_DELIVERY_NO_THANKS_CLICK);
                          Navigator.pop(context);
                        },
                        child: Text(
                          S.of(context).driver_no_thanks,
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: Dimens.sp_15,
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: () async {
                          Future.delayed(Duration(milliseconds: 300))
                              .then((value) => callback());
                          //tracking
                          TrackingUtils.instance!.tracking(TConstants.D_DELIVERY_TURN_ON_CLICK);
                          Navigator.pop(context);
                        },
                        child: Text(
                          S.of(context).driver_turn_on,
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: Dimens.sp_15,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
