import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:url_launcher/url_launcher.dart';

///phone actionSheet
class DriverPhone {
  static show(BuildContext context, String? phone, bool isPickup) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context, phone, isPickup);
        },
        context: context);
  }

  static Widget buildBottomSheetWidget(
      BuildContext context, String? phone, bool isPickup) {
    return Container(
      height: isPickup ? Dimens.dp_120 : Dimens.dp_170,
      child: Column(
        children: [
          buildItem(context, S.of(context).driver_call, //google maps
              onTap: () async {
            LogManager.instance!.log("DriverPhone", "", "phone::$phone");
            if (phone != null && phone.isNotEmpty) {
              var params = Map<String, dynamic>();
              params["phone"] = "$phone";
              TrackingUtils.instance!
                  .tracking(TConstants.D_DELIVERY_CALL_CLICK, value: params);

              await launch("tel:$phone");
            }
          }),
          !isPickup
              ? Divider(
                  height: Dimens.dp_1,
                )
              : EmptyContainer(),
          !isPickup
              ? buildItem(context, S.of(context).driver_sms, onTap: () async {
                  LogManager.instance!.log("DriverPhone", "", "sms::$phone");
                  if (phone != null && phone.isNotEmpty) {
                    var params = Map<String, dynamic>();
                    params["phone"] = "$phone";
                    TrackingUtils.instance!.tracking(
                        TConstants.D_DELIVERY_SMS_CLICK,
                        value: params);

                    await launch("sms:$phone");
                  }
                })
              : EmptyContainer(), //apple maps
          Divider(
            color: Colors.grey[200],
            thickness: Dimens.dp_10,
          ),
          buildItem(context, S.of(context).cancel), //cancel
        ],
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        await Future.delayed(Duration(milliseconds: 300));
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_50,
        child: Text(
          title,
          style: TextStyle(
              color: S.of(context).cancel == title
                  ? Colors.black87
                  : MyColors.portal,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
