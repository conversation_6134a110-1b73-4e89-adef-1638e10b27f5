import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:sprintf/sprintf.dart';

import 'driver_one_dialog.dart';

class DriverMoreDialog extends Dialog {
  DriverMoreDialog({Key? key}) : super(key: key);

  String inputCode = "";

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: SizedBox(
          width: Dimens.dp_300,
          height: Dimens.dp_200,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(Dimens.dp_15),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Container(
                  alignment: Alignment.topCenter,
                  margin: EdgeInsets.only(top: Dimens.dp_25),
                  child: Text(
                    S.of(context).driver_input_four_passcode,
                    style: TextStyle(
                        fontSize: Dimens.sp_16,
                        color: Colors.black,
                        fontWeight: FontWeight.bold),
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  child: TextField(
                    onChanged: (v) {
                      inputCode = v;
                    },
                    textAlignVertical: TextAlignVertical.bottom,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(4),
                      FilteringTextInputFormatter.digitsOnly
                    ],
                    autofocus: true,
                    style: TextStyle(
                        fontSize: Dimens.sp_20,
                        color: Colors.black,
                        fontWeight: FontWeight.bold),
                    decoration: InputDecoration(
                        isDense: true,
                        contentPadding: EdgeInsets.fromLTRB(Dimens.dp_10,
                            Dimens.dp_8, Dimens.dp_10, Dimens.dp_8),
                        focusedBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                                color: Colors.black, width: Dimens.dp_1)),
                        border: OutlineInputBorder(
                            borderSide: BorderSide(width: Dimens.dp_1))),
                  ),
                  constraints: BoxConstraints(maxWidth: Dimens.dp_75),
                ),
                Container(
                  margin: EdgeInsets.only(bottom: Dimens.dp_15),
                  padding:
                      EdgeInsets.only(left: Dimens.dp_25, right: Dimens.dp_25),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: <Widget>[
                      ElevatedButton(
                        onPressed: () {
                          TrackingUtils.instance!.tracking(
                              TConstants.D_DELIVERY_GET_MORE_CANCEL_CLICK);
                          Navigator.pop(context);
                        },
                        style: ButtonStyle(
                            padding: MaterialStateProperty.all(EdgeInsets.only(
                                left: Dimens.dp_20,
                                top: Dimens.dp_8,
                                right: Dimens.dp_20,
                                bottom: Dimens.dp_8)),
                            backgroundColor: MaterialStateProperty.all(Colors.white),
                            elevation: MaterialStateProperty.all(0.2),
                            shape: MaterialStateProperty.all(RoundedRectangleBorder(
                                side: BorderSide(
                                    color: MyColors.portal, width: Dimens.dp_1),
                                borderRadius: BorderRadius.circular(Dimens.dp_25)))
                        ),
                        child: Text(
                          S.of(context).cancel,
                          style: TextStyle(
                            color: MyColors.portal,
                            fontSize: Dimens.sp_15,
                          ),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () async {
                          if (inputCode.length == 4) {
                            var entity =
                                context.read<DriverModel>().driverOrderEntity!;
                            if (entity.route != null) {
                              var params = Map<String, dynamic>();
                              params["passcode"] = inputCode;
                              TrackingUtils.instance!.tracking(
                                  TConstants.D_DELIVERY_GET_MORE_CONFIRM_CLICK,
                                  value: params);

                              bool isFind = false;
                              var nextResId =
                                  entity.next!.order!.restaurant!.sId;
                              String? routeOrderId = "";

                              /// start to match order
                              entity.route!.forEach((element) {
                                var type = element.type;
                                var routePassCode = element.order?.passcode;
                                var resId = element.order?.restaurant?.sId;
                                if (type == "pickup" &&
                                    nextResId == resId &&
                                    routePassCode == inputCode) {
                                  routeOrderId = element.order?.sId;
                                  isFind = true;
                                }
                              });

                              /// No matched order was found
                              if (!isFind) {
                                var response = await context
                                    .read<DriverModel>()
                                    .menuReady(nextResId, inputCode);
                                if (response != null) {
                                  DriverOneDialog.show(context,
                                      S.of(context).driver_thanks_feedback,
                                      callback: () {
                                    Navigator.pop(context);
                                  });
                                }
                              } else {
                                ///matched
                                LoadingUtils.show();
                                var response = await context
                                    .read<DriverModel>()
                                    .matchOrder(nextResId, routeOrderId);
                                if (response == null) return;

                                TrackingUtils.instance!.tracking(
                                    TConstants
                                        .D_DELIVERY_GET_MORE_MATCH_SUCCESS,
                                    value: params);

                                var responsePick = await context
                                    .read<DriverModel>()
                                    .pickComplete(routeOrderId);
                                if (responsePick == null) return;

                                TrackingUtils.instance!.tracking(
                                    TConstants.D_DELIVERY_GET_MORE_SUCCESS,
                                    value: params);

                                DriverOneDialog.show(
                                    context,
                                    sprintf(S.of(context).driver_match_order,
                                        ["$inputCode"]), callback: () {
                                  Navigator.pop(context);
                                });
                              }
                            }
                          } else {
                            ToastUtils.show(
                                S.of(context).driver_input_passcode);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: MyColors.portal,
                            elevation: 0.2,
                            padding: EdgeInsets.only(
                                left: Dimens.dp_20,
                                top: Dimens.dp_8,
                                right: Dimens.dp_20,
                                bottom: Dimens.dp_8),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(Dimens.dp_25))
                        ),
                        child: Text(
                          S.of(context).confirm,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: Dimens.sp_15,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
