import 'package:connect/common/MyStyles.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/ui/pickup_failed/pickup_failed.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/object_util.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import 'bean/destination_entity.dart';
import 'bean/driver_orders_entity.dart';
import 'bean/menu_entity.dart';
import 'driver_destination.dart';
import 'driver_flow.dart';
import 'driver_more_orders.dart';
import 'driver_stops.dart';

class DriverMain extends StatefulWidget {
  DriverMain(this.stop, this.isMoreStop, {Key? key}) : super(key: key);

  final DriverOrdersNext? stop;
  final bool isMoreStop;

  @override
  _DriverMainState createState() => _DriverMainState();
}

class _DriverMainState extends State<DriverMain>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  String nextFlow = "";
  late DestinationEntity destinationEntity;
  late MenuEntity menuEntity;

  ///create destination and menu object
  buildDesAndMenuObj(bool isPickup, DriverOrdersNext? next, String? title,
      String? note, String? num) {
    //destination
    destinationEntity.formatted = next?.address?.formatted;
    if (isPickup) {
      //pickup
      var name = next?.order?.restaurant?.name;
      destinationEntity.resName =
          "${ServerMultiLan.multiAdapt(name)} ${name?.enUs ?? ""}";

      var note = next?.order?.restaurant?.delivery?.note;
      destinationEntity.note = "${ServerMultiLan.multiAdapt2(note)}";

      destinationEntity.phone = "";

      //stops.next.order.confirmedAt + stops.next.order.restaurant.delivery.prepare
      var prepare = next?.order?.restaurant?.delivery?.prepare;
      var confirmedAt = next?.order?.confirmedAt;

      if (prepare != null && confirmedAt != null) {
        String est = formatDate(
            DateUtil.getDateTime(confirmedAt)!
                .toLocal()
                .add(Duration(minutes: prepare.toInt())),
            [HH, ':', nn]);
        destinationEntity.est = est;
      }
    } else {
      //dropoff
      destinationEntity.resName = "";
      destinationEntity.addressUnit = next?.address?.unit;
      destinationEntity.note = next?.address?.note;
      destinationEntity.phone = next?.phone;
      destinationEntity.est = "";
    }
    destinationEntity.distance = next?.distance;
    var arriveAt = next?.arriveAt;
    if (ObjectUtil.isNotEmpty(arriveAt)) {
      String formatArriveAt =
          formatDate(DateUtil.getDateTime(arriveAt!)!.toLocal(), [HH, ':', nn]);
      destinationEntity.arriveAt = formatArriveAt;
    }
    destinationEntity.lat = next?.address?.location?.coordinates?[1];
    destinationEntity.log = next?.address?.location?.coordinates?[0];
    destinationEntity.next = next;

    //menu
    menuEntity.orderTitle = title;
    menuEntity.orderNum = num;
    menuEntity.orderSecondaryLine = note;
    menuEntity.phone = next?.phone;
    menuEntity.next = next;

    menuEntity.bags = next?.order?.delivery?.bags ?? 1;
  }

  @override
  void initState() {
    super.initState();
    destinationEntity = DestinationEntity();
    menuEntity = MenuEntity();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    LogUtil.v("DriverMain::build");
    bool isShowMore = false;
    bool isPickup = false;
    bool routeToPickUp = false;
    int bags = 0;

    DriverOrdersNext? next = widget.stop;

    //bags count
    bags = next?.order?.delivery?.bags?.toInt() ?? 0;

    /// en route to pickup
    if (next != null &&
        next.type == "pickup" &&
        ObjectUtil.isEmpty(next.arrivedAt)) {
      nextFlow = S.of(context).driver_order_at_pickup;
      isPickup = true;
      routeToPickUp = true;
      buildDesAndMenuObj(
          true,
          next,
          S.of(context).driver_order_title,
          S.of(context).driver_order_note,
          sprintf(S.of(context).driver_order_num, ["XX"]));
      LogUtil.v("pickup");
    } else if (next != null &&
        next.type == "pickup" &&
        (next.arrivedAt != null && next.arrivedAt!.isNotEmpty)) {
      /// at pickup
      nextFlow = S.of(context).driver_order_pickup_complete;
      isPickup = true;
      isShowMore = true;
      String passcode;
      if (ConnectCache.isRelease()) {
        passcode = "${next.order?.passcode?.substring(0, 2)}";
      } else {
        passcode = "${next.order?.passcode}";
      }
      buildDesAndMenuObj(
          true,
          next,
          S.of(context).driver_order_title,
          S.of(context).driver_order_at_pickup_note,
          sprintf(S.of(context).driver_order_num, ["$passcode"]));
      LogUtil.v("at pickup");
    } else if (next != null &&
        next.type == "dropoff" &&
        (next.arrivedAt == null || next.arrivedAt!.isEmpty)) {
      /// route to dropoff
      nextFlow = S.of(context).driver_order_at_dropoff;
      buildDesAndMenuObj(
          false,
          next,
          S.of(context).driver_route_dropoff,
          next.order?.restaurant?.name?.enUs,
          sprintf(S.of(context).driver_route_dropoff_num,
              ["${next.order?.passcode}"]));
    } else if (next != null &&
        next.type == "dropoff" &&
        (next.arrivedAt != null && next.arrivedAt!.isNotEmpty)) {
      /// at dropoff
      nextFlow = S.of(context).driver_order_delivery_complete;
      buildDesAndMenuObj(
          false,
          next,
          S.of(context).driver_route_dropoff,
          next.order?.restaurant?.name?.enUs,
          sprintf(S.of(context).driver_route_dropoff_num,
              ["${next.order?.passcode}"]));
    }

    return Card(
      elevation: 0,
      margin: EdgeInsets.symmetric(horizontal: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            DriverDestination(
                destinationEntity, isPickup, routeToPickUp, widget.isMoreStop),
            SizedBox(height: 16),
            Divider(color: MyColors.line),
            SizedBox(height: 16),
            DriverStops(menuEntity, isPickup),
            isShowMore ? DriverMoreOrders(routeToPickUp) : EmptyContainer(),
            isShowMore || (routeToPickUp && !ConnectCache.getOnline()!)
                ? PickupFailed(routeToPickUp)
                : EmptyContainer(),
            SizedBox(height: 32),
            DriverFlow(
                nextFlow,
                widget.stop?.order?.sId,
                widget.stop?.order?.restaurant?.sId,
                widget.stop!.isNext,
                widget.stop?.order?.passcode,
                bags)
          ],
        ),
      ),
    );
  }
}
