import 'package:connect/generated/json/driver_orders_entity.g.dart';

import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class DriverOrdersEntity {

	DriverOrdersEntity();

	factory DriverOrdersEntity.fromJson(Map<String, dynamic> json) => $DriverOrdersEntityFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersEntityToJson(this);

  int? alert;
  List<DriverOrdersRoute>? route;
  List<DriverOrdersNext>? more;//对象数据格式相同
  String? startAt;
  DriverOrdersNext? next;
}

@JsonSerializable()
class DriverOrdersMore {

	DriverOrdersMore();

	factory DriverOrdersMore.fromJson(Map<String, dynamic> json) => $DriverOrdersMoreFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersMoreToJson(this);

  @JSONField(name: "location_id")
  String? locationId;
  @J<PERSON><PERSON>ield(name: "location_name")
  String? locationName;
  @J<PERSON><PERSON>ield(name: "arrival_time")
  int? arrivalTime;
  @JSONField(name: "finish_time")
  int? finishTime;
  String? type;
  double? distance;
  String? arriveAt;
  String? finishAt;
  String? estimateAt;
  String? phone;
  DriverOrdersMoreOrder? order;
}

@JsonSerializable()
class DriverOrdersRoute {

	DriverOrdersRoute();

	factory DriverOrdersRoute.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteToJson(this);

  @JSONField(name: "location_id")
  String? locationId;
  @JSONField(name: "location_name")
  String? locationName;
  @JSONField(name: "arrival_time")
  int? arrivalTime;
  @JSONField(name: "finish_time")
  int? finishTime;
  String? type;
  double? distance;
  @JSONField(name: "too_late")
  bool? tooLate;
  @JSONField(name: "late_by")
  DriverOrdersRouteLateBy? lateBy;
  String? arriveAt;
  String? arrivedAt;
  String? finishAt;
  String? estimateAt;
  DriverOrdersRouteAddress? address;
  String? phone;
  DriverOrdersRouteOrder? order;
}

@JsonSerializable()
class DriverOrdersRouteLateBy {

	DriverOrdersRouteLateBy();

	factory DriverOrdersRouteLateBy.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteLateByFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteLateByToJson(this);

  int? minutes;
  int? seconds;
}

@JsonSerializable()
class DriverOrdersRouteAddress {

	DriverOrdersRouteAddress();

	factory DriverOrdersRouteAddress.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteAddressFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteAddressToJson(this);

  String? unit;
  String? city;
  String? note;
  DriverOrdersRouteAddressLocation? location;
  String? zipcode;
  String? country;
  String? formatted;
  String? state;
}

@JsonSerializable()
class DriverOrdersRouteAddressLocation     {

	DriverOrdersRouteAddressLocation();

	factory DriverOrdersRouteAddressLocation.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteAddressLocationFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteAddressLocationToJson(this);

  String? type;
  List<double>? coordinates;
}

@JsonSerializable()
class DriverOrdersRouteOrder {

	DriverOrdersRouteOrder();

	factory DriverOrdersRouteOrder.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteOrderFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteOrderToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? passcode;
  String? createdAt;
  DriverOrdersRouteOrderRegion? region;
  String? confirmedAt;
  DriverOrdersRouteOrderDelivery? delivery;
  DriverOrdersRouteOrderRestaurant? restaurant;
  DriverOrdersRouteOrderCustomer? customer;
  List<DriverOrdersRouteOrderItem>? items;
}

@JsonSerializable()
class DriverOrdersMoreOrder {

	DriverOrdersMoreOrder();

	factory DriverOrdersMoreOrder.fromJson(Map<String, dynamic> json) => $DriverOrdersMoreOrderFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersMoreOrderToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? passcode;
  String? createdAt;
  DriverOrdersMoreOrderRegion? region;
  String? confirmedAt;
  DriverOrdersMoreOrderRestaurant? restaurant;
}

@JsonSerializable()
class DriverOrdersMoreOrderRegion     {

	DriverOrdersMoreOrderRegion();

	factory DriverOrdersMoreOrderRegion.fromJson(Map<String, dynamic> json) => $DriverOrdersMoreOrderRegionFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersMoreOrderRegionToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? name;
}

@JsonSerializable()
class DriverOrdersRouteOrderRegion     {

	DriverOrdersRouteOrderRegion();

	factory DriverOrdersRouteOrderRegion.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteOrderRegionFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteOrderRegionToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? name;
}

@JsonSerializable()
class DriverOrdersRouteOrderDelivery     {

	DriverOrdersRouteOrderDelivery();

	factory DriverOrdersRouteOrderDelivery.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteOrderDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteOrderDeliveryToJson(this);

  String? provider;
  DriverOrdersRouteOrderDeliveryAddress? address;
  num? driverPay;
}

@JsonSerializable()
class DriverOrdersRouteOrderDeliveryAddress {

	DriverOrdersRouteOrderDeliveryAddress();

	factory DriverOrdersRouteOrderDeliveryAddress.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteOrderDeliveryAddressFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteOrderDeliveryAddressToJson(this);

  String? formatted;
  String? unit;
  String? city;
  String? state;
  String? country;
  String? zipcode;
}

@JsonSerializable()
class DriverOrdersRouteOrderRestaurant     {

	DriverOrdersRouteOrderRestaurant();

	factory DriverOrdersRouteOrderRestaurant.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteOrderRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteOrderRestaurantToJson(this);

  DriverOrdersRouteOrderRestaurantName? name;
  @JSONField(name: "_id")
  String? sId;
  DriverOrdersRouteOrderRestaurantDelivery? delivery;
}

@JsonSerializable()
class DriverOrdersMoreOrderRestaurant     {

	DriverOrdersMoreOrderRestaurant();

	factory DriverOrdersMoreOrderRestaurant.fromJson(Map<String, dynamic> json) => $DriverOrdersMoreOrderRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersMoreOrderRestaurantToJson(this);

  DriverOrdersMoreOrderRestaurantName? name;
  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class DriverOrdersRouteOrderRestaurantName     {

	DriverOrdersRouteOrderRestaurantName();

	factory DriverOrdersRouteOrderRestaurantName.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteOrderRestaurantNameFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteOrderRestaurantNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class DriverOrdersMoreOrderRestaurantName     {

	DriverOrdersMoreOrderRestaurantName();

	factory DriverOrdersMoreOrderRestaurantName.fromJson(Map<String, dynamic> json) => $DriverOrdersMoreOrderRestaurantNameFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersMoreOrderRestaurantNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class DriverOrdersRouteOrderRestaurantDelivery     {

	DriverOrdersRouteOrderRestaurantDelivery();

	factory DriverOrdersRouteOrderRestaurantDelivery.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteOrderRestaurantDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteOrderRestaurantDeliveryToJson(this);

  int? prepare;
  dynamic note;
  dynamic batch;
  dynamic scheduled;
}

@JsonSerializable()
class DriverOrdersRouteOrderCustomer     {

	DriverOrdersRouteOrderCustomer();

	factory DriverOrdersRouteOrderCustomer.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteOrderCustomerFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteOrderCustomerToJson(this);

  String? language;
}

@JsonSerializable()
class DriverOrdersRouteOrderItem {

	DriverOrdersRouteOrderItem();

	factory DriverOrdersRouteOrderItem.fromJson(Map<String, dynamic> json) => $DriverOrdersRouteOrderItemFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersRouteOrderItemToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class DriverOrdersNext {

	DriverOrdersNext();

	factory DriverOrdersNext.fromJson(Map<String, dynamic> json) => $DriverOrdersNextFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextToJson(this);

  @JSONField(name: "location_id")
  String? locationId;
  @JSONField(name: "location_name")
  String? locationName;
  @JSONField(name: "arrival_time")
  int? arrivalTime;
  @JSONField(name: "finish_time")
  int? finishTime;
  String? type;
  double? distance;
  String? arriveAt;
  String? arrivedAt;
  String? failAt;
  DriverOrdersNextAddress? address;
  String? phone;
  DriverOrdersNextOrder? order;
  bool isNext = false;//complete dropoff,when current tab is next,orderId do not pass to serve.more will pass.
}

@JsonSerializable()
class DriverOrdersNextAddress {

	DriverOrdersNextAddress();

	factory DriverOrdersNextAddress.fromJson(Map<String, dynamic> json) => $DriverOrdersNextAddressFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextAddressToJson(this);

  String? number;
  String? street;
  String? unit;
  String? city;
  String? state;
  String? country;
  String? zipcode;
  String? formatted;
  DriverOrdersNextAddressLocation? location;
  String? note;
}

@JsonSerializable()
class DriverOrdersNextAddressLocation {

	DriverOrdersNextAddressLocation();

	factory DriverOrdersNextAddressLocation.fromJson(Map<String, dynamic> json) => $DriverOrdersNextAddressLocationFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextAddressLocationToJson(this);

  String? type;
  List<double>? coordinates;
}

@JsonSerializable()
class DriverOrdersNextOrder {

	DriverOrdersNextOrder();

	factory DriverOrdersNextOrder.fromJson(Map<String, dynamic> json) => $DriverOrdersNextOrderFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextOrderToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? passcode;
  String? createdAt;
  DriverOrdersNextOrderRegion? region;
  String? confirmedAt;
  DriverOrdersNextOrderDelivery? delivery;
  DriverOrdersNextOrderRestaurant? restaurant;
  DriverOrdersNextOrderCustomer? customer;
  List<DriverOrdersNextOrderItem>? items;
}

@JsonSerializable()
class DriverOrdersNextOrderRegion     {

	DriverOrdersNextOrderRegion();

	factory DriverOrdersNextOrderRegion.fromJson(Map<String, dynamic> json) => $DriverOrdersNextOrderRegionFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextOrderRegionToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? name;
}

@JsonSerializable()
class DriverOrdersNextOrderDelivery     {

	DriverOrdersNextOrderDelivery();

	factory DriverOrdersNextOrderDelivery.fromJson(Map<String, dynamic> json) => $DriverOrdersNextOrderDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextOrderDeliveryToJson(this);

  String? provider;
  num? bags;
}

@JsonSerializable()
class DriverOrdersNextOrderRestaurant     {

	DriverOrdersNextOrderRestaurant();

	factory DriverOrdersNextOrderRestaurant.fromJson(Map<String, dynamic> json) => $DriverOrdersNextOrderRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextOrderRestaurantToJson(this);

  MultiNameEntity? name;
  @JSONField(name: "_id")
  String? sId;
  DriverOrdersNextOrderRestaurantDelivery? delivery;
}

@JsonSerializable()
class DriverOrdersNextOrderRestaurantDelivery     {

	DriverOrdersNextOrderRestaurantDelivery();

	factory DriverOrdersNextOrderRestaurantDelivery.fromJson(Map<String, dynamic> json) => $DriverOrdersNextOrderRestaurantDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextOrderRestaurantDeliveryToJson(this);

  MultiNameEntity? note;
  String? batch;
  num? prepare;
}

@JsonSerializable()
class DriverOrdersNextOrderCustomer     {

	DriverOrdersNextOrderCustomer();

	factory DriverOrdersNextOrderCustomer.fromJson(Map<String, dynamic> json) => $DriverOrdersNextOrderCustomerFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextOrderCustomerToJson(this);

  String? language;
}

@JsonSerializable()
class DriverOrdersNextOrderItem {

	DriverOrdersNextOrderItem();

	factory DriverOrdersNextOrderItem.fromJson(Map<String, dynamic> json) => $DriverOrdersNextOrderItemFromJson(json);

	Map<String, dynamic> toJson() => $DriverOrdersNextOrderItemToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}
