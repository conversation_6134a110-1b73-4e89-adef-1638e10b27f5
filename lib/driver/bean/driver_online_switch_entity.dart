import 'package:connect/generated/json/driver_online_switch_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class DriverOnlineSwitchEntity {

	DriverOnlineSwitchEntity();

	factory DriverOnlineSwitchEntity.fromJson(Map<String, dynamic> json) => $DriverOnlineSwitchEntityFromJson(json);

	Map<String, dynamic> toJson() => $DriverOnlineSwitchEntityToJson(this);

	@JSONField(name: "_id")
	String? sId;
	bool? onCall;
}
