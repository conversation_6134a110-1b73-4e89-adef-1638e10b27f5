import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/location_manager.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/cupertino.dart';
import 'dart:async';
import 'package:location/location.dart';

class LocationTimer {
  static Timer? _timer;

  ///start submit location to server when app in foreground
  static startSubmit(BuildContext context) {
    if (_timer == null) {
      _timer = Timer.periodic(Duration(seconds: 10), (Timer t) async {
        var hasPermission = await Location.instance.hasPermission();
        if (hasPermission == PermissionStatus.granted) {
          var position = await LocationManager.getCurrentPosition();

          if (position == null) {
            return;
          }

          var res = await DriverModel.submitLocation(
              position.latitude, position.longitude);

          if (res != null) {
            LogUtil.v("report success,${DateUtil.getNowDateStr()}");
          }

          LogManager.instance!.uploadLog(false);
        }
      });
    }
  }

  /// cancel submit
  static cancelSubmit() {
    if (_timer != null) {
      _timer!.cancel();
      _timer = null;
    }
  }
}
