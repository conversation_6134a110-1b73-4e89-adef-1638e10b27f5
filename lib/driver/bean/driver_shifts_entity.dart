import 'package:connect/generated/json/driver_shifts_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class DriverShiftsEntity {

	DriverShiftsEntity();

	factory DriverShiftsEntity.fromJson(Map<String, dynamic> json) => $DriverShiftsEntityFromJson(json);

	Map<String, dynamic> toJson() => $DriverShiftsEntityToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? start;
  String? end;
  String? batch;
  DriverShiftsRegion? region;
  List<DriverShiftsDriver>? drivers;
  double? instances;
  double? available;
  num? extraPay;
  num? guarantee;
  String? notes;
  String? address;
  bool isMyShift = false;
  bool isContained = false;
}

@JsonSerializable()
class DriverShiftsRegion {

	DriverShiftsRegion();

	factory DriverShiftsRegion.fromJson(Map<String, dynamic> json) => $DriverShiftsRegionFromJson(json);

	Map<String, dynamic> toJson() => $DriverShiftsRegionToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class DriverShiftsDriver {

	DriverShiftsDriver();

	factory DriverShiftsDriver.fromJson(Map<String, dynamic> json) => $DriverShiftsDriverFromJson(json);

	Map<String, dynamic> toJson() => $DriverShiftsDriverToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? email;
  String? assignedAt;
  DriverShiftsDriverSwap? swap;
}

@JsonSerializable()
class DriverShiftsDriverSwap {

	DriverShiftsDriverSwap();

	factory DriverShiftsDriverSwap.fromJson(Map<String, dynamic> json) => $DriverShiftsDriverSwapFromJson(json);

	Map<String, dynamic> toJson() => $DriverShiftsDriverSwapToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? email;
  String? date;
}
