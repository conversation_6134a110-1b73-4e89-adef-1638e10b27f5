import 'package:connect/generated/json/driver_report_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class DriverReportEntity {

	DriverReportEntity();

	factory DriverReportEntity.fromJson(Map<String, dynamic> json) => $DriverReportEntityFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportEntityToJson(this);

  @JSONField(name: "_id")
  DriverReportId? dId;
  double? number;
  double? uniqueNumber;
  double? singleNumber;
  double? bundleNumber;
  String? deliveryTime;
  DriverReportFees? fees;
  DriverReportCommission? commission;
  DriverReportAdjustments? adjustments;
  DriverReportDistribution? distribution;
  DriverReportRating? rating;
  DriverReportDriving? driving;
}

@JsonSerializable()
class DriverReportRating {

	DriverReportRating();

	factory DriverReportRating.fromJson(Map<String, dynamic> json) => $DriverReportRatingFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportRatingToJson(this);

  num? good;
  num? bad;
}

@JsonSerializable()
class DriverReportDriving {

	DriverReportDriving();

	factory DriverReportDriving.fromJson(Map<String, dynamic> json) => $DriverReportDrivingFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportDrivingToJson(this);

  DriverReportDrivingTime? time;
  DriverReportDrivingDistance? distance;
}

@JsonSerializable()
class DriverReportDrivingTime {

	DriverReportDrivingTime();

	factory DriverReportDrivingTime.fromJson(Map<String, dynamic> json) => $DriverReportDrivingTimeFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportDrivingTimeToJson(this);

  num? pickup;
  num? dropoff;
  num? total;
}

@JsonSerializable()
class DriverReportDrivingDistance     {

	DriverReportDrivingDistance();

	factory DriverReportDrivingDistance.fromJson(Map<String, dynamic> json) => $DriverReportDrivingDistanceFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportDrivingDistanceToJson(this);

  num? pickup;
  num? dropoff;
  num? total;
}

@JsonSerializable()
class DriverReportId {

	DriverReportId();

	factory DriverReportId.fromJson(Map<String, dynamic> json) => $DriverReportIdFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportIdToJson(this);

  String? batch;
}

@JsonSerializable()
class DriverReportFees {

	DriverReportFees();

	factory DriverReportFees.fromJson(Map<String, dynamic> json) => $DriverReportFeesFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportFeesToJson(this);

  DriverReportFeesTip? tip;
}

@JsonSerializable()
class DriverReportFeesTip {

	DriverReportFeesTip();

	factory DriverReportFeesTip.fromJson(Map<String, dynamic> json) => $DriverReportFeesTipFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportFeesTipToJson(this);

  double? amount;
}

@JsonSerializable()
class DriverReportCommission {

	DriverReportCommission();

	factory DriverReportCommission.fromJson(Map<String, dynamic> json) => $DriverReportCommissionFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportCommissionToJson(this);

  double? driver;
}

@JsonSerializable()
class DriverReportAdjustments {

	DriverReportAdjustments();

	factory DriverReportAdjustments.fromJson(Map<String, dynamic> json) => $DriverReportAdjustmentsFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportAdjustmentsToJson(this);

  double? driver;
}

@JsonSerializable()
class DriverReportDistribution {

	DriverReportDistribution();

	factory DriverReportDistribution.fromJson(Map<String, dynamic> json) => $DriverReportDistributionFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportDistributionToJson(this);

  double? driver;
}
