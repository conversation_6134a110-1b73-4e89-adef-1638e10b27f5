import 'package:connect/generated/json/driver_report_adjust_entity.g.dart';

import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class DriverReportAdjustEntity {

	DriverReportAdjustEntity();

	factory DriverReportAdjustEntity.fromJson(Map<String, dynamic> json) => $DriverReportAdjustEntityFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportAdjustEntityToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? passcode;
  String? passcodeExt;
  DriverReportAdjustRegion? region;
  DriverReportAdjustRestaurant? restaurant;
  List<DriverReportAdjustAdj>? adj;
  DriverReportAdjustAdjustments? adjustments;
  DriverReportAdjustBundle? bundle;
  DriverReportAdjustDistribution? distribution;
  DriverReportAdjustRating? rating;
  String? status;
  String? createdAt;
  String? updatedAt;
  bool? doubt;
  String? confirmedAt;
}

@JsonSerializable()
class DriverReportAdjustDistribution     {

	DriverReportAdjustDistribution();

	factory DriverReportAdjustDistribution.fromJson(Map<String, dynamic> json) => $DriverReportAdjustDistributionFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportAdjustDistributionToJson(this);

  double? driver;
}

@JsonSerializable()
class DriverReportAdjustRating {

	DriverReportAdjustRating();

	factory DriverReportAdjustRating.fromJson(Map<String, dynamic> json) => $DriverReportAdjustRatingFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportAdjustRatingToJson(this);

  num? stars;
  String? content;
  List<String>? reasons;
}

@JsonSerializable()
class DriverReportAdjustRegion {

	DriverReportAdjustRegion();

	factory DriverReportAdjustRegion.fromJson(Map<String, dynamic> json) => $DriverReportAdjustRegionFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportAdjustRegionToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class DriverReportAdjustRestaurant     {

	DriverReportAdjustRestaurant();

	factory DriverReportAdjustRestaurant.fromJson(Map<String, dynamic> json) => $DriverReportAdjustRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportAdjustRestaurantToJson(this);

  @JSONField(name: "_id")
  String? sId;
  MultiNameEntity? name;
}

@JsonSerializable()
class DriverReportAdjustAdj {

	DriverReportAdjustAdj();

	factory DriverReportAdjustAdj.fromJson(Map<String, dynamic> json) => $DriverReportAdjustAdjFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportAdjustAdjToJson(this);

  double? customer;
  double? ricepo;
  double? restaurant;
  String? reason;
}

@JsonSerializable()
class DriverReportAdjustAdjustments     {

	DriverReportAdjustAdjustments();

	factory DriverReportAdjustAdjustments.fromJson(Map<String, dynamic> json) => $DriverReportAdjustAdjustmentsFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportAdjustAdjustmentsToJson(this);

  double? customer;
  double? ricepo;
  double? restaurant;
  String? reason;
  double? driver;
}

@JsonSerializable()
class DriverReportAdjustBundle     {

	DriverReportAdjustBundle();

	factory DriverReportAdjustBundle.fromJson(Map<String, dynamic> json) => $DriverReportAdjustBundleFromJson(json);

	Map<String, dynamic> toJson() => $DriverReportAdjustBundleToJson(this);

  String? combineId;
}
