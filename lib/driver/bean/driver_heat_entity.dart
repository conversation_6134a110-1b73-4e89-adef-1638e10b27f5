import 'package:connect/generated/json/base/json_field.dart';
import 'package:connect/generated/json/driver_heat_entity.g.dart';


@JsonSerializable()
class DriverHeatEntity {

	DriverHeatEntity();

	factory DriverHeatEntity.fromJson(Map<String, dynamic> json) => $DriverHeatEntityFromJson(json);

	Map<String, dynamic> toJson() => $DriverHeatEntityToJson(this);

	String? type;
	List<double>? coordinates;
}
