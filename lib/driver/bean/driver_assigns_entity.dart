import 'package:connect/generated/json/driver_assigns_entity.g.dart';



import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class DriverAssignsEntity {

	DriverAssignsEntity();

	factory DriverAssignsEntity.fromJson(Map<String, dynamic> json) => $DriverAssignsEntityFromJson(json);

	Map<String, dynamic> toJson() => $DriverAssignsEntityToJson(this);

	@JSONField(name: "_id")
	String? sId;
	String? passcode;
	String? passcodeExt;
	String? confirmedAt;
	DriverAssignsRestaurant? restaurant;
	DriverAssignsCustomer? customer;
	DriverAssignsDelivery? delivery;
	DriverAssignsBundle? bundle;
	String? status;
}

@JsonSerializable()
class DriverAssignsBundle {

	DriverAssignsBundle();

	factory DriverAssignsBundle.fromJson(Map<String, dynamic> json) => $DriverAssignsBundleFromJson(json);

	Map<String, dynamic> toJson() => $DriverAssignsBundleToJson(this);

	String? combineId;
}

@JsonSerializable()
class DriverAssignsCustomer {

	DriverAssignsCustomer();

	factory DriverAssignsCustomer.fromJson(Map<String, dynamic> json) => $DriverAssignsCustomerFromJson(json);

	Map<String, dynamic> toJson() => $DriverAssignsCustomerToJson(this);

	String phone = "";
}

@JsonSerializable()
class DriverAssignsRestaurant {

	DriverAssignsRestaurant();

	factory DriverAssignsRestaurant.fromJson(Map<String, dynamic> json) => $DriverAssignsRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $DriverAssignsRestaurantToJson(this);

	@JSONField(name: "_id")
	String? sId;
	MultiNameEntity? name;
	String phone = "";
}

@JsonSerializable()
class DriverAssignsDelivery {

	DriverAssignsDelivery();

	factory DriverAssignsDelivery.fromJson(Map<String, dynamic> json) => $DriverAssignsDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $DriverAssignsDeliveryToJson(this);

	@JSONField(name: "_id")
	String? sId;
	String? provider;
	String? status;
	String? completedAt;
	DriverAssignsDeliveryStats? stats;
	num? time;
}

@JsonSerializable()
class DriverAssignsDeliveryStats {

	DriverAssignsDeliveryStats();

	factory DriverAssignsDeliveryStats.fromJson(Map<String, dynamic> json) => $DriverAssignsDeliveryStatsFromJson(json);

	Map<String, dynamic> toJson() => $DriverAssignsDeliveryStatsToJson(this);

	@JSONField(name: "en-route-to-pickup")
	num? enRouteToPickup;
	@JSONField(name: "at-pickup")
	num? atPickup;
	@JSONField(name: "min-pickup-completed")
	num? minPickupCompleted;
	@JSONField(name: "pickup-completed")
	num? pickupCompleted;
	@JSONField(name: "en-route-to-dropoff")
	num? enRouteToDropoff;
}
