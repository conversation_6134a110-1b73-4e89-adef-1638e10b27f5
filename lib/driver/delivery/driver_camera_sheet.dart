import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class DriverCameraSheet {
  static show(BuildContext context, ValueChanged<int> callback) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context, callback);
        },
        context: context);
  }

  static Widget buildBottomSheetWidget(
      BuildContext context, ValueChanged<int> callback) {
    return Container(
      height: Dimens.dp_178,
      child: Column(
        children: [
          buildItem(context, S.of(context).driver_camera, onTap: () async {
            callback(0);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, S.of(context).driver_gallery, onTap: () async {
            callback(1);
          }),
          Divider(
            color: Colors.grey[200],
            thickness: Dimens.dp_10,
          ),
          buildItem(context, S.of(context).cancel),
        ],
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        await Future.delayed(Duration(milliseconds: 300));
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_50,
        child: Text(
          title,
          style: TextStyle(
              color:
                  S.of(context).cancel == title ? Colors.black87 : Colors.blue,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
