import 'dart:io';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_dropoff_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';
import 'package:provider/provider.dart';
import 'driver_camera_sheet.dart';

class DriverDropoffUploadSelect2 extends StatefulWidget {
  DriverDropoffUploadSelect2({Key? key}) : super(key: key);

  @override
  _DriverDropoffUploadSelect2State createState() =>
      _DriverDropoffUploadSelect2State();
}

class _DriverDropoffUploadSelect2State
    extends State<DriverDropoffUploadSelect2> {
  List<AssetEntity> _imageFiles = [];

  @override
  void initState() {
    super.initState();
    _imageFiles = [];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(S.of(context).driver_upload_max,
                style: TextStyle(
                  color: Colors.black87,
                  fontSize: Dimens.sp_16,
                )),
            SizedBox(height: 16.0),
            AlignedGridView.count(
              shrinkWrap: true,
              mainAxisSpacing: 4.0,
              crossAxisSpacing: 4.0,
              crossAxisCount: 3,
              itemCount: _imageFiles.length == 5
                  ? _imageFiles.length
                  : _imageFiles.length + 1,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (BuildContext context, int index) {
                if (_imageFiles.length < 5 && index == 0) {
                  return InkWell(
                    onTap: () {
                      FocusScope.of(context).requestFocus(FocusNode());

                      DriverCameraSheet.show(context, (value) async {
                        if (value == 0) {
                          _onCameraImage();
                        } else if (value == 1) {
                          _onPickImage();
                        }
                      });
                    },
                    child: Container(
                      height: 100,
                      padding: EdgeInsets.all(5),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5.0),
                          color: Color(0xFFF6F7F8),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.add,
                            color: Color(0xFFB4B4B4),
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                  );
                } else {
                  int imgIndex = _imageFiles.length < 5 ? index - 1 : index;
                  return Stack(
                    children: [
                      Container(
                        height: 100,
                        padding: EdgeInsets.all(5),
                        child: InkWell(
                          child: FutureBuilder<File?>(
                            future: _imageFiles[imgIndex].file,
                            builder: (context, snapshot) {
                              return snapshot.connectionState ==
                                      ConnectionState.done
                                  ? Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.rectangle,
                                        borderRadius:
                                            BorderRadius.circular(6.0),
                                        image: DecorationImage(
                                          image: FileImage(snapshot.data!),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    )
                                  : Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.rectangle,
                                        borderRadius:
                                            BorderRadius.circular(6.0),
                                        color: Color(0xFFF6F7F8),
                                      ),
                                      child: Center(
                                        child: CupertinoActivityIndicator(),
                                      ),
                                    );
                            },
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () => _deleteImage(
                            _imageFiles.length < 5 ? index - 1 : index),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(99.0),
                            color: Colors.red,
                          ),
                          padding: EdgeInsets.all(2.0),
                          child: Icon(
                            Icons.close,
                            size: 20.0,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  );
                }
              },
              // staggeredTileBuilder: (int index) =>
              //     new StaggeredTile.count(1, 1),

            ),
          ],
        ),
      ),
    );
  }

  /// select pictures
  _onPickImage() async {

    try {
      ///check permission
      await AssetPicker.permissionCheck(requestOption:
      PermissionRequestOption(androidPermission: AndroidPermission(
        type: RequestType.image,
        mediaLocation: false,
      ),));
    } catch (e) {
      MessageDialog.messageAlert(S.of(context).driver_permission_photo);
      TrackingUtils.instance!.tracking(TConstants.D_PERMISSION_GALLERY_DENIED);
      return;
    }

    TrackingUtils.instance!.tracking(TConstants.D_GALLERY_PICKER);

    List<AssetEntity>? assets = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        maxAssets: 5 - _imageFiles.length,
        themeColor: Theme.of(context).primaryColor,
        requestType: RequestType.image,
        previewThumbnailSize: ThumbnailSize.square(200),
        gridThumbnailSize: ThumbnailSize.square(100),
      ),
    );

    // var asset = assets[0];
    // var uint8list = await assets[0].thumbDataWithSize(asset.width,asset.height,quality:5);
    // uint8list.toList();

    // await Future.forEach(assets, (element) async {
    //   var file = await element.file;
    //   LogUtil.v("file::${file.path}");
    // });

    if (assets == null || assets.length <= 0) return;
    setState(() {
      _imageFiles.addAll(assets);
      context.read<DriverDroppoffModel>().setAssetEntity(_imageFiles);
    });
  }

  /// camera
  _onCameraImage() async {
    // 只在Android平台检查相机权限，iOS平台由系统处理
    if (Platform.isAndroid) {
      var status = await Permission.camera.request();
      if (!status.isGranted) {
        MessageDialog.messageAlert(S.of(context).driver_permission_camera);
        TrackingUtils.instance!.tracking(TConstants.D_PERMISSION_CAMERA_DENIED);
        return;
      }
    }

    TrackingUtils.instance!.tracking(TConstants.D_CAMERA_PICKER);

    final AssetEntity? asset = await CameraPicker.pickFromCamera(context);
    if (asset == null) return;
    setState(() {
      _imageFiles.add(asset);
      context.read<DriverDroppoffModel>().setAssetEntity(_imageFiles);
    });
  }

  /// delete picture
  _deleteImage(int index) {
    if (_imageFiles.length <= index) return;
    setState(() {
      _imageFiles.removeAt(index);
      if (_imageFiles.length == 0) {
        context.read<DriverDroppoffModel>().setAssetEntity([]);
      }
    });
  }
}
