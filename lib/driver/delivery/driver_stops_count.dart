import 'package:connect/common/MyStyles.dart';
import 'package:connect/driver/bean/driver_orders_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';

class DriverStopsCount extends StatefulWidget {
  DriverStopsCount(this.entity, {Key? key}) : super(key: key);

  final DriverOrdersEntity? entity;

  // final bool isNoOrder;
  // final bool isNewOrder;

  @override
  _DriverStopsCountState createState() => _DriverStopsCountState();
}

class _DriverStopsCountState extends State<DriverStopsCount> {
  @override
  Widget build(BuildContext context) {
    // bool showStops = widget.isNoOrder || widget.isNewOrder;
    var entity = widget.entity;
    var pickups = [];
    var dropOffs = [];
    if (entity != null && entity.route != null) {
      pickups =
          entity.route!.where((element) => element.type == "pickup").toList();
      LogUtil.v("pickups::${pickups.length}");

      dropOffs =
          entity.route!.where((element) => element.type == "dropoff").toList();
      LogUtil.v("dropOffs::${dropOffs.length}");
    }

    var pickedUpCount = dropOffs.length - pickups.length;
    var pickupCount = pickups.length;
    var willDropoffCount = dropOffs.length;

    return entity == null || entity.route == null
        ? EmptyContainer()
        : Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              TaskStaticsView(
                title: S.of(context).driver_profile_picked_up,
                count: pickedUpCount,
              ),
              TaskStaticsView(
                title: S.of(context).driver_profile_will_pick_up,
                count: pickupCount,
              ),
              TaskStaticsView(
                title: S.of(context).driver_profile_will_dropoff,
                count: willDropoffCount,
              ),
            ],
          );
  }
}

class TaskStaticsView extends StatelessWidget {
  const TaskStaticsView({Key? key, this.count, this.title}) : super(key: key);

  final count;
  final title;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          "$count",
          style: MyStyles.m16.copyWith(
            color: MyColors.color333,
          ),
        ),
        SizedBox(
          height: 2,
        ),
        Text(
          "$title",
          style: MyStyles.m13.copyWith(
            color: MyColors.color666,
          ),
        ),
      ],
    );
  }
}
