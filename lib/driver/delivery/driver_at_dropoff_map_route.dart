import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui show Image;
import 'dart:ui';
import 'package:connect/driver/bean/driver_orders_entity.dart';
import 'package:connect/driver/bean/dropoff_map_entity.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/object_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart' hide TextDirection;
import 'package:provider/provider.dart';

class DriverAtDropoffMapRoute extends StatefulWidget {
  static String tag = "DriverAtDropoffMapRoute";

  DriverAtDropoffMapRoute({Key? key}) : super(key: key);

  @override
  _DriverAtDropoffMapRouteState createState() =>
      _DriverAtDropoffMapRouteState();
}

class _DriverAtDropoffMapRouteState extends State<DriverAtDropoffMapRoute> {
  Map<MarkerId, Marker> markers = <MarkerId, Marker>{};
  int _markerIdCounter = 1;
  double? cameraLatitude;
  double? cameraLongitude;
  DriverOrdersEntity? entity;
  List<DropoffMapEntity> latLngCollection = [];

  // GoogleMapController _mapController;
  String? nextOrderId;

  void _onMapCreated(GoogleMapController controller) {
    // this._mapController = controller;
  }

  @override
  void dispose() {
    super.dispose();
    nextOrderId = "";
    // if (_mapController != null) _mapController.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    //receive driver orders arguments
    entity = ModalRoute.of(context)!.settings.arguments as DriverOrdersEntity?;

    if (entity == null) return;
    //add next stop address
    var coordinates = entity?.next?.address?.location?.coordinates;
    cameraLatitude = coordinates?[1];
    cameraLongitude = coordinates?[0];

    if (cameraLatitude == null || cameraLongitude == null) return;

    //arriveAt
    String arriveAt = "";
    var arriveAtVar = entity?.next?.arriveAt;
    if (ObjectUtil.isNotEmpty(arriveAtVar)) {
      var arriveAtDateTime = DateUtil.getDateTime(arriveAtVar!)!.toLocal();
      arriveAt = DateFormat.Hm().format(arriveAtDateTime);
    }

    nextOrderId = entity?.next?.order?.sId ?? "";
    latLngCollection.add(DropoffMapEntity(
        LatLng(cameraLatitude!, cameraLongitude!), arriveAt, nextOrderId, true));

    //route stop
    if (ObjectUtil.isNotEmpty(entity?.route)) {
      //filter pickup status
      var pickups = entity!.route!
          .where((element) => element.type == "pickup")
          .map((e) => e.order?.sId)
          .toList();
      LogUtil.v("pickups::${pickups.length}");

      //remove the haven't picked up stops among dropoffs
      var dropoffRoute = entity!.route!.where((element) {
        var elementOrderId = element.order?.sId;
        return pickups.indexOf(elementOrderId) == -1;
      });
      LogUtil.v("dropoffRoute::${dropoffRoute.length}");

      //filter current driver status is en route to dropoff
      dropoffRoute.forEach((element) {
        if (element.type == "dropoff" &&
            ObjectUtil.isEmpty(element.arrivedAt) &&
            ObjectUtil.isEmpty(element.order?.restaurant?.delivery?.batch)) {
          //add route stop address
          var coordinatesRoute = element.address?.location?.coordinates;
          var arriveAtDateTime = DateUtil.getDateTime(element.arriveAt!)!.toLocal();
          String arriveAt = DateFormat.Hm().format(arriveAtDateTime);
          latLngCollection.add(DropoffMapEntity(
              LatLng(coordinatesRoute![1], coordinatesRoute[0]),
              arriveAt,
              element.order?.sId,
              false));
        }
      });
    }

    WidgetsBinding.instance!.addPostFrameCallback((timeStamp) async {
      LogUtil.v("DriverAtDropoffMapRoute::WidgetsBinding");
      //refresh marker
      await Future.forEach(latLngCollection, (dynamic element) async {
        await _add(element);
      });

      LogUtil.v("latLngCollection::${latLngCollection.length}");
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("DriverAtDropoffMapRoute::build");
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: Colors.black87,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        backgroundColor: Colors.white,
        title: Text(S.of(context).driver_dropoffs_map,
          style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_18),
        ),
        elevation: 2,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      body: GoogleMap(
        mapType: MapType.terrain,
        initialCameraPosition: CameraPosition(
          target: LatLng(cameraLatitude!, cameraLongitude!),
          zoom: 16.0,
        ),
        markers: Set<Marker>.of(markers.values),
        // circles: Set<Circle>.of(circles.values),
        onMapCreated: _onMapCreated,
      ),
    );
  }

  ///add marker on google map
  _add(DropoffMapEntity mapEntity) async {
    final String markerIdVal = 'marker_id_$_markerIdCounter';
    _markerIdCounter++;

    var bitmapDescriptor = await createCustomMarkerBitmap(mapEntity);

    final MarkerId markerId = MarkerId(markerIdVal);

    final Marker marker = Marker(
      markerId: markerId,
      position: LatLng(
        mapEntity.latLng.latitude,
        mapEntity.latLng.longitude,
      ),
      icon: bitmapDescriptor,
      onTap: mapEntity.isNext
          ? null
          : () {
        DriverDialog.show(context, S.of(context).driver_next_selected_dropoff, () {
          changeDropoff(mapEntity);
        });
      },
    );
    markers[markerId] = marker;
  }

  Future<ui.Image> getAssetImage(String asset, {width, height}) async {
    ByteData data = await rootBundle.load(asset);
    Codec codec = await instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width, targetHeight: height);
    FrameInfo fi = await codec.getNextFrame();
    return fi.image;
  }

  Future<BitmapDescriptor> createCustomMarkerBitmap(
      DropoffMapEntity mapEntity) async {
    const int radius = 120;

    PictureRecorder recorder = new PictureRecorder();
    Canvas canvas = new Canvas(recorder);

    //draw image
    ui.Image images;
    if (mapEntity.isNext) {
      images = await getAssetImage('assets/images/marker_red.png');
    } else {
      images = await getAssetImage('assets/images/marker_black.png');
    }
    canvas.drawImage(images, Offset(0, 0), Paint());

    //draw text
    TextSpan span = new TextSpan(
        style: new TextStyle(
          color: Colors.white,
          fontSize: 30.0,
          fontWeight: FontWeight.bold,
        ),
        text: "${mapEntity.arriveAt}");

    TextPainter tp = new TextPainter(
      text: span,
      textDirection: TextDirection.ltr,
    );
    tp.layout();
    tp.paint(canvas, Offset(20, 40));

    Picture p = recorder.endRecording();
    ByteData? pngBytes = await ((await p.toImage(radius, radius))
        .toByteData(format: ImageByteFormat.png));

    Uint8List data = Uint8List.view(pngBytes!.buffer);

    return BitmapDescriptor.fromBytes(data);
  }

  ///Update stops for selected dropoff
  changeDropoff(DropoffMapEntity mapEntity) async {
    try {
      LoadingUtils.show();
      //reset stop.next status from dropoff to pickup-completed at first
      await context.read<DriverModel>().resetToPickComplete(nextOrderId);
      //Skip to the target stop
      await context.read<DriverModel>().changeDropoff(mapEntity.orderId);
      LoadingUtils.dismiss();
    } catch (e) {} finally {
      Navigator.pop(context);
    }
  }
}
