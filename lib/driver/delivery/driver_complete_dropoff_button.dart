import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_dropoff_model.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/widget/driver_one_dialog.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/location_manager.dart';
import 'package:connect/utils/object_util.dart';
import 'package:connect/utils/throttle.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';


class DriverCompleteDropoffButton extends StatefulWidget {
  DriverCompleteDropoffButton(this.orderId, this.isNext, {Key? key})
      : super(key: key);

  final String? orderId;
  final bool? isNext;

  @override
  _DriverCompleteDropoffButtonState createState() =>
      _DriverCompleteDropoffButtonState();
}

class _DriverCompleteDropoffButtonState
    extends State<DriverCompleteDropoffButton> {
  bool isClickable = false;

  static const String TAG = "complete_dropoff";

  @override
  void initState() {
    super.initState();
    if (!mounted) return;
    context.read<DriverDroppoffModel>().imagesEntity = [];
    context.read<DriverDroppoffModel>().explanation = "";
  }

  @override
  Widget build(BuildContext context) {
    DriverDroppoffModel model = context.watch<DriverDroppoffModel>();
    var explanation = model.explanation;
    var images = model.imagesEntity;
    if (ObjectUtil.isNotEmpty(explanation) && ObjectUtil.isNotEmpty(images)) {
      isClickable = true;
    } else {
      isClickable = false;
    }
    return Container(
        margin: const EdgeInsets.fromLTRB(
            Dimens.dp_25, Dimens.dp_20, Dimens.dp_25, Dimens.dp_20),
        alignment: Alignment.center,
        child: SizedBox(
          width: double.infinity,
          height: Dimens.dp_40,
          child: ElevatedButton(
            style: ButtonStyle(
                backgroundColor: MaterialStateProperty.resolveWith((states) {
                  if (states.contains(MaterialState.disabled)) {
                    return RColors.r_orange_alpha;
                  }
                  return RColors.r_orange;
                }),
                shape: MaterialStateProperty.all(RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(Dimens.dp_20)))),
            child: Text(S.of(context).driver_order_delivery_complete,
                style: TextStyle(color: Colors.white, fontSize: Dimens.sp_16)),
            onPressed: isClickable
                ? throttle(() async{
                    FocusScope.of(context).requestFocus(FocusNode());

                    await Future.delayed(Duration(milliseconds: 300));
                    if (!mounted) {
                      LogManager.instance!.log(TAG, "", "unmounted");
                      return;
                    }
                    flowApi("completed", explanation, images);
                  }) as void Function()?
                : null,
          ),
        ));
  }

  /// complete dropOff flow
  flowApi(String status, String explanation, List<AssetEntity> images) async {

    List<String> photosUrls = [];
    LoadingUtils.show();
    int time1 = DateTime.now().millisecondsSinceEpoch;

    LogManager.instance!.log(TAG, "", "start forEach");
    await Future.forEach(images, (AssetEntity element) async {
      // LogUtil.v("width::${element.width},height::${element.height}");
      var uint8list = await element.thumbnailDataWithSize(ThumbnailSize(300, 300), quality: 70);
      var file = await element.file;
      // var byteData = await element.getByteData(quality: 5);
      LogManager.instance!.log(TAG, "", "start uploadFile");

      if (uint8list != null && file != null) {
        var photosUrl = await context.read<DriverModel>().uploadFile(uint8list.toList(), file.path, "delivery");
        if (photosUrl != null && photosUrl.isNotEmpty) {
          photosUrls.add(photosUrl);
        }
      }
    }).catchError((e) {
      LoadingUtils.dismiss();
    });
    try{
      if (photosUrls.length == 0) {
        LoadingUtils.dismiss();
        ToastUtils.show(S.of(context).driver_upload_file_fail);
        LogManager.instance!.log(TAG, "", "photosUrls array is empty");

        return;
      }

      LogManager.instance!.log(TAG, "", "start uploadPhotos :: ${photosUrls.length}, orderId::${widget.orderId}");
      var response = await context.read<DriverModel>().uploadPhotos(widget.orderId, explanation, photosUrls);
      if (response == null) {
        return;
      }

      DriverOneDialog.show(
          context, S.of(context).driver_upload_photo_successful,
          callback: () async {
            /// current position
            LoadingUtils.show();
            var position = await LocationManager.getCurrentPosition();

            if (position == null) {
              LoadingUtils.dismiss();
              return;
            }

            //complete dropoff
            LogManager.instance!.log(TAG, "", "put stops,isNext:${widget.isNext},orderId:${widget.orderId}");
            await context.read<DriverModel>().arrivedStatus(
                position.latitude, position.longitude, status,
                orderId: widget.isNext! ? null : widget.orderId,
                option: true, callback: (value) async {

              LoadingUtils.dismiss();

              int time2 = DateTime.now().millisecondsSinceEpoch;
              TrackingManager.trackStopsTime(time1, time2, "completed_dropoff_upload");

              //tracking
              var params = Map<String, dynamic>();
              params["isNext"] = "${widget.isNext}";
              params["orderId"] = "${widget.orderId}";
              params["photosUrls"] = "${photosUrls.toString()}";
              TrackingUtils.instance!.tracking(TConstants.D_DELIVERY_COMPLETED_DROPOFF_SUCCESS, value: params);

              //exit current route
              LogManager.instance!.log(TAG, "", "pop route");
              Navigator.pop(context);

              if (widget.isNext! && value!.next == null) {
                //create new order
                LogManager.instance!.log(TAG, "", "pop stops");
                GlobalConfig.eventBus.fire(DriverCreateOrder());
              }
            });
          });
    } catch (e) {
      LogManager.instance!.log(TAG, "", "${e.toString()}");
      Sentry.captureException("dropOff,userId::${ConnectCache.getUser()!.userId},${widget.orderId},e::${e.toString()}");
    }
  }
}
