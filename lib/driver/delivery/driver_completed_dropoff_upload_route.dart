import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'driver_complete_dropoff_button.dart';
import 'driver_dropoff_explanation.dart';
import 'driver_dropoff_upload_select2.dart';

class DriverCompletedDropOffUploadRoute extends StatefulWidget {
  static String tag = "DriverCompletedDropOffUploadRoute";

  DriverCompletedDropOffUploadRoute({Key? key}) : super(key: key);

  @override
  _DriverCompletedDropOffUploadRouteState createState() =>
      _DriverCompletedDropOffUploadRouteState();
}

class _DriverCompletedDropOffUploadRouteState
    extends State<DriverCompletedDropOffUploadRoute> {
  bool? isNext = false;
  String? orderId = "";

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    var arguments =
        ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
    isNext = arguments["isNext"];
    orderId = arguments["orderId"];
    LogUtil.v("isNext:$isNext,orderId:$orderId");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          systemOverlayStyle: SystemUiOverlayStyle.light,
          backgroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios_rounded,
              color: Colors.black87,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          elevation: 0,
          title: Text(S.of(context).driver_upload,
              style: TextStyle(
                  color: Colors.black87,
                  fontSize: Dimens.sp_17,
                  fontWeight: FontWeight.bold)),
          centerTitle: true,
        ),
        body: GestureDetector(
          onTap: () {
            //hide keyboard when tap region is blank space
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Column(
            children: [
              Expanded(
                child: ListView(
                  children: [
                    DriverDropoffExplanation(),
                    DriverDropoffUploadSelect2(),
                  ],
                ),
              ),
              DriverCompleteDropoffButton(orderId, isNext),
              SizedBox(
                height: Dimens.dp_20,
              ),
            ],
          ),
        ));
  }
}
