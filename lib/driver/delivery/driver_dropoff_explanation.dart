import 'package:connect/driver/provider/driver_dropoff_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class DriverDropoffExplanation extends StatefulWidget {
  DriverDropoffExplanation({Key? key}) : super(key: key);

  @override
  _DriverDropoffExplanationState createState() =>
      _DriverDropoffExplanationState();
}

class _DriverDropoffExplanationState extends State<DriverDropoffExplanation> {
  TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    var isEnEnv = ConnectCache.getLocale().toLanguageTag() == "en-US";
    return Container(
      margin: const EdgeInsets.symmetric(
          vertical: Dimens.dp_25, horizontal: Dimens.dp_15),
      child: Column(
        children: [
          Row(
            children: [
              Text(S.of(context).driver_upload_explanation,
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: Dimens.sp_16,
                  )),
            ],
          ),
          Row(
            children: [
              Container(
                alignment: Alignment.topLeft,
                width: MediaQuery.of(context).size.width - Dimens.dp_30,
                margin: const EdgeInsets.only(top: Dimens.dp_25),
                child: TextField(
                  controller: _controller,
                  autofocus: true,
                  maxLines: 5,
                  onChanged: (v) {
                    context.read<DriverDroppoffModel>().setExplanation(v);
                  },
                  decoration: InputDecoration(
                      filled: true,
                      fillColor: Colors.grey[200],
                      border: InputBorder.none,
                      focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                          borderSide: BorderSide.none),
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 12.0, horizontal: 15)),
                ),
              ),
            ],
          ),
          isEnEnv
              ? EmptyContainer()
              : Container(
                  margin: const EdgeInsets.only(top: Dimens.dp_10),
                  alignment: Alignment.centerLeft,
                  child: Wrap(
                    spacing: 10,
                    alignment: WrapAlignment.start,
                    children: [
                      buildRawChip(
                          context, S.of(context).driver_proof_food_door),
                      buildRawChip(
                          context, S.of(context).driver_proof_food_lobby),
                      buildRawChip(
                          context, S.of(context).driver_proof_food_frontdesk),
                      buildRawChip(
                          context, S.of(context).driver_proof_food_downstairs)
                    ],
                  ),
                )
        ],
      ),
    );
  }

  RawChip buildRawChip(BuildContext context, String name) {
    return RawChip(
      label: Text(name),
      onPressed: () {
        var text = name;
        _controller.text = text;
        context.read<DriverDroppoffModel>().setExplanation(text);
      },
    );
  }
}
