import 'package:connect/driver/bean/driver_heat_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class DriverHeatMapWeb extends StatefulWidget {
  static String tag = "DriverHeatMapWeb";

  DriverHeatMapWeb({Key? key}) : super(key: key);

  @override
  _DriverHeatMapWebState createState() => _DriverHeatMapWebState();
}

class _DriverHeatMapWebState extends State<DriverHeatMapWeb> {
  static const String HEAT_MAP_URL = "assets/heatmap/index.html";
  late WebViewController _controller;
  double progress = 0;
  var _heatMaps = [];
  bool hasPoints = false;

  @override
  void initState() {
    super.initState();

    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller =
    WebViewController.fromPlatformCreationParams(params);

    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
        ..setMediaPlaybackRequiresUserGesture(false);
    }

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel("connect_flutter_heatmap", onMessageReceived: (message) {
        LogUtil.v("${message.message}");
      })
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
            setState(() {
              this.progress = progress / 100.0;
            });
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {
            _controller.runJavaScript("initMap('${_heatMaps.toString()}')");
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadFlutterAsset(HEAT_MAP_URL);

    _controller = controller;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    List<DriverHeatEntity>? heatEntity =
        ModalRoute.of(context)!.settings.arguments as List<DriverHeatEntity>?;

    if (heatEntity != null && heatEntity.length > 0) {
      // LogUtil.v("heatEntity::${heatEntity.length}");
      hasPoints = true;
      heatEntity.forEach((element) {
        var coordinates = element.coordinates;
        var _heatMapsPoints = [];
        _heatMapsPoints.add(coordinates![1]);
        _heatMapsPoints.add(coordinates[0]);
        _heatMaps.add(_heatMapsPoints);
      });
    }
    LogUtil.v("jsonStr::${_heatMaps.toString()}");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: Colors.black87,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        backgroundColor: Colors.white,
        title: Text(
          S.of(context).driver_heat_map,
          style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_18),
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
        elevation: 2,
        centerTitle: true,
      ),
      body: Column(
        children: [
          progress == 1
              ? EmptyContainer()
              : LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(
                      Colors.blue), // the color of progressIndicator
                  minHeight: 3,
                ),
          Expanded(
            child: hasPoints
                ? WebViewWidget(controller: _controller,)
                : EmptyView(S.of(context).no_data),
          ),
        ],
      ),
    );
  }

  /// js调用flutter
  // JavascriptChannel _javascriptChannel(BuildContext context) {
  //   return JavascriptChannel(
  //       name: 'connect_flutter_heatmap',
  //       onMessageReceived: (JavascriptMessage message) {
  //         LogUtil.v("${message.message}");
  //       });
  // }
}
