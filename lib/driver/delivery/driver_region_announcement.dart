import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/driver/provider/driver_region_announcement_model.dart';
import 'package:connect/driver/widget/driver_announcement_dialog.dart';
import 'package:connect/gen/assets.gen.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/object_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sprintf/sprintf.dart';

class DriverRegionAnnouncement extends StatefulWidget {
  DriverRegionAnnouncement({Key? key}) : super(key: key);

  @override
  _DriverRegionAnnouncementState createState() =>
      _DriverRegionAnnouncementState();
}

class _DriverRegionAnnouncementState extends State<DriverRegionAnnouncement> {
  @override
  void initState() {
    super.initState();
    context.read<DriverRegionAnnouncementModel>().regionEntity = null;
    WidgetsBinding.instance!.addPostFrameCallback((_) {
      if (mounted)
        context.read<DriverRegionAnnouncementModel>().regionAnnouncement();
    });
  }

  @override
  Widget build(BuildContext context) {
    var entity = context.watch<DriverRegionAnnouncementModel>().regionEntity;
    List<MultiNameEntity>? motd = entity?.shift?.motd;
    return !ObjectUtil.isEmptyList(motd)
        ? InkWell(
            onTap: () {
              showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return DriverAnnouncementDialog(() {}, motd!);
                  });

              TrackingUtils.instance!
                  .tracking(TConstants.D_DELIVERY_MOTD_CLICK);
            },
            child: Card(
              elevation: 0,
              margin: EdgeInsets.fromLTRB(20, 0, 20, 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Assets.images.driverTab.icoNotice.svg(width: 20),
                    SizedBox(
                      width: 8,
                    ),
                    Text(
                      sprintf(S.of(context).driver_announcements_count,
                          ["${motd?.length}"]),
                      style: MyStyles.r13.copyWith(
                        color: MyColors.color666,
                      ),
                    ),
                    Spacer(),
                    Icon(
                      Icons.arrow_forward_ios_sharp,
                      color: MyColors.color999,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          )
        : EmptyContainer();
  }
}
