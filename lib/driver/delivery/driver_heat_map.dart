// import 'dart:async';
// import 'package:connect/driver/bean/driver_heat_entity.dart';
// import 'package:connect/generated/l10n.dart';
// import 'package:connect/res/dimens.dart';
// import 'package:connect/utils/log_util.dart';
// import 'package:flutter/material.dart';
// import 'package:google_maps_flutter_heatmap/google_maps_flutter_heatmap.dart';
//
// class DriverHeatMap extends StatefulWidget {
//   static String tag = "DriverHeatMap";
//
//   DriverHeatMap({Key key}) : super(key: key);
//
//   @override
//   _DriverHeatMapState createState() => _DriverHeatMapState();
// }
//
// class _DriverHeatMapState extends State<DriverHeatMap> {
//   Completer<GoogleMapController> _controller = Completer();
//   final Set<Heatmap> _heatMaps = {};
//   List<DriverHeatEntity> heatEntity;
//   double cameraLatitude = 0;
//   double cameraLongitude = 0;
//
//   @override
//   void didChangeDependencies() {
//     super.didChangeDependencies();
//     heatEntity = ModalRoute.of(context).settings.arguments as List<DriverHeatEntity>;
//
//     if (heatEntity != null && heatEntity.length > 0) {
//       cameraLatitude = heatEntity[0].coordinates[1];
//       cameraLongitude = heatEntity[0].coordinates[0];
//
//       LogUtil.v(
//           "cameraLatitude::$cameraLatitude, cameraLongitude::$cameraLongitude");
//
//       heatEntity.forEach((element) {
//         var coordinates = element.coordinates;
//         _addHeatMap(LatLng(coordinates[1], coordinates[0]));
//       });
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         leading: IconButton(
//           icon: Icon(
//             Icons.arrow_back_ios_rounded,
//             color: Colors.black87,
//           ),
//           onPressed: () {
//             Navigator.of(context).pop();
//           },
//         ),
//         backgroundColor: Colors.white,
//         title: Text(
//           S.of(context).driver_heat_map,
//           style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_18),
//         ),
//         systemOverlayStyle: SystemUiOverlayStyle.light,
//         elevation: 2,
//         centerTitle: true,
//       ),
//       body: GoogleMap(
//         mapType: MapType.normal,
//         initialCameraPosition: CameraPosition(
//           target: LatLng(cameraLatitude, cameraLongitude),
//           zoom: 11,
//         ),
//         heatmaps: _heatMaps,
//         onMapCreated: (GoogleMapController controller) {
//           _controller.complete(controller);
//         },
//       ),
//     );
//   }
//
//   _addHeatMap(LatLng location) {
//     var heatMap = Heatmap(
//         heatmapId: HeatmapId(location.toString()),
//         points: _createPoints(location),
//         radius: 15,
//         visible: true,
//         gradient: HeatmapGradient(
//             colors: <Color>[Colors.green, Colors.red],
//             startPoints: <double>[0.2, 0.8]));
//
//     _heatMaps.add(heatMap);
//   }
//
//   //heatmap generation helper functions
//   List<WeightedLatLng> _createPoints(LatLng location) {
//     final List<WeightedLatLng> points = <WeightedLatLng>[];
//     points.add(_createWeightedLatLng(location.latitude, location.longitude, 1));
//     return points;
//   }
//
//   WeightedLatLng _createWeightedLatLng(double lat, double lng, int weight) {
//     return WeightedLatLng(point: LatLng(lat, lng), intensity: weight);
//   }
// }



