import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/driver/provider/driver_online.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:flutter/material.dart';
import 'bean/driver_orders_entity.dart';
import 'driver_switch.dart';
import 'package:provider/provider.dart';

class DriverOnlineSwitch extends StatefulWidget {
  DriverOnlineSwitch(this.entity, {Key? key}) : super(key: key);

  final DriverOrdersEntity? entity;

  @override
  _DriverOnlineSwitchState createState() => _DriverOnlineSwitchState();
}

class _DriverOnlineSwitchState extends State<DriverOnlineSwitch> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance!.addPostFrameCallback((timeStamp) {
      GlobalConfig.eventBus.on<DriverOnlineSwitchEvent>().listen((event) {
        if (!mounted) return;
        setState(() {
          if (event.isOnline != null) {
            ConnectCache.saveOnline(event.isOnline!);
          }
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    // LogUtil.v("DriverOnlineSwitch build");
    ///observe online status,process build if driver online change
    context.watch<DriverOnline>();

    return Container(
      color: MyColors.W,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              ConnectCache.getOnline()!
                  ? S.of(context).online
                  : S.of(context).offline,
              style: MyStyles.m13.copyWith(color: MyColors.color333),
            ),
            SizedBox(
              width: Dimens.dp_5,
            ),
            DriverSwitch(),
          ],
        ),
      ),
    );
  }
}
