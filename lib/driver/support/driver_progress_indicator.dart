import 'package:connect/driver/provider/driver_progress_model.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class DriverProgressIndicator extends StatefulWidget {
  DriverProgressIndicator({Key? key}) : super(key: key);

  @override
  _DriverProgressIndicatorState createState() => _DriverProgressIndicatorState();
}

class _DriverProgressIndicatorState extends State<DriverProgressIndicator> {

  @override
  void initState() {
    super.initState();

    context.read<DriverProgressModel>().progress = 0;
  }

  @override
  Widget build(BuildContext context) {
    double progress = context.watch<DriverProgressModel>().progress;
    return progress == 1
        ? EmptyContainer()
        : LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            // the color of progressIndicator
            minHeight: 3,
          );
  }
}
