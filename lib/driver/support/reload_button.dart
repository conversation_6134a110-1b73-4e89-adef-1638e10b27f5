import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class ReloadButton extends StatelessWidget {
  ReloadButton(this.callback, {Key? key}) : super(key: key);

  final VoidCallback callback;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(Colors.blue),
          minimumSize: MaterialStateProperty.all(<PERSON><PERSON>(120, 50))

        ),
        child: Text(
          S.of(context).driver_support_try_again,
          style: TextStyle(fontSize: Dimens.sp_18, color: Colors.white),
        ),
        onPressed: () {
          callback();
        },
      ),
    );
  }
}
