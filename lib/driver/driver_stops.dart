import 'package:connect/common/MyStyles.dart';
import 'package:connect/driver/widget/driver_phone.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import '../data/index.dart';
import 'bean/menu_entity.dart';

class DriverStops extends StatefulWidget {
  DriverStops(this.entity, this.isPickup, {Key? key}) : super(key: key);

  final MenuEntity? entity;
  final bool isPickup;

  @override
  _DriverStopsState createState() => _DriverStopsState();
}

class _DriverStopsState extends State<DriverStops> {
  bool isRequestingPhone = false;
  @override
  Widget build(BuildContext context) {
    var entity = widget.entity!;
    return Column(
      children: [
        Row(
          children: [
            Text(
              entity.orderTitle ?? "",
              style: MyStyles.m15.copyWith(color: MyColors.color333),
            ),
          ],
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text.rich(TextSpan(children: [
              TextSpan(
                text: entity.orderNum,
                style: MyStyles.m13.copyWith(color: MyColors.color333),
              ),
              entity.orderTitle == S.of(context).driver_route_dropoff &&
                      entity.bags != null
                  ? TextSpan(
                      text: "  ( ${sprintf(S.of(context).driver_bags_num, [
                            entity.bags
                          ])} ) ",
                      style: TextStyle(color: MyColors.portal))
                  : TextSpan(
                      text: "",
                    ),
            ])),
            InkWell(
              child: Container(
                width: Dimens.dp_40,
                height: Dimens.dp_30,
                child: Icon(
                  Icons.phone,
                  size: 20,
                  color: MyColors.portal,
                ),
              ),
              onTap: () {
                if (isRequestingPhone) return;
                callPhone(context, entity);
              },
            )
          ],
        ),
        Row(
          children: [
            Expanded(
              child: Text(
                entity.orderSecondaryLine!,
                style: MyStyles.r13.copyWith(color: MyColors.color666),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> callPhone(BuildContext context, MenuEntity entity) async {
    if (entity.next?.order?.sId == null) {
      return;
    }

    if (entity.next?.type == "dropoff") {
      isRequestingPhone = true;
      var params = Map<String, dynamic>();
      params["type"] = "driver";
      var url = HttpUri.PHONE_MASKING
          .replaceAll("{order_id}", entity.next!.order!.sId!);
      var response = await HttpManager.instance!
          .post(url, params: params, showMsg: false, withLoading: false);
      isRequestingPhone = false;
      if (response?.data != null) {
        var phoneMasking = JsonConvert.fromJsonAsT<PhoneMasking>(response!.data);
        DriverPhone.show(context, phoneMasking?.phone, widget.isPickup);
      }
    } else {
      DriverPhone.show(context, entity.phone, widget.isPickup);
    }
  }
}
