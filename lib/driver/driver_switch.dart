import 'dart:io';
import 'dart:async';
import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/widget/driver_disclosure_dialog.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/location_manager.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:connect/utils/update_dialog.dart';
import 'package:connect/utils/update_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import '../utils/push_permission.dart';
import 'bean/location_timer.dart';
import 'package:location/location.dart' hide LocationAccuracy;
import 'package:firebase_messaging/firebase_messaging.dart';

typedef LocationCall<T, S, W, M, E> = Function(
    double lat, double log, double accuracy, String startTime, String endTime);

class DriverSwitch extends StatefulWidget {
  DriverSwitch({Key? key}) : super(key: key);

  @override
  _DriverSwitchState createState() => _DriverSwitchState();
}

class _DriverSwitchState extends State<DriverSwitch> {
  static const String TAG = "DriverSwitch";

  double preLat = 0;
  double preLog = 0;

  @override
  void initState() {
    super.initState();
    _checkOnlineAndPermission(true);
    //when switch app from background to foreground trigger
    GlobalConfig.eventBus.on<CheckDriverOnline>().listen((event) {
      LogUtil.v("background to foreground");
      _backGroundCancel();
      _checkOnlineAndPermission(false);
    });
    //when switch app from foreground to background trigger
    GlobalConfig.eventBus.on<SwitchBackground>().listen((event) {
      LogUtil.v("foreground to background");
      _listenLocation();
    });

    //receive notification
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      LogUtil.d('Got a message whilst in the foreground!');
      LogUtil.d('Message data: ${message.data}');
      if (message.notification == null) return;
      LogUtil.d(
          'Message also contained a notification: ${message.notification}');
      if (GlobalConfig.isBackground) return;

      context.read<DriverModel>().driverOrders(showMsg: false, loading: false);
      var body = message.notification?.body;
      var title = message.notification?.title;

      if (title != null && body != null) {
        showNotificationDialog(Text(title), Text(body), context);
      }

      ///收到自动下线通知
      if (body != null && body.contains(Constants.AUTO_LOG_OUT_NOTIFICATION)) {
        context.read<DriverModel>().driverOnline(false);
      }
    });
  }


  ///check driver is online ,then submit current location and turn on timer reporting location in 10 seconds the same time
  _checkOnlineAndPermission(bool check) {
    WidgetsBinding.instance!.addPostFrameCallback((_) {
      if (!mounted) {
        return;
      }
      // refresh driver orders
      context.read<DriverModel>().driverOrders(loading: false);

      // oneSignal push permission
      Future.delayed(Duration(milliseconds: 1000))
          .then((value) => pushPermission());

      if (check) LoadingUtils.show();
      context.read<DriverModel>().checkOnline().then((onCall) async {
        // maybe someone clear app cache in system setting
        if (onCall != ConnectCache.getOnline()) {
          setState(() {
            GlobalConfig.eventBus.fire(DriverOnlineSwitchEvent(onCall));
          });
        }
        var permissionStatus = await Geolocator.checkPermission();
        if (permissionStatus != LocationPermission.always) {
          if (Platform.isAndroid) {
            LoadingUtils.dismiss();
            Future.delayed(Duration(milliseconds: 200)).then((value) {
              showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return DriverDisclosureDialog(() {
                      _disclosure();
                    });
                  });
            });
          } else {
            _disclosure();
          }
        } else {
          _disclosure();
        }
      }).whenComplete(() {
        //upgrade app
        if (!GlobalConfig.isSunmi) {
          LogUtil.v("isShowing::${UpdateDialog.isShowing}");
          if (!UpdateDialog.isShowing) {
            context.read<DriverModel>().checkAppUpgrade().then((value) {
              UpdateUtils.checkUpdate(context, value);
            });
          }
        }
      });
    });
  }

  _disclosure() async {
    if (await _checkLocationPermission()) {
      _listenLocation();
      if (!GlobalConfig.isBackground) {
        Geolocator.getCurrentPosition().then((value) =>
            DriverModel.submitLocation(value.latitude, value.longitude));
        LocationTimer.startSubmit(context);
      }
    } else {
      LoadingUtils.dismiss();
      permissionAlert();
    }
  }

  StreamSubscription<LocationData>? _backGroundLocation;

  Future<void> _listenLocation() async {
    _backGroundCancel();
    var locationPermission = await Geolocator.checkPermission();
    LogUtil.v("_listenLocation locationPermission :: $locationPermission");
    if (locationPermission != LocationPermission.always) {
      _backGroundCancel();
      return;
    }
    bool changeSettings = await Location.instance
        .changeSettings(interval: 1000, distanceFilter: 20);
    LogUtil.v("changeSettings:$changeSettings");
    if (changeSettings) {
      _backGroundLocation =
          Location.instance.onLocationChanged.handleError((dynamic err) {
        LogUtil.v("start background error:${err.toString()}");
        _backGroundCancel();
      }).listen((LocationData locationData) async {
        if (GlobalConfig.isBackground) {
          // FLog.info(text: "background locationData::$locationData");
          LogUtil.v("background report start");
          var res = await DriverModel.submitLocation(
              locationData.latitude!, locationData.longitude!);

          if (res != null) {
            LogUtil.v("background report success");
          }
        }
      });
    }
  }

  void _backGroundCancel() {
    if (_backGroundLocation != null) _backGroundLocation!.cancel();
  }

  void _offline() async {
    /// offline
    DriverDialog.show(context, S.of(context).offline_confirmed, () async {
      LoadingUtils.show();

      var res = await context.read<DriverModel>().driverOnline(false);

      LoadingUtils.dismiss();

      if (res != null) {
        LocationTimer.cancelSubmit();
        _backGroundCancel();
      }
    });
  }

  void _online() async {
    var permissionStatus = await Geolocator.checkPermission();
    LogUtil.v("_online permissionStatus:: $permissionStatus");
    if (permissionStatus != LocationPermission.always) {
      if (Platform.isAndroid) {
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return DriverDisclosureDialog(() {
                onlineConfirm();
              });
            });
      } else {
        onlineConfirm();
      }
    } else {
      onlineConfirm();
    }
  }

  onlineConfirm() async {
    if (await _checkLocationPermission()) {
      LoadingUtils.show();

      _listenLocation();

      LogManager.instance!.log(TAG, "", "start online");
      var position = await LocationManager.getCurrentPosition();

      if (position == null) {
        LoadingUtils.dismiss();
        LogManager.instance!.log(TAG, "", "can not get position");
        return;
      }

      preLat = position.latitude;
      preLog = position.longitude;

      LogManager.instance!.log(TAG, "",
          "current lat,long::${position.latitude},${position.longitude}");
      // request driver online
      var res = await context
          .read<DriverModel>()
          .driverOnline(true, lat: position.latitude, lon: position.longitude);

      LoadingUtils.dismiss();

      // get driver orders
      if (res != null) {
        context.read<DriverModel>().driverOrders(loading: false);
      }
    } else {
      // show custom permission alert
      LogManager.instance!.log(TAG, "", "permission denied");
      permissionAlert();
    }
  }

  ///forward to app system setting for location permission
  permissionAlert() {
    DriverDialog.show(
        context,
        Platform.isAndroid
            ? S.of(context).location_deny_access_android
            : S.of(context).location_deny_access_ios, () async {
      LogUtil.v("openAppSetting...");
      await Geolocator.openAppSettings();
    }, valueChanged: (data) {
      //tracking
      TrackingUtils.instance!.tracking(TConstants.D_DELIVERY_LOCATION_CLICK,
          value: {"value": data});
    });
  }

  Future<bool> _checkLocationPermission() async {
    bool enable = await Location.instance.serviceEnabled();
    if (enable) {
      try {
        if (Platform.isAndroid) {
          var bool = await Location.instance.enableBackgroundMode(enable: true);
          LogUtil.v("enableBackgroundMode:: $bool");
          return bool;
        } else {
          var permissionStatus = await Geolocator.checkPermission();
          LogUtil.v("permissionStatus:: $permissionStatus");
          if (permissionStatus != LocationPermission.always) {
            var locationPermission = await Geolocator.requestPermission();
            if (locationPermission != LocationPermission.always) {
              return false;
            } else {
              var isEnabled = await Location.instance.isBackgroundModeEnabled();
              if (!isEnabled) {
                await Location.instance.enableBackgroundMode(enable: true);
              }
              return true;
            }
          } else {
            return await Location.instance.enableBackgroundMode(enable: true);
          }
        }
      } on PlatformException catch (e) {
        LogUtil.v("permission:${e.toString()}");
        return false;
      }
    } else {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildSwitch();
  }

  Widget _buildSwitch() {
    if (Platform.isIOS) {
      return CupertinoSwitch(
          value: ConnectCache.getOnline()!,
          activeColor: MyColors.portal,
          onChanged: (bool) {
            change(bool);
          });
    } else {
      return Switch(
          value: ConnectCache.getOnline()!,
          activeColor: MyColors.portal,
          onChanged: (bool) {
            change(bool);
          });
    }
  }

  change(bool b) async {
    LogManager.instance!.log(TAG, "", "online::$b");
    b ? _online() : _offline();
    TrackingUtils.instance!.tracking(b
        ? TConstants.D_DELIVERY_START_CLICK
        : TConstants.D_DELIVERY_STOP_CLICK);
  }
}
