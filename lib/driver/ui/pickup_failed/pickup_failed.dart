import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/ui/pickup_failed/pickup_auto_failed.dart';
import 'package:connect/driver/ui/pickup_failed/pickup_failed_des_route.dart';
import 'package:connect/driver/ui/pickup_failed/ready_delay_sheet.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/driver/widget/driver_one_dialog.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/location_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// pickup failed
class PickupFailed extends StatefulWidget {
  PickupFailed(this.routeToPickUp, {Key? key}) : super(key: key);

  final bool routeToPickUp;

  @override
  _PickupFailedState createState() => _PickupFailedState();
}

class _PickupFailedState extends State<PickupFailed> {
  @override
  void initState() {
    super.initState();
    //the reason of pickup failed
    GlobalConfig.eventBus.on<PickupFailedEvent>().listen((event) {
      if (mounted) {
        switch (event.index) {
          case Constants.ZERO: //food not ready
            if (event.autoFailed) {
              DriverOneDialog.show(
                  context, S.of(context).driver_pickup_too_long_tips,
                  callback: () async {
                LoadingUtils.show();

                var position = await LocationManager.getCurrentPosition();

                if (position == null) {
                  LoadingUtils.dismiss();
                  return;
                }

                context.read<DriverModel>().pickFailedReason(
                    position.latitude,
                    position.longitude,
                    "food-not-ready",
                    "delay 15 min", callback: () {
                  DriverOneDialog.show(
                      context, S.of(context).driver_assign_new_order);
                });
              });
            } else {
              ReadyDelaySheet.show(context, (min) async {
                LoadingUtils.show();

                var position = await LocationManager.getCurrentPosition();

                if (position == null) {
                  LoadingUtils.dismiss();
                  return;
                }

                //tracking
                var params = Map<String, dynamic>();
                params["minutes"] = "$min";
                TrackingUtils.instance!.tracking(
                    TConstants.D_DELIVERY_DELAY_MINUTES_CLICK,
                    value: params);

                context.read<DriverModel>().pickFailedReason(position.latitude,
                    position.longitude, "food-not-ready", "delay $min min");
              });
            }
            break;
          case Constants.ONE: //pickup failed
          case Constants.THREE: //other
            Navigator.of(context).pushNamed(PickupFailedDesRoute.tag);
            break;
          case Constants.TWO: //offline
            DriverDialog.show(
                context, S.of(context).driver_pickup_failed_offline, () async {
              LoadingUtils.show();

              var position = await LocationManager.getCurrentPosition();

              if (position == null) {
                LoadingUtils.dismiss();
                return;
              }

              //submit pickup failed reason
              await context.read<DriverModel>().pickFailedReason(
                  position.latitude, position.longitude, "driver-off", "");
              //driver offline
              await context.read<DriverModel>().driverOnline(false,
                  lat: position.latitude, lon: position.longitude);
            });
            break;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        widget.routeToPickUp ? EmptyContainer() : PickupAutoFailed(),
      ],
    );
  }
}
