

import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

///restaurant ready meal delay
class ReadyDelaySheet {
  static show(BuildContext context,ValueChanged callback) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context,callback);
        },
        context: context);
  }

  static Widget buildBottomSheetWidget(BuildContext context, callback) {
    return Container(
      height: Dimens.dp_280,
      child: Column(
        children: [
          buildItem(context, S.of(context).select_delay_time), //can
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(
              context, "10 ${S.of(context).minutes}", //google maps
              onTap: () async {
                callback(10);
              }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, "20 ${S.of(context).minutes}",
              onTap: () async {
                callback(20);
              }), //apple maps
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, "30 ${S.of(context).minutes}",
              onTap: () async {
                callback(30);
              }), //apple maps
          Divider(
            thickness: Dimens.dp_10,
          ),
          buildItem(context, S.of(context).cancel), //cancel
        ],
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        await Future.delayed(Duration(milliseconds: 300));
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: Dimens.dp_20),
        height: Dimens.dp_50,
        child: Text(
          title,
          style: TextStyle(
              color: S.of(context).select_delay_time == title
                  ? Colors.black87
                  : Colors.blue,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
