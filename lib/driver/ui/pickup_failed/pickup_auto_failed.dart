import 'dart:async';
import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sprintf/sprintf.dart';

class PickupAutoFailed extends StatefulWidget {
  PickupAutoFailed({Key? key}) : super(key: key);

  @override
  _PickupAutoFailedState createState() => _PickupAutoFailedState();
}

class _PickupAutoFailedState extends State<PickupAutoFailed> {
  Timer? _timer;
  String? countTime;

  @override
  void initState() {
    super.initState();
    LogUtil.v("PickupAutoFailed::initState");
    // _countDown();
  }

  _countDown() {
    _timer = Timer.periodic(Duration(seconds: 1), (Timer t) {
      if ("00:00" == countTime) {
        _timer!.cancel();
        _timer = null;
        GlobalConfig.eventBus.fire(PickupFailedEvent(0, autoFailed: true));
      } else {
        setState(() {});
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    LogUtil.v("PickupAutoFailed::didChangeDependencies");
  }

  @override
  void dispose() {
    super.dispose();
    LogUtil.v("PickupAutoFailed::dispose");
    if (_timer != null) {
      _timer!.cancel();
      _timer = null;
    }
  }

  //time formatted
  String _printDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "$twoDigitMinutes:$twoDigitSeconds";
  }

  @override
  Widget build(BuildContext context) {
    // LogUtil.v("PickupAutoFailed::build,$countTime");
    if (_timer == null) {
      _countDown();
    }
    var entity = context.watch<DriverModel>().driverOrderEntity;
    if (entity != null && entity.next != null && entity.next!.failAt != null) {
      var localFinishAtDate =
          DateUtil.getDateTime(entity.next!.failAt!)!.toLocal();
      // LogUtil.v("fail time::$localFinishAtDate");
      var diff = localFinishAtDate.difference(DateTime.now());
      if (diff.inSeconds < 0) {
        countTime = "00:00";
      } else {
        countTime = _printDuration(diff);
      }
    }

    if (entity != null && entity.next != null) {
      var getMoreTitle = S.of(context).driver_tip_get_more;
      var getMoreKeyword = S.of(context).driver_get_more;
      var failKeyword = S.of(context).driver_not_pickup_complete;
      var failTip =
          sprintf(S.of(context).driver_order_auto_failed, ["$countTime"]);

      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Icon(
                  Icons.circle,
                  color: MyColors.MY,
                  size: 4,
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Expanded(
                child:
                    _buildFailRichText(context, getMoreTitle, getMoreKeyword),
              ),
            ],
          ),
          SizedBox(
            height: 12,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Icon(
                  Icons.circle,
                  color: MyColors.MY,
                  size: 4,
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Expanded(
                child: _buildFailRichText(context, failTip, failKeyword),
              ),
            ],
          ),
        ],
      );
    } else {
      return EmptyContainer();
    }
  }

  Widget _buildFailRichText(
      BuildContext context, String title, String keyword) {
    var failTips = title.split("@1");

    return RichText(
      text: TextSpan(children: [
        TextSpan(
          text: failTips.first,
          style: MyStyles.r12.copyWith(color: MyColors.color333),
        ),
        TextSpan(
          text: keyword,
          style: MyStyles.r12.copyWith(color: MyColors.MY),
        ),
        TextSpan(
          text: failTips.last,
          style: MyStyles.r12.copyWith(color: MyColors.color333),
        ),
      ]),
    );
  }
}
