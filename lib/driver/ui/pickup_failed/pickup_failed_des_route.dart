import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/location_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

class PickupFailedDesRoute extends StatefulWidget {
  static String tag = "pickup_failed_route";

  PickupFailedDesRoute({Key? key}) : super(key: key);

  @override
  _PickupFailedRouteDesState createState() => _PickupFailedRouteDesState();
}

class _PickupFailedRouteDesState extends State<PickupFailedDesRoute> {
  TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey[200],
        appBar: PreferredSize(
            child: AppBar(
              elevation: 0,
              systemOverlayStyle: SystemUiOverlayStyle.light,
              backgroundColor: Colors.white,
            ),
            preferredSize: Size.fromHeight(0.0)),
        body: Column(
          children: [
            Container(
              color: Colors.white,
              alignment: Alignment.center,
              padding: const EdgeInsets.only(
                  left: Dimens.dp_15, right: Dimens.dp_15),
              height: Dimens.dp_50,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      width: Dimens.dp_50,
                      height: Dimens.dp_30,
                      child: Text(
                        S.of(context).cancel,
                        style: TextStyle(
                            color: Colors.black87, fontSize: Dimens.sp_15),
                      ),
                    ),
                  ),
                  Text(S.of(context).driver_des,
                      style: TextStyle(
                          color: Colors.black87, fontSize: Dimens.sp_16)),
                  Container(
                    height: Dimens.dp_30,
                    child: ElevatedButton(
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(
                            _controller.text.isEmpty
                                ? Colors.grey[350]
                                : RColors.r_orange),
                        padding: MaterialStateProperty.all(
                            const EdgeInsets.only(
                                left: Dimens.dp_10,
                                right: Dimens.dp_10,
                                top: Dimens.dp_0,
                                bottom: Dimens.dp_0)),
                      ),
                      child: Text(
                        S.of(context).confirm,
                        style: TextStyle(
                            color: _controller.text.isEmpty
                                ? Colors.grey[500]
                                : Colors.white,
                            fontSize: Dimens.sp_15),
                      ),
                      onPressed: _controller.text.isEmpty
                          ? null
                          : () async {
                              FocusScope.of(context).requestFocus(FocusNode());
                              LoadingUtils.show();

                              var position = await LocationManager.getCurrentPosition();

                              if (position == null) {
                                LoadingUtils.dismiss();
                                return;
                              }

                              var params = Map<String, dynamic>();
                              params["description"] = "${_controller.text}";
                              TrackingUtils.instance!.tracking(TConstants.D_DELIVERY_UNABLE_TO_PICKUP_CONFIRM_CLICK,value: params);

                              context.read<DriverModel>().pickFailedReason(
                                  position.latitude,
                                  position.longitude,
                                  "unable-dropoff",
                                  _controller.text, callback: () {
                                Navigator.of(context).pop();
                              });
                            },
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: Dimens.dp_1,
            ),
            Padding(
              padding: const EdgeInsets.all(Dimens.dp_15),
              child: Container(
                alignment: Alignment.topLeft,
                height: Dimens.dp_250,
                child: TextField(
                  controller: _controller,
                  autofocus: true,
                  maxLines: 10,
                  onChanged: (v) {
                    setState(() {});
                  },
                  decoration: InputDecoration(
                      filled: true,
                      fillColor: Colors.white,
                      border: InputBorder.none,
                      focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(15),
                          borderSide: BorderSide.none),
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 12.0, horizontal: 15)),
                ),
              ),
            ),
          ],
        ));
  }
}
