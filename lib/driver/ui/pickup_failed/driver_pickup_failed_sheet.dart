import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';

import '../../../common/MyStyles.dart';

class DriverPickupFailedSheet {
  static show(BuildContext context, bool routeToPickUp) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context, routeToPickUp);
        },
        context: context);
  }

  static tracking(String reason) {
    var params = Map<String, dynamic>();
    params["reason"] = reason;
    TrackingUtils.instance!.tracking(
        TConstants.D_DELIVERY_PICKUP_FAILED_REASON_CLICK,
        value: params);
  }

  static Widget buildBottomSheetWidget(
      BuildContext context, bool routeToPickUp) {
    return Container(
      height: routeToPickUp ? Dimens.dp_290 : Dimens.dp_340,
      child: Column(
        children: [
          buildItem(context, S.of(context).driver_pick_failed_reason), //can
          Divider(
            height: Dimens.dp_1,
          ),
          routeToPickUp
              ? EmptyContainer()
              : buildItem(
                  context, S.of(context).driver_pick_failed_ready, //google maps
                  onTap: () async {
                  tracking("food not ready");
                  GlobalConfig.eventBus.fire(PickupFailedEvent(Constants.ZERO));
                }),
          routeToPickUp
              ? EmptyContainer()
              : Divider(
                  height: Dimens.dp_1,
                ),
          buildItem(context, S.of(context).driver_pick_failed_delivery,
              onTap: () async {
            tracking("unable to pickup");
            GlobalConfig.eventBus.fire(PickupFailedEvent(Constants.ONE));
          }), //apple maps
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, S.of(context).driver_pick_failed_offline,
              onTap: () async {
            tracking("am off");
            GlobalConfig.eventBus.fire(PickupFailedEvent(Constants.TWO));
          }), //apple maps
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, S.of(context).driver_pick_failed_other,
              onTap: () async {
            tracking("other");
            GlobalConfig.eventBus.fire(PickupFailedEvent(Constants.THREE));
          }), //apple maps
          Divider(
            thickness: Dimens.dp_10,
          ),
          buildItem(context, S.of(context).cancel), //cancel
        ],
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        await Future.delayed(Duration(milliseconds: 300));
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: Dimens.dp_20),
        height: Dimens.dp_50,
        child: Text(
          title,
          style: TextStyle(
              color: S.of(context).driver_pick_failed_reason == title
                  ? Colors.black87
                  : MyColors.portal,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
