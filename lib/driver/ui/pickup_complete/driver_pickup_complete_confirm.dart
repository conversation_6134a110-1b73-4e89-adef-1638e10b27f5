import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/provider/driver_passcode.dart';
import 'package:connect/driver/widget/driver_one_dialog.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/button_style_manager.dart';
import 'package:connect/utils/location_manager.dart';
import 'package:connect/utils/tracking_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:provider/provider.dart';

class DriverPickupCompleteConfirm extends StatefulWidget {
  DriverPickupCompleteConfirm(this.isNext, this.orderId, this.restId, this.bags,
      {Key? key})
      : super(key: key);

  final bool? isNext;
  final String? orderId;
  final String? restId;
  final int? bags;

  @override
  _DriverPickupCompleteConfirmState createState() =>
      _DriverPickupCompleteConfirmState();
}

class _DriverPickupCompleteConfirmState
    extends State<DriverPickupCompleteConfirm> {
  static const String TAG = "pickup_complete";

  var passCode;
  num? mBags = 1;

  @override
  void initState() {
    super.initState();
    passCode = context.read<DriverPassCode>().passCode;
    LogUtil.v("pickup,passCode::$passCode");
    if (widget.bags! > 0) {
      mBags = widget.bags;
    }

    GlobalConfig.eventBus.on<DriverBags>().listen((event) {
      if (!mounted) return;
      mBags = event.bagsNum;
      LogUtil.v("bags::$mBags");
    });
  }

  @override
  Widget build(BuildContext context) {
    var lastCode = context.watch<DriverPassCode>().lastPassCode;
    bool light = lastCode.length == 2;
    return Container(
        margin: const EdgeInsets.fromLTRB(
            Dimens.dp_25, Dimens.dp_25, Dimens.dp_25, Dimens.dp_50),
        alignment: Alignment.center,
        child: SizedBox(
          width: double.infinity,
          height: Dimens.dp_40,
          child: ElevatedButton(
            style: ButtonStyleManager.buttonStyle(),
            child: Text(
              S.of(context).driver_submit,
              style: TextStyle(color: Colors.white, fontSize: Dimens.sp_16),
            ),
            onPressed: light
                ? () async {
                    TrackingUtils.instance!.tracking(
                        TConstants.D_DELIVERY_COMPLETED_PICKUP_SUBMIT_CLICK);

                    FocusScope.of(context).requestFocus(FocusNode());
                    var fistCode = context.read<DriverPassCode>().fistPassCode;

                    if ("$fistCode$lastCode" != passCode) {
                      DriverOneDialog.show(
                          context, S.of(context).driver_input_passcode,
                          callback: () {
                        GlobalConfig.eventBus
                            .fire(DriverClearCompletePickupNum());
                      });
                    } else {
                      LogManager.instance!.log(TAG, "", "start");

                      LoadingUtils.show();

                      var position = await LocationManager.getCurrentPosition();

                      if (position == null) {
                        LoadingUtils.dismiss();
                        return;
                      }

                      int time1 = DateTime.now().millisecondsSinceEpoch;

                      ///如果当前选中的tab订单是more，需要先assign给司机，然后再将订单状态变为completed pickup
                      if (!widget.isNext!) {
                        var assignRes = await context
                            .read<DriverModel>()
                            .matchOrder(widget.restId, widget.orderId);
                        if (assignRes == null) return;
                      }

                      //update stops status
                      var order = await context
                          .read<DriverModel>()
                          .arrivedStatus(position.latitude, position.longitude,
                              "completed",
                              orderId: widget.isNext! ? null : widget.orderId,
                              bags: mBags,
                              option: true);

                      if (order == null) return;

                      LogManager.instance!.log(TAG, "",
                          "isNext::${widget.isNext},next::${order.next}");

                      int time2 = DateTime.now().millisecondsSinceEpoch;
                      TrackingManager.trackStopsTime(
                          time1, time2, "completed_pickup");

                      if (widget.isNext! && order.next == null) {
                        LogManager.instance!.log(TAG, "", "post stops start");

                        //create order
                        var response =
                            await context.read<DriverModel>().createOrder();

                        if (response == null) {
                          TrackingUtils.instance!.tracking(TConstants
                              .D_DELIVERY_COMPLETED_PICKUP_CREATE_FAIL);

                          LogManager.instance!.log(TAG, "", "post stops fail");

                          return;
                        }

                        TrackingUtils.instance!.tracking(TConstants
                            .D_DELIVERY_COMPLETED_PICKUP_CREATE_SUCCESS);
                      }

                      LoadingUtils.dismiss();

                      Navigator.pop(context);
                    }
                  }
                : null,
          ),
        ));
  }
}
