import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/driver/bean/driver_orders_entity.dart';
import 'package:connect/driver/provider/driver_passcode.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

class DriverPickupCompleteCode extends StatefulWidget {
  DriverPickupCompleteCode({Key? key}) : super(key: key);

  @override
  _DriverPickupCompleteCodeState createState() =>
      _DriverPickupCompleteCodeState();
}

class _DriverPickupCompleteCodeState extends State<DriverPickupCompleteCode> {
  DriverOrdersEntity? entity;
  var passCode;
  TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    context.read<DriverPassCode>().setLastPassCode("", notify: false);

    //get passCode
    passCode = context.read<DriverPassCode>().passCode;
    context.read<DriverPassCode>().setFirstPassCode(passCode.substring(0, 2));

    //clear the last two num when input wrong number
    GlobalConfig.eventBus.on<DriverClearCompletePickupNum>().listen((event) {
      if (!mounted) return;
      setState(() {
        _controller.text = "";
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Dimens.dp_20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  S.of(context).digit_passcode,
                  style: MyStyles.r15.copyWith(color: MyColors.color333),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          SizedBox(
            height: Dimens.dp_20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                passCode.substring(0, 2),
                style: TextStyle(fontSize: 30),
              ),
              SizedBox(
                width: Dimens.dp_4,
              ),
              Container(
                alignment: Alignment.center,
                color: Colors.white,
                width: Dimens.dp_100,
                child: TextField(
                  controller: _controller,
                  autofocus: true,
                  maxLines: 1,
                  style: TextStyle(fontSize: 30),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(2),
                    FilteringTextInputFormatter.digitsOnly
                  ],
                  onChanged: (v) {
                    context.read<DriverPassCode>().setLastPassCode(v);
                    //auto hide keyboard
                    if (v != null && v.length == 2) {
                      FocusScope.of(context).requestFocus(FocusNode());
                    }
                  },
                  decoration: InputDecoration(
                      isDense: true,
                      border: OutlineInputBorder(
                        //for hintText show in center
                        borderSide: BorderSide(
                          color: Colors.grey,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: Dimens.dp_5, vertical: Dimens.dp_5)),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
