import 'dart:convert';
import 'package:connect/common/MyStyles.dart';
import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/driver/bean/driver_orders_entity.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'driver_pickup_complete_item.dart';

class DriverPickupCompleteMenu extends StatefulWidget {
  DriverPickupCompleteMenu(this.isNext, this.orderId, {Key? key})
      : super(key: key);

  final bool? isNext;
  final String? orderId;

  @override
  _DriverPickupCompleteMenuState createState() =>
      _DriverPickupCompleteMenuState();
}

class _DriverPickupCompleteMenuState extends State<DriverPickupCompleteMenu> {
  DriverOrdersEntity? entity;
  List<DriverOrdersNextOrderItem>? items;
  List<Widget> menuItems = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _buildTextFood();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: Dimens.dp_30,
        ),
        Text(
          S.of(context).driver_menu_items,
          style: MyStyles.r15.copyWith(color: MyColors.color333),
        ),
        Expanded(
          child: ListView(
            children: menuItems,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildTextFood() {
    menuItems.clear();
    entity = context.read<DriverModel>().driverOrderEntity;
    if (widget.isNext!) {
      if (entity!.next != null &&
          entity!.next!.order != null &&
          entity!.next!.order!.items != null) {
        items = entity!.next!.order!.items;
      }
    } else {
      if (entity!.more != null) {
        var moreList = entity!.more!
            .where((element) => element.order!.sId == widget.orderId)
            .toList();
        items = moreList.first.order!.items;
      }
    }
    Map<String, int> foodMaps = {};
    items!.forEach((element) {
      var jsonStr = jsonEncode(element);
      if (foodMaps[jsonStr] == null) {
        foodMaps[jsonStr] = 1;
      } else {
        foodMaps[jsonStr] = foodMaps[jsonStr]! + 1;
      }
    });

    foodMaps.forEach((key, value) {
      menuItems.add(_buildMenu(
          JsonConvert.fromJsonAsT<MultiNameEntity>(jsonDecode(key)), value));
    });
    return menuItems;
  }

  Widget _buildMenu(MultiNameEntity? element, int size) {
    if (element == null) return Container();
    return DriverPickupCompleteItem(element, size);
  }
}
