import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class DriverPickupCompleteBags extends StatefulWidget {
  final double height;

  final double width;

  final double iconWidth;

  final int? bags;

  DriverPickupCompleteBags(
    this.bags, {
    this.height = 40,
    this.width = 40,
    this.iconWidth = 40,
  });

  @override
  _DriverPickupCompleteBagsState createState() =>
      _DriverPickupCompleteBagsState();
}

class _DriverPickupCompleteBagsState extends State<DriverPickupCompleteBags> {
  String numText = "1";

  @override
  void initState() {
    super.initState();
    var b = widget.bags;
    if (b != null && b > 0) {
      numText = widget.bags.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Text(
          S.of(context).driver_bags,
          textAlign: TextAlign.center,
          style: MyStyles.r15.copyWith(color: MyColors.color333),
        ),
        SizedBox(
          height: 16,
        ),
        Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: MyColors.portal,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              //-
              _iconButton(icon: Icons.remove, isAdd: false),
              Container(
                width: widget.width,
                child: Text(
                  numText,
                  textAlign: TextAlign.center,
                  style: MyStyles.m16.copyWith(color: MyColors.W),
                ),
              ),
              //+
              _iconButton(icon: Icons.add, isAdd: true),
            ],
          ),
        )
      ],
    );
  }

  Widget _iconButton({IconData? icon, bool? isAdd}) {
    return Container(
      width: widget.iconWidth,
      child: IconButton(
        iconSize: 16,
        color: MyColors.W,
        padding: EdgeInsets.all(0),
        icon: Icon(icon),
        onPressed: () {
          setState(() {
            var num = int.parse(numText);
            if (!isAdd! && num == 1) return;
            if (isAdd) {
              num++;
            } else {
              num--;
            }
            numText = '$num';
            GlobalConfig.eventBus.fire(DriverBags(num));
          });
        },
      ),
    );
  }
}
