import 'package:connect/common/t_constants.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'driver_pickup_complete_bags.dart';
import 'driver_pickup_complete_code.dart';
import 'driver_pickup_complete_confirm.dart';
import 'driver_pickup_complete_menu.dart';
import 'driver_pickup_complete_title.dart';

class DriverPickupCompleteRoute extends StatefulWidget {
  static String tag = "driver_pickup_route";

  DriverPickupCompleteRoute({Key? key}) : super(key: key);

  @override
  _DriverPickupCompleteRouteState createState() =>
      _DriverPickupCompleteRouteState();
}

class _DriverPickupCompleteRouteState extends State<DriverPickupCompleteRoute> {
  bool? isNext = false;
  String? orderId = "";
  String? restId = "";
  int? bags = 0;

  @override
  void initState() {
    super.initState();
    TrackingUtils.instance!
        .tracking(TConstants.D_DELIVERY_COMPLETED_PICKUP_ENTER);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    var arguments =
        ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
    isNext = arguments["isNext"];
    orderId = arguments["orderId"];
    restId = arguments["restId"];
    bags = arguments["bags"];
    LogUtil.v("pickup,isNext:$isNext,orderId:$orderId,bags:$bags");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false,
        appBar: PreferredSize(
            child: AppBar(
              elevation: 0,
              systemOverlayStyle: SystemUiOverlayStyle.light,
              backgroundColor: Colors.white,
            ),
            preferredSize: Size.fromHeight(0.0)),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            DriverPickupCompleteTitle(),
            SizedBox(
              height: 20,
            ),
            DriverPickupCompleteCode(),
            SizedBox(
              height: 40,
            ),
            DriverPickupCompleteBags(
              bags,
              height: 36,
            ),
            Expanded(
              child: DriverPickupCompleteMenu(isNext, orderId),
            ),
            DriverPickupCompleteConfirm(isNext, orderId, restId, bags),
          ],
        ));
  }
}
