import 'package:connect/common/MyStyles.dart';
import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';

class DriverPickupCompleteItem extends StatefulWidget {
  DriverPickupCompleteItem(this.item, this.size, {Key? key}) : super(key: key);

  final MultiNameEntity item;
  final int size;

  @override
  _DriverPickupCompleteItemState createState() =>
      _DriverPickupCompleteItemState();
}

class _DriverPickupCompleteItemState extends State<DriverPickupCompleteItem> {
  bool? _checkboxSelected = false;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Checkbox(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          side: BorderSide(
            color: MyColors.ccc,
            width: 1,
          ),
          value: _checkboxSelected,
          activeColor: MyColors.portal,
          onChanged: (value) {
            setState(() {
              _checkboxSelected = value;
            });
          },
        ),
        Text(
          "${widget.size}X ${ServerMultiLan.multiAdapt2(widget.item)}",
          style: MyStyles.r15.copyWith(
            color: MyColors.color999,
          ),
        ),
      ],
    );
  }
}
