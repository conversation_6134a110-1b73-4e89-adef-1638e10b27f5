import 'package:connect/common/MyStyles.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class DriverPickupCompleteTitle extends StatefulWidget {
  DriverPickupCompleteTitle({Key? key}) : super(key: key);

  @override
  _DriverPickupCompleteTitleState createState() =>
      _DriverPickupCompleteTitleState();
}

class _DriverPickupCompleteTitleState extends State<DriverPickupCompleteTitle> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: Alignment.center,
          padding:
              const EdgeInsets.only(left: Dimens.dp_15, right: Dimens.dp_15),
          height: Dimens.dp_50,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  alignment: Alignment.center,
                  width: Dimens.dp_55,
                  height: Dimens.dp_30,
                  child: Text(
                    S.of(context).cancel,
                    style: TextStyle(
                        color: Colors.black87, fontSize: Dimens.sp_15),
                  ),
                ),
              ),
              Text(
                S.of(context).driver_order_pickup_complete,
                style: MyStyles.m16.copyWith(color: MyColors.color333),
              ),
              Container(
                width: Dimens.dp_50,
                height: Dimens.dp_30,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
