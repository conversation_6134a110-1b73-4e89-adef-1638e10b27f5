import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_payment_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/roles_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class DriverPaymentDashboard extends StatelessWidget {
  DriverPaymentDashboard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var hasPaymentDashboard = RolesManager.hasPaymentDashboard();
    LogUtil.v("hasPaymentDashboard::$hasPaymentDashboard");
    return hasPaymentDashboard
        ? Column(
            children: [
              InkWell(
                onTap: () async {
                  TrackingUtils.instance!
                      .tracking(TConstants.D_MORE_PAYMENT_DASHBOARD_CLICK);
                  var response = await DriverPaymentModel.paymentDashboard();
                  if (response == null) return;
                  try {
                    await launch(response.data);
                  } catch (e) {
                    LogUtil.v("dashboard exception::${e.toString()}");
                  }
                },
                child: Container(
                    height: Dimens.dp_45,
                    color: Colors.white,
                    padding: const EdgeInsets.only(
                        left: Dimens.dp_20, right: Dimens.dp_20),
                    child: Row(
                      children: [
                        Image(
                            width: 16,
                            height: 16,
                            image: AssetImage("assets/images/more/identity.png")),
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              S.of(context).driver_payment_dashboard,
                              style: TextStyle(color: Colors.black87),
                            ),
                          ),
                        ),
                        Icon(
                          Icons.keyboard_arrow_right,
                          color: Colors.black26,
                          size: Dimens.dp_20,
                        ),
                      ],
                    )),
              ),
            ],
          )
        : EmptyContainer();
  }
}
