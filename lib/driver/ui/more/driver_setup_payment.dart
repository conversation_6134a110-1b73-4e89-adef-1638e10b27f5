import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_payment_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/roles_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class DriverSetupPayment extends StatefulWidget {
  DriverSetupPayment({Key? key}) : super(key: key);

  @override
  _DriverSetupPaymentState createState() => _DriverSetupPaymentState();
}

class _DriverSetupPaymentState extends State<DriverSetupPayment> {
  @override
  Widget build(BuildContext context) {
    var hasSetupPayment = RolesManager.hasSetupPayment();
    LogUtil.v("hasSetupPayment::$hasSetupPayment");
    return hasSetupPayment
        ? Column(
            children: [
              InkWell(
                onTap: () async {
                  TrackingUtils.instance!.tracking(TConstants.D_MORE_SETUP_PAYMENT_CLICK);
                  var response = await DriverPaymentModel.setupPayment();
                  if (response == null) return;
                  try {
                    var launched = await launch(response.data);
                    if (launched) {
                      GlobalConfig.paymentRefresh = true;
                    }
                    LogUtil.v("isLaunch::$launched");
                  } catch (e) {
                    LogUtil.v("payment exception::${e.toString()}");
                  }
                },
                child: Container(
                    height: Dimens.dp_45,
                    color: Colors.white,
                    padding: const EdgeInsets.only(
                        left: Dimens.dp_20, right: Dimens.dp_20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          S.of(context).driver_create_payment_account,
                          style: TextStyle(color: Colors.black87),
                        ),
                        Icon(
                          Icons.arrow_forward_ios_sharp,
                          color: Colors.black54,
                          size: Dimens.dp_20,
                        ),
                      ],
                    )),
              ),
              Divider(
                height: Dimens.dp_1,
              ),
            ],
          )
        : EmptyContainer();
  }
}
