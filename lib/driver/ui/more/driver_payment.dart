import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';
import 'driver_payment_dashboard.dart';
import 'driver_setup_payment.dart';

class DriverPayment extends StatefulWidget {
  DriverPayment({Key? key}) : super(key: key);

  @override
  _DriverPaymentState createState() => _DriverPaymentState();
}

class _DriverPaymentState extends State<DriverPayment> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          // DriverSetupPayment(),
          DriverPaymentDashboard(),
        ],
      ),
    );
  }
}
