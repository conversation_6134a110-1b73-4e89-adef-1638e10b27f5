import 'package:connect/gen/assets.gen.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';

import '../common/MyStyles.dart';
import '../generated/l10n.dart';
import '../utils/connect_cache.dart';
import 'bean/driver_orders_entity.dart';
import 'delivery/driver_at_dropoff_map_route.dart';
import 'delivery/driver_stops_count.dart';

class DriverProfileCard extends StatefulWidget {
  const DriverProfileCard({Key? key, this.entity}) : super(key: key);

  final DriverOrdersEntity? entity;

  @override
  State<DriverProfileCard> createState() => _DriverProfileCardState();
}

class _DriverProfileCardState extends State<DriverProfileCard> {
  @override
  Widget build(BuildContext context) {
    var user = ConnectCache.getUser();
    bool showDropoffMap = false;
    var entity = widget.entity;
    if (entity != null &&
        entity.next != null &&
        entity.next!.type == "dropoff" &&
        (entity.next!.arrivedAt == null || entity.next!.arrivedAt!.isEmpty) &&
        entity.next!.order!.restaurant!.delivery!.batch == null) {
      showDropoffMap = true;
    }

    return Card(
      elevation: 0,
      margin: EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  children: [
                    Assets.images.driverTab.profileAvatar.svg(width: 40),
                    SizedBox(
                      width: 8,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          S.of(context).driver_profile_title,
                          style: MyStyles.m15.copyWith(
                            color: MyColors.color333,
                          ),
                        ),
                        SizedBox(
                          height: 2,
                        ),
                        Text(
                          "${user?.phone}",
                          style: MyStyles.r12.copyWith(
                            color: MyColors.color333,
                          ),
                        ),
                      ],
                    ),
                    Spacer(),
                    _buildDropoffMapButton(context, showDropoffMap),
                  ],
                ),
                widget.entity != null
                    ? SizedBox(
                        height: 24,
                      )
                    : EmptyContainer(),
                widget.entity != null
                    ? DriverStopsCount(widget.entity)
                    : EmptyContainer(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropoffMapButton(BuildContext context, bool showDropoffMap) {
    if (!showDropoffMap) return EmptyContainer();
    return Container(
      height: 24,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 6),
          backgroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          side: BorderSide(
            width: 1.0,
            color: MyColors.portal,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.location_on,
              color: MyColors.portal,
              size: 14,
            ),
            Text(
              S.of(context).driver_orders_dropoff_map,
              style: MyStyles.m12.copyWith(color: MyColors.portal),
            ),
          ],
        ),
        onPressed: () {
          Navigator.of(context)
              .pushNamed(DriverAtDropoffMapRoute.tag, arguments: widget.entity);
        },
      ),
    );
  }
}
