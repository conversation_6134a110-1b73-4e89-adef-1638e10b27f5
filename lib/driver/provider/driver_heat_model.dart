import 'package:connect/driver/bean/driver_heat_entity.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:flutter/cupertino.dart';
import 'package:sprintf/sprintf.dart';

class DriverHeatModel extends ChangeNotifier {
  List<DriverHeatEntity>? heatEntity;

  Future<List<DriverHeatEntity>?> heatPickups() async {
    var regionId = ConnectCache.getCurrentRole()!.scope;
    String? url = sprintf(HttpUri.DRIVER_PICKUPS_HEAT_MAP, [regionId]);
    var response = await HttpManager.instance!.get(url, withLoading: true);
    if (response != null) {
      heatEntity =
          JsonConvert.fromJsonAsT<List<DriverHeatEntity>>(response.data);
      return heatEntity;
    }
    return null;
  }
}
