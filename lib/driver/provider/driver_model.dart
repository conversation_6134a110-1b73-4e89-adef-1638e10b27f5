import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/user_entity.dart';
import 'package:connect/driver/bean/driver_online_switch_entity.dart';
import 'package:connect/driver/bean/driver_orders_entity.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_error.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/gps_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:sprintf/sprintf.dart';

class DriverModel extends ChangeNotifier {
  ///driver online or offline
  Future<DriverOnlineSwitchEntity?> driverOnline(bool isOnline,
      {double? lat, double? lon}) async {
    var data = Map<String, dynamic>();

    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);

    if (isOnline) {
      var convert = convertLocation(lat!, lon!);
      var location = Map<String, double?>();
      location["lat"] = convert[0] as double?;
      location["lon"] = convert[1] as double?;
      data["location"] = location;
      data["onCall"] = true;
    } else {
      data["onCall"] = false;
    }
    var userId = ConnectCache.getUser()!.userId;
    String url = sprintf(HttpUri.DRIVER_ONLINE, [userId]);

    var response = await HttpManager.instance!
        .put(url, data: data, option: requestOptions, withLoading: false);

    if (response == null) return null;

    DriverOnlineSwitchEntity? switchEntity =
        JsonConvert.fromJsonAsT<DriverOnlineSwitchEntity>(response.data);
    //update switchButton online or offline
    if (switchEntity?.onCall != null) {
      GlobalConfig.eventBus.fire(DriverOnlineSwitchEvent(switchEntity!.onCall));
    }

    return switchEntity;
  }

  DriverOrdersEntity? driverOrderEntity;

  /// driver`s orders
  Future<Response?> driverOrders(
      {bool showMsg = true, bool loading = true}) async {
    var response;
    var userId = ConnectCache.getUser()!.userId;
    String? driverOrderUrl = sprintf(HttpUri.DRIVER_ORDER_STOPS, [userId]);
    return await (HttpManager.instance!
        .get(driverOrderUrl, showMsg: showMsg, withLoading: loading)
        .then((value) {
      if (value != null) {
        response = value.data;
        if (response != null && response.toString().length <= 2) {
          //resolve oneByteString type response sometimes
          driverOrderEntity = null;
        } else {
          driverOrderEntity =
              JsonConvert.fromJsonAsT<DriverOrdersEntity>(value.data);
        }
        notifyListeners();
      }
    }).catchError((e) {
      Sentry.captureException(
          "driver_orders_exception::${e.toString()},url::$driverOrderUrl,response::${response.toString()}");
      // MessageDialog.messageAlert(HttpError.UNEXPECTED_ERROR, ok: () {});
    }));
  }

  /// Submit location information to the server
  static Future<Response?> submitLocation(double lat, double lon) async {
    var convert = convertLocation(lat, lon);
    var data = Map<String, dynamic>();
    var location = Map<String, double?>();
    location["lat"] = convert[0] as double?;
    location["lon"] = convert[1] as double?;
    data["location"] = location;

    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);

    var userId = ConnectCache.getUser()!.userId;
    String? url = sprintf(HttpUri.DRIVER_ONLINE, [userId]);
    return await HttpManager.instance!.put(url,
        data: data, option: requestOptions, showMsg: false, withLoading: false);
  }

  /// check if the driver is online
  Future<bool> checkOnline() async {
    var response;
    try {
      var opExtras = Map<String, dynamic>();
      opExtras["type"] = "driver";
      Options requestOptions = new Options(extra: opExtras);
      response = await HttpManager.instance!.get(HttpUri.CHECK_DRIVER_ONLINE,
          option: requestOptions, showMsg: false, withLoading: false);
      if (response != null && response.toString().isNotEmpty) {
        UserEntity? userEntity =
            JsonConvert.fromJsonAsT<UserEntity>(response.data);
        bool call = userEntity?.onCall ?? false;
        return call;
      }
    } catch (e) {
      if (response != null)
        Sentry.captureException(
            "checkOnline_exception::${e.toString()},response::${response.toString()}");
    }
    return false;
  }

  Future<String?> checkAppUpgrade() async {
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);
    var response = await HttpManager.instance!.get(HttpUri.APP_UPDATE,
        option: requestOptions, showMsg: false, withLoading: false);
    if (response != null && response.data.toString().isNotEmpty) {
      ///tracking what cause _OneByteString is not a subtype of type int of index
      var params = Map<String, dynamic>();
      params["response"] = response.data.toString();
      TrackingUtils.instance!
          .tracking(TConstants.CHECK_APP_UPGRADE, value: params);

      var result = response.data["update"];
      return result;
    }
    return null;
  }

  ///driver will have the latest order
  Future<DriverOrdersEntity?> createOrder(
      {VoidCallback? callback,
      VoidCallback? errorCallback,
      ValueChanged? valueChanged}) async {
    var userId = ConnectCache.getUser()!.userId;
    String? url = sprintf(HttpUri.DRIVER_ORDER_STOPS, [userId]);

    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);

    var response = await HttpManager.instance!
        .post(url,
            option: requestOptions,
            withLoading: false,
            showMsg: false,
            callback: valueChanged)
        .catchError((e) {
      DioError? error = e as DioError;
      Map? data = error.response?.data as Map;
      String? message = data["message"];
      if (message != null && message.isNotEmpty) {
        MessageDialog.messageAlert(message, ok: () {
          if (errorCallback != null) errorCallback();
        });
      }
    });

    if (response == null) return null;

    driverOrderEntity =
        JsonConvert.fromJsonAsT<DriverOrdersEntity>(response.data);
    notifyListeners();
    if (callback != null) callback();

    return driverOrderEntity;
  }

  ///driver status
  Future<DriverOrdersEntity?> arrivedStatus(
      double lat, double lon, String status,
      {ValueChanged<DriverOrdersEntity?>? callback,
      bool option = false,
      String? orderId,
      num? bags}) async {
    // LogUtil.v("orderId::$orderId");
    var convert = convertLocation(lat, lon);
    var userId = ConnectCache.getUser()!.userId;
    String? url = sprintf(HttpUri.DRIVER_ORDER_STOPS, [userId]);
    var data = Map<String, dynamic>();
    var location = Map<String, double?>();
    location["lat"] = convert[0] as double?;
    location["lon"] = convert[1] as double?;
    data["location"] = location;
    data["status"] = status;
    if (bags != null) {
      data["bags"] = bags;
    }
    if (orderId != null) {
      data["orderId"] = orderId;
    }
    Options? requestOptions;
    if (option) {
      var opExtras = Map<String, dynamic>();
      opExtras["type"] = "driver";
      requestOptions = new Options(extra: opExtras);
    }
    var response = await HttpManager.instance!
        .put(url, data: data, option: requestOptions, withLoading: false);

    if (response == null) return null;

    driverOrderEntity =
        JsonConvert.fromJsonAsT<DriverOrdersEntity>(response.data);
    notifyListeners();
    if (callback != null) callback(driverOrderEntity);

    return driverOrderEntity;
  }

  ///try to get new order when driver input passCode correctly
  Future<Response?> matchOrder(String? restId, String? orderId,
      {VoidCallback? callback}) async {
    var data = Map<String, dynamic>();
    var userId = ConnectCache.getUser()!.userId;
    data["driver"] = userId;
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);
    String? url = sprintf(HttpUri.DRIVER_MATCH_NEW_ORDER, [restId, orderId]);
    return await HttpManager.instance!
        .post(url, params: data, option: requestOptions, withLoading: false);
  }

  ///mark this order have already pickup complete
  Future<Response?> pickComplete(String? orderId,
      {VoidCallback? callback}) async {
    String? url = sprintf(HttpUri.PICKUP_COMPLETE_URL, [orderId]);
    var data = Map<String, dynamic>();
    data["status"] = "pickup-completed";
    return await HttpManager.instance!.put(url, data: data, withLoading: false);
  }

  Future<Response?> menuReady(String? restId, String passcode) async {
    String? url = sprintf(HttpUri.MENU_ALREADY, [restId, passcode]);
    var data = Map<String, dynamic>();
    data["ready"] = true;
    return await HttpManager.instance!
        .put(url, data: data, withLoading: true)
        .catchError((e) {
      // MessageDialog.messageAlert(HttpError.errorHandler(e), ok: () {});
    });
  }

  ///meal delay
  Future<Response?> pickFailedReason(
      double lat, double lon, String reason, String desc,
      {VoidCallback? callback}) async {
    var convert = convertLocation(lat, lon);
    var userId = ConnectCache.getUser()!.userId;
    String? url = sprintf(HttpUri.DRIVER_ORDER_STOPS, [userId]);
    var data = Map<String, dynamic>();
    var location = Map<String, double?>();
    location["lat"] = convert[0] as double?;
    location["lon"] = convert[1] as double?;
    data["reason"] = reason;
    data["location"] = location;
    data["description"] = desc;
    data["status"] = "failed";
    return await (HttpManager.instance!
        .put(url, data: data, withLoading: false)
        .then((value) {
      if (value != null) {
        driverOrderEntity =
            JsonConvert.fromJsonAsT<DriverOrdersEntity>(value.data);
        notifyListeners();
        if (callback != null) {
          callback();
        }
      }
    }).catchError((e) {
      Sentry.captureException("pickFailedReason::${e.toString()}}");
    }));
  }

  static List<num?> convertLocation(double lat, double lon) {
    var convert = GpsUtil.gps84ToGcj02(lat, lon);
    LogUtil.v("convert_to_gps::[$lat,$lon]");
    LogUtil.v("convert_to_gps84::$convert");
    return convert;
  }

  Future<String?> uploadFile(List<int> imageData, String? path, String type,
      {VoidCallback? callback}) async {
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);

    FormData formData = FormData.fromMap({
      "type": type,
      "image": MultipartFile.fromBytes(
        imageData,
        filename: '$path',
      ),
    });

    var response = await HttpManager.instance!.post(HttpUri.DRIVER_DROPOFF_FILE,
        params: formData, option: requestOptions, withLoading: false);

    if (response != null) {
      LogUtil.v("photoUrl::${response.data}");
      return response.data;
    }
    LogManager.instance!
        .log("DriverModel", "uploadFile", "uploadFile response is null");
    return "";
  }

  Future<Response?> uploadLogFile(
      List<int> imageData, String path, String fileName,
      {VoidCallback? callback}) async {
    // List<int> imageData = byteData.buffer.asUint8List().toList();
    FormData formData = FormData.fromMap({
      "type": "log-connect",
      "image": MultipartFile.fromBytes(
        imageData,
        filename: '$path',
      ),
      "name": fileName
    });
    return await HttpManager.instance!
        .post(HttpUri.DRIVER_DROPOFF_FILE, params: formData, withLoading: false)
        .catchError((e) {
      Sentry.captureException("uploadLogFile::${e.toString()}");
    });
  }

  Future<Response?> uploadPhotos(
      String? orderId, String message, List<String> photosUrls,
      {VoidCallback? callback, bool option = false}) async {
    var userId = ConnectCache.getUser()!.userId;
    String? url = sprintf(HttpUri.DRIVER_DROPOFF_UPLOAD, [userId]);
    var data = Map<String, dynamic>();
    data["orderId"] = orderId;
    data["message"] = message;
    data["photos"] = photosUrls;
    return await HttpManager.instance!
        .post(url, params: data, withLoading: false);
  }

  Future<Response?> resetToPickComplete(String? orderId,
      {VoidCallback? callback}) async {
    String? url = sprintf(HttpUri.PICKUP_COMPLETE_URL, [orderId]);
    var data = Map<String, dynamic>();
    data["status"] = "pickup-completed";
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);
    return await HttpManager.instance!
        .put(url, data: data, option: requestOptions, withLoading: false);
  }

  Future<void> changeDropoff(String? orderId) async {
    var userId = ConnectCache.getUser()!.userId;
    String? url = sprintf(HttpUri.DRIVER_ORDER_STOPS, [userId]);
    var data = Map<String, dynamic>();
    data["type"] = "dropoff";
    data["orderId"] = orderId;
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);
    await HttpManager.instance!
        .post(url, params: data, option: requestOptions, withLoading: false)
        .then((value) {
      if (value != null) {
        driverOrderEntity =
            JsonConvert.fromJsonAsT<DriverOrdersEntity>(value.data);
        notifyListeners();
      }
    });
  }

  Options optionLoading() {
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);
    return requestOptions;
  }

  /// Reject order with reason
  Future<Response?> rejectOrder(String regionId, String orderId, String reason) async {
    var data = Map<String, dynamic>();
    data["reason"] = reason;
    
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);
    
    String url = "/v1/regions/$regionId/orders/$orderId/tron/reject";
    
    return await HttpManager.instance!.post(
      url,
      params: data,
      option: requestOptions,
      withLoading: false,
      showMsg: false,
    );
  }
}
