import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/roles_manager.dart';
import 'package:dio/dio.dart';
import 'package:sprintf/sprintf.dart';

class DriverPaymentModel {
  static Future<Response?> setupPayment() async {
    var userId = ConnectCache.getUser()!.userId;
    var scope = ConnectCache.getCurrentRole()!.scope;
    var driverRole = RolesManager.isDriverRole();
    String? url = sprintf(
        driverRole
            ? HttpUri.DRIVER_SETUP_PAYMENT
            : HttpUri.RESTAURANT_SETUP_PAYMENT,
        [driverRole ? userId : scope]);
    return await HttpManager.instance!.post(url, withLoading: true);
  }

  static Future<Response?> paymentDashboard() async {
    var userId = ConnectCache.getUser()!.userId;
    var scope = ConnectCache.getCurrentRole()!.scope;
    var driverRole = RolesManager.isDriverRole();
    String? url = sprintf(
        driverRole
            ? HttpUri.DRIVER_PAYMENT_DASHBOARD
            : HttpUri.RESTAURANT_PAYMENT_DASHBOARD,
        [driverRole ? userId : scope]);
    return await HttpManager.instance!.post(url, withLoading: true);
  }
}
