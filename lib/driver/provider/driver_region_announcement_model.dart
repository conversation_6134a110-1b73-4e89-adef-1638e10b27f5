import 'package:connect/driver/bean/driver_region_entity.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:sprintf/sprintf.dart';

class DriverRegionAnnouncementModel extends ChangeNotifier {
  DriverRegionEntity? regionEntity;

  regionAnnouncement() async {
    var regionId = ConnectCache.getCurrentRole()!.scope;
    String? url = sprintf(HttpUri.DRIVER_REGION, [regionId]);
    LogUtil.v("DriverRegionAnnouncementModel::regionAnnouncement - URL: $url");
    await HttpManager.instance!.get(url, showMsg: false, withLoading: false).then((value) {
      if (value != null &&
          value.data != null &&
          value.data.toString().isNotEmpty) {
        LogUtil.v("DriverRegionAnnouncementModel::regionAnnouncement - Raw response: ${value.data}");
        regionEntity = JsonConvert.fromJsonAsT<DriverRegionEntity>(value.data);
        LogUtil.v("DriverRegionAnnouncementModel::regionAnnouncement - Parsed regionEntity: $regionEntity");
        if (regionEntity != null) {
          LogUtil.v("DriverRegionAnnouncementModel::regionAnnouncement - Tron: ${regionEntity!.tron}");
          if (regionEntity!.tron != null) {
            LogUtil.v("DriverRegionAnnouncementModel::regionAnnouncement - Tron Options: ${regionEntity!.tron!.options}");
            if (regionEntity!.tron!.options != null) {
              LogUtil.v("DriverRegionAnnouncementModel::regionAnnouncement - Reject Reasons: ${regionEntity!.tron!.options!.rejectReasons}");
            }
          }
        }
        notifyListeners();
      }
    });
  }
}
