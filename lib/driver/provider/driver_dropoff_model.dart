import 'package:flutter/cupertino.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class DriverDroppoffModel extends ChangeNotifier {
  String explanation = "";
  List<AssetEntity> imagesEntity =[];

  setExplanation(String desc) {
    this.explanation = desc;
    notifyListeners();
  }

  setAssetEntity(List<AssetEntity> images) {
    this.imagesEntity = images;
    notifyListeners();
  }
}
