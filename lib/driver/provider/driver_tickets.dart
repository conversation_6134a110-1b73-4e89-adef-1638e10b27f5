import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

class DriverTickets extends ChangeNotifier {
  Future<Response?> postTickets(String subject, List<String> images,
      {String type = "driver-feedback"}) async {
    var data = Map<String, dynamic>();

    data["images"] = images;

    data["subject"] = subject;
    data["type"] = type;
    return await HttpManager.instance!
        .post(HttpUri.DRIVER_TICKETS, params: data, withLoading: true)
        .catchError((e) {});
  }
}
