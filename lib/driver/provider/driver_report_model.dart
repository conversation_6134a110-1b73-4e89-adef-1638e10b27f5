import 'package:connect/driver/bean/driver_report_adjust_entity.dart';
import 'package:connect/driver/bean/driver_report_entity.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:sprintf/sprintf.dart';

class DriverReportModel extends ChangeNotifier {
  List<DriverReportEntity>? driverReportEntity;
  List<DriverReportAdjustEntity>? driverReportAdjustEntity;

  static List<String> listParams = [
    "passcode",
    "passcodeExt",
    "status",
    "createdAt",
    "updatedAt",
    "confirmedAt",
    "adjustments",
    "restaurant._id",
    "restaurant.name",
    "region._id",
    "bundle",
    "restRating",
    "doubt",
    "rating"
  ];

  reset() {
    driverReportEntity = null;
    driverReportAdjustEntity = null;
  }

  Future<Response?> driverReport(String from, String to,
      {bool loading = false}) async {
    var regionId = ConnectCache.getCurrentRole()!.scope;
    var userId = ConnectCache.getUser()!.userId;
    String? url = sprintf(HttpUri.DRIVER_REPORT, [regionId, userId]);
    var data = Map<String, dynamic>();
    data["from"] = from;
    data["to"] = to;
    data["groupBy"] = "batch";
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);
    return await (HttpManager.instance!
        .get(url, params: data, option: requestOptions, withLoading: loading)
        .then((value) {
      if (value != null) {
        driverReportEntity =
            JsonConvert.fromJsonAsT<List<DriverReportEntity>>(value.data);
      }
      notifyListeners();
    }));
  }

  Future<Response?> driverReportAdjust(String from, String to,
      {int page = 1, bool loading = false}) async {
    var regionId = ConnectCache.getCurrentRole()!.scope;
    var userId = ConnectCache.getUser()!.userId;
    String? url = sprintf(HttpUri.DRIVER_REPORT_ADJUST, [regionId, userId]);
    var data = Map<String, dynamic>();
    data["from"] = from;
    data["to"] = to;
    data["page"] = page.toString();
    data["f"] = listParams;
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);
    return await (HttpManager.instance!
        .getUri(url, params: data, option: requestOptions, withLoading: loading)
        .then((value) {
      if (value != null) {
        List<DriverReportAdjustEntity> responseList =
            JsonConvert.fromJsonAsT<List<DriverReportAdjustEntity>>(value.data) ?? [];
        if (page > 1) {
          driverReportAdjustEntity!.addAll(responseList);
        } else {
          driverReportAdjustEntity = responseList;
        }
        notifyListeners();
      }
    }));
  }
}
