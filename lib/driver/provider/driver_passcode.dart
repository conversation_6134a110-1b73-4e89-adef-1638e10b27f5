import 'package:flutter/cupertino.dart';

class DriverPassCode extends ChangeNotifier {
  String lastPassCode = "";
  String? fistPassCode = "";
  String? passCode = "";

  setLastPassCode(String code, {bool notify = true}) {
    this.lastPassCode = code;
    if (notify) notifyListeners();
  }

  setFirstPassCode(String? code) {
    this.fistPassCode = code;
  }

  setPassCode(String? code) {
    this.passCode = code;
  }
}
