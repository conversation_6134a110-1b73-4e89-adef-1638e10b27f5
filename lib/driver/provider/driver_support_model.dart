import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

class DriverSupportModel extends ChangeNotifier {

  Future<String?> chatToken(ValueChanged ? error) async {
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "driver";
    Options requestOptions = new Options(extra: opExtras);

    var response = await HttpManager.instance!.post(
        HttpUri.DRIVER_CHAT,
        option: requestOptions,
        withLoading: false,
        callback: error);

    if (response != null) {
      return response.data['token'];
    }
    return null;
  }
}

