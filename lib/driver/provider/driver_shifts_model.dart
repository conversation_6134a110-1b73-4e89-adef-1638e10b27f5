import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/bean/driver_shifts_entity.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:sprintf/sprintf.dart';

class DriverShiftsModel extends ChangeNotifier {
  List<DriverShiftsEntity> items = [];
  List<DriverShiftsEntity> myItems = [];
  List<DriverShiftsEntity> availableItems = [];

  reset(){
    items = [];
    myItems = [];
    availableItems = [];
  }

  Future<Response?> driverShifts(String from, String to,
      {int page = 1, bool loading = true}) async {
    var regionId = ConnectCache.getCurrentRole()!.scope;
    String? url = sprintf(HttpUri.DRIVER_SHIFTS, [regionId]);
    var data = Map<String, dynamic>();
    // data["start"] = "2021-05-17T00:00:00+08:00";
    // data["end"] = "2021-05-30T23:59:59+08:00";
    data["start"] = from;
    data["end"] = to;
    return await (HttpManager.instance!
        .getUri(url, params: data, withLoading: loading)
        .then((value) {
      if (value != null) {
        items = JsonConvert.fromJsonAsT<List<DriverShiftsEntity>>(value.data) ?? [];
        availableItems.clear();
        myItems.clear();
        items.forEach((element) {
          if (element.drivers!.isEmpty) {
            element.isMyShift = false;
            availableItems.add(element);
          } else {
            var myShifts = element.drivers!.where((driver) => driver.sId == ConnectCache.getUser()!.userId).toList();
            // LogUtil.v("myShifts::${myShifts.length}");
            if (myShifts.length > 0) {
              element.isMyShift = true;
              myItems.add(element);
            }else{
              element.isMyShift = false;
              availableItems.add(element);
            }
            // element.drivers.forEach((driver) {
            //   LogUtil.v(
            //       "shift_id::${driver.sId},${ConnectCache.getUser().userId}");
            //   if (driver.sId == ConnectCache.getUser().userId) {
            //     element.isMyShift = true;
            //     myItems.add(element);
            //   } else {
            //     element.isMyShift = false;
            //     availableItems.add(element);
            //   }
            // });
          }
        });
        notifyListeners();
      }
    }));
  }

  Future<Response?> assign(String? shiftId, String shiftsTime,{ValueChanged? error}) async {
    var userId = ConnectCache.getUser()!.userId;
    var regionId = ConnectCache.getCurrentRole()!.scope;
    String? url = sprintf(HttpUri.DRIVER_ASSIGN, [regionId, shiftId, userId]);
    return await (HttpManager.instance!
        .post(url, callback: error, withLoading: true)
        .then((value) {
      if (value != null) {
        ToastUtils.show(
            sprintf(S.of(GlobalConfig.navigatorKey.currentContext!).driver_assign_shifts, [shiftsTime]),
            toast: Toast.LENGTH_LONG);

        TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_ASSIGN_SUCCESS);

        // MessageDialog.messageAlert("已选择周一$shiftsTime班次");
        GlobalConfig.eventBus.fire(DriverShiftsAssign());
      }
    }));
  }

  Future<Response?> unassign(String? shiftId, String shiftsTime) async {
    var userId = ConnectCache.getUser()!.userId;
    var regionId = ConnectCache.getCurrentRole()!.scope;
    String? url = sprintf(HttpUri.DRIVER_ASSIGN, [regionId, shiftId, userId]);
    return await (HttpManager.instance!
        .delete(url, withLoading: true)
        .then((value) {
      if (value != null) {
        ToastUtils.show(
            sprintf(S.of(GlobalConfig.navigatorKey.currentContext!).driver_unassign_shifts, [shiftsTime]), toast: Toast.LENGTH_LONG);
        GlobalConfig.eventBus.fire(DriverShiftsAssign());
      }
    }));
  }

  Future<Response?> unAssignWithSwap(String? shiftId, String email) async {
    var userId = ConnectCache.getUser()!.userId;
    var regionId = ConnectCache.getCurrentRole()!.scope;
    String? url = sprintf(HttpUri.DRIVER_ASSIGN_SWAP, [regionId, shiftId, userId]);
    var data = Map<String, dynamic>();
    if (email == null || email == "") {
      data["swap"] = true;
    } else {
      data["email"] = email;
    }
    return await (HttpManager.instance!
        .post(url, params: data, withLoading: true)
        .then((value) {
      if (value != null) {
        ToastUtils.show(S.of(GlobalConfig.navigatorKey.currentContext!).driver_swap_successful);
        GlobalConfig.eventBus.fire(DriverShiftsAssign());
      }
    }));
  }

  Future<void> requestSwap(String? shiftId, String? requestSwapUserId, int index) async {
    var userId = ConnectCache.getUser()!.userId;
    LogUtil.v("userId::$userId");
    var regionId = ConnectCache.getCurrentRole()!.scope;
    String? url = sprintf(HttpUri.DRIVER_ASSIGN_SWAP_EMAIL, [regionId, shiftId, userId, requestSwapUserId]);
    var data = Map<String, dynamic>();
    if (index == 0) {
      data["status"] = "confirm";
    } else if (index == 1) {
      data["status"] = "reject";
    }
    var response = await HttpManager.instance!.post(url, params: data, withLoading: true);
    if (response != null) {
      GlobalConfig.eventBus.fire(DriverShiftsAssign());
    }
  }
}
