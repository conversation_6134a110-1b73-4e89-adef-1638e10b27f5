import 'package:connect/driver/bean/driver_assigns_entity.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:flutter/cupertino.dart';
import 'package:sprintf/sprintf.dart';

class DriverAssignsModel extends ChangeNotifier {
  static List<String> listParams = [
    "passcode",
    "passcodeExt",
    "confirmedAt",
    "status",
    "restaurant._id",
    "restaurant.name",
    "restaurant.phone",
    "bundle.combineId",
    "customer.phone",
    "delivery",
  ];

  List<DriverAssignsEntity>? responseList = [];

  Future<void> driverAssigns() async {
    var regionId = ConnectCache.getCurrentRole()!.scope;
    var userId = ConnectCache.getUser()!.userId;
    String? url = sprintf(HttpUri.DRIVER_ALL_ASSIGNS, [regionId, userId]);
    var data = Map<String, dynamic>();
    data["f"] = listParams;

    var res = await HttpManager.instance!.getUri(url, params: data, withLoading: false);

    if(res == null) return;

    responseList = JsonConvert.fromJsonAsT<List<DriverAssignsEntity>>(res.data);
    notifyListeners();

  }
}
