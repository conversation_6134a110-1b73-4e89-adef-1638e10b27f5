import 'package:connect/driver/provider/driver_progress_model.dart';
import 'package:connect/driver/provider/driver_support_model.dart';
import 'package:connect/driver/support/driver_progress_indicator.dart';
import 'package:connect/driver/support/reload_button.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:async';
import 'dart:io';
import 'package:webview_flutter/webview_flutter.dart';
// Import for Android features.
import 'package:webview_flutter_android/webview_flutter_android.dart';
// Import for iOS features.
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
import 'package:provider/provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../ui/view/message_dialog.dart';

class DriverSupportRoute extends StatefulWidget {
  DriverSupportRoute({Key? key}) : super(key: key);

  @override
  _DriverSupportRouteState createState() => _DriverSupportRouteState();
}

class _DriverSupportRouteState extends State<DriverSupportRoute>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  static const String baseUrl = "https://chat.rice.rocks/?";
  double progress = 0;
  String? chatUrl;
  bool isRequestTokenSuccess = true;
  late Widget content;

  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();
    // if (Platform.isAndroid) WebView.platform = SurfaceAndroidWebView();

    WidgetsBinding.instance!.addPostFrameCallback((_) {
      _fetchToken();
    });

    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller =
    WebViewController.fromPlatformCreationParams(params);

    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
        ..setOnShowFileSelector(_androidFilePicker)
        ..setMediaPlaybackRequiresUserGesture(false);
    }

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
            context.read<DriverProgressModel>().setProgress(progress / 100.0);
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      );

    _controller = controller;

  }

  Future<List<String>> _androidFilePicker(FileSelectorParams params) async {
    try {
      ///check permission
      await AssetPicker.permissionCheck(requestOption:
      PermissionRequestOption(androidPermission: AndroidPermission(
        type: RequestType.image,
        mediaLocation: false,
      ),));
    } catch (e) {
      MessageDialog.messageAlert(S.of(context).driver_permission_photo);
      return [];
    }

    List<AssetEntity>? assets = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        maxAssets: 1,
        themeColor: Theme.of(context).primaryColor,
        requestType: RequestType.image,
        previewThumbnailSize: ThumbnailSize.square(200),
        gridThumbnailSize: ThumbnailSize.square(100),
      ),
    );

    if (assets == null || assets.length <= 0) return [];
    List<String> items = [];

    for (var element in assets) {
      File? file = await element.file;
      if (file != null) {
        items.add(file.uri.toString());
      }
    }

    return items;

  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (chatUrl == null && isRequestTokenSuccess) {
      content = _empty();
    } else if (chatUrl == null && !isRequestTokenSuccess) {
      content = _reload();
    } else if (chatUrl != null && isRequestTokenSuccess) {
      _controller.loadRequest(Uri.parse(chatUrl!));
      content = _webView();
    }


    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          S.of(context).title_support,
          style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_17),
        ),
      ),
      backgroundColor: Colors.grey[200],
      body: content,
    );
  }

  Widget _empty() {
    return EmptyView(S.of(context).connect_chat_loading);
  }

  Widget _reload() {
    return ReloadButton(() {
      setState(() {
        isRequestTokenSuccess = true;
      });
      _fetchToken();
    });
  }

  Widget _webView() {
    return Column(
      children: [
        DriverProgressIndicator(),
        Expanded(
          child: WebViewWidget(controller: _controller,),
        )
      ],
    );
  }

  Future<void> _fetchToken() async {
    var token = await context.read<DriverSupportModel>().chatToken((e) {
      setState(() {
        isRequestTokenSuccess = false;
      });
    });

    if (token != null) {
      setState(() {
        isRequestTokenSuccess = true;

        chatUrl = "$baseUrl" +
            "token=$token" +
            "&channelId=${ConnectCache.getUser()?.phone}" +
            "&channelName=${ConnectCache.getUser()?.email}" +
            "&sourceType=driver" +
            "&sourceId=${ConnectCache.getUser()?.userId}";
      });
    }
  }
}
