import 'package:connect/driver/bean/driver_report_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sprintf/sprintf.dart';

import '../../common/MyStyles.dart';
import '../../ui/view/gif_header.dart';
import '../../ui/view/loading.dart';
import '../provider/driver_report_model.dart';

class DriverReportSummary extends StatefulWidget {
  DriverReportSummary(this.from, this.to, {Key? key}) : super(key: key);

  final String from;
  final String to;

  @override
  _DriverReportSummaryState createState() => _DriverReportSummaryState();
}

class _DriverReportSummaryState extends State<DriverReportSummary>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  late RefreshController _refreshController;

  List<Widget> _summaryWidget = [];
  List<Widget> _commissionWidget = [];
  Map<String, num> _commissionMap = {};
  Map<String, num?> _summaryMap = {};

  //customer payment
  num orderCount = 0;
  num orderBundleCount = 0;
  num tip = 0;

  //commission
  num commission = 0;
  num adjustments = 0;

  //good and bad
  num drivingGood = 0;
  num drivingBad = 0;

  //driving time
  num drivingTime = 0;
  num pickupDrivingTime = 0;
  num dropOffDrivingTime = 0;

  //driving distance
  num drivingDistance = 0;
  num pickupDrivingDistance = 0;
  num dropOffDrivingDistance = 0;

  //total benefit
  num totalBenefit = 0;

  String country = "";

  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: true);
  }

  void _onRefresh({bool isLoading = false}) async {
    if (isLoading) LoadingUtils.show();
    var reportModel = context.read<DriverReportModel>();
    await reportModel.driverReport(widget.from, widget.to);
    if (isLoading) LoadingUtils.dismiss();
    if (_refreshController.isRefresh) {
      _refreshController.refreshCompleted();
    }
  }

  _summaryMapMethod() {
    _summaryMap.clear();
    _summaryWidget.clear();
    _summaryMap = {
      S.of(context).summary_order_count: orderCount,
      S.of(context).summary_order_bundle_count: orderBundleCount,
      S.of(context).summary_tip: tip / 100,
    };

    _summaryMap.forEach((key, value) {
      _summaryWidget.add(_buildItem(key, value));
    });

    _commissionMap.clear();
    _commissionWidget.clear();
    _commissionMap = {
      S.of(context).summary_commission: commission / 100,
      S.of(context).summary_adjustments: adjustments / 100
    };

    _commissionMap.forEach((key, value) {
      _commissionWidget.add(_buildCommissionItem(key, value));
    });
  }

  _summaryValueReset() {
    orderCount = 0;
    orderBundleCount = 0;
    tip = 0;

    commission = 0;
    adjustments = 0;

    //good and bad
    drivingGood = 0;
    drivingBad = 0;

    //driving time
    drivingTime = 0;
    pickupDrivingTime = 0;
    dropOffDrivingTime = 0;

    //driving distance
    drivingDistance = 0;
    pickupDrivingDistance = 0;
    dropOffDrivingDistance = 0;

    totalBenefit = 0;
  }

  _summaryValueMethod(List<DriverReportEntity>? driverReportEntity) {
    List<DriverReportEntity>? driverReportList = driverReportEntity;
    _summaryValueReset();
    driverReportList?.forEach((element) {
      orderCount += element.singleNumber ?? 0;

      orderBundleCount += element.bundleNumber ?? 0;

      tip += element.fees?.tip?.amount ?? 0;

      commission += element.commission?.driver ?? 0;

      adjustments += element.adjustments?.driver ?? 0;

      //rating
      drivingGood += element.rating?.good ?? 0;
      drivingBad += element.rating?.bad ?? 0;

      //driving time
      drivingTime += element.driving?.time?.total ?? 0;

      pickupDrivingTime += element.driving?.time?.pickup ?? 0;

      dropOffDrivingTime += element.driving?.time?.dropoff ?? 0;

      //driving distance
      drivingDistance += element.driving?.distance?.total ?? 0;

      pickupDrivingDistance += element.driving?.distance?.pickup ?? 0;

      dropOffDrivingDistance += element.driving?.distance?.dropoff ?? 0;

      totalBenefit += element.distribution?.driver ?? 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var model = context.watch<DriverReportModel>();
    _summaryValueMethod(model.driverReportEntity);
    _summaryMapMethod();

    return SmartRefresher(
      controller: _refreshController,
      physics: AlwaysScrollableScrollPhysics(),
      header: GifHeader(),
      onRefresh: _onRefresh,
      child: ListView(children: [
        _buildGroup(
          Column(
            children: _summaryWidget,
          ),
        ),
        _buildGroup(
          Column(
            children: _commissionWidget,
          ),
        ),
        _buildGroup(
          _buildGoodAndBad(),
        ),
        _buildGroup(
          _buildDrivingTime(),
        ),
        _buildGroup(
          _buildRevenue(),
        ),
      ]),
    );
  }

  Widget _buildGroup(Widget content) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      margin: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: content,
      ),
    );
  }

  Widget _buildItem(String name, num? value) {
    return Column(
      children: [
        _buildRow(name, value),
        SizedBox(
          height: Dimens.dp_8,
        ),
      ],
    );
  }

  Widget _buildCommissionItem(String name, num value) {
    return Column(
      children: [
        _buildRow(name, value),
        SizedBox(
          height: Dimens.dp_8,
        ),
      ],
    );
  }

  Widget _buildRow(String name, num? value) {
    String coinSymbolValue = ServerMultiLan.coinSymbolCountry(country, value);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          name,
          style: MyStyles.r13.copyWith(color: MyColors.color333),
        ),
        S.of(context).summary_order_count == name ||
                S.of(context).summary_order_bundle_count == name
            ? Text(
                "${value!.toInt()}",
                style: MyStyles.r13.copyWith(color: MyColors.color333),
              )
            : Text(
                value! >= 0 ? coinSymbolValue : "($coinSymbolValue)",
                style: MyStyles.r13.copyWith(color: MyColors.color333),
              ),
      ],
    );
  }

  Widget _buildGoodAndBad() {
    return Column(
      children: [
        Row(
          children: [
            Text(
              S.of(context).summary_rating,
              style: MyStyles.r13.copyWith(color: MyColors.color333),
            ),
            Expanded(
                child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Text(
                      S.of(context).summary_good,
                      style: MyStyles.r13.copyWith(color: MyColors.color333),
                    ),
                    SizedBox(
                      width: 3,
                    ),
                    Text(
                      "$drivingGood",
                      style: MyStyles.r13.copyWith(color: MyColors.color333),
                    ),
                  ],
                ),
                SizedBox(
                  width: 8,
                ),
                Row(
                  children: [
                    Text(
                      S.of(context).summary_bad,
                      style: MyStyles.r13.copyWith(color: MyColors.color333),
                    ),
                    SizedBox(
                      width: 3,
                    ),
                    Text(
                      "$drivingBad",
                      style: MyStyles.r13.copyWith(color: MyColors.color333),
                    ),
                  ],
                ),
              ],
            )),
          ],
        ),
      ],
    );
  }

  // num propGuarantee = 0;
  Widget _buildDrivingTime() {
    // propGuarantee = 18 * (drivingTime / 60) + 0.3 * drivingDistance;
    // String propGuaranteeStr = ServerMultiLan.coinSymbolCountry(country, propGuarantee);
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.of(context).summary_driving_time,
              style: MyStyles.r13.copyWith(color: MyColors.color333),
            ),
            Text(
              "${_printDuration(drivingTime)}",
              style: MyStyles.r13.copyWith(color: MyColors.color333),
            ),
          ],
        ),
        SizedBox(
          height: 8,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.of(context).summary_driving_distance,
              style: MyStyles.r13.copyWith(color: MyColors.color333),
            ),
            Text(
              "${sprintf(S.of(context).summary_driving_distance_cell, [
                    "$drivingDistance"
                  ])}",
              style: MyStyles.r13.copyWith(color: MyColors.color333),
            ),
          ],
        ),
      ],
    );
  }

  String _printDuration(num time) {
    Duration duration = Duration(minutes: time as int);
    String twoDigits(int n) => n.toString().padLeft(1, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    if (time >= 60) {
      return "${twoDigits(duration.inHours)} hr $twoDigitMinutes min";
    } else {
      return "$time min";
    }
  }

  // num benefit = 0;
  Widget _buildRevenue() {
    String benefitValue =
        ServerMultiLan.coinSymbolCountry(country, totalBenefit / 100);
    // String propGuaranteeValue = ServerMultiLan.coinSymbolCountry(country, propGuarantee);
    // benefit = totalBenefit / 100;
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.of(context).summary_revenue,
              style: MyStyles.r13.copyWith(color: MyColors.color333),
            ),
            Text(
              totalBenefit >= 0 ? benefitValue : "($benefitValue)",
              style: MyStyles.r13.copyWith(color: MyColors.color333),
            ),
          ],
        ),
      ],
    );
  }
}
