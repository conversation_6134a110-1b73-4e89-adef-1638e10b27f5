import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/constants.dart';
import 'package:connect/driver/widget/driver_one_dialog.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/format_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tz_date.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';

typedef Date<T, S> = Function(String from, String to);

class DriverReportDate extends StatefulWidget {
  DriverReportDate(this.from, this.to, this.selectedDate, {Key? key})
      : super(key: key);

  final DateTime from;
  final DateTime to;
  final Date selectedDate;

  @override
  _DriverReportDateState createState() => _DriverReportDateState();
}

class _DriverReportDateState extends State<DriverReportDate> {
  String? dateTextFromFormat = "";
  String? dateTextToFormat = "";
  String dateTextFrom = "";
  String dateTextTo = "";

  late DateTime fromDate;
  late DateTime toDate;
  var detroit;
  var from = "";
  var to = "";
  bool isFromClick = false;
  bool isToClick = false;

  _makeDateText(BuildContext context) {
    dateTextFromFormat = dateTextFrom;
    dateTextToFormat = dateTextTo;
  }

  @override
  void initState() {
    super.initState();
    fromDate = widget.from;
    toDate = widget.to;
    dateTextFromFormat = "";
    dateTextToFormat = "";
    dateTextFrom = "";
    dateTextTo = "";
    detroit = null;
    var dateNow = DateTime.now();
    from = formatDate(TzDate.getOldDate(fromDate, 0),
            [yyyy, '-', mm, '-', dd, 'T', '00', ':', '00', ':', '00']) +
        FormatUtils.format(dateNow);
    to = formatDate(TzDate.getOldDate(toDate, 0),
            [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
        FormatUtils.format(dateNow);
    isFromClick = false;
    isToClick = false;
  }

  @override
  Widget build(BuildContext context) {
    var dateNow = DateTime.now();
    if (!isFromClick) {
      dateTextFrom = DateUtil.formatDate(TzDate.getOldDate(fromDate, 0),
          format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
    }
    if (!isToClick) {
      dateTextTo = DateUtil.formatDate(TzDate.getOldDate(toDate, 0),
          format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
    }
    _makeDateText(context);
    return Row(
      children: [
        Chip(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          backgroundColor: MyColors.bg,
          label: _fromDate(),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: SizedBox(
            width: 6,
            height: 1,
            child: DecoratedBox(
              decoration: BoxDecoration(color: MyColors.color666),
            ),
          ),
        ),
        Chip(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          backgroundColor: MyColors.bg,
          label: _toDate(),
        ),
      ],
    );
  }

  Widget _fromDate() {
    return InkWell(
      onTap: () {
        var tzNow = DateTime.now();
        showDatePicker(
          context: context,
          initialDate: fromDate,
          firstDate: DateTime.parse("1970-01-01"),
          lastDate: tzNow,
        ).then((value) {
          if (value != null) {
            LogUtil.v("value:${value.toString()}");
            var inDays = DateTime.now().difference(value).inDays;
            LogUtil.v("inDays:$inDays");
            if (inDays >= 1) {
              fromDate = value;
              var fromCompareTo = value.difference(toDate).inDays;
              if (fromCompareTo > 0) {
                DriverOneDialog.show(context, "Invalid Date Range");
              } else {
                setState(() {
                  isFromClick = true;
                  dateTextFrom = DateUtil.formatDate(value,
                      format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
                });
                var formatTimeZone = FormatUtils.format(tzNow);
                LogUtil.v("timeZoneOffset:${tzNow.timeZoneOffset}");
                from = formatDate(value, [
                      yyyy,
                      '-',
                      mm,
                      '-',
                      dd,
                      'T',
                      '00',
                      ':',
                      '00',
                      ':',
                      '00'
                    ]) +
                    formatTimeZone;
                widget.selectedDate(from, to);
              }
            } else {
              DriverOneDialog.show(context,
                  "Report will be generated the day after the selected date");
            }
          }
        });
      },
      child: Row(
        children: [
          Text(
            dateTextFromFormat!,
            style: MyStyles.r12.copyWith(color: MyColors.color666),
          ),
          SizedBox(width: 4),
          Icon(
            Icons.arrow_drop_down,
            color: MyColors.color666,
          ),
        ],
      ),
    );
  }

  Widget _toDate() {
    return InkWell(
      onTap: () {
        var tzNow = DateTime.now();
        showDatePicker(
          context: context,
          initialDate: toDate,
          firstDate: DateTime.parse("1970-01-01"),
          lastDate: tzNow,
        ).then((value) {
          if (value != null) {
            var inDays = DateTime.now().difference(value).inDays;
            if (inDays >= 1) {
              toDate = value;
              var toCompareFrom = value.difference(fromDate).inDays;
              if (toCompareFrom >= 0) {
                setState(() {
                  isToClick = true;
                  dateTextTo = DateUtil.formatDate(value,
                      format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
                });
                var formatTimeZone = FormatUtils.format(tzNow);
                to = formatDate(value, [
                      yyyy,
                      '-',
                      mm,
                      '-',
                      dd,
                      'T',
                      '23',
                      ':',
                      '59',
                      ':',
                      '59'
                    ]) +
                    formatTimeZone;
                widget.selectedDate(from, to);
              } else {
                DriverOneDialog.show(context, "Invalid Date Range");
              }
            } else {
              DriverOneDialog.show(context,
                  "Report will be generated the day after the selected date");
            }
          }
        });
      },
      child: Row(
        children: [
          Text(
            dateTextToFormat!,
            style: MyStyles.r12.copyWith(color: MyColors.color666),
          ),
          SizedBox(width: 4),
          Icon(Icons.arrow_drop_down, color: MyColors.color666),
        ],
      ),
    );
  }
}
