import 'package:connect/common/t_constants.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'driver_report_date.dart';

typedef Date<T, S> = Function(String from, String to);

class DriverReportDateGroup extends StatefulWidget {
  DriverReportDateGroup(this.from, this.to, this.dateSelected, {Key? key})
      : super(key: key);

  final Date dateSelected;
  final DateTime from;
  final DateTime to;

  @override
  _DriverReportDateGroupState createState() => _DriverReportDateGroupState();
}

class _DriverReportDateGroupState extends State<DriverReportDateGroup> {
  @override
  Widget build(BuildContext context) {
    return DriverReportDate(widget.from, widget.to, (from, to) {
      LogUtil.v("DriverReportDate::from::$from,to::$to");
      //tracking
      var params = Map<String, dynamic>();
      params["from"] = "$from";
      params["to"] = "$to";
      TrackingUtils.instance!
          .tracking(TConstants.D_REPORT_DATE_OK_CLICK, value: params);
      widget.dateSelected(from, to);
    });
  }
}
