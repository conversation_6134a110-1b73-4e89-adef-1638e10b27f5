import 'package:connect/driver/bean/driver_report_adjust_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../ui/view/gif_header.dart';
import '../../ui/view/loading.dart';
import '../provider/driver_report_model.dart';
import 'driver_report_adjustments_item.dart';

class DriverReportAdjustment extends StatefulWidget {
  DriverReportAdjustment(this.from, this.to, {Key? key}) : super(key: key);

  final String from;
  final String to;

  @override
  _DriverReportAdjustmentState createState() => _DriverReportAdjustmentState();
}

class _DriverReportAdjustmentState extends State<DriverReportAdjustment>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  late RefreshController _refreshController;
  int page = 1;

  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: true);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var model = context.watch<DriverReportModel>();
    var items = model.driverReportAdjustEntity;

    var bundleList = items?.where((element) => element.bundle != null).toList();

    List<String?> responseListID = [];
    List<DriverReportAdjustEntity> responseList = [];

    items?.forEach((element) {
      if (element.bundle == null) {
        responseList.add(element);
      } else if (!responseListID.contains(element.bundle!.combineId)) {
        responseListID.add(element.bundle!.combineId);
        responseList.add(element);
      }
    });

    return SmartRefresher(
      controller: _refreshController,
      enablePullUp: true,
      physics: AlwaysScrollableScrollPhysics(),
      header: GifHeader(),
      onRefresh: _onRefresh,
      onLoading: _onLoading,
      footer: ClassicFooter(
        loadStyle: LoadStyle.ShowWhenLoading,
      ),
      child: ListView.builder(
        itemBuilder: (context, index) =>
            DriverReportAdjustmentsItem(responseList[index], bundleList),
        itemCount: responseList.length,
      ),
    );
  }

  void _onRefresh({bool isLoading = false}) async {
    page = 1;
    if (isLoading) LoadingUtils.show();
    var reportModel = context.read<DriverReportModel>();
    await reportModel.driverReportAdjust(widget.from, widget.to,
        loading: false);
    if (isLoading) LoadingUtils.dismiss();
    if (_refreshController.isRefresh) {
      _refreshController.refreshCompleted();
    }
    setState(() {});
  }

  void _onLoading() {
    page++;
    context
        .read<DriverReportModel>()
        .driverReportAdjust(widget.from, widget.to, page: page)
        .whenComplete(() => _refreshController.loadComplete());
  }
}
