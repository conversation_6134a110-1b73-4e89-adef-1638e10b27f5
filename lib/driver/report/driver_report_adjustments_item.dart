import 'package:connect/driver/bean/driver_report_adjust_entity.dart';
import 'package:connect/driver/widget/driver_star.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';

import '../../common/MyStyles.dart';

class DriverReportAdjustmentsItem extends StatefulWidget {
  DriverReportAdjustmentsItem(this.entity, this.bundleList, {Key? key})
      : super(key: key);

  final DriverReportAdjustEntity entity;
  final List<DriverReportAdjustEntity>? bundleList;

  @override
  _DriverReportAdjustmentsItemState createState() =>
      _DriverReportAdjustmentsItemState();
}

class _DriverReportAdjustmentsItemState
    extends State<DriverReportAdjustmentsItem> {
  var formatDate;

  @override
  void initState() {
    super.initState();
    formatDate = "";
  }

  @override
  Widget build(BuildContext context) {
    var entity = widget.entity;
    var bundleList = widget.bundleList;

    var createdAt = entity.createdAt;
    if (createdAt != null) {
      formatDate = DateUtil.formatDateStr(createdAt,
          isUtc: false, format: "MM/dd/yy HH:mm");
    }

    bool hasBundle = false;
    List<DriverReportAdjustEntity> combineList = [];
    if (entity.bundle != null) {
      hasBundle = true;
      var combineListTemp = bundleList
          ?.where((element) =>
              element.sId != entity.sId &&
              element.bundle!.combineId == entity.bundle!.combineId)
          .toList();
      if (combineListTemp != null) {
        combineList.addAll(combineListTemp);
      }
      combineList.insert(0, entity);
    }

    return hasBundle
        ? ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return _buildRow(combineList[index], true);
            },
            separatorBuilder: (context, index) {
              return EmptyContainer();
            },
            itemCount: combineList.length)
        : _buildRow(entity, false);
  }

  Widget _buildGroup(Widget content) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      margin: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: content,
      ),
    );
  }

  Widget _buildRow(DriverReportAdjustEntity item, bool isBundle) {
    var driver = item.adjustments?.driver ?? 0;
    var driverQ = driver / 100;
    String driverQStr = "";
    if (driverQ > 0) {
      driverQStr = "+\€$driverQ";
    } else {
      driverQStr = "-\€$driverQ";
    }

    var content = Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                isBundle
                    ? Icon(
                        Icons.merge_type_rounded,
                        color: MyColors.MY,
                      )
                    : EmptyContainer(),
                Text.rich(
                  TextSpan(
                    style: MyStyles.m15.copyWith(color: MyColors.color333),
                    children: [
                      TextSpan(
                        text: "#${item.passcode}  ",
                      ),
                      TextSpan(
                        text: ServerMultiLan.multiAdapt2(item.restaurant?.name),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Text(
                  "$formatDate",
                  style: MyStyles.r13.copyWith(color: MyColors.color666),
                ),
                (item.distribution != null && item.distribution!.driver != null)
                    ? Row(
                        children: [
                          SizedBox(width: 4),
                          Text(
                            ServerMultiLan.coinSymbolCountry(
                                "", item.distribution!.driver! / 100),
                            style:
                                MyStyles.r13.copyWith(color: MyColors.color666),
                          ),
                        ],
                      )
                    : EmptyContainer(),
              ],
            )
          ],
        ),
        driverQ == 0 ? EmptyContainer() : SizedBox(width: 4),
        driverQ == 0
            ? EmptyContainer()
            : Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Expanded(
                          child: Text(
                        "${item.adjustments?.reason ?? ""}",
                        textAlign: TextAlign.end,
                        style: MyStyles.r13.copyWith(color: MyColors.portal),
                      )),
                      SizedBox(
                        width: 4,
                      ),
                      Text(
                        "$driverQStr",
                        style: MyStyles.r13.copyWith(color: MyColors.portal),
                      ),
                    ],
                  )
                ],
              ),
        item.rating == null
            ? EmptyContainer()
            : DriverStar(item.rating!.stars, item.rating!.reasons),
      ],
    );
    return _buildGroup(content);
  }
}
