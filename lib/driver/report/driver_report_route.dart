import 'package:connect/ui/home/<USER>/daily_report.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:connect/generated/l10n.dart' as L10n;
import 'package:connect/utils/tz_date.dart';

import '../../common/MyStyles.dart';
import '../../common/constants.dart';
import '../../gen/assets.gen.dart';
import '../../ui/home/<USER>';
import '../../ui/view/message_dialog.dart';
import '../../utils/date_util.dart';
import '../../utils/format_utils.dart';
import '../driver_report_route.dart';

class DriverReportRouteV2Arguments {
  final DateTime from;
  final DateTime to;

  DriverReportRouteV2Arguments(this.from, this.to);
}

class DriverReportRouteV2 extends StatefulWidget {
  const DriverReportRouteV2({Key? key}) : super(key: key);

  @override
  _DriverReportRouteV2State createState() => _DriverReportRouteV2State();
}

class _DriverReportRouteV2State extends State<DriverReportRouteV2>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  var from;
  var to;
  var tzNow;

  @override
  void initState() {
    super.initState();

    () async {
      setState(() {
        var dateTime = TzDate.getOldDate(DateTime.now(), -1);
        from = dateTime;
        to = dateTime;
        tzNow = dateTime;
      });
    }();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: false,
        title: Text(
          L10n.S.of(context).title_report,
          style: TextStyle(
              color: Colors.black87, fontSize: 20, fontWeight: FontWeight.w500),
        ),
      ),
      backgroundColor: MyColors.W,
      body: Padding(
        padding: const EdgeInsets.fromLTRB(20, 32, 20, 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildFrom(context),
            SizedBox(height: 20),
            _buildTo(context),
            SizedBox(height: 20),
            Spacer(),
            Container(
              height: 40,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  backgroundColor: MyColors.portal,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(L10n.S.of(context).dailyReport_getReport,
                    style: MyStyles.m15.copyWith(color: MyColors.W)),
                onPressed: _getReport,
              ),
            ),
            SizedBox(
              height: 40,
            ),
          ],
        ),
      ),
    );
  }

  void _getReport() {
    if (this.from == null || this.to == null) {
      MessageDialog.messageAlert(
          L10n.S.of(context).dailyReport_invalidDateRange,
          ok: () {});
      return;
    }

    if (this.to.isBefore(this.from)) {
      MessageDialog.messageAlert(
          L10n.S.of(context).dailyReport_invalidDateRange,
          ok: () {});
      return;
    }

    final arguments = DriverReportRouteV2Arguments(from, to);
    Navigator.pushNamed(context, DriverReportRoute.tag, arguments: arguments);
  }

  Widget _buildFrom(BuildContext context) {
    var dateTextFrom = DateUtil.formatDate(from,
        format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
    return Container(
      padding: EdgeInsets.fromLTRB(16, 10, 16, 10),
      decoration: BoxDecoration(
        border: Border.all(
          color: MyColors.line,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Assets.images.report.calc.image(width: 16),
          SizedBox(width: 4),
          Text(L10n.S.of(context).dailyReport_from,
              style: MyStyles.r15.copyWith(color: MyColors.color333)),
          Spacer(),
          InkWell(
            onTap: () {
              showDatePicker(
                context: context,
                initialDate: from,
                firstDate: DateTime.parse("1970-01-01"),
                lastDate: tzNow,
              ).then((value) {
                if (value != null) {
                  setState(() {
                    this.from = value;
                  });
                }
              });
            },
            child: Row(
              children: [
                Text(dateTextFrom,
                    style: MyStyles.m13.copyWith(color: MyColors.portal)),
                Assets.images.report.down.image(width: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTo(BuildContext context) {
    var dateTextTo = DateUtil.formatDate(to,
        format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
    return Container(
      padding: EdgeInsets.fromLTRB(16, 10, 16, 10),
      decoration: BoxDecoration(
        border: Border.all(
          color: MyColors.line,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Assets.images.report.calc.image(width: 16),
          SizedBox(width: 4),
          Text(L10n.S.of(context).dailyReport_to,
              style: MyStyles.r15.copyWith(color: MyColors.color333)),
          Spacer(),
          InkWell(
            onTap: () {
              showDatePicker(
                context: context,
                initialDate: to,
                firstDate: DateTime.parse("1970-01-01"),
                lastDate: tzNow,
              ).then((value) {
                if (value != null) {
                  setState(() {
                    this.to = value;
                  });
                }
              });
            },
            child: Row(
              children: [
                Text(dateTextTo,
                    style: MyStyles.m13.copyWith(color: MyColors.portal)),
                Assets.images.report.down.image(width: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String restDateformate(DateTime dt, DateFormatType type, String? tz) {
    var timezone = tz ?? FormatUtils.format(dt);

    var result = "";
    switch (type) {
      case DateFormatType.startOfDay:
        result = formatDate(
                dt, [yyyy, '-', mm, '-', dd, 'T', '00', ':', '00', ':', '00']) +
            timezone;
        break;
      case DateFormatType.endOfDay:
        result = formatDate(
                dt, [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
            timezone;
        break;
      default:
    }

    return result;
  }
}
