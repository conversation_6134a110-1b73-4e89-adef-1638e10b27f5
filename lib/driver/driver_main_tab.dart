import 'dart:math';
import 'package:connect/driver/bean/driver_orders_entity.dart';
import 'package:connect/driver/provider/driver_passcode.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/object_util.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'driver_main.dart';
import 'package:provider/provider.dart';

class DriverMainTab extends StatefulWidget {
  DriverMainTab(this.entity, {Key? key}) : super(key: key);

  final DriverOrdersEntity? entity;

  @override
  _DriverMainTabState createState() => _DriverMainTabState();
}

class _DriverMainTabState extends State<DriverMainTab>
    with TickerProviderStateMixin {
  List<DriverOrdersNext?> sameDropoff = [];
  TabController? _tabController;
  int currentIndex = 0;

  _pickOrDropStops(bool showTab) {
    sameDropoff.clear();

    var entity = widget.entity!;

    //init passCode
    context.read<DriverPassCode>().setPassCode(entity.next?.order?.passcode);

    sameDropoff.add(entity.next);
    if (showTab) sameDropoff.addAll(entity.more!);

    LogUtil.v("sameDropoff.length::${sameDropoff.length}");

    sameDropoff.forEach((element) {
      LogUtil.v("DriverMainTab passcode::${element?.order?.passcode}");
    });
  }

  @override
  void dispose() {
    super.dispose();
    LogUtil.v("DriverMainTab::dispose");
    if (_tabController != null) _tabController!.dispose();
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("DriverMainTab::build");
    bool isMoreStop = false;
    bool isCompletePickupOrDropoffStop = false;
    bool isCompletePickupStop = false;

    var entity = widget.entity;
    //more array is not empty
    if (entity != null && entity.more != null && entity.more!.length > 0) {
      isMoreStop = true;
    }

    if (entity != null &&
        entity.next != null &&
        entity.next!.type == "pickup" &&
        (entity.next!.arrivedAt != null &&
            entity.next!.arrivedAt!.isNotEmpty)) {
      //complete pickup status
      isCompletePickupOrDropoffStop = true;
      isCompletePickupStop = true;
    } else if (entity != null &&
        entity.next != null &&
        entity.next!.type == "dropoff" &&
        (entity.next!.arrivedAt != null &&
            entity.next!.arrivedAt!.isNotEmpty)) {
      //complete dropoff status
      isCompletePickupOrDropoffStop = true;
    }

    LogUtil.v(
        "isMoreStop::$isMoreStop,isCompletePickupOrDropoffStop::$isCompletePickupOrDropoffStop");

    var showTab = isMoreStop && isCompletePickupOrDropoffStop;

    //tab list
    _pickOrDropStops(showTab);

    if (showTab) {
      //create tabController
      _tabController = TabController(
        length: sameDropoff.length,
        vsync: this,
      );

      _tabController!.addListener(() {
        if (_tabController!.index == _tabController!.animation!.value) {
          if (isCompletePickupStop) {
            context.read<DriverPassCode>().setPassCode(
                sameDropoff[_tabController!.index]?.order?.passcode);
          }
        }
      });
    }
    return showTab
        ? Column(
            children: [
              Container(
                padding: const EdgeInsets.only(top: Dimens.dp_15),
                child: Text(
                  isCompletePickupStop
                      ? S.of(context).driver_pickup_same_time
                      : S.of(context).driver_dropoff_same_time,
                  style: TextStyle(
                      color: Colors.grey,
                      fontSize: Dimens.sp_16,
                      fontWeight: FontWeight.bold),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: Dimens.dp_15),
                child: TabBar(
                  controller: _tabController,
                  labelColor: Colors.blue,
                  isScrollable: true,
                  indicatorColor: Colors.blue,
                  indicator: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    ),
                  ),
                  indicatorPadding:
                      const EdgeInsets.symmetric(horizontal: Dimens.dp_10),
                  unselectedLabelColor: Colors.grey,
                  unselectedLabelStyle: TextStyle(
                    fontSize: 15,
                    color: Colors.grey,
                    fontWeight: FontWeight.w700,
                  ),
                  labelStyle: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                  tabs: sameDropoff
                      .map((e) => Container(
                            child: tabTitle(isCompletePickupStop, e),
                            padding: const EdgeInsets.all(Dimens.dp_10),
                          ))
                      .toList(),
                ),
              ),
              Container(
                height: 600,
                child: TabBarView(
                  key: Key(Random().nextDouble().toString()),
                  controller: _tabController,
                  children: sameDropoff
                      .map((e) => generateTabContent(e, isMoreStop))
                      .toList(),
                ),
              )
            ],
          )
        : DriverMain(entity!.next, isMoreStop);
  }

  Widget generateTabContent(DriverOrdersNext? stop, bool isMoreStop) {
    return DriverMain(stop, isMoreStop);
  }

  Widget tabTitle(
    bool isCompletePickupStop,
    DriverOrdersNext? e,
  ) {
    //tab title
    String tabTitle = "";
    String tabSubTitle = "";
    String? street = "";
    String? number = "";
    if (isCompletePickupStop) {
      var restName = ServerMultiLan.multiAdapt2(e?.order?.restaurant?.name);
      tabTitle = "#${e?.order?.passcode!.substring(0, 2)}XX";
      if (restName.length > 10) {
        restName = "${restName.substring(0, 10)}...";
      }
      tabSubTitle = restName;
    } else {
      if (ObjectUtil.isNotEmpty(e?.address?.street)) {
        street = e?.address?.street;
        LogUtil.v("address::$street");
        if (street!.length > 15) {
          street = "${e?.address?.street!.substring(0, 15)}...";
        }
      }
      if (ObjectUtil.isNotEmpty(e?.address?.number)) {
        number = e?.address?.number;
      }
      tabTitle = "#${e?.order?.passcode}";
      tabSubTitle = "$number $street";
    }
    return Column(
      children: [
        Text(tabTitle, textAlign: TextAlign.center),
        SizedBox(
          height: 2,
        ),
        Text(
          tabSubTitle,
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: Dimens.sp_12),
        ),
      ],
    );
  }
}
