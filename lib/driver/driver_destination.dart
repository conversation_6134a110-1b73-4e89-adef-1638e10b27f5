import 'package:connect/common/MyStyles.dart';
import 'package:connect/driver/widget/driver_map.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/object_util.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import 'bean/destination_entity.dart';
import 'driver_destination_message.dart';

class DriverDestination extends StatefulWidget {
  DriverDestination(
      this.entity, this.isPickup, this.routeToPickUp, this.isMoreStop,
      {Key? key})
      : super(key: key);

  final DestinationEntity? entity;
  final bool isPickup;
  final bool routeToPickUp;
  final bool isMoreStop;

  @override
  _DriverDestinationState createState() => _DriverDestinationState();
}

class _DriverDestinationState extends State<DriverDestination> {
  @override
  Widget build(BuildContext context) {
    var entity = widget.entity!;
    var isPickup = widget.isPickup;
    String? secondaryLine = "";
    if (isPickup) {
      secondaryLine = entity.resName;
    } else {
      secondaryLine = ObjectUtil.isEmpty(entity.addressUnit)
          ? ""
          : "APT#:${entity.addressUnit}";
    }

    bool existNote = (entity.note != null && entity.note!.isNotEmpty);
    bool existPhone = false;
    String? phone = "";
    if (ObjectUtil.isNotEmpty(entity.phone)) {
      existPhone = true;
      phone = sprintf(S.of(context).driver_last_four_phone, [
        entity.phone!.substring(entity.phone!.length - 4, entity.phone!.length)
      ]);
    }

    LogUtil.v("phone::$phone,existPhone::$existPhone");

    return Column(
      children: [
        Row(
          children: [
            Text(
              S.of(context).driver_destination,
              style: MyStyles.m15.copyWith(
                color: MyColors.color333,
              ),
            ),
          ],
        ),
        SizedBox(
          height: 16,
        ),
        widget.routeToPickUp
            ? DriverDestinationMessage(entity)
            : EmptyContainer(),
        SizedBox(
          height: 6,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    entity.formatted ?? "",
                    style: MyStyles.m13.copyWith(color: MyColors.color333),
                  ),
                  SizedBox(
                    height: 6,
                  ),
                  ObjectUtil.isNotEmpty(secondaryLine)
                      ? Row(
                          children: [
                            Flexible(
                              child: Text(
                                secondaryLine!,
                                style: MyStyles.r13
                                    .copyWith(color: MyColors.color666),
                              ),
                            ),
                          ],
                        )
                      : EmptyContainer(),
                ],
              ),
            ),
            InkWell(
              child: Container(
                width: 36,
                height: 36,
                child: Icon(
                  Icons.near_me,
                  size: 20,
                  color: MyColors.portal,
                ),
              ),
              onTap: () {
                if (isPickup) {
                  DriverMap.show(context, widget.entity, "pickup");
                } else {
                  DriverMap.show(context, widget.entity, "dropoff");
                }
              },
            )
          ],
        ),
        SizedBox(
          height: 6,
        ),
        existNote
            ? Column(
                children: [
                  Row(
                    children: [
                      Text(
                        entity.note!,
                        style: TextStyle(color: MyColors.portal),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: Dimens.dp_3,
                  ),
                ],
              )
            : EmptyContainer(),
        existPhone
            ? Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          phone,
                          style: TextStyle(color: Colors.grey[700]),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: Dimens.dp_3,
                  ),
                ],
              )
            : EmptyContainer(),
        Row(
          children: [
            Text(
              "${entity.distance} mi · ",
              style: MyStyles.r13.copyWith(
                color: MyColors.color999,
              ),
            ),
            Text(
              "ETA ${entity.arriveAt}",
              style: MyStyles.r13.copyWith(
                color: MyColors.color999,
              ),
            ),
            isPickup
                ? Expanded(
                    child: Text(
                      " · ${sprintf(S.of(context).driver_food_ready, [
                            "${entity.est}"
                          ])}",
                      style: MyStyles.r13.copyWith(
                        color: MyColors.portal,
                      ),
                    ),
                  )
                : EmptyContainer(),
          ],
        ),
      ],
    );
  }
}
