import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/ui/pickup_complete/driver_pickup_complete_route.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/button_style_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/dialog_utils.dart';
import 'package:connect/utils/location_manager.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
import 'delivery/driver_complete_dropoff_sheet.dart';
import 'delivery/driver_completed_dropoff_upload_route.dart';

class DriverFlow extends StatefulWidget {
  DriverFlow(this.driverStatus, this.orderId, this.restId, this.isNext,
      this.passCode, this.bags,
      {Key? key})
      : super(key: key);

  final String driverStatus;
  final String? orderId;
  final String? restId;
  final bool isNext;
  final String? passCode;
  final int bags;

  @override
  _DriverFlowState createState() => _DriverFlowState();
}

class _DriverFlowState extends State<DriverFlow> {
  static const String TAG = "DriverFlow";

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: Dimens.dp_40,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: MyColors.portal,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
            widget.driverStatus == S.of(context).driver_order_delivery_complete
                ? "${widget.driverStatus} #${widget.passCode}"
                : widget.driverStatus,
            style: TextStyle(color: Colors.white, fontSize: Dimens.sp_16)),
        onPressed: () {
          if (widget.driverStatus == S.of(context).driver_order_at_pickup) {
            tracking("arrived_pickup");
            flowApi(S.of(context).driver_order_arrived_pickup_tip, "arrived",
                "arrived_pickup");
          } else if (widget.driverStatus ==
              S.of(context).driver_order_pickup_complete) {
            tracking("completed_pickup");
            Navigator.of(context)
                .pushNamed(DriverPickupCompleteRoute.tag, arguments: {
              "isNext": widget.isNext,
              "orderId": widget.orderId,
              "restId": widget.restId,
              "bags": widget.bags
            });
          } else if (widget.driverStatus ==
              S.of(context).driver_order_at_dropoff) {
            tracking("arrived_dropoff");
            flowApi(S.of(context).driver_order_arrived_dropoff_tip, "arrived",
                "arrived_dropoff");
          } else if (widget.driverStatus ==
              S.of(context).driver_order_delivery_complete) {
            tracking("completed_dropoff");
            flowApi(S.of(context).driver_completed_dropoff, "completed",
                "completed_dropoff");
          }
        },
      ),
    );
  }

  tracking(String stops) {
    var params = Map<String, dynamic>();
    params["flow"] = stops;
    TrackingUtils.instance!
        .tracking(TConstants.D_DELIVERY_STOPS_FLOW_CLICK, value: params);
  }

  flowApi(String content, String status, String stops) async {
    try {
      LogManager.instance!.log(TAG, "flowApi", stops);

      /// next driver delivery status
      if (content == S.of(context).driver_completed_dropoff) {
        //complete dropoff
        DriverCompleteDropoffSheet.show(context, (index) async {
          if (index == 0) {
            LogManager.instance!
                .log(TAG, "flowApi", S.of(context).driver_deliver_in_person);

            LoadingUtils.show();

            /// current position
            var position = await LocationManager.getCurrentPosition();

            if (position == null) {
              LoadingUtils.dismiss();
              return;
            }

            double? lat = position.latitude;
            double? log = position.longitude;

            //dev easy test
            if (!ConnectCache.isRelease()) {
              var entity = context.read<DriverModel>().driverOrderEntity;
              lat = entity?.next?.address?.location?.coordinates?[1];
              log = entity?.next?.address?.location?.coordinates?[0];
            }

            int time1 = DateTime.now().millisecondsSinceEpoch;

            if (lat == null || log == null) return;

            var arrivedResponse = await context
                .read<DriverModel>()
                .arrivedStatus(lat, log, status,
                    orderId: widget.isNext ? null : widget.orderId,
                    option: true);

            if (arrivedResponse == null) return;

            LogManager.instance!.log(TAG, "flowApi",
                "isNext::${widget.isNext},next::${arrivedResponse.next}");

            //tracking
            int time2 = DateTime.now().millisecondsSinceEpoch;
            TrackingManager.trackStopsTime(time1, time2, stops);

            if (widget.isNext && arrivedResponse.next == null) {
              LogManager.instance!.log(TAG, "flowApi", "createOrder");
              var createResponse =
                  await context.read<DriverModel>().createOrder();

              if (createResponse == null) return;
            }

            LoadingUtils.dismiss();
          } else if (index == 1) {
            Navigator.of(context)
                .pushNamed(DriverCompletedDropOffUploadRoute.tag, arguments: {
              "isNext": widget.isNext,
              "orderId": widget.orderId
            });
          }
        });
      } else {
        ///test code for easy of testing
        if (!ConnectCache.isRelease()) {
          LoadingUtils.show();

          var entity = context.read<DriverModel>().driverOrderEntity;

          double? lat = entity?.next?.address?.location?.coordinates?[1];
          double? log = entity?.next?.address?.location?.coordinates?[0];

          if (lat == null || log == null) return;

          await context
              .read<DriverModel>()
              .arrivedStatus(lat, log, status, option: true);

          LoadingUtils.dismiss();
        } else {
          DriverDialog.show(context, content, () {
            _arrivedLocation(status, stops);
          });
        }
      }
    } catch (e) {
      LoadingUtils.dismiss();
      DialogUtils.showAlert(context, Text(S.of(context).location_failed), null);

      LogManager.instance!.log(TAG, "flowApi", "exception::${e.toString()}");
    }
  }

  _arrivedLocation(String status, String stops) async {
    LogManager.instance!.log(TAG, "_arrivedLocation", "start");

    LoadingUtils.show();

    var position = await LocationManager.getCurrentPosition();

    if (position == null) {
      LoadingUtils.dismiss();
      return;
    }

    int time1 = DateTime.now().millisecondsSinceEpoch;
    await context.read<DriverModel>().arrivedStatus(
          position.latitude,
          position.longitude,
          status,
          option: true,
        );
    int time2 = DateTime.now().millisecondsSinceEpoch;
    TrackingManager.trackStopsTime(time1, time2, stops);

    LogManager.instance!.log(TAG, "_arrivedLocation", "end");

    LoadingUtils.dismiss();
  }
}
