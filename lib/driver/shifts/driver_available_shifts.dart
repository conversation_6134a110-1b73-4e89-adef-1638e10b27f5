import 'package:connect/driver/bean/driver_shifts_entity.dart';
import 'package:connect/driver/provider/driver_shifts_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/object_util.dart';
import 'package:flutter/material.dart';
import 'driver_shifts_new_item.dart';
import 'package:provider/provider.dart';

import 'driver_shifts_search.dart';

class DriverAvailableShifts extends StatefulWidget {
  DriverAvailableShifts({Key? key}) : super(key: key);

  @override
  _DriverAvailableShiftsState createState() => _DriverAvailableShiftsState();
}

class _DriverAvailableShiftsState extends State<DriverAvailableShifts> {
  bool showAll = true;
  List<DriverShiftsEntity> availableShifts = [];

  @override
  Widget build(BuildContext context) {
    List<DriverShiftsEntity> availableItems =
        context.watch<DriverShiftsModel>().availableItems;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        DriverShiftsSearch((value) {
          Future.delayed(Duration(milliseconds: 300)).then((_) {
            LogUtil.v("search value::$value");
            availableShifts.clear();
            if (ObjectUtil.isEmpty(value)) {
              setState(() {
                showAll = true;
              });
            } else {
              bool hasSearchResult = false;
              availableItems.forEach((element) {
                String? batch = element.batch;
                String? notes = element.notes;
                String? address = element.address;
                LogUtil.v("batch::$batch,notes::$notes,address::$address");
                if ((batch != null &&
                        (batch.toLowerCase().contains(value.toLowerCase()) ||
                            batch.toLowerCase() == value.toLowerCase())) ||
                    (notes != null &&
                        notes.toLowerCase().contains(value.toLowerCase())) ||
                    (address != null &&
                        address.toLowerCase().contains(value.toLowerCase()))) {
                  hasSearchResult = true;
                  setState(() {
                    showAll = false;
                    availableShifts.add(element);
                  });
                }
              });

              if(!hasSearchResult){
                setState(() {
                  showAll = false;
                  availableShifts.clear();
                });
              }
            }
          });
        }),
        Expanded(
            child: availableItems.isEmpty
                ? EmptyView(S.of(context).driver_no_shifts)
                : ListView.separated(
                    itemBuilder: (context, index) {
                      return showAll
                          ? DriverShiftsNewItem(availableItems[index])
                          : DriverShiftsNewItem(availableShifts[index]);
                    },
                    itemCount: showAll
                        ? availableItems.length
                        : availableShifts.length,
                    separatorBuilder: (context, index) {
                      return SizedBox(height: 10,);
                    })),
      ],
    );
  }
}
