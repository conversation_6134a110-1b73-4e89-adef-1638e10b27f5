import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_shifts_model.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class DriverShiftsAssignButton extends StatefulWidget {
  DriverShiftsAssignButton(this.shiftId,this.shiftsTime,{Key? key}) : super(key: key);

  final String? shiftId;
  final String shiftsTime;

  @override
  _DriverShiftsAssignButtonState createState() =>
      _DriverShiftsAssignButtonState();
}

class _DriverShiftsAssignButtonState extends State<DriverShiftsAssignButton> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 150,
      child: ElevatedButton(
        child: Text(S.of(context).driver_shifts_assign, style: TextStyle(color: Colors.white, fontSize: Dimens.sp_16)),
        style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(RColors.mr),
            shape: MaterialStateProperty.all(RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8)))),
        onPressed: (){
          FocusScope.of(context).requestFocus(FocusNode());
          DriverDialog.show(context, S.of(context).driver_assign_tip, () {

            TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_ASSIGN_CONFIRM_CLICK);
            context.read<DriverShiftsModel>().assign(widget.shiftId, widget.shiftsTime, error: (e) {
              //tracking
              var params = Map<String, dynamic>();
              params["fail_reason"] = "${e.toString()}";
              TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_ASSIGN_FAIL);
            });
          });
        },
      ),
    );
  }
}
