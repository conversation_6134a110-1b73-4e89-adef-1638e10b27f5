import 'package:auto_size_text/auto_size_text.dart';
import 'package:connect/driver/bean/destination_entity.dart';
import 'package:connect/driver/bean/driver_shifts_entity.dart';
import 'package:connect/driver/provider/driver_shifts_model.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/driver/widget/driver_map.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/date_util.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:sprintf/sprintf.dart';
import 'driver_shifts_assign_button.dart';
import 'driver_shifts_unassign_button.dart';
import 'package:provider/provider.dart';

import 'driver_swap_sheet.dart';

class DriverShiftsNewItem extends StatefulWidget {
  DriverShiftsNewItem(this.entity, {Key? key}) : super(key: key);

  final DriverShiftsEntity entity;

  @override
  _DriverShiftsNewItemState createState() => _DriverShiftsNewItemState();
}

class _DriverShiftsNewItemState extends State<DriverShiftsNewItem> {
  bool isAvailable = false;
  late String available;
  late DriverShiftsEntity entity;
  String shiftsTime = "";

  @override
  Widget build(BuildContext context) {
    entity = widget.entity;
    available = S.of(context).driver_shifts_available;
    if (entity.instances! > entity.drivers!.length) {
      isAvailable = true;
      available = S.of(context).driver_shifts_available;
    } else {
      isAvailable = false;
      available = S.of(context).driver_full;
    }

    var startVar = entity.start;
    var endVar = entity.end;
    if (startVar != null && endVar != null) {
      var startDateTime = DateUtil.getDateTime(startVar)!.toLocal();
      String start = DateFormat.jm().format(startDateTime);
      var endDateTime = DateUtil.getDateTime(endVar)!.toLocal();
      String end = DateFormat.jm().format(endDateTime);
      shiftsTime = "$start - $end";
    }

    return Card(
      color: Colors.white,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      margin: const EdgeInsets.only(left: 20, top: 20, right: 20),
      child: Padding(
          padding: const EdgeInsets.all(15),
          child: Column(
            children: [
              Row(
                children: [
                  _buildAvailableOrFull(),
                  _buildShiftTime(),
                ],
              ),
              SizedBox(height: 6),
              _buildSwap(),
              _buildNote(),
              _buildStartLocation(),
              _buildReward(),
              _buildAssign(),
            ],
          )),
    );
  }

  Widget _buildShiftTime() {
    var entity = widget.entity;
    String batch = "";
    if (entity.batch != null && entity.batch!.isNotEmpty) {
      batch = "[${entity.batch}]";
    }

    String shiftsTime = "";
    String hoursAndMinutes = "";

    var startVar = entity.start;
    var endVar = entity.end;
    if (startVar != null && endVar != null) {
      var startDateTime = DateUtil.getDateTime(startVar)!.toLocal();
      String start = DateFormat.jm().format(startDateTime);
      var endDateTime = DateUtil.getDateTime(endVar)!.toLocal();
      String end = DateFormat.jm().format(endDateTime);
      shiftsTime = "$start - $end";


      var inMinutes = endDateTime.difference(startDateTime).inMinutes;
      var inHours = endDateTime.difference(startDateTime).inHours;
      if (inHours > 0) {
        var i = inMinutes - inHours * 60;
        if (i > 0) {
          if (i < 10) {
            hoursAndMinutes =
            "${inHours.toString()}hr 0${inMinutes - inHours * 60}mins";
          } else {
            hoursAndMinutes =
            "${inHours.toString()}hr ${inMinutes - inHours * 60}mins";
          }
        } else {
          hoursAndMinutes = "${inHours.toString()}hr";
        }
      } else if (inMinutes > 0 && inMinutes < 60) {
        if (inMinutes < 10) {
          hoursAndMinutes = "0${inMinutes.toString()}mins";
        } else {
          hoursAndMinutes = "${inMinutes.toString()}mins";
        }
      } else {
        //==0
        hoursAndMinutes = "${inHours.toString()}hr";
      }
    }

    return Flexible(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: AutoSizeText(
          "$batch $shiftsTime ( $hoursAndMinutes )",
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
            fontSize: 17
          ),
          maxFontSize: 17,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          minFontSize: 10,
        ),
      ),
    );
  }


  Widget _buildAvailableOrFull(){
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: isAvailable ? RColors.green : RColors.yellow,
        borderRadius: BorderRadius.circular(20)
      ),
      child:Text(available,
        style: TextStyle(color: Colors.white)),
    );
  }

  Widget _buildSwap(){
    bool hasSwap = false;
    late DriverShiftsDriver driver;
    String? swapId;
    String swapStr = "";
    List<DriverShiftsDriver>? drivers = entity.drivers;
    if (drivers != null && drivers.length > 0) {
      //取sid==userId
      if(entity.isMyShift){
        drivers.forEach((element) {
          //找到当前司机的myshift排班，并确认当前司机有没有申请代班（包括all和代班司机）,swap是代班的人
          if (element.sId == ConnectCache.getUser()!.userId) {
            if (element.swap != null) {
              hasSwap = true;
              driver = element;
              swapId = element.swap!.sId;
              var localDate = DateUtil.getDateTime(driver.swap!.date!)!.toLocal();
              var formatDate = DateUtil.formatDate(localDate, format: "MM/dd HH:mm");
              String? sp;
              if (element.swap!.sId == "all") {
                sp = sprintf(S.of(context).driver_request_swap_with, ["All", "$formatDate"]);
              } else {
                sp = sprintf(S.of(context).driver_request_swap_with, ["${element.swap!.email}", "$formatDate"]);
              }
              swapStr = "${element.email!.split("@")[0]}$sp";
            }
          }
        });
      }else{
        //找到有没有人向我申请代班的
        drivers.forEach((element) {
          if (element.swap != null) {
            hasSwap = true;
            driver = element;
            swapId = element.swap!.sId;
          }
        });
      }
    }

    return Column(
      children: [
        Row(
          children: [
            hasSwap && !entity.isMyShift  //available swap
                ? Padding(
                    padding: const EdgeInsets.only(left: 10, right: 10, bottom: 5),
                    child: Row(
                      children: [
                        swapId == "all"
                            ? EmptyContainer()
                            : Text("${driver.email!.split("@")[0]}  "),
                        InkWell(
                          onTap: () {
                            if (swapId == "all") {
                              DriverDialog.show(
                                  context, S.of(context).driver_assign_tip, () {
                                // TrackingUtils.instance.tracking(TConstants.D_SHIFTS_ASSIGN_CONFIRM_CLICK);
                                context.read<DriverShiftsModel>().assign(entity.sId, shiftsTime, error: (e) {
                                  //tracking
                                  // var params = Map<String, dynamic>();
                                  // params["fail_reason"] = "${e.toString()}";
                                  // TrackingUtils.instance.tracking(TConstants.D_SHIFTS_ASSIGN_FAIL);
                                });
                              });
                            } else {
                              var start = entity.start;
                              var localDate = DateUtil.getDateTime(start!)!.toLocal();
                              var formatDate = DateUtil.formatDate(localDate, format: "MM/dd HH:mm");
                              DriverSwapSheet.show(context, driver.email, formatDate, (index) {
                                context.read<DriverShiftsModel>().requestSwap(entity.sId, driver.sId, index);
                              });
                            }
                          },
                          child: Text(
                            swapId == "all"
                                ? S.of(context).driver_swap_shifts
                                : S.of(context).driver_request_swap_shifts,
                            style: TextStyle(color: RColors.yellow),
                          ),
                        )
                      ],
                    ),
                  )
                : EmptyContainer()
          ],
        ),
        hasSwap && entity.isMyShift    //myShifts swap
            ? Padding(
                padding: const EdgeInsets.only(left: 10, right: 10, bottom: 5),
                child: Row(children: [
                  Expanded(
                      child: Text(swapStr, style: TextStyle(color: Colors.black87),
                  )),
                ]),
              )
            : EmptyContainer(),
      ],
    );
  }

  Widget _buildNote(){
    return entity.notes == null || entity.notes!.isEmpty
        ? EmptyContainer()
        : Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10, bottom: 5),
                  child: Row(
                    children: [Text(entity.notes!)],
                  ),
                ),
              ),
            ],
          );
  }

  Widget _buildReward() {
    var entity = widget.entity;
    var extraPay;
    var guarantee;
    if (entity.extraPay != null) {
      extraPay = entity.extraPay! / 100;
    }
    if (entity.guarantee != null) {
      guarantee = entity.guarantee! / 100;
    }

    bool showPay = extraPay != null || guarantee != null;

    return showPay
        ? Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            color: RColors.light_yellow,
            child: Column(
              children: [
                Row(
                  children: [
                    extraPay == null
                        ? EmptyContainer()
                        : Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Image(image: AssetImage(
                                  "assets/images/schedule/points.png"
                                ), width: 20, height: 20,),
                                SizedBox(
                                  width: 3,
                                ),
                                Text(
                                  S.of(context).driver_shifts_extra_pay,
                                ),
                                Text("\€$extraPay",
                                    style: TextStyle(
                                        color: RColors.yellow,
                                        fontWeight: FontWeight.bold)),
                              ],
                            ),
                          ),
                    Expanded(
                      child: guarantee == null
                          ? EmptyContainer()
                          : Row(
                              mainAxisAlignment: extraPay == null
                                  ? MainAxisAlignment.start
                                  : MainAxisAlignment.end,
                              children: [
                                Text(
                                  S.of(context).driver_shifts_guarantee_pay,
                                ),
                                Text("\€$guarantee",
                                    style: TextStyle(
                                        color: RColors.yellow,
                                        fontWeight: FontWeight.bold)),
                              ],
                            ),
                    ),
                  ],
                ),
              ],
            ),
          )
        : EmptyContainer();
  }

  Widget _buildStartLocation() {
    var entity = widget.entity;
    return entity.address == null
        ? EmptyContainer()
        : Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: InkWell(
              child: Row(
                children: [
                  Expanded(child: Text(S.of(context).driver_start_location)),
                  Text.rich(TextSpan(children: [
                    TextSpan(
                    text: entity.address,
                    style: TextStyle(color: RColors.mr)),
                    WidgetSpan(
                    child: Icon(
                  Icons.chevron_right,
                  size: Dimens.dp_16,
                  color: RColors.mr,
                    ))
                  ])),
                ],
              ),
              onTap: () {
                var destinationEntity = DestinationEntity();
                destinationEntity.formatted = entity.address;
                destinationEntity.from = "shifts";
                DriverMap.show(context, destinationEntity, "shifts");
              },
            ),
          );
  }

  Widget _buildAssign() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          entity.isMyShift
              ? DriverShiftsUnAssignButton(entity.sId, shiftsTime)
              : isAvailable
                  ? DriverShiftsAssignButton(entity.sId, shiftsTime)
                  : EmptyContainer(),
        ],
      ),
    );
  }
}
