import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:flutter/material.dart';
import 'driver_available_shifts.dart';
import 'driver_my_shifts.dart';

class DriverShiftsTab extends StatefulWidget {
  DriverShiftsTab({Key? key}) : super(key: key);

  @override
  _DriverShiftsTabState createState() => _DriverShiftsTabState();
}

class _DriverShiftsTabState extends State<DriverShiftsTab>
    with SingleTickerProviderStateMixin {
  List tabs = [];
  late TabController _controller;

  buildShiftsTab() {
    tabs = [
      S.of(context).driver_my_shifts,
      S.of(context).driver_available_shifts
    ];
  }

  @override
  void initState() {
    super.initState();
    _controller = TabController(
      initialIndex: 1,
      length: 2,
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    buildShiftsTab();
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        flexibleSpace: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TabBar(
              controller: _controller,
              labelColor: Colors.black,
              unselectedLabelColor: RColors.gray_66,
              indicatorColor: RColors.mr,
              tabs: tabs
                  .map((e) => Tab(
                        child: Text(e),
                      ))
                  .toList(),
            )
          ],
        ),
      ),
      body: TabBarView(
        controller: _controller,
        children: [
          DriverMyShifts(),
          DriverAvailableShifts(),
        ],
      ),
    );
  }
}
