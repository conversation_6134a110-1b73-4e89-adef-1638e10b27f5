import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/widget/pre_order/pre_date_picker.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/format_utils.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';

typedef Date<T, S> = Function(String from, String to);

class DriverShiftsDate extends StatefulWidget {
  DriverShiftsDate(this.selectedDate, {Key? key}) : super(key: key);

  final Date selectedDate;

  @override
  _DriverShiftsDateState createState() => _DriverShiftsDateState();
}

class _DriverShiftsDateState extends State<DriverShiftsDate> {
  @override
  Widget build(BuildContext context) {
    double width = (MediaQuery.of(context).size.width - 9) / 10;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        PreDatePicker(
          DateTime.now(),
          locale: ConnectCache.getLocale().toLanguageTag(),
          height: Dimens.dp_60,
          width: width,
          initialSelectedDate: DateTime.now(),
          selectionColor: RColors.mr,
          selectedTextColor: Colors.white,
          daysCount: 10,
          onDateChange: (date) {
            // New date selected
            var fromFormat = formatDate(date,
                [yyyy, '-', mm, '-', dd, 'T', '00', ':', '00', ':', '00']);
            var toFormat = formatDate(date,
                [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']);
            var formatTimeZone = FormatUtils.format(date);
            String from = fromFormat + formatTimeZone;
            String to = toFormat + formatTimeZone;
            widget.selectedDate(from, to);
          },
        ),
      ],
    );
  }
}
