import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';

class DriverSwapSheet {
  static show(BuildContext context, String? email, String date,
      ValueChanged<int> valueChanged) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context, email, date, valueChanged);
        },
        context: context);
  }


  static Widget buildBottomSheetWidget(BuildContext context, String? email,
      String date, ValueChanged<int> valueChanged) {
    return Container(
      height: Dimens.dp_240,
      child: Column(
        children: [
          buildItemTitle(context, sprintf(S.of(context).driver_request_swap_shifts_tip, [email, date])),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, S.of(context).confirm, onTap: () async {
            valueChanged(0);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, S.of(context).Reject, onTap: () async {
            valueChanged(1);
          }),
          Divider(
            thickness: Dimens.dp_10,
            color: Colors.grey[200],
          ),
          buildItem(context, S.of(context).cancel),
        ],
      ),
    );
  }

  static Widget buildItemTitle(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        await Future.delayed(Duration(milliseconds: 300));
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 15),
        height: Dimens.dp_70,
        child: Text(
          title,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        await Future.delayed(Duration(milliseconds: 300));
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_50,
        child: Text(
          title,
          style: TextStyle(color: Colors.blue, fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
