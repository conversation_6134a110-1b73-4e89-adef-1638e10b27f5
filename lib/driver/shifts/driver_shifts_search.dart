import 'package:connect/common/global.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';

class DriverShiftsSearch extends StatefulWidget {
  DriverShiftsSearch(this.callback, {Key? key}) : super(key: key);

  final ValueChanged<String> callback;

  @override
  DriverShiftsSearchState createState() => DriverShiftsSearchState();
}

class DriverShiftsSearchState extends State<DriverShiftsSearch> {
  TextEditingController _searchController = TextEditingController();

  clearText() {
    if (_searchController.text != null && _searchController.text.isNotEmpty) {
      setState(() {
        _searchController.text = "";
        widget.callback("");
      });
    }
  }

  @override
  void initState() {
    super.initState();
    GlobalConfig.eventBus.on<DriverShiftsSearchClearEvent>().listen((event) {
      clearText();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.only(right: Dimens.dp_20),
            decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8)),
            height: Dimens.dp_40,
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    onChanged: (v) {
                      setState(() {});
                    },
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.only(
                          left: Dimens.dp_20, right: Dimens.dp_20),
                      hintStyle: TextStyle(
                        color: RColors.gray_99,
                        fontSize: Dimens.sp_14,
                      ),
                      hintText: S.of(context).search_label,
                      border: OutlineInputBorder(
                        //for hintText show in center
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      disabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                    ),
                  ),
                  flex: 1,
                ),
                _searchController.text.isNotEmpty
                    ? InkWell(
                        child: Icon(Icons.cancel,
                            color: Colors.black54, size: Dimens.dp_20),
                        onTap: () {
                          setState(() {
                            _searchController.text = "";
                            widget.callback("");
                          });
                        },
                      )
                    : EmptyContainer(),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            setState(() {});
            widget.callback(_searchController.text);
          },
          child: Container(
            width: 72,
            height: 36,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.search,
              color: Colors.white,
            ),
            margin: EdgeInsets.only(right: 20),
          ),
        )
      ],
    );
  }
}
