import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_shifts_model.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/format_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'driver_shifts_date.dart';
import 'driver_shifts_tab.dart';

class DriverShiftsRoute extends StatefulWidget {
  DriverShiftsRoute({Key? key}) : super(key: key);

  @override
  _DriverShiftsRouteState createState() => _DriverShiftsRouteState();
}

class _DriverShiftsRouteState extends State<DriverShiftsRoute> with AutomaticKeepAliveClientMixin{
  @override
  bool get wantKeepAlive => true;

  String from = "";
  String to = "";

  @override
  void initState() {
    super.initState();
    context.read<DriverShiftsModel>().reset();
    from = formatDate(DateTime.now(),
            [yyyy, '-', mm, '-', dd, 'T', '00', ':', '00', ':', '00']) +
        FormatUtils.format(DateTime.now());
    to = formatDate(DateTime.now(),
            [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
        FormatUtils.format(DateTime.now());

    WidgetsBinding.instance!.addPostFrameCallback((_) {
      requestShifts();
    });

    GlobalConfig.eventBus.on<DriverShiftsAssign>().listen((event) {
      requestShifts();
    });
  }

  requestShifts(){
    context.read<DriverShiftsModel>().driverShifts(from, to);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: false,
        title: Text(
          S.of(context).driver_shifts,
          style: TextStyle(color: Colors.black87, fontSize: 20, fontWeight: FontWeight.bold),
        ),
      ),
      backgroundColor: Colors.grey[200],
      body: Column(
        children: [
          DriverShiftsDate((selectedFrom, selectedTo) {
            from = selectedFrom;
            to = selectedTo;
            GlobalConfig.eventBus.fire(DriverShiftsSearchClearEvent());
            requestShifts();

            var params = Map<String, dynamic>();
            params["from"] = "$selectedFrom";
            params["to"] = "$selectedTo";
            TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_DATE_CLICK, value: params);
          }),
          Expanded(
            flex: 1,
            child: DriverShiftsTab(),
          )
        ],
      ),
    );
  }
}
