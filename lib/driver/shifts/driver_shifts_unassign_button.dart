import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_shifts_model.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'driver_unassign_sheet.dart';
import 'driver_unassign_swap_dialog.dart';

class DriverShiftsUnAssignButton extends StatefulWidget {
  DriverShiftsUnAssignButton(this.shiftId, this.shiftsTime, {Key? key})
      : super(key: key);

  final String? shiftId;
  final String shiftsTime;

  @override
  _DriverShiftsUnAssignButtonState createState() =>
      _DriverShiftsUnAssignButtonState();
}

class _DriverShiftsUnAssignButtonState
    extends State<DriverShiftsUnAssignButton> {
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        child: Container(
            decoration: BoxDecoration(
                color: Colors.grey[100],
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.all(Radius.circular(8))),
            alignment: Alignment.center,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(S.of(context).driver_unassign,
                  style: TextStyle(color: Colors.grey, fontSize: Dimens.sp_16)),
            )),
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
          DriverUnAssignSheet.show(context, callback: (index) {
            if (index == 0) {
              //swap with driver
              var params = Map<String, dynamic>();
              params["item"] = "swap with driver";
              TrackingUtils.instance!.tracking(
                  TConstants.D_SHIFTS_UNASSING_SHEET_CLICK,
                  value: params);

              showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return DriverUnAssignSwapDialog((email) {
                      var params = Map<String, dynamic>();
                      params["driver"] = "$email";
                      TrackingUtils.instance!.tracking(
                          TConstants.D_SHIFTS_SWAP_CONFIRM_CLICK,
                          value: params);

                      context
                          .read<DriverShiftsModel>()
                          .unAssignWithSwap(widget.shiftId, email);
                    });
                  });
            } else if (index == 1) {
              //Unassign
              DriverDialog.show(context, S.of(context).driver_unassign_tip, () {
                TrackingUtils.instance!
                    .tracking(TConstants.D_SHIFTS_UNASSIGN_CONFIRM_CLICK);
                context
                    .read<DriverShiftsModel>()
                    .unassign(widget.shiftId, widget.shiftsTime);
              });
            }
          });
        },
      ),
    );
  }
}
