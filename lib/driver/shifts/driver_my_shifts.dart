import 'package:connect/driver/bean/driver_shifts_entity.dart';
import 'package:connect/driver/provider/driver_shifts_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/utils/object_util.dart';
import 'package:flutter/material.dart';
import 'driver_shifts_new_item.dart';
import 'package:provider/provider.dart';
import 'driver_shifts_search.dart';

class DriverMyShifts extends StatefulWidget {
  DriverMyShifts({Key? key}) : super(key: key);

  @override
  _DriverMyShiftsState createState() => _DriverMyShiftsState();
}

class _DriverMyShiftsState extends State<DriverMyShifts> {
  bool showAll = true;
  List<DriverShiftsEntity> myShifts = [];

  @override
  Widget build(BuildContext context) {
    List<DriverShiftsEntity> myItems =
        context.watch<DriverShiftsModel>().myItems;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        DriverShiftsSearch((value) {
          Future.delayed(Duration(milliseconds: 300)).then((_) {
            myShifts.clear();
            if (ObjectUtil.isEmpty(value)) {
              setState(() {
                showAll = true;
              });
            } else {
              bool hasSearchResult = false;
              myItems.forEach((element) {
                String? batch = element.batch;
                String? notes = element.notes;
                String? address = element.address;
                if ((batch != null &&
                        (batch.toLowerCase().contains(value.toLowerCase()) ||
                            batch.toLowerCase() == value.toLowerCase())) ||
                    (notes != null &&
                        notes.toLowerCase().contains(value.toLowerCase())) ||
                    (address != null &&
                        address.toLowerCase().contains(value.toLowerCase()))) {
                  hasSearchResult = true;
                  setState(() {
                    showAll = false;
                    myShifts.add(element);
                  });
                }
              });

              if(!hasSearchResult){
                setState(() {
                  showAll = false;
                  myShifts.clear();
                });
              }
            }
          });
        }),
        Expanded(
            child: myItems.isEmpty
                ? EmptyView(S.of(context).driver_no_shifts)
                : ListView.separated(
                    itemBuilder: (context, index) {
                      return showAll
                          ? DriverShiftsNewItem(myItems[index])
                          : DriverShiftsNewItem(myShifts[index]);
                    },
                    itemCount: showAll ? myItems.length : myShifts.length,
                    separatorBuilder: (context, index) {
                      return SizedBox(height: 10,);
                    })),
      ],
    );
  }
}
