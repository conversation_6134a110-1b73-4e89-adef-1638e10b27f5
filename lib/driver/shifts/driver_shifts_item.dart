import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/bean/destination_entity.dart';
import 'package:connect/driver/bean/driver_shifts_entity.dart';
import 'package:connect/driver/provider/driver_shifts_model.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/driver/widget/driver_map.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:sprintf/sprintf.dart';
import 'driver_swap_sheet.dart';
import 'driver_unassign_sheet.dart';
import 'driver_unassign_swap_dialog.dart';

class DriverShiftsItem extends StatefulWidget {
  DriverShiftsItem(this.entity, {Key? key}) : super(key: key);

  final DriverShiftsEntity entity;

  @override
  _DriverShiftsItemState createState() => _DriverShiftsItemState();
}

class _DriverShiftsItemState extends State<DriverShiftsItem> {

  @override
  Widget build(BuildContext context) {
    String available = S.of(context).driver_shifts_available;
    var entity = widget.entity;
    String batch = "";
    if (entity.batch != null && entity.batch!.isNotEmpty) {
      batch = "[${entity.batch}]";
    }
    String shiftsTime = "";
    String hoursAndMinutes = "";
    var startVar = entity.start;
    var endVar = entity.end;
    if (startVar != null && endVar != null) {
      var startDateTime = DateUtil.getDateTime(startVar)!.toLocal();
      String start = DateFormat.jm().format(startDateTime);
      var endDateTime = DateUtil.getDateTime(endVar)!.toLocal();
      String end = DateFormat.jm().format(endDateTime);
      shiftsTime = "$start - $end";

      var inMinutes = endDateTime.difference(startDateTime).inMinutes;
      var inHours = endDateTime.difference(startDateTime).inHours;
      if (inHours > 0) {
        var i = inMinutes - inHours * 60;
        if (i > 0) {
          if (i < 10) {
            hoursAndMinutes =
            "${inHours.toString()}hr 0${inMinutes - inHours * 60}mins";
          } else {
            hoursAndMinutes =
            "${inHours.toString()}hr ${inMinutes - inHours * 60}mins";
          }
        } else {
          hoursAndMinutes = "${inHours.toString()}hr";
        }
      } else if (inMinutes > 0 && inMinutes < 60) {
        if (inMinutes < 10) {
          hoursAndMinutes = "0${inMinutes.toString()}mins";
        } else {
          hoursAndMinutes = "${inMinutes.toString()}mins";
        }
      } else {
        //==0
        hoursAndMinutes = "${inHours.toString()}hr";
      }
    }

    var extraPay;
    var guarantee;
    if (entity.extraPay != null) {
      extraPay = entity.extraPay! / 100;
    }
    if (entity.guarantee != null) {
      guarantee = entity.guarantee! / 100;
    }

    bool showPay = extraPay != null || guarantee != null;

    bool isAvailable = false;
    if (entity.instances! > entity.drivers!.length) {
      isAvailable = true;
      available = S.of(context).driver_shifts_available;
    } else {
      isAvailable = false;
      available = S.of(context).driver_full;
    }

    bool hasSwap = false;
    late DriverShiftsDriver driver;
    var swapId;
    String swapStr = "";
    List<DriverShiftsDriver>? drivers = entity.drivers;
    if (drivers != null && drivers.length > 0 && drivers[0].swap != null) {
      driver = drivers[0];
      hasSwap = true;
      swapId = driver.swap!.sId;

      if(entity.isMyShift){
        var localDate = DateUtil.getDateTime(driver.swap!.date!)!.toLocal();
        var formatDate = DateUtil.formatDate(localDate,format: "MM/dd HH:mm");
        String? sp;
        if (driver.swap!.sId == "all") {
           sp = sprintf(S.of(context).driver_request_swap_with,["all","$formatDate"]);
        } else {
           sp = sprintf(S.of(context).driver_request_swap_with,["${driver.swap!.email}","$formatDate"]);
        }
        swapStr = "${driver.email!.split("@")[0]}$sp";
      }
    }

    return Container(
      padding: const EdgeInsets.all(Dimens.dp_15),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text("$batch $shiftsTime ( $hoursAndMinutes )",
                    style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: Dimens.sp_17)),
              ),
              InkWell(
                child: entity.isMyShift
                    ? Text(S.of(context).driver_unassign,
                        style: TextStyle(
                            color: Colors.blue, fontSize: Dimens.sp_17))
                    : isAvailable
                        ? Text(S.of(context).driver_shifts_assign,
                            style: TextStyle(
                                color: Colors.blue, fontSize: Dimens.sp_17))
                        : EmptyContainer(),
                onTap: () {
                  FocusScope.of(context).requestFocus(FocusNode());
                  if (entity.isMyShift) {
                    DriverUnAssignSheet.show(context, callback: (index) {
                      if (index == 0) {
                        //swap with driver
                        var params = Map<String, dynamic>();
                        params["item"] = "swap with driver";
                        TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_UNASSING_SHEET_CLICK, value: params);

                        showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return DriverUnAssignSwapDialog((email) {
                                var params = Map<String, dynamic>();
                                params["driver"] = "$email";
                                TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_SWAP_CONFIRM_CLICK, value: params);

                                context.read<DriverShiftsModel>().unAssignWithSwap(entity.sId, email);
                              });
                            });
                      } else if (index == 1) {
                        //Unassign
                        DriverDialog.show(context, S.of(context).driver_unassign_tip, () {
                          TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_UNASSIGN_CONFIRM_CLICK);
                          context.read<DriverShiftsModel>().unassign(entity.sId, shiftsTime);
                        });
                      }
                    });
                  } else {
                    DriverDialog.show(context, S.of(context).driver_assign_tip, () {

                      TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_ASSIGN_CONFIRM_CLICK);
                      context.read<DriverShiftsModel>().assign(entity.sId, shiftsTime, error: (e) {
                        //tracking
                        var params = Map<String, dynamic>();
                        params["fail_reason"] = "${e.toString()}";
                        TrackingUtils.instance!.tracking(TConstants.D_SHIFTS_ASSIGN_FAIL);
                      });
                    });
                  }
                },
              ),
            ],
          ),
          showPay
              ? Column(
                  children: [
                    SizedBox(
                      height: Dimens.dp_5,
                    ),
                    Row(
                      children: [
                        extraPay == null
                            ? EmptyContainer()
                            : Expanded(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Icon(
                                      Icons.monetization_on_outlined,
                                      size: 15,
                                    ),
                                    Text(
                                      S.of(context).driver_shifts_extra_pay,
                                    ),
                                    Text("\€$extraPay",
                                        style: TextStyle(color: Colors.green)),
                                  ],
                                ),
                              ),
                        Expanded(
                          child: guarantee == null
                              ? EmptyContainer()
                              : Row(
                                  mainAxisAlignment: extraPay == null
                                      ? MainAxisAlignment.start
                                      : MainAxisAlignment.end,
                                  children: [
                                    Icon(
                                      Icons.security,
                                      size: 15,
                                    ),
                                    Text(
                                      S.of(context).driver_shifts_guarantee_pay,
                                    ),
                                    Text("\€$guarantee",
                                        style: TextStyle(color: Colors.green)),
                                  ],
                                ),
                        ),
                      ],
                    ),
                  ],
                )
              : EmptyContainer(),
          SizedBox(
            height: Dimens.dp_3,
          ),
          /// swap shift
          Row(
            children: [
              Text(available, style: TextStyle(color: isAvailable ? Colors.green : Colors.red)),
              SizedBox(width: 5,),
              hasSwap && !entity.isMyShift
                  ?  Row(
                      children: [
                        swapId == "all"
                            ? EmptyContainer()
                            : Text("${driver.email!.split("@")[0]}"),
                        SizedBox(
                          width: 5,
                        ),
                        InkWell(
                          onTap: () {
                            if (swapId == "all") {
                              DriverDialog.show(context, S.of(context).driver_assign_tip, () {

                                // TrackingUtils.instance.tracking(TConstants.D_SHIFTS_ASSIGN_CONFIRM_CLICK);
                                context.read<DriverShiftsModel>().assign(entity.sId, shiftsTime, error: (e) {
                                  //tracking
                                  // var params = Map<String, dynamic>();
                                  // params["fail_reason"] = "${e.toString()}";
                                  // TrackingUtils.instance.tracking(TConstants.D_SHIFTS_ASSIGN_FAIL);
                                });
                              });
                            }else{
                              var start = entity.start;
                              var localDate = DateUtil.getDateTime(start!)!.toLocal();
                              var formatDate = DateUtil.formatDate(localDate,format: "MM/dd HH:mm");
                              DriverSwapSheet.show(context, driver.email, formatDate, (index) {
                                context.read<DriverShiftsModel>().requestSwap(entity.sId, driver.sId, index);
                              });
                            }
                          },
                          child: Text(
                            swapId == "all"
                                ? S.of(context).driver_swap_shifts
                                : S.of(context).driver_request_swap_shifts, style: TextStyle(color: Colors.blue),),
                        )
                      ],
                    )
                  : EmptyContainer()
            ],
          ),
          hasSwap && entity.isMyShift
              ? Row(children: [
                  Expanded(child: Text(swapStr,style: TextStyle(color: Colors.black87),)),
                ])
              : EmptyContainer(),
          entity.notes == null || entity.notes!.isEmpty
              ? EmptyContainer()
              : SizedBox(
                  height: Dimens.dp_3,
                ),
          entity.notes == null || entity.notes!.isEmpty
              ? EmptyContainer()
              : Row(
                  children: [
                    Expanded(
                      child: Text(entity.notes!),
                    ),
                  ],
                ),
          SizedBox(
            height: Dimens.dp_3,
          ),
          entity.address == null
              ? EmptyContainer()
              : InkWell(
                  child: Row(
                    children: [
                      Expanded(
                          child: Text.rich(TextSpan(children: [
                        TextSpan(
                          text: S.of(context).driver_start_location,
                        ),
                        TextSpan(
                            text: entity.address,
                            style: TextStyle(color: Colors.blue)),
                        WidgetSpan(
                            child: Icon(
                          Icons.chevron_right,
                          size: Dimens.dp_16,
                          color: Colors.blue,
                        ))
                      ]))),
                    ],
                  ),
                  onTap: () {
                    var destinationEntity = DestinationEntity();
                    destinationEntity.formatted = entity.address;
                    destinationEntity.from = "shifts";
                    DriverMap.show(context, destinationEntity, "shifts");
                  },
                ),
        ],
      ),
    );
  }
}
