import 'dart:convert';
import 'dart:io';
import 'package:connect/data/log_entity.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:dio/dio.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import '../http_manager.dart';

class ConnectInterceptor extends Interceptor {
  static const String TAG = "ConnectInterceptor";

  var method;
  var path;
  var data;

  _cusHeaders(String? deviceId, String? version) {
    var headers = Map<String, dynamic>();
    String? token = ConnectCache.getToken();
    headers["Authorization"] = "JWT $token";
    headers["X-Ricepo-Client"] = "connect-flutter/$version";
    headers["X-Ricepo-Device"] = deviceId;

    var packageName = ConnectCache.getPackageName();
    var versionCode = ConnectCache.getBuildNumber();
    String osVersion = Platform.operatingSystemVersion;
    var clientName = "unknown";

    if (Platform.isAndroid) {
      clientName = "android-portal";
    } else if (Platform.isIOS) {
      clientName = "ios-portal";
    }
    var ua =
        "$clientName/$version ($packageName; build:$versionCode; ${Platform.operatingSystem}:$osVersion)";
    headers["user-agent"] = ua;
    return headers;
  }

  _createLogEntity(RequestOptions options, bool isRequest) {
    var logEntity = LogEntity();
    if (isRequest) {
      logEntity.header = options.headers.toString();
    }
    logEntity.bodyParams = options.queryParameters.toString();
    logEntity.url = options.path;
    logEntity.method = options.method;

    return logEntity;
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // HttpManager.instance?.dio.lock();
    options.headers.addAll(
        _cusHeaders(ConnectCache.getDeviceId(), ConnectCache.getVersion()));
    // HttpManager.instance?.dio.unlock();

    method = options.method;
    data = options.data;
    path = options.path;
    // LogUtil.v("method::$method,data::$data,path::$path");

    LogEntity logEntity = _createLogEntity(options, true);
    // persistent request log
    LogManager.instance!.log(TAG, "onRequest", jsonEncode(logEntity));

    return super.onRequest(options, handler);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    LoadingUtils.dismiss();
    return super.onError(err, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // LogUtil.v("extra::${response.headers.toString()}");
    if (response.data.toString().isEmpty) {
      LogUtil.v("response is null");
      var requestMaps = Map<String, dynamic>();
      requestMaps["method"] = "$method";
      requestMaps["path"] = "$path";
      requestMaps["data"] = "$data";
      var sentryEvent =
          SentryEvent(extra: requestMaps, throwable: "response null");
      Sentry.captureEvent(sentryEvent);

      //auto dismiss loading when api completed
      LoadingUtils.dismiss();
    } else {
      var extra = response.requestOptions.extra;
      // LogUtil.v("extra::$extra");
      if (extra.isEmpty) {
        LoadingUtils.dismiss();
      }

      // persistent response log
      LogEntity logEntity = _createLogEntity(response.requestOptions, false);

      var length = response.data.toString().length;

      if (length < 10000) {
        logEntity.response = response.data.toString();
      }
      logEntity.responseLength = length;
      logEntity.statusCode = response.statusCode;

      LogManager.instance!.log(TAG, "onResponse", jsonEncode(logEntity));
    }
    return super.onResponse(response, handler);
  }
}
