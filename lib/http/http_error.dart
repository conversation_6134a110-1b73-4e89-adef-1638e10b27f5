import 'dart:convert';
import 'package:connect/common/global.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/log_util.dart';
import 'package:dio/dio.dart';

class HttpError {
  static const int UNAUTHORIZED = 401;
  static const int FORBIDDEN = 403;
  static const int NOT_FOUND = 404;
  static const int REQUEST_TIMEOUT = 408;
  static const int INTERNAL_SERVER_ERROR = 500;
  static const int BAD_GATEWAY = 502;
  static const int SERVICE_UNAVAILABLE = 503;
  static const int GATEWAY_TIMEOUT = 504;
  static const int UNKNOWN = 999;

  static const String AUTH_FAILED = "auth-failed";
  static const String AUTH_FAILED_MSG = "Authentication failed.";
  static const String UNEXPECTED_ERROR = "Unexpected Error, please retry.";

  static String? errorHandler(DioError err) {
    try {
      LoadingUtils.dismiss();
      String? message;
      if (err.response != null && err.response?.data != null) {
        LogUtil.v("Interceptors_status::${err.response?.statusCode}");
        LogUtil.v("Interceptors_data::${err.response?.data}");
        if (err.type == DioExceptionType.connectionTimeout ||
            err.type == DioExceptionType.connectionError ||
            err.type == DioExceptionType.sendTimeout ||
            err.type == DioExceptionType.receiveTimeout ||
            err.type == DioExceptionType.cancel ||
            err.type == DioExceptionType.unknown) {
          message = err.response?.statusMessage;
        } else if (err.type == DioExceptionType.badResponse) {
          // message = "${err.response.data}";
          var decode = jsonDecode(err.response.toString());
          if (decode["message"] != null) {
            message = "${decode["message"]}";
          } else if (decode["error"] != null) {
            message = "${decode["error"]}";
          }
          LogUtil.v("Interceptors_errorEntity::$message");
        }
      } else {
        message = err.error.toString();
        if (message != null &&
            (message.contains("SocketException:") ||
                message.contains("HttpException:"))) {
          message =
              S.of(GlobalConfig.navigatorKey.currentContext!).network_check;
        }
        LogUtil.v("Interceptors_message::$message");
      }
      return message;
    } catch (e) {
      return "$UNEXPECTED_ERROR::${e.toString()}";
    }
  }
}
