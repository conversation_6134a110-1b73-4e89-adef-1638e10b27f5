class HttpUri {
  /// develop environment
  static const String DEV_URL = "https://dev.isengard.rice.rocks";

  // static const String DEV_URL = "http://0.0.0.0:3000";

  // static const String DEV_URL = "http://*************:3000";

  // static const String DEV_URL = "http://vanguard-test-lb-1272110301.us-east-1.elb.amazonaws.com";

  /// online environment
  static const String PROD_URL = "https://internal.isengard.rice.rocks";

  static const String GOOGLE_MAP_HOST = "maps.google.com";

  static const String APPLE_MAP_HOST = "maps.apple.com";

  static const String PRINT_URL = "http://s.rice.rocks/r/";

  static const String SMS_CODE = "/v1/auth/driver";

  /// both login with password and renewToken use this
  static const String LOGIN_WITH_PASSWORD = "/v1/auth";

  ///app update check
  static const String APP_UPDATE = "/v1/update";

  static const String LOGIN_WITH_PHONE = "/v1/auth/driver";

  static const String ORDERS_LIST = "/v1/restaurants/{restaurant_id}/orders";

  ///order details
  static const String ORDERS_DETAILS =
      "/v1/restaurants/111111111111c000105ea965/orders/%s";

  ///cancel order, params :: restId,orderId
  static const String ORDERS_CANCEL = "/v1/restaurants/%s/orders/%s/status";

  /// confirm order
  static const String ORDERS_STATUS =
      "/v1/restaurants/{restaurant_id}/orders/{order_id}/status";

  /// ready
  static const String ORDERS_READY =
      "/v1/restaurants/{restaurant_id}/orders/{order_id}/prepare";

  /// report
  static const String REPORT = "/v1/restaurants/{restaurant_id}/report";

  /// update password
  static const String UPDATE_PASSWORD = "/v1/accounts/%s";

  /// restaurants details,params{restId}
  static const String RESTAURANTS_DETAILS = "/v1/restaurants/%s";

  /// driver online
  static const String DRIVER_ONLINE = "/v1/accounts/%s";

  ///update driver order
  static const String DRIVER_ORDER_STOPS = "/v1/accounts/%s/stops";

  ///get driver info
  static const String CHECK_DRIVER_ONLINE = "/v1/auth";

  ///driver arrived at restaurant api
  static const String ARRIVED_RESTAURANT = "/v1/auth";

  static const String DRIVER_MATCH_NEW_ORDER =
      "/v1/restaurants/%s/orders/%s/driver";

  static const String PICKUP_COMPLETE_URL = "/v1/orders/%s/delivery/status";

  static const String MENU_ALREADY = "/v1/restaurants/%s/passcode/%s/prepare";

  ///param :: scope
  static const String REST_MENU_LIST = "/v1/restaurants/%s/menu";

  ///param :: scope,foodId
  static const String REST_MENU_TOGGLE = "/v1/restaurants/%s/food/%s";

  ///param :: scope,foodId,optionId
  static const String REST_MENU_OPTIONS =
      "/v1/restaurants/%s/food/%s/options/%s";

  ///push token
  static const String PUSH_TOKEN = "/v1/accounts/%s";

  ///driver report
  static const String DRIVER_REPORT = "/v1/regions/%s/drivers/%s/report";

  ///driver report adjustment
  static const String DRIVER_REPORT_ADJUST = "/v1/regions/%s/drivers/%s/orders";

  ///driver shifts list
  static const String DRIVER_SHIFTS = "/v1/regions/%s/shifts";

  static const String DRIVER_ASSIGN = "/v1/regions/%s/shifts/%s/driver/%s";

  ///params:regionId,shiftId,userId
  static const String DRIVER_ASSIGN_SWAP =
      "/v1/regions/%s/shifts/%s/driver/%s/swap";

  ///params:regionId,shiftId,userId,request swap userId
  static const String DRIVER_ASSIGN_SWAP_EMAIL =
      "/v1/regions/%s/shifts/%s/driver/%s/swap/%s";

  static const String DRIVER_REGION = "/v1/regions/%s";

  ///upload file to amazons
  static const String DRIVER_DROPOFF_FILE = "/v1/file";

  static const String DRIVER_DROPOFF_UPLOAD = "/v1/accounts/%s/stops/photos";

  static const String DRIVER_PICKUPS_HEAT_MAP = "/v1/regions/%s/pickups";

  static const String DRIVER_ALL_ASSIGNS = "/v1/regions/%s/drivers/%s/orders";

  ///param::userId
  static const String DRIVER_SETUP_PAYMENT = "/v1/accounts/%s/stripe/express";

  ///param::scope
  static const String RESTAURANT_SETUP_PAYMENT =
      "/v1/restaurants/%s/stripe/express";

  ///param::userId
  static const String DRIVER_PAYMENT_DASHBOARD =
      "/v1/accounts/%s/stripe/dashboard";

  ///param::scope
  static const String RESTAURANT_PAYMENT_DASHBOARD =
      "/v1/restaurants/%s/stripe/dashboard";

  static const String DRIVER_TICKETS = "/v1/tickets";

  ///support
  static const String DRIVER_CHAT = "/v1/flex/chat/token";

  ///phone masking
  static const String PHONE_MASKING =
      "/v1/twilio/phoneMasking/orders/{order_id}";

  //onDemand Delivery quote
  static const String REST_ID_HOLDER = "{rest_id}";
  static const String ON_DEMNAD_DELIVERY_QUOTE = "/v1/bifrost/restaurants/$REST_ID_HOLDER/quote";
  static const String ON_DEMAND_DELIVERY_ORDER = "/v1/bifrost/restaurants/$REST_ID_HOLDER/order";
}
