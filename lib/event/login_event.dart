import 'package:connect/data/orders_entity.dart';

class LoginPhoneEvent {
  bool notify;

  LoginPhoneEvent(this.notify);
}

class LoginPasswordEvent {
  LoginPasswordEvent();
}

class AutoConfirmed {
  AutoConfirmed();
}

class RingToneSwitch {
  bool isClose;

  RingToneSwitch(this.isClose);
}

class ResetSmsCode {
  ResetSmsCode();
}

class CheckDriverOnline {
  CheckDriverOnline();
}

class SwitchBackground {
  SwitchBackground();
}

class PickupFailedEvent {
  final int index;
  final bool autoFailed;

  PickupFailedEvent(this.index, {this.autoFailed = false});
}

class DriverOnlineSwitchEvent {
  final bool? isOnline;

  DriverOnlineSwitchEvent(this.isOnline);
}

class ResNotifyOrderDetails {
  final OrdersEntity? responseItem;

  ResNotifyOrderDetails(this.responseItem);
}

class ResSyncOrderStatus {
  final OrdersEntity? responseItem;

  ResSyncOrderStatus(this.responseItem);
}

class ResRefreshOrders {
  ResRefreshOrders();
}

class ResRefreshScheduledOrders {
  ResRefreshScheduledOrders();
}

class ResRefreshHistoryOrders {
  ResRefreshHistoryOrders();
}

class SearchRefreshOrders {
  SearchRefreshOrders();
}

class DriverShiftsAssign {
  DriverShiftsAssign();
}

class DriverShiftsSearchClearEvent {
  DriverShiftsSearchClearEvent();
}

class DriverBags {
  num bagsNum;

  DriverBags(this.bagsNum);
}

class DriverClearCompletePickupNum {
  DriverClearCompletePickupNum();
}

class DriverSetupPaymentRefresh {
  DriverSetupPaymentRefresh();
}

class DriverFeedbackType {
  String type;

  DriverFeedbackType(this.type);
}

class DriverFeedbackInfo {
  String info;

  DriverFeedbackInfo(this.info);
}

class DriverCreateOrder {
  DriverCreateOrder();
}
