import 'package:connect/common/MyStyles.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class ButtonStyleManager {
  static ButtonStyle buttonStyle() {
    return ButtonStyle(
      backgroundColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return MyColors.portal.withOpacity(0.6);
        }
        return MyColors.portal;
      }),
      shape: MaterialStateProperty.all(RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(Dimens.dp_20),
      )),
    );
  }

  static ButtonStyle buttonStyleAllPadding(
      EdgeInsetsGeometry edgeInsetsGeometry) {
    return ButtonStyle(
      backgroundColor: MaterialStateProperty.all(RColors.r_orange),
      shape: MaterialStateProperty.all(RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(Dimens.dp_20),
      )),
      padding: MaterialStateProperty.all(edgeInsetsGeometry),
    );
  }

  static ButtonStyle buttonStyleAll() {
    return ButtonStyle(
      backgroundColor: MaterialStateProperty.all(Colors.blue),
      shape: MaterialStateProperty.all(RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(Dimens.dp_20),
      )),
    );
  }
}
