import 'package:amplitude_flutter/amplitude.dart';
import 'package:amplitude_flutter/configuration.dart';
import 'package:connect/common/global.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

import '../common/constants.dart';

class TrackingUtils {
  factory TrackingUtils() => _getInstance()!;

  static TrackingUtils? get instance => _getInstance();
  static TrackingUtils? _instance;

  TrackingUtils._internal() {
    _initTracking();
  }

  static TrackingUtils? _getInstance() {
    if (_instance == null) {
      _instance = TrackingUtils._internal();
    }
    return _instance;
  }

  late FirebaseAnalytics analytics;
  late Amplitude amplitude;

  _initTracking() {
    analytics = FirebaseAnalytics.instance;
    amplitude = Amplitude(Configuration(apiKey: Constants.AMP_API_KEY, instanceName: "connect"));
  }

  Future<void> tracking(String eventName, {Map<String, dynamic>? value}) async {
    if (GlobalConfig.isRelease) {
      _initTracking();
      var user = ConnectCache.getUser();
      if (user == null) return;

      //set userId
      analytics.setUserId(id: user.userId);
      amplitude.setUserId(user.userId);

      //log event
      Map<String, Object> result = value?.cast<String, Object>() ?? {};
      analytics.logEvent(name: eventName, parameters: result);
      // amplitude.logEvent(eventName, eventProperties: value);
    }
  }
}
