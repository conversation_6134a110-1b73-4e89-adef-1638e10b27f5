import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class ToastUtils {
  static show(String? message, {Toast toast = Toast.LENGTH_SHORT}) {
    if (message == null) return;
    Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black54,
        textColor: Colors.white,
        fontSize: 14.0);
  }

  static showWithGravity(String? message, {Toast toast = Toast.LENGTH_SHORT, ToastGravity gravity = ToastGravity.BOTTOM}) {
    if (message == null) return;
    Fluttertoast.showToast(
        msg: message,
        toastLength: toast,
        gravity: gravity,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black54,
        textColor: Colors.white,
        fontSize: 14.0);
  }
}
