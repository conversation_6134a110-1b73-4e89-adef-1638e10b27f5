import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/utils/connect_cache.dart';

class ServerMultiLan {
  static String multiAdapt(MultiNameEntity? name) {
    var languageTag = ConnectCache.getLocale().toLanguageTag();
    if (languageTag == "zh-HK") {
      return name?.zhHk ?? "";
    }
    return name?.zhCn ?? "";
  }

  static String multiAdapt2(MultiNameEntity? name) {
    var languageTag = ConnectCache.getLocale().toLanguageTag();
    if (languageTag == "zh-HK") {
      return name?.zhHk ?? "";
    } else if (languageTag == "en-US") {
      return name?.enUs ?? "";
    }
    return name?.zhCn ?? "";
  }

  static String coinSymbol(OrdersRestaurant? restaurant, String coin) {
    if (restaurant?.address?.country == null) {
      return "\€$coin";
    }
    switch (restaurant?.address?.country) {
      case "US":
        return "\$$coin";
      case "ES":
        return "\€$coin";
      case "GB":
        return "\£$coin";
    }
    return "\€$coin";
  }

  static String coinSymbolCountry(String? country, num? coin) {
    if (coin == null) {
      return "";
    }
    if (country == null || country.isEmpty) {
      return "\€${coin.abs()}";
    }
    switch (country) {
      case "US":
        return "\$${coin.abs()}";
      case "ES":
        return "\€${coin.abs()}";
      case "GB":
        return "\£${coin.abs()}";
    }
    return "\€${coin.abs()}";
  }
}
