import 'dart:ui';
import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/data/user_entity.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/utils/log_util.dart';
import 'package:sp_util/sp_util.dart';
import 'package:timezone/timezone.dart';

class ConnectCache {
  static void firstDisclosure(bool isFirst) {
    SpUtil.putBool(Constants.FIRST_DISCLOSURE, isFirst);
  }

  static bool? isFirstDisclosure() {
    return SpUtil.getBool(Constants.FIRST_DISCLOSURE, defValue: true);
  }

  ///保存上传日志时间
  static void saveDateTime(String time) {
    SpUtil.putString(Constants.CONNECT_DATE_TIME, time);
  }

  static String? getDateTime() {
    return SpUtil.getString(Constants.CONNECT_DATE_TIME,
        defValue: DateTime.now().add(Duration(days: -1)).toString());
  }

  ///PRD,DEV
  static void saveEnv(String env, String baseUrl) {
    SpUtil.putString(Constants.CONNECT_ENV, env);
    SpUtil.putString(Constants.CONNECT_BASE_URL, baseUrl);
  }

  static String? getEnv() {
    return SpUtil.getString(Constants.CONNECT_ENV,
        defValue: GlobalConfig.isRelease ? "PRD" : "DEV");
  }

  static String? getBaseUrl() {
    return SpUtil.getString(Constants.CONNECT_BASE_URL,
        defValue: GlobalConfig.isRelease ? HttpUri.PROD_URL : HttpUri.DEV_URL);
  }

  ///setup proxy ip
  static void saveProxyIp(String env) {
    SpUtil.putString(Constants.CONNECT_PROXY_IP, env);
  }

  static String? getProxyIp() {
    return SpUtil.getString(Constants.CONNECT_PROXY_IP, defValue: "");
  }

  static bool isRelease() {
    String? env = getEnv();

    LogUtil.v(env);

    if (env == "PRD") {
      return true;
    }
    return false;
  }

  static void saveToken(String token) {
    SpUtil.putString(Constants.TOKEN, token);
  }

  static String? getToken() {
    return SpUtil.getString(Constants.TOKEN);
  }

  static void clearToken() {
    SpUtil.putString(Constants.TOKEN, "");
  }

  static void saveUser(UserEntity? entity) {
    if (entity?.token == null) return;
    saveToken(entity!.token!);
    SpUtil.putObject(Constants.USER, entity);
  }

  static UserEntity? getUser() {
    var object = SpUtil.getObject(Constants.USER);
    if (object == null) return null;

    UserEntity? user = JsonConvert.fromJsonAsT<UserEntity>(object);
    if (user?.userId == null) return null;
    return user;
  }

  static Map? getMapUser() {
    if (SpUtil.getObject(Constants.USER) == null ||
        SpUtil.getObject(Constants.USER)!.isEmpty) {
      return null;
    }
    return SpUtil.getObject(Constants.USER);
  }

  static void clearUser() {
    SpUtil.putObject(Constants.USER, UserEntity());
  }

  static void saveAutoReceiveStatus(bool flag) {
    SpUtil.putBool(Constants.AUTO_RECEIVE, flag);
  }

  static bool? getAutoReceiveStatus() {
    return SpUtil.getBool(Constants.AUTO_RECEIVE);
  }

  static void savePrepareTime(String minute) {
    SpUtil.putString(Constants.PREPARE_TIME, minute);
  }

  static String? getPrepareTime() {
    return SpUtil.getString(Constants.PREPARE_TIME, defValue: '15');
  }

  static void saveLocaleStr(String locale) {
    SpUtil.putString(Constants.CONNECT_LOCALE, locale);
  }

  static Locale getLocale() {
    Locale _locale;
    var locale = getLocaleStr();
    if (locale == Constants.zh_CN) {
      _locale = const Locale('zh', 'CN');
    } else if (locale == Constants.zh_HK) {
      _locale = const Locale('zh', 'HK');
    } else if (locale == Constants.es) {
      _locale = const Locale('es', 'ES');
    } else if (locale == Constants.fr) {
      _locale = const Locale('fr', 'FR');
    } else {
      _locale = const Locale('en', 'US');
    }
    return _locale;
  }

  static String? getLocaleStr() {
    return SpUtil.getString(Constants.CONNECT_LOCALE, defValue: Constants.en);
  }

  static void saveRingTone(bool b) {
    SpUtil.putBool(Constants.RING_TONE, b);
  }

  static bool? getRingTone() {
    return SpUtil.getBool(Constants.RING_TONE, defValue: true);
  }

  static void saveDoublePrint(bool b) {
    SpUtil.putBool(Constants.DOUBLE_PRINT, b);
  }

  static bool? getDoublePrint() {
    return SpUtil.getBool(Constants.DOUBLE_PRINT);
  }

  static void saveOnline(bool b) {
    SpUtil.putBool(Constants.ONLINE, b);
  }

  static bool? getOnline() {
    return SpUtil.getBool(Constants.ONLINE, defValue: false);
  }

  static void saveFirstHistoryTap(bool b) {
    SpUtil.putBool("history_tap", b);
  }

  static bool? getFirstHistoryTap() {
    return SpUtil.getBool("history_tap", defValue: true);
  }

  static void saveFirstScheduledTap(bool b) {
    SpUtil.putBool("scheduled_tap", b);
  }

  static bool? getFirstScheduledTap() {
    return SpUtil.getBool("scheduled_tap", defValue: true);
  }

  static void saveLogPath(String path) {
    SpUtil.putString(Constants.LOG_PATH, path);
  }

  static String? getLogPath() {
    return SpUtil.getString(Constants.LOG_PATH);
  }

  static void saveDeviceId(String deviceId) {
    SpUtil.putString(Constants.C_DEVICE_ID, deviceId);
  }

  static String? getDeviceId() {
    return SpUtil.getString(Constants.C_DEVICE_ID);
  }

  static void saveVersion(String version) {
    SpUtil.putString(Constants.C_VERSION, version);
  }

  static String? getVersion() {
    return SpUtil.getString(Constants.C_VERSION);
  }

  static void saveBuildNumber(String version) {
    SpUtil.putString(Constants.C_BUILD_NUMBER, version);
  }

  static String? getBuildNumber() {
    return SpUtil.getString(Constants.C_BUILD_NUMBER);
  }

  static void savePackageName(String packageName) {
    SpUtil.putString(Constants.C_PACKAGE_NAME, packageName);
  }

  static String? getPackageName() {
    return SpUtil.getString(Constants.C_PACKAGE_NAME);
  }

  static void clearCurrentRoleId() {
    SpUtil.putString(Constants.CONNECT_ROLE, "");
  }

  ///real default value
  static void saveCurrentRoleId(String roleId) {
    SpUtil.putString(Constants.CONNECT_ROLE, roleId);
  }

  ///has default value
  static String? getCurrentRoleId2() {
    return SpUtil.getString(Constants.CONNECT_ROLE);
  }

  static String getCurrentRoleId() {
    var user = getUser()!;
    var roleId = SpUtil.getString(Constants.CONNECT_ROLE);
    if (roleId != null && roleId.isNotEmpty) {
      return roleId;
    }
    //set true when match roleId
    var roles = user.roles!
        .where((element) =>
            element!.name!.contains("driver") ||
            element.name!.contains("restaurant"))
        .toList();

    if (roles.length > 0) {
      var role = roles.first;
      if (role != null) {
        role.isSelected = true;
        saveUser(user);
        roleId = role.sId;
      }
    }
    // LogUtil.v("roleId::$roleId");
    return roleId!;
  }

  static UserRole? getCurrentRole() {
    var user = getUser()!;
    var roles = user.roles!
        .where((element) => element!.sId == getCurrentRoleId())
        .toList();
    if (roles.length > 0) {
      //storage role
      return roles.first;
    }
    //default roles
    return user.roles!.first;
  }
}
