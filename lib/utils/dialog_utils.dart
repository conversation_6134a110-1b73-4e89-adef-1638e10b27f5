import 'package:flutter/material.dart';
import 'package:connect/generated/l10n.dart';

class DialogUtils {


  static Future<void> showAlert(BuildContext context, Widget? tittle, Widget? content) async {
    if (ModalRoute
        .of(context)
        ?.isCurrent == true) {
      showDialog<void>(
        context: context,
        barrierDismissible: false, // user must tap button!
        builder: (BuildContext context) {
          return AlertDialog(
            title: tittle,
            content: content,
            actions: <Widget>[
              TextButton(
                child: Text(S.of(context).ok),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    }
  }

}