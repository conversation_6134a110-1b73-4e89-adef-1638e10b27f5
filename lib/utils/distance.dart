import 'dart:math';

class Distance{
  static String getDistance(double lat1, double lng1, double lat2, double lng2) {
    double def = 6378137.0;
    double radLat1 = _rad(lat1);
    double radLat2 = _rad(lat2);
    double a = radLat1 - radLat2;
    double b = _rad(lng1) - _rad(lng2);
    double s = 2 *
        asin(sqrt(pow(sin(a / 2), 2) +
            cos(radLat1) * cos(radLat2) * pow(sin(b / 2), 2)));
    return ((s * def).roundToDouble() / 1000).toStringAsPrecision(2);
  }

  static double _rad(double d) {
    return d * pi / 180.0;
  }
}