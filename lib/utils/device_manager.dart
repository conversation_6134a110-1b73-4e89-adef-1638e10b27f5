import 'package:connect/utils/log_manager.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'connect_cache.dart';

class DeviceManager {
  static const String TAG = "DeviceManager";

  static writeDeviceInfo(String? appVersion) async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (defaultTargetPlatform == TargetPlatform.android) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      LogManager.instance!.log(TAG, "", "model::${androidInfo.model}");
      LogManager.instance!
          .log(TAG, "", "version::${androidInfo.version.release}");
      LogManager.instance!.log(TAG, "", "brand::${androidInfo.brand}");
      LogManager.instance!.log(TAG, "", "device::${androidInfo.device}");
      LogManager.instance!
          .log(TAG, "", "isPhysicalDevice::${androidInfo.isPhysicalDevice}");
      LogManager.instance!.log(TAG, "", "product::${androidInfo.product}");
      LogManager.instance!.log(TAG, "", "appVersion::$appVersion");
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      print('Running on ${iosInfo.utsname.machine}');
      LogManager.instance!.log(TAG, "", "name::${iosInfo.name}");
      LogManager.instance!.log(TAG, "", "systemName::${iosInfo.systemName}");
      LogManager.instance!
          .log(TAG, "", "systemVersion::${iosInfo.systemVersion}");
      LogManager.instance!.log(TAG, "", "utsname::${iosInfo.utsname.machine}");
      LogManager.instance!
          .log(TAG, "", "isPhysicalDevice::${iosInfo.isPhysicalDevice}");
      LogManager.instance!.log(TAG, "", "appVersion::$appVersion");
    }
    LogManager.instance!
        .log(TAG, "", "user::${ConnectCache.getUser()!.toJson()}");
  }
}
