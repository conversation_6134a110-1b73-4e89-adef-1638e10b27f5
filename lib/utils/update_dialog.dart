import 'dart:io';
import 'package:connect/utils/log_util.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:launch_app_store/launch_app_store.dart';

class UpdateDialog extends Dialog {
  final String? upDateContent;
  final bool? isForce;

  static bool isShowing = false;
  // static const String AMAZON_DOWNLOAD =
  //     "https://s3.eu-west-1.amazonaws.com/static.rice.rocks/connect-flutter/app-release-universal.apk";

  UpdateDialog({this.upDateContent, this.isForce});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Container(
            width: 319,
            height: 240,
            child: Stack(
              children: <Widget>[
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: Colors.white),
                  width: double.infinity,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.only(bottom: 20),
                        child: Text(
                          S.of(context).connect_new_version,
                          style: TextStyle(
                              fontSize: 20,
                              color: Colors.blue,
                              decoration: TextDecoration.none),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          alignment: Alignment.center,
                          child: Text(
                            upDateContent!,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: 16,
                                color: Colors.black54,
                                decoration: TextDecoration.none),
                          ),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 20),
                        width: 250,
                        height: 42,
                        child: ElevatedButton(
                            style: ButtonStyle(
                              backgroundColor:
                                  MaterialStateProperty.all(Colors.blue),
                              shape: MaterialStateProperty.all(StadiumBorder()),
                            ),
                            child: Text(
                              S.of(context).connect_new_update,
                              style:
                                  TextStyle(fontSize: 20, color: Colors.white),
                            ),
                            onPressed: () async {
                              if (Platform.isAndroid) {
                                DeviceInfoPlugin deviceInfo =
                                    DeviceInfoPlugin();
                                AndroidDeviceInfo androidInfo =
                                    await deviceInfo.androidInfo;
                                LogUtil.v("brand::${androidInfo.brand}");
                                // if ("Amazon" == androidInfo.brand) {
                                //   launch(AMAZON_DOWNLOAD);
                                // } else {
                                _launchMarket();
                                // }
                              } else {
                                _launchMarket();
                              }
                            }),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              isShowing = false;
              Navigator.pop(context);
            },
            child: Offstage(
              offstage: isForce!,
              child: Container(
                  margin: EdgeInsets.only(top: 25),
                  child: Icon(
                    Icons.cancel,
                    color: Colors.white,
                    size: 40,
                  )),
            ),
          ),
        ],
      ),
    );
  }

  _launchMarket() {
    LaunchReview.launch(
        androidAppId: "rocks.rice.connect", iOSAppId: "1560287010");
  }

  static showUpdateDialog(
      BuildContext context, String mUpdateContent, bool mIsForce) {
    isShowing = true;
    return showDialog(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) {
          return WillPopScope(
              child: UpdateDialog(
                  upDateContent: mUpdateContent, isForce: mIsForce),
              onWillPop: _onWillPop);
        });
  }

  static Future<bool> _onWillPop() async {
    return false;
  }
}
