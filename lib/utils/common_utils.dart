
class CommonUtils{

  /// Check if a phone number if valid or not.
  /// [phoneNumber] The phone number which will be validated.
  /// Return true if the phone number is valid. Otherwise, return false.
  static bool isValidPhoneNumber(String string) {
    // Null or empty string is invalid phone number
    if (string == null || string.isEmpty) {
      return false;
    }
    const pattern = r'^(?:[+0][1-9])?[0-9]{10,12}$';
    final regExp = RegExp(pattern);

    if (!regExp.hasMatch(string)) {
      return false;
    }
    return true;
  }
}


