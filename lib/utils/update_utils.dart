import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/update_dialog.dart';
import 'package:flutter/cupertino.dart';

class UpdateUtils {
  static checkUpdate(BuildContext context, String? update) async {
    if ("required" == update) {
      UpdateDialog.showUpdateDialog(
          context, S.of(context).connect_update_required, true);
    } else if ("optional" == update) {
      UpdateDialog.showUpdateDialog(
          context, S.of(context).connect_update_optional, false);
    }
  }
}
