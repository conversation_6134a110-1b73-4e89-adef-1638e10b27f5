class FormatUtils {
  static String format(DateTime date) {
    final sb = StringBuffer();
    if (date.timeZoneOffset.inMinutes == 0) {
      sb.write('Z');
    } else {
      if (date.timeZoneOffset.isNegative) {
        sb.write('-');
        sb.write(_digits((-date.timeZoneOffset.inHours) % 24, 2));
        sb.write(':');
        sb.write(_digits((-date.timeZoneOffset.inMinutes) % 60, 2));
      } else {
        sb.write('+');
        sb.write(_digits(date.timeZoneOffset.inHours % 24, 2));
        sb.write(':');
        sb.write(_digits(date.timeZoneOffset.inMinutes % 60, 2));
      }
    }
    return sb.toString();
  }

  static String _digits(int value, int length) {
    String ret = '$value';
    if (ret.length < length) {
      ret = '0' * (length - ret.length) + ret;
    }
    return ret;
  }
}
