import 'package:connect/common/t_constants.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';

class TrackingManager {
  static trackStopsTime(int time1, int time2, String stops) {
    int diff = time2 - time1;
    LogUtil.v("diff::$diff");
    TrackingUtils.instance!.tracking(TConstants.D_STOPS_API_TIME,
        value: {"time": "$diff", "stops": stops});
  }
}
