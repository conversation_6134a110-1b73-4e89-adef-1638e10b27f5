import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:connect/common/assets_path.dart';
import 'package:connect/utils/log_util.dart';

class AudioManager {
  // late AudioCache _player;
  // late AudioPlayer _audioPlayerInstance;
  late AudioPlayer _player;

  factory AudioManager() => _getInstance()!;

  static AudioManager? get instance => _getInstance();
  static AudioManager? _instance;

  AudioManager._internal() {
    _initPlay();
  }

  static AudioManager? _getInstance() {
    if (_instance == null) {
      _instance = AudioManager._internal();
    }
    return _instance;
  }

  _initPlay() {
    _player = AudioPlayer();
    //play audio
    // if (Platform.isAndroid) {
    //   _audioPlayerInstance = new AudioPlayer();
    //   _player = AudioCache(fixedPlayer: _audioPlayerInstance);
    // } else {
    //   _player = AudioCache();
    // }
  }

  play() {
    _player
        .play(AssetSource(AssetsPath.ORDER_AUDIO))
        .then((value) => LogUtil.v("AudioManager::play success}"))
        .catchError((e) {
      LogUtil.e("AudioManager::play audio error::$e");
    });
  }

  dispose() {
    _player.release();
    // _player.clear(Uri(path: AssetsPath.ORDER_AUDIO));
    // if (Platform.isAndroid) {
    //   _audioPlayerInstance.dispose();
    // }
    _instance = null;
  }
}
