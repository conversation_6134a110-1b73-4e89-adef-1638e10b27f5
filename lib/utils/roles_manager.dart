import 'package:connect/common/constants.dart';
import 'package:connect/data/user_entity.dart';

import 'connect_cache.dart';

class RolesManager {
  static isSwitchDriverRole(String roleId) {
    var user = ConnectCache.getUser()!;
    var roles = user.roles!.where((element) => element!.sId == roleId).toList();
    if (roles.length > 0) {
      UserRole role = roles.first!;
      return role.name!.contains('driver');
    }
    return true;
  }

  static isRestaurantManagerRole() {
    var user = ConnectCache.getUser()!;
    var roles = user.roles!
        .where((element) => element!.sId == ConnectCache.getCurrentRoleId())
        .toList();
    if (roles.length > 0) {
      UserRole role = roles.first!;
      return role.name == Constants.REGION_RESTAURANT;
    }
    return true;
  }

  static isDriverRole() {
    return isSwitchDriverRole(ConnectCache.getCurrentRoleId());
  }

  static bool hasSetupPayment() {
    var user = ConnectCache.getUser()!;
    if (user.stripe == null ||
        (user.stripe != null && user.stripe!.status != "succeed")) {
      return true;
    }
    return false;
  }

  static bool hasPaymentDashboard() {
    var user = ConnectCache.getUser()!;
    if (user.stripe != null &&
        user.stripe!.id != null &&
        user.stripe!.id!.isNotEmpty) {
      return true;
    }
    return false;
  }
}
