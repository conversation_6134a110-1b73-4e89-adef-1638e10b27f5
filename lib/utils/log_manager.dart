import 'dart:io';
import 'package:archive/archive_io.dart';
import 'package:connect/common/global.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_logs/flutter_logs.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'connect_cache.dart';
import 'dart:async';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'date_util.dart';
import 'device_manager.dart';
import 'package:provider/provider.dart';

/// persistent log
class LogManager {
  static const String TAG = "LogManager";

  factory LogManager() => _getInstance()!;

  static LogManager? get instance => _getInstance();

  static LogManager? _instance;

  LogManager._internal() {
    init();

    // Logs Exported Callback
    FlutterLogs.channel.setMethodCallHandler((call) async {
      LogUtil.v("call method ::${call.method}");
      if (call.method == 'logsExported') {
        // Contains file name of zip
        LogManager.instance!.log(
            TAG, "initState", "logsExported: ${call.arguments.toString()}");
        // Notify Future with value
        _completer.complete(call.arguments.toString());
      }
    });
  }

  static LogManager? _getInstance() {
    if (_instance == null) {
      _instance = LogManager._internal();
    }
    return _instance;
  }

  static const MethodChannel _channel =
      const MethodChannel('com.connect/print');

  void init() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      if (ConnectCache.isRelease()) FlutterLogs.setDebugLevel(0);

      await FlutterLogs.initLogs(
        logLevelsEnabled: [
          LogLevel.INFO,
          LogLevel.WARNING,
          LogLevel.ERROR,
          LogLevel.SEVERE
        ],
        timeStampFormat: TimeStampFormat.TIME_FORMAT_24_FULL,
        logsRetentionPeriodInDays: 5,
        directoryStructure: DirectoryStructure.FOR_DATE,
        logFileExtension: LogFileExtension.TXT,
        logsWriteDirectoryName: "connect",
        logsExportDirectoryName: "zip/exported",
        singleLogFileSize: 1,
        debugFileOperations: ConnectCache.isRelease() ? false : true,
        isDebuggable: ConnectCache.isRelease() ? false : true,
      );
    }
  }

  log(String tag, String subTag, String message) async {
    if (!ConnectCache.isRelease()) LogUtil.v("$tag,$message");

    if (defaultTargetPlatform == TargetPlatform.iOS) {
      var arguments = Map();
      arguments['tag'] = tag;
      arguments['subTag'] = subTag;
      arguments['message'] = message;

      await _channel.invokeMethod("persistentLog", arguments);
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      FlutterLogs.logInfo(tag, subTag, "${DateTime.now()} : $message");
    }
  }

  iosLogPath() async {
    return await _channel.invokeMethod("logPath");
  }

  uploadLog(bool isManual, {VoidCallback? error}) async {
    try {
      if (GlobalConfig.isWeb) {
        return;
      }

      ///后端没有设置该账号的autoLog,并且不是手动上传，则不用上传日志
      var autoLog = ConnectCache.getUser()?.autoLog;
      if ((autoLog == null || !autoLog) && !isManual) {
        return;
      }

      /// 如果手动点击上传日志，将不检查时间
      /// 如果离上次上传时间大于等于1天，则上传日志
      if (!isManual) {
        DateTime? sendLogTime =
            DateUtil.getDateTime(ConnectCache.getDateTime()!);

        var nowTime = DateTime.now();
        var difference = nowTime.difference(sendLogTime!).inDays;
        if (difference < 1) {
          return;
        }
      }

      ///记录上传时间
      ConnectCache.saveDateTime(DateTime.now().toString());

      /// device info
      await DeviceManager.writeDeviceInfo(ConnectCache.getVersion());

      if (defaultTargetPlatform == TargetPlatform.android) {
        await _android(isManual);
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        await _ios(isManual);
      }
    } catch (e) {
      if (error != null) error();
      ToastUtils.show("${e.toString()}");
      Sentry.captureException(
          "submit_exception::${e.toString()},userId::${ConnectCache.getUser()!.userId}");
    }
  }

  static Completer _completer = new Completer<String>();

  Future<String> exportAllLogs() async {
    FlutterLogs.exportLogs(exportType: ExportType.ALL);
    return _completer.future as FutureOr<String>;
  }

  _android(bool isManual) async {
    /// export and then get file reference
    await exportAllLogs().then((zipPath) async {
      Directory? externalDirectory = await getExternalStorageDirectory();

      if (externalDirectory == null) {
        throw Exception("can not find storage directory");
      }

      File file = File("${externalDirectory.path}/$zipPath");

      LogManager.instance!.log(TAG, "path", 'Path: \n${file.path.toString()}');

      if (file.existsSync()) {
        LogManager.instance!
            .log(TAG, "existsSync", 'Logs found and ready to export!');

        var filePath = file.path.toString();

        _submitToServer(filePath, isManual);
      } else {
        LogManager.instance!
            .log(TAG, "existsSync", "File not found in storage.");

        ToastUtils.show("File not found in storage.");
      }
    });
  }

  _ios(bool isManual) async {
    String path = await LogManager.instance!.iosLogPath();

    //compress the file into zip
    var fileDirectory = path.split("/connect.log")[0];

    var encoder = ZipFileEncoder();

    encoder.zipDirectory(Directory(fileDirectory));

    // _submitToServer(encoder.zip_path, isManual);

    // await _emailSubmit(path);
  }

  _submitToServer(String zipPath, bool isManual) async {
    var email = ConnectCache.getUser()?.email;

    var uint8list = await File(zipPath).readAsBytes();

    /// submit log to server
    var response = await GlobalConfig.context()
        .read<DriverModel>()
        .uploadLogFile(uint8list.toList(), zipPath, "${DateTime.now()}_$email");

    if (response != null && isManual) {
      ToastUtils.show(S.of(GlobalConfig.context()).connect_upload_success);
    }
  }

  _emailSubmit(String zipPath) async {
    //create email
    Email email = Email(
      body: S.of(GlobalConfig.context()).connect_log_feedback,
      subject: 'Connect Logs',
      recipients: ['<EMAIL>'],
      attachmentPaths: [zipPath],
      isHTML: false,
    );

    //send email
    await FlutterEmailSender.send(email);
  }
}
