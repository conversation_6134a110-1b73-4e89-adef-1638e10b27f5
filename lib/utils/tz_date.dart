import 'package:connect/utils/log_util.dart';
import 'package:date_format/date_format.dart';
import 'package:timezone/timezone.dart' as tz;

import 'format_utils.dart';

class TzDate {
  static String tzFrom(String timezone) {
    var location = tz.getLocation(timezone);
    tz.setLocalLocation(location);
    var tzNow = tz.TZDateTime.now(location);
    return formatDate(
            tzNow, [yyyy, '-', mm, '-', dd, 'T', "00", ':', "00", ':', "00"]) +
        FormatUtils.format(tzNow);
  }

  static String tzTo(String timezone) {
    var location = tz.getLocation(timezone);
    tz.setLocalLocation(location);
    var tzNow = tz.TZDateTime.now(location);
    return formatDate(
            tzNow, [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
        FormatUtils.format(tzNow);
  }

  //yesterday
  static String localFrom(DateTime date) {
    return formatDate(getOldDate(date, -1),
            [yyyy, '-', mm, '-', dd, 'T', "00", ':', "00", ':', "00"]) +
        FormatUtils.format(date);
  }

  //yesterday
  static String localTo(DateTime date) {
    return formatDate(getOldDate(date, -1),
            [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
        FormatUtils.format(date);
  }

  static DateTime getOldDate(DateTime startTime, int dayNumber) {
    var dateTime = new DateTime.fromMillisecondsSinceEpoch(
        startTime.millisecondsSinceEpoch + dayNumber * 24 * 60 * 60 * 1000);
    LogUtil.v("dateTime::$dateTime");
    return dateTime;
  }
}
