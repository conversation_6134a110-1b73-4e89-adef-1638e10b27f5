import 'dart:convert';
import 'dart:io';
import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/ui/home/<USER>/order_status_manager.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:date_format/date_format.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/services.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'connect_cache.dart';

class PrintManager {
  static const String TAG = "PrintManager";

  PrintManager._constructor();

  static const MethodChannel _channel =
      const MethodChannel('com.connect/print');

  static final PrintManager _instance = PrintManager._constructor();

  static PrintManager get instance {
    return _instance;
  }

  Future<bool> isSunmi() async {
    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      var androidDeviceInfo = await deviceInfoPlugin.androidInfo;
      if (androidDeviceInfo.brand != null &&
          androidDeviceInfo.brand.toLowerCase().contains(Constants.SUNMI)) {
        LogUtil.v("brand::${androidDeviceInfo.brand}");
        return true;
      }
    }
    return false;
  }

  sunmiPrint(OrdersEntity entity) async {
    LogManager.instance!.log(TAG, "",
        "sunmiPrint, userId::${ConnectCache.getUser()!.userId}, autoPrint::${ConnectCache.getAutoReceiveStatus()}");
    var arguments = Map<String, dynamic>();
    try {
      arguments["passCode"] = passCode(entity);
      arguments["orderNum"] = "#${entity.count}";
      arguments["restaurantNameEn"] =
          (entity.restaurant != null && entity.restaurant!.name != null)
              ? entity.restaurant!.name!.enUs
              : "";
      arguments["restaurantNameZh"] =
          (entity.restaurant != null && entity.restaurant!.name != null)
              ? entity.restaurant!.name!.zhCn
              : "";
      arguments["orderTime"] = formatDate(
          DateUtil.getDateTimeWitTimeZone(
              entity.createdAt!, entity.restaurant!.timezone)!,
          [mm, '-', dd, '-', yyyy, " ", HH, ':', nn, ':', ss]);

      var pickupTime = entity.pickupDateTime();
      if (pickupTime != null) {
        arguments["pickupTime"] = formatDate(
            pickupTime, [mm, '-', dd, '-', yyyy, " ", HH, ':', nn, ':', ss]);
      }

      arguments["note"] = entity.comments == null ? "" : entity.comments;
      arguments["food"] = mapToJson(entity);
      arguments["subTotal"] = entity.subtotal == null
          ? ""
          : ServerMultiLan.coinSymbol(
              entity.restaurant, "${(entity.subtotal! / 100).toString()}");
      arguments["tax"] = entity.fees != null
          ? ServerMultiLan.coinSymbol(
              entity.restaurant, "${(entity.fees!.tax!) / 100}")
          : "";
      if (OrderStatusManager.restaurantDeliverySelf(entity)) {
        arguments["tip"] = (entity.fees != null &&
                entity.fees!.tip != null &&
                entity.fees!.tip!.amount != null)
            ? ServerMultiLan.coinSymbol(
                entity.restaurant, "${(entity.fees!.tip!.amount)! / 100}")
            : "";
        arguments["delta"] = entity.fees == null
            ? ""
            : ServerMultiLan.coinSymbol(
                entity.restaurant, "${(entity.fees!.delta! / 100).toString()}");
        arguments["deliveryFees"] =
            (entity.fees != null && entity.fees!.delivery != null)
                ? ServerMultiLan.coinSymbol(
                    entity.restaurant, "${entity.fees!.delivery! / 100}")
                : "";
      }
      arguments["customerAddress"] =
          (entity.delivery != null && entity.delivery!.address != null)
              ? entity.delivery!.address!.addressWithUnit()
              : "";
      arguments["customerPhone"] =
          entity.customer != null ? _maskPhone(entity.customer!.phone) : "";
      arguments["orderId"] = entity.orderId;
      arguments["country"] = entity.restaurant!.address!.country;
      arguments["provider"] = "${OrderStatusManager.hasProvider(entity)}";
      arguments["specialType"] = "${OrderStatusManager.specialType(entity)}";

      await _channel.invokeMethod("print", {"connect_print": arguments});

      //tracking print info
      TrackingUtils.instance!
          .tracking(TConstants.R_PRINT_CLICK, value: arguments);
    } catch (e) {
      //print exception
      printException();
      LogManager.instance!.log(
          TAG, "", "sunmi::${arguments.toString()},exception::${e.toString()}");
      Sentry.captureException(
          "sunmi ::${arguments.toString()},exception::${e.toString()}");

      var params = Map<String, dynamic>();
      params["error"] = "${e.toString()}";
      TrackingUtils.instance!
          .tracking(TConstants.R_SUNMI_PRINT_ERROR, value: arguments);
    }
  }

  String _maskPhone(String? phone) {
    if (phone == null) return '';
    if (phone.length <= 3) return phone;

    if (phone.length >= 7) {
      String prefix = phone.substring(0, phone.length - 6);
      String suffix = phone.substring(phone.length - 3);
      String masked = prefix + "****" + suffix;
      return masked;
    }

    String prefix = phone.substring(0, 1);
    String suffix = phone.substring(phone.length - 3);
    String masked = prefix + "****" + suffix;
    return masked;
  }

  String passCode(OrdersEntity entity) {
    var mPassCode = "";
    var mPassCodeExt = "";
    if (entity.passcode != null && entity.passcode!.isNotEmpty) {
      mPassCode = "#${entity.passcode}";
    }
    if (entity.passcodeExt != null && entity.passcodeExt!.isNotEmpty) {
      mPassCodeExt = "-${entity.passcodeExt}";
    }
    return mPassCode + mPassCodeExt;
  }

  String mapToJson(OrdersEntity entity) {
    Map<String, int> foodMaps = {};

    entity.items!.forEach((element) {
      var jsonStr = jsonEncode(element);
      if (foodMaps[jsonStr] == null) {
        foodMaps[jsonStr] = 1;
      } else {
        foodMaps[jsonStr] = foodMaps[jsonStr]! + 1;
      }
    });

    var foodStr = jsonEncode(foodMaps);
    // LogUtil.v("foodStr::$foodStr");
    return foodStr;
  }

  void printException() {
    ConnectCache.saveAutoReceiveStatus(false);
    GlobalConfig.eventBus.fire(AutoConfirmed());
  }
}
