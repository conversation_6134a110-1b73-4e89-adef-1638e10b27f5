import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import 'package:connect/generated/l10n.dart';

import '../http/commom/http_uri.dart';
import '../http/http_manager.dart';
import 'connect_cache.dart';
import 'log_util.dart';

pushPermission() async {
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );
  if (settings.authorizationStatus == AuthorizationStatus.authorized) {
    LogUtil.d('User granted permission');
  } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
    LogUtil.d('User granted provisional permission');
  } else {
    LogUtil.d('User declined or has not accepted permission');
  }
  bool allowed =
      settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;

  if (allowed) {
    submitPushToken();
  }
}

submitPushToken() async {
  final fcmToken = await FirebaseMessaging.instance.getToken();
  LogUtil.d("fcmToken: $fcmToken");
  if (fcmToken != null) pushToken(fcmToken);
  FirebaseMessaging.instance.onTokenRefresh.listen((fcmToken) {
    LogUtil.d("fcmToken: $fcmToken");
    pushToken(fcmToken);
  }).onError((err) {
    // Error getting token.
    LogUtil.e("fcmToken error: $err");
  });
}

///submit pushToken to server
Future<Response?> pushToken(String token, {VoidCallback? callback}) async {
  print('fuckingpermission： $token');
  var userId = ConnectCache.getUser()?.userId;
  if (userId == null) return Future.value(null);
  String? url = sprintf(HttpUri.PUSH_TOKEN, [userId]);
  var data = Map<String, dynamic>();
  data["apn"] = [token];
  final response = await HttpManager.instance!
      .put(url, data: data, showMsg: false, withLoading: false);
  return response;
}

Future<void> showNotificationDialog(
  Widget tittle,
  Widget content,
  BuildContext context,
) async {
  if (ModalRoute.of(context)?.isCurrent == true) {
    showDialog<void>(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          title: tittle,
          content: content,
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(S.of(context).ok),
            )
          ],
        );
      },
    );
  }
}
