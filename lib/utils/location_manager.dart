import 'dart:async';
import 'dart:io';
import 'package:connect/common/global.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'dialog_utils.dart';
import 'log_manager.dart';
import 'log_util.dart';

class LocationManager {
  static const TAG = "LocationManager";
  static Position? _position;

  static Future<Position?> getCurrentPosition() async {
    try {
      if (Platform.isAndroid) {
        return await Geolocator.getCurrentPosition(timeLimit: Duration(seconds: 20));
      } else {
        if (_position == null) {
          startLocationListening();
          return await Geolocator.getCurrentPosition(timeLimit: Duration(seconds: 20));
        }
        return _position;
      }
    } catch (e) {
      var context = GlobalConfig.navigatorKey.currentContext!;
      final text = S.of(context).location_failed;
      DialogUtils.showAlert(context, Text(text), null);
      LogManager.instance!.log(TAG, "", S.of(context).location_failed);
    }

    return null;
  }

  static GeolocatorPlatform _geolocatorPlatform = GeolocatorPlatform.instance;
  static StreamSubscription<Position>? _positionStreamSubscription;

  ///ios can not get location immediately by getCurrentPosition,so we listening location when launch app
  static startLocationListening() async{
    if (Platform.isIOS) {
      var permissionStatus = await Geolocator.checkPermission();
      if (permissionStatus != LocationPermission.always) {
        LogManager.instance!.log(TAG, "permissionStatus", "${permissionStatus.toString()}");
        return;
      }
      if (_positionStreamSubscription == null) {
        var positionStream = _geolocatorPlatform.getPositionStream();
        _positionStreamSubscription = positionStream.handleError((error) {
          _positionStreamSubscription?.cancel();
          _positionStreamSubscription = null;
        }).listen((position){
          LogUtil.v("startLocationListening----------${position.latitude},${position.longitude}");
          _position = position;
        });
      }
    }
  }

  static cancelLocationListening(){
    if (_positionStreamSubscription != null) {
      _positionStreamSubscription?.cancel();
      _positionStreamSubscription = null;
    }
  }
}
