import 'package:connect/generated/json/orders_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';
import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/ui/home/<USER>/order_status_manager.dart';
import 'package:connect/utils/date_util.dart';
import 'package:timezone/timezone.dart' as tz;

@JsonSerializable()
class OrdersEntity {

	OrdersEntity();

	factory OrdersEntity.fromJson(Map<String, dynamic> json) => $OrdersEntityFromJson(json);

	Map<String, dynamic> toJson() => $OrdersEntityToJson(this);

  @JSONField(name: "_id")
  String? orderId;
  String? comments;
  num? count;
  String? passcode;
  String? passcodeExt;
  List<OrdersItem?>? items;
  OrdersFees? fees;
  OrdersRegion? region;
  OrdersCustomer? customer;
  OrdersRestaurant? restaurant;
  OrdersDelivery? delivery;
  OrdersDistribution? distribution;
  List<OrdersAdj?>? adj;
  OrdersAdjustments? adjustments;
  String? status;
  String? createdAt;
  String? updatedAt;
  String? confirmedAt;
  num? subtotal;
  bool? doubt;
  bool? isSelected = false;

  bool shouldShowDelivery() {
    bool showDelivery = true;

    if (this.delivery != null && this.delivery!.courier == null) {
      //not match courier
      showDelivery = false;
    }

    if (OrderStatusManager.cancelOrder(this)) {
      showDelivery = false;
    }

    if (OrderStatusManager.restaurantDeliverySelf(this) ||
        OrderStatusManager.customerPickupSelf(this)) {
      showDelivery = false;
    }
    return showDelivery;
  }

  String? createdAtTime() {
    if (createdAt != null) {
      var createdAtDate = DateUtil.getDateTime(createdAt!);
      if (createdAtDate != null && restaurant?.timezone != null) {
        var location = tz.getLocation(restaurant!.timezone!);
        var tzFromDate = tz.TZDateTime.from(createdAtDate, location);
        return tzFromDate.toString().split(".")[0];
      }
    }
    return null;
  }

  String? pickupTime() {
    if (!OrderStatusManager.customerPickupSelf(this)) {
      return null;
    }

    var pickupWindow = restaurant?.delivery?.window;
    var start = pickupWindow?.start;
    if (start != null) {
      var startDate = DateUtil.getDateTime(start);
      if (startDate != null && restaurant?.timezone != null) {
        var location = tz.getLocation(restaurant!.timezone!);
        var tzFromDate = tz.TZDateTime.from(startDate, location);
        return tzFromDate.toString().split(".")[0];
      }
    }
    return null;
  }

  DateTime? pickupDateTime() {
    var pickupWindow = restaurant?.delivery?.window;
    var start = pickupWindow?.start;
    if (start != null) {
      var startDate = DateUtil.getDateTime(start);
      if (startDate != null && restaurant?.timezone != null) {
        var location = tz.getLocation(restaurant!.timezone!);
        return tz.TZDateTime.from(startDate, location);
      }
      return startDate?.toLocal();
    }
    return null;
  }
}

@JsonSerializable()
class OrdersItem {

	OrdersItem();

	factory OrdersItem.fromJson(Map<String, dynamic> json) => $OrdersItemFromJson(json);

	Map<String, dynamic> toJson() => $OrdersItemToJson(this);

  @JSONField(name: "_id")
  String? foodId;
  MultiNameEntity? name;
  List<OrdersItemOptions?>? options;
  num? price;
  String code = "";
}

@JsonSerializable()
class OrdersDistribution {

	OrdersDistribution();

	factory OrdersDistribution.fromJson(Map<String, dynamic> json) => $OrdersDistributionFromJson(json);

	Map<String, dynamic> toJson() => $OrdersDistributionToJson(this);

  num? restaurant;
}

@JsonSerializable()
class OrdersItemOptions {

	OrdersItemOptions();

	factory OrdersItemOptions.fromJson(Map<String, dynamic> json) => $OrdersItemOptionsFromJson(json);

	Map<String, dynamic> toJson() => $OrdersItemOptionsToJson(this);

  @JSONField(name: "_id")
  String? optionId;
  MultiNameEntity? name;
  num? price;
}

@JsonSerializable()
class OrdersFees {

	OrdersFees();

	factory OrdersFees.fromJson(Map<String, dynamic> json) => $OrdersFeesFromJson(json);

	Map<String, dynamic> toJson() => $OrdersFeesToJson(this);

  OrdersFeesTip? tip;
  num? delta;
  num? tax;
  num? service;
  num? delivery;
  num? credit;
}

@JsonSerializable()
class OrdersFeesTip {

	OrdersFeesTip();

	factory OrdersFeesTip.fromJson(Map<String, dynamic> json) => $OrdersFeesTipFromJson(json);

	Map<String, dynamic> toJson() => $OrdersFeesTipToJson(this);

  bool? cash;
  num? amount;
}

@JsonSerializable()
class OrdersRegion {

	OrdersRegion();

	factory OrdersRegion.fromJson(Map<String, dynamic> json) => $OrdersRegionFromJson(json);

	Map<String, dynamic> toJson() => $OrdersRegionToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class OrdersCustomer {

	OrdersCustomer();

	factory OrdersCustomer.fromJson(Map<String, dynamic> json) => $OrdersCustomerFromJson(json);

	Map<String, dynamic> toJson() => $OrdersCustomerToJson(this);

  String? phone;
  num? orderCount;
  String? language;
}

@JsonSerializable()
class OrdersRestaurant {

	OrdersRestaurant();

	factory OrdersRestaurant.fromJson(Map<String, dynamic> json) => $OrdersRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $OrdersRestaurantToJson(this);

  @JSONField(name: "_id")
  String? restaurantId;
  MultiNameEntity? name;
  bool? manual;
  String? timezone;
  OrdersRestaurantAddress? address;
  OrdersRestaurantDelivery? delivery;
}

@JsonSerializable()
class OrdersRestaurantAddress {

	OrdersRestaurantAddress();

	factory OrdersRestaurantAddress.fromJson(Map<String, dynamic> json) => $OrdersRestaurantAddressFromJson(json);

	Map<String, dynamic> toJson() => $OrdersRestaurantAddressToJson(this);

  String? number;
  String? street;
  String? unit;
  String? city;
  String? state;
  String? country;
  String? zipcode;
  String? formatted;
  OrdersRestaurantAddressLocation? location;
  String? note;
}

@JsonSerializable()
class OrdersRestaurantAddressLocation     {

	OrdersRestaurantAddressLocation();

	factory OrdersRestaurantAddressLocation.fromJson(Map<String, dynamic> json) => $OrdersRestaurantAddressLocationFromJson(json);

	Map<String, dynamic> toJson() => $OrdersRestaurantAddressLocationToJson(this);

  String? type;
  List<double>? coordinates;
}

@JsonSerializable()
class OrdersRestaurantDelivery {

	OrdersRestaurantDelivery();

	factory OrdersRestaurantDelivery.fromJson(Map<String, dynamic> json) => $OrdersRestaurantDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $OrdersRestaurantDeliveryToJson(this);

  String? provider;
  num? prepare;
  OrdersRestaurantDeliveryFee? fee;
  List<OrdersRestaurantDeliveryFee?>? fees;
  num? minimum;
  num? prepareTime;
  OrdersRestaurantDeliveryEstimate? estimate;
  OrdersRestaurantDeliveryWindow? window;
}

@JsonSerializable()
class OrdersRestaurantDeliveryWindow     {

	OrdersRestaurantDeliveryWindow();

	factory OrdersRestaurantDeliveryWindow.fromJson(Map<String, dynamic> json) => $OrdersRestaurantDeliveryWindowFromJson(json);

	Map<String, dynamic> toJson() => $OrdersRestaurantDeliveryWindowToJson(this);

  String? end;
  MultiNameEntity? formatted;
  String? start;
}

@JsonSerializable()
class OrdersRestaurantDeliveryFee     {

	OrdersRestaurantDeliveryFee();

	factory OrdersRestaurantDeliveryFee.fromJson(Map<String, dynamic> json) => $OrdersRestaurantDeliveryFeeFromJson(json);

	Map<String, dynamic> toJson() => $OrdersRestaurantDeliveryFeeToJson(this);

  num? factor;
  num? flat;
}

@JsonSerializable()
class OrdersRestaurantDeliveryFeesAdjustments     {

	OrdersRestaurantDeliveryFeesAdjustments();

	factory OrdersRestaurantDeliveryFeesAdjustments.fromJson(Map<String, dynamic> json) => $OrdersRestaurantDeliveryFeesAdjustmentsFromJson(json);

	Map<String, dynamic> toJson() => $OrdersRestaurantDeliveryFeesAdjustmentsToJson(this);

  num? customer;
  String? reason;
  num? restaurant;
  num? ricepo;
}

@JsonSerializable()
class OrdersRestaurantDeliveryEstimate     {

	OrdersRestaurantDeliveryEstimate();

	factory OrdersRestaurantDeliveryEstimate.fromJson(Map<String, dynamic> json) => $OrdersRestaurantDeliveryEstimateFromJson(json);

	Map<String, dynamic> toJson() => $OrdersRestaurantDeliveryEstimateToJson(this);

  String? deadline;
  num? max;
  num? min;
}

@JsonSerializable()
class OrdersDelivery {

	OrdersDelivery();

	factory OrdersDelivery.fromJson(Map<String, dynamic> json) => $OrdersDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $OrdersDeliveryToJson(this);

  @JSONField(name: "_id")
  dynamic nId;
  String? provider;
  OrdersDeliveryCourier? courier;
  String? status;
  OrdersDeliveryStats? stats;
  num? time;
  String? finishAt;
  OrdersDeliveryEstimate? estimate;
  OrdersDeliveryAddress? address;
}

@JsonSerializable()
class OrdersDeliveryStats {

	OrdersDeliveryStats();

	factory OrdersDeliveryStats.fromJson(Map<String, dynamic> json) => $OrdersDeliveryStatsFromJson(json);

	Map<String, dynamic> toJson() => $OrdersDeliveryStatsToJson(this);

  @JSONField(name: "en-route-to-pickup")
  num? enRouteToPickup;
  @JSONField(name: "at-pickup")
  num? atPickup;
  @JSONField(name: "min-pickup-completed")
  num? minPickupCompleted;
  @JSONField(name: "pickup-completed")
  num? pickupCompleted;
  @JSONField(name: "en-route-to-dropoff")
  num? enRouteToDropoff;
}

@JsonSerializable()
class OrdersDeliveryAddress {

	OrdersDeliveryAddress();

	factory OrdersDeliveryAddress.fromJson(Map<String, dynamic> json) => $OrdersDeliveryAddressFromJson(json);

	Map<String, dynamic> toJson() => $OrdersDeliveryAddressToJson(this);

  String? city;
  String? country;
  String? formatted;
  String? unit;
  OrdersDeliveryAddressLocation? location;

  String addressWithUnit() {
    String result = formatted ?? "";
    if (unit != null && unit!.isNotEmpty) {
      result += " #$unit";
    }
    return result;
  }
}

@JsonSerializable()
class OrdersDeliveryAddressLocation     {

	OrdersDeliveryAddressLocation();

	factory OrdersDeliveryAddressLocation.fromJson(Map<String, dynamic> json) => $OrdersDeliveryAddressLocationFromJson(json);

	Map<String, dynamic> toJson() => $OrdersDeliveryAddressLocationToJson(this);

  List<double>? coordinates;
  String? type;
}

@JsonSerializable()
class OrdersDeliveryCourier {

	OrdersDeliveryCourier();

	factory OrdersDeliveryCourier.fromJson(Map<String, dynamic> json) => $OrdersDeliveryCourierFromJson(json);

	Map<String, dynamic> toJson() => $OrdersDeliveryCourierToJson(this);

  @JSONField(name: "_id")
  String? cid;
  String? email;
  String? phone;
}

@JsonSerializable()
class OrdersDeliveryEstimate {

	OrdersDeliveryEstimate();

	factory OrdersDeliveryEstimate.fromJson(Map<String, dynamic> json) => $OrdersDeliveryEstimateFromJson(json);

	Map<String, dynamic> toJson() => $OrdersDeliveryEstimateToJson(this);

  num? max;
  num? min;
}

@JsonSerializable()
class OrdersAdj {

	OrdersAdj();

	factory OrdersAdj.fromJson(Map<String, dynamic> json) => $OrdersAdjFromJson(json);

	Map<String, dynamic> toJson() => $OrdersAdjToJson(this);

  num? customer;
  String? reason;
  num? restaurant;
  num? ricepo;
}

@JsonSerializable()
class OrdersAdjustments {

	OrdersAdjustments();

	factory OrdersAdjustments.fromJson(Map<String, dynamic> json) => $OrdersAdjustmentsFromJson(json);

	Map<String, dynamic> toJson() => $OrdersAdjustmentsToJson(this);

  num? customer;
  String? reason;
  num? restaurant;
  num? ricepo;
}
