import 'package:connect/generated/json/history_total_report_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class HistoryTotalReportEntity {

	HistoryTotalReportEntity();

	factory HistoryTotalReportEntity.fromJson(Map<String, dynamic> json) => $HistoryTotalReportEntityFromJson(json);

	Map<String, dynamic> toJson() => $HistoryTotalReportEntityToJson(this);

  @JSONField(name: "_id")
  HistoryTotalReportId? hId;
  double? number;
  double? subtotal;
  HistoryTotalReportCost? cost;
  HistoryTotalReportFees? fees;
  double? total;
  HistoryTotalReportCommission? commission;
  HistoryTotalReportDistribution? distribution;
  HistoryTotalReportAdjustments? adjustments;
}

@JsonSerializable()
class HistoryTotalReportId {

	HistoryTotalReportId();

	factory HistoryTotalReportId.fromJson(Map<String, dynamic> json) => $HistoryTotalReportIdFromJson(json);

	Map<String, dynamic> toJson() => $HistoryTotalReportIdToJson(this);

  bool? delivery;
  bool? provider;
}

@JsonSerializable()
class HistoryTotalReportCost {

	HistoryTotalReportCost();

	factory HistoryTotalReportCost.fromJson(Map<String, dynamic> json) => $HistoryTotalReportCostFromJson(json);

	Map<String, dynamic> toJson() => $HistoryTotalReportCostToJson(this);

  double? subtotal;
  double? tax;
}

@JsonSerializable()
class HistoryTotalReportFees {

	HistoryTotalReportFees();

	factory HistoryTotalReportFees.fromJson(Map<String, dynamic> json) => $HistoryTotalReportFeesFromJson(json);

	Map<String, dynamic> toJson() => $HistoryTotalReportFeesToJson(this);

  double? delivery;
  HistoryTotalReportFeesTip? tip;
  double? tax;
  double? service;
  double? credit;
  double? delta;
}

@JsonSerializable()
class HistoryTotalReportFeesTip {

	HistoryTotalReportFeesTip();

	factory HistoryTotalReportFeesTip.fromJson(Map<String, dynamic> json) => $HistoryTotalReportFeesTipFromJson(json);

	Map<String, dynamic> toJson() => $HistoryTotalReportFeesTipToJson(this);

  double? amount;
}

@JsonSerializable()
class HistoryTotalReportCommission     {

	HistoryTotalReportCommission();

	factory HistoryTotalReportCommission.fromJson(Map<String, dynamic> json) => $HistoryTotalReportCommissionFromJson(json);

	Map<String, dynamic> toJson() => $HistoryTotalReportCommissionToJson(this);

  double? subtotal;
  double? total;
  double? service;
}

@JsonSerializable()
class HistoryTotalReportDistribution     {

	HistoryTotalReportDistribution();

	factory HistoryTotalReportDistribution.fromJson(Map<String, dynamic> json) => $HistoryTotalReportDistributionFromJson(json);

	Map<String, dynamic> toJson() => $HistoryTotalReportDistributionToJson(this);

  double? restaurant;
}

@JsonSerializable()
class HistoryTotalReportAdjustments     {

	HistoryTotalReportAdjustments();

	factory HistoryTotalReportAdjustments.fromJson(Map<String, dynamic> json) => $HistoryTotalReportAdjustmentsFromJson(json);

	Map<String, dynamic> toJson() => $HistoryTotalReportAdjustmentsToJson(this);

  double? restaurant;
}
