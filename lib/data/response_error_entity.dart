import 'package:connect/generated/json/base/json_field.dart';
import 'package:connect/generated/json/response_error_entity.g.dart';

import 'dart:io';


@JsonSerializable()
class ResponseErrorEntity extends IOException
    {

	ResponseErrorEntity();

	factory ResponseErrorEntity.fromJson(Map<String, dynamic> json) => $ResponseErrorEntityFromJson(json);

	Map<String, dynamic> toJson() => $ResponseErrorEntityToJson(this);

  String? code;
  String? message;
  ResponseErrorDetails? details;
  String? error;
}

@JsonSerializable()
class ResponseErrorDetails {

	ResponseErrorDetails();

	factory ResponseErrorDetails.fromJson(Map<String, dynamic> json) => $ResponseErrorDetailsFromJson(json);

	Map<String, dynamic> toJson() => $ResponseErrorDetailsToJson(this);

  String? skipSentry;
}
