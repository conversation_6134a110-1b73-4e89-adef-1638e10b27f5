import 'package:connect/generated/json/rest_menu_toggle_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class RestMenuToggleEntity {

	RestMenuToggleEntity();

	factory RestMenuToggleEntity.fromJson(Map<String, dynamic> json) => $RestMenuToggleEntityFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuToggleEntityToJson(this);

  @JSONField(name: "_id")
  String? foodId;
  bool? available;
}
