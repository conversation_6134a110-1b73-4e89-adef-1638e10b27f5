import 'package:connect/generated/json/user_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';
import "package:connect/ext/ext.dart";

@JsonSerializable()
class UserEntity {

	UserEntity();

	factory UserEntity.fromJson(Map<String, dynamic> json) => $UserEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserEntityToJson(this);

  @JSONField(name: "_id")
  String? userId;
  String? email;
  List<UserRole?>? roles;
  String? createdAt;
  UserCommission? commission;
  UserStripe? stripe;
  bool? onCall;
  String? phone;
  String? regionCode;
  String? language;
  String? token;
  String? loginType;
  bool? autoLog;
}

@JsonSerializable()
class UserRole {

	UserRole();

	factory UserRole.fromJson(Map<String, dynamic> json) => $UserRoleFromJson(json);

	Map<String, dynamic> toJson() => $UserRoleToJson(this);

  String? scope;
  String? name;
  String? description;
  @JSONField(name: "_id")
  String? sId;
  String? timezone;
  bool? isSelected = false;

  String nameAndDesc() {
    String temp = "";
    String nameAndDesc = "";
    if (name != null) {
      var split = name!.split(".");
      var first = split[0].toString().capitalize();
      var last = split[1].toString().capitalize();
      temp = "$first $last";
    }
    bool descEmpty = description == null || description!.isEmpty;
    nameAndDesc = descEmpty ? "$temp" : "$temp - $description";
    return nameAndDesc;
  }
}

@JsonSerializable()
class UserStripe {

	UserStripe();

	factory UserStripe.fromJson(Map<String, dynamic> json) => $UserStripeFromJson(json);

	Map<String, dynamic> toJson() => $UserStripeToJson(this);

  String? id;
  bool? onboarding;
  String? status;
}

@JsonSerializable()
class UserCommission {

	UserCommission();

	factory UserCommission.fromJson(Map<String, dynamic> json) => $UserCommissionFromJson(json);

	Map<String, dynamic> toJson() => $UserCommissionToJson(this);

  num? flat;
  num? factor;
}
