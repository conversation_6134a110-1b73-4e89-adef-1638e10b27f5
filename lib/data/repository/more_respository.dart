import 'package:connect/generated/l10n.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/Quote.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:dio/dio.dart';
import 'package:sprintf/sprintf.dart';

class MoreRepository {

  static Future updatePwd(String pwd) async {
    var data = Map<String, dynamic>();
    data["password"] = pwd;
    var userId = ConnectCache.getUser()!.userId;
    LogUtil.v("userId::$userId");
    var url = sprintf(HttpUri.UPDATE_PASSWORD, [userId]);
    return await HttpManager.instance!.put(url, data: data, withLoading: true);
  }

  static Future evaluateDelivery({
    required Quote data,
    String? countryCode,
    required Function(String) onSuccess,
    required Function(String) onError,
    Function(Map<String,dynamic>)? onNext,
  }) async {
    var id = ConnectCache.getCurrentRole()?.scope;
    if (id?.isNotEmpty ?? false) {
      var url = HttpUri.ON_DEMNAD_DELIVERY_QUOTE.replaceAll(
          HttpUri.REST_ID_HOLDER,
          id!
      );
      return await HttpManager.instance!.post(url,
          params: data.toJson(), withLoading: true
      ).then((value) {
        if(value.isEmpty()) {
          onError(S.current.alert_out_of_range);
          return;
        }
        onNext?.call(value?.data);
        var fee = (
            (value?.data as Map<String, dynamic>)["expenses"] as Map<String, dynamic>)["delivery"];
        var result = (fee as Map<String, dynamic>)["delivery"];
        onSuccess(
            "${sprintf(S.current.delivery_cost,
                ["${ServerMultiLan.coinSymbolCountry(countryCode, result / 100)}"])}"
        );
      });
    }
  }

  static Future placeOrder(CreateOrder data, Function() onSuccess) async {
    var id = ConnectCache.getCurrentRole()?.scope;
    if (id?.isNotEmpty ?? false) {
      var url = HttpUri.ON_DEMAND_DELIVERY_ORDER.replaceAll(
          HttpUri.REST_ID_HOLDER,
          id!
      );
      return await HttpManager.instance!.post(url,
          params: data.toJson(), withLoading: true
      ).then((value) {
        if(value.isSuccess()) onSuccess();
      }).onError((error, stackTrace){
        LogUtil.e(error);
      });
    }
  }
}

extension ResponseExtension on Response? {
  isSuccess() =>
      this != null &&
      this?.statusCode != null &&
      this!.statusCode! >= 200 &&
      this!.statusCode! < 300;
  isEmpty() => isSuccess() && (this?.data?.toString().isEmpty??true);
}
