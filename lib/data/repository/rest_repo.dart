import 'package:connect/data/rest_entity.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:dio/dio.dart';
import 'package:sprintf/sprintf.dart';

class UIRestaurant {
  String? country;
  bool? enableConnect;
  String? area;
  UIRestaurant({required this.country, required this.enableConnect, this.area});
}

class RestaurantRepo {
  static Future<UIRestaurant?> restaurantDetails() async {
    var restId = ConnectCache.getCurrentRole()!.scope;
    var url = sprintf(HttpUri.RESTAURANTS_DETAILS, ["$restId"]);
    RestEntity? restEntity;
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "rest";
    Options requestOptions = new Options(extra: opExtras);
    var response = await HttpManager.instance!
        .get(url, option: requestOptions, withLoading: false);
    if (response != null) {
      restEntity = JsonConvert.fromJsonAsT<RestEntity>(response.data);
    }
    final bifrost = (response?.data as Map<String, dynamic>)["bifrost"];
    final enableConnect = (bifrost as Map<String, dynamic>)["enableConnect"];
    return UIRestaurant(
        country: restEntity?.address?.country, enableConnect: enableConnect);
  }
}
