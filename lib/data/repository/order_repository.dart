import 'dart:async';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_error.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:dio/dio.dart';

import '../user_entity.dart';

typedef Success<T> = Function(T data);
typedef Error<T, S> = Function(int code, String msg);

class OrderRepository {
  static List<String> listParams = [
    "count",
    "passcode",
    "passcodeExt",
    "status",
    "createdAt",
    "updatedAt",
    "confirmedAt",
    "adjustments",
    "restaurant._id",
    "restaurant.name",
    "region._id",
    "bundle",
    "restRating",
    "doubt",
    "fees",
    "subtotal",
    "restaurant.manual",
    "restaurant.phone",
    "restaurant.address",
    "restaurant.delivery",
    "restaurant.timezone",
    "restaurant.bundle",
    "restaurant.bundles",
    "delivery.estimate",
    "delivery.address",
    "delivery.stats",
    "delivery.time",
    "delivery.scheduledAt",
    "delivery.finishAt",
    "delivery.provider",
    "delivery.status",
    "delivery.courier",
    "delivery.estCourier",
    "delivery._id",
    "delivery.restArriveAt",
    "customer.orderCount",
    "customer.phone",
    "customer.language",
    "customer.subscription",
    "comments",
    "items.name",
    "items.code",
    "items.price",
    "items.options.name",
    "items.options.price",
    "distribution"
  ];

  static Future requestOrder(BuildContext context,
      {String? text,
      String? from,
      String? to,
      int? page,
      bool showMsg = true,
      bool isShowLoading = false,
      bool option = false,
      Success? success,
      Error? error,
      ValueChanged? callback}) async {
    var params = Map<String, dynamic>();
    params["f"] = listParams;
    if (text != null && text.isNotEmpty) {
      params["q"] = text;
    }
    if (from != null && from.isNotEmpty) {
      params["from"] = from;
    }
    if (to != null && to.isNotEmpty) {
      params["to"] = to;
    }
    if (page != null && page != 0) {
      params["page"] = page.toString();
    }

    Options? requestOptions;
    if (option) {
      var opExtras = Map<String, dynamic>();
      opExtras["type"] = "restaurant";
      requestOptions = new Options(extra: opExtras);
    }

    UserRole? role = ConnectCache.getCurrentRole();

    if (role == null) {
      error!(HttpError.UNKNOWN, "roles collection is null");
      return;
    }

    var restaurantId = role.scope;
    // LogUtil.v("restaurantId::$restaurantId");
    //may cause cannot convert ID parameter to ObjectId if restaurantId is null
    if (restaurantId == null) {
      error!(HttpError.UNKNOWN, "restaurant_id must not be null");
      return;
    }
    var orderUri =
        HttpUri.ORDERS_LIST.replaceAll("{restaurant_id}", restaurantId);
    return await HttpManager.instance!.getUri(orderUri,
        params: params,
        option: requestOptions,
        showMsg: showMsg,
        withLoading: isShowLoading);
  }

  static Future requestConfirmOrder(String restaurantId, String orderId,
      {int prepare = 0, ValueChanged? error}) async {
    var data = Map<String, dynamic>();
    data["status"] = "confirmed";
    if (prepare == 0) {
      data["prepare"] = int.parse(ConnectCache.getPrepareTime()!);
    } else {
      data["prepare"] = prepare;
    }
    LogUtil.v("prepare::$prepare");
    var confirmUrl = HttpUri.ORDERS_STATUS
        .replaceAll("{restaurant_id}", restaurantId)
        .replaceAll("{order_id}", orderId);
    // LogUtil.v("restaurantId::$restaurantId");
    // LogUtil.v("orderId::$orderId");
    return await HttpManager.instance!
        .put(confirmUrl, data: data, withLoading: false, callback: error);
  }

  static Future requestReady(String restaurantId, String orderId,
      {int bags = 0, ValueChanged? error}) async {
    var data = Map<String, dynamic>();
    data["ready"] = true;
    if (bags > 0) {
      data["bags"] = bags;
    }
    var readyUrl = HttpUri.ORDERS_READY
        .replaceAll("{restaurant_id}", restaurantId)
        .replaceAll("{order_id}", orderId);
    return await HttpManager.instance!
        .put(readyUrl, data: data, withLoading: false, callback: error);
  }
}
