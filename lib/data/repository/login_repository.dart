import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/user_entity.dart';
import 'package:connect/driver/driver_home_route.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>';
import 'package:connect/ui/login/login_route.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/roles_manager.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import '../../http/commom/http_uri.dart';
import '../../http/http_manager.dart';

class LoginRepository {
  static Future requestSmsCode(String phone) async {
    var params = Map<String, dynamic>();
    params["phone"] = phone;
    await HttpManager.instance!.post(HttpUri.SMS_CODE, params: params);
  }

  static renewToken(BuildContext context) async {
    await HttpManager.instance!
        .get(HttpUri.LOGIN_WITH_PASSWORD, showMsg: false, withLoading: false)
        .then((value) {
      if (value != null) {
        _handleData(context, value.data, "");
      }
    });
  }

  static loginWithPassword(
      BuildContext context, String email, String password) async {
    var params = Map<String, dynamic>();
    params["email"] = email;
    params["password"] = password;

    await HttpManager.instance!
        .post(HttpUri.LOGIN_WITH_PASSWORD, params: params, callback: (e) {
      GlobalConfig.eventBus.fire(LoginPasswordEvent());

      var params = Map<String, dynamic>();
      params["failReason"] = "${e.toString()}";
      params["loginType"] = "password";
      params["userName"] = "$email";
      TrackingUtils.instance!.tracking(TConstants.log_in_fail, value: params);
    }).then((value) {
      if (value != null) {
        _handleData(context, value.data, Constants.PASSWORD);
      }
    }).catchError((err) {});
  }

  static loginWithPhone(
      BuildContext context, String phone, String vcode) async {
    var params = Map<String, dynamic>();
    params["phone"] = phone;
    params["vcode"] = vcode;

    await HttpManager.instance!.post(HttpUri.LOGIN_WITH_PHONE, params: params,
        callback: (e) {
      //login failed
      GlobalConfig.eventBus.fire(LoginPhoneEvent(true));

      var params = Map<String, dynamic>();
      params["failReason"] = "${e.toString()}";
      params["loginType"] = "phone";
      params["userName"] = "$phone";
      TrackingUtils.instance!.tracking(TConstants.log_in_fail, value: params);
    }).then((value) {
      if (value != null) {
        _handleData(context, value.data, Constants.PHONE);
      }
    }).catchError((err) {});
  }

  static void _handleData(BuildContext context, dynamic data, String type) {
    UserEntity? userEntity = JsonConvert.fromJsonAsT<UserEntity>(data);

    //check local role whether in backend data when reopen app, because connect backend may delete account role
    if (type.isEmpty) {
      //check whether switched role
      var currentRoleId = ConnectCache.getCurrentRoleId2();
      if (currentRoleId?.isNotEmpty == true) {
        //role switched before
        var matchRoles = userEntity?.roles
            ?.where((element) => element?.sId == currentRoleId)
            .toList();
        if (matchRoles?.length == 0) {
          //not match
          MessageDialog.messageAlert(S.of(context).connect_login_again, ok: () {
            ConnectCache.clearCurrentRoleId();
            ConnectCache.clearToken();
            Navigator.pushNamedAndRemoveUntil(
                GlobalConfig.navigatorKey.currentContext!,
                LoginRoute.tag,
                (route) => false);
          });

          return;
        }
      }
    }

    if (type.isNotEmpty) {
      userEntity?.loginType = type;
    } else {
      userEntity?.loginType = ConnectCache.getUser()?.loginType;
    }
    ConnectCache.saveUser(userEntity);
    //check and storage if the driver is online
    if (userEntity?.onCall != null) {
      ConnectCache.saveOnline(userEntity!.onCall!);
    }

    if (type.isNotEmpty) {
      Navigator.pushReplacementNamed(context,
          RolesManager.isDriverRole() ? DriverHomeRoute.tag : HomeRoute.tag);
    }
  }
}
