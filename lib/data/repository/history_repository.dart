import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/format_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:date_format/date_format.dart';
import '../../http/commom/http_uri.dart';
import '../../http/http_manager.dart';
import '../user_entity.dart';

typedef Error<T, S> = Function(int code, String msg);

class HistoryRepository {
  static Future historyReport() async {
    UserRole? role = ConnectCache.getCurrentRole();

    var restaurantId = role?.scope;

    LogUtil.v("restaurantId::$restaurantId");
    if (restaurantId == null) {
      return;
    }
    var params = Map<String, dynamic>();
    params["from"] = "2014-01-01T00:00:00+08:00";
    params["to"] = formatDate(DateTime.now(),
            [yyyy, '-', mm, '-', dd, 'T', HH, ':', nn, ':', ss]) +
        FormatUtils.format(DateTime.now());
    var url = HttpUri.REPORT.replaceAll("{restaurant_id}", restaurantId);
    return await HttpManager.instance!
        .get(url, params: params, withLoading: false);
  }
}
