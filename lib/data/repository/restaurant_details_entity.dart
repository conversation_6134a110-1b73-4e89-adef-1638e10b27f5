import 'package:connect/generated/json/base/json_field.dart';
import 'package:connect/generated/json/restaurant_details_entity.g.dart';


@JsonSerializable()
class RestaurantDetailsEntity {

	RestaurantDetailsEntity();

	factory RestaurantDetailsEntity.fromJson(Map<String, dynamic> json) => $RestaurantDetailsEntityFromJson(json);

	Map<String, dynamic> toJson() => $RestaurantDetailsEntityToJson(this);

  RestaurantDetailsAddress? address;
  String? timezone;
  RestaurantDetailsDelivery? delivery;
}

@JsonSerializable()
class RestaurantDetailsAddress {

	RestaurantDetailsAddress();

	factory RestaurantDetailsAddress.fromJson(Map<String, dynamic> json) => $RestaurantDetailsAddressFromJson(json);

	Map<String, dynamic> toJson() => $RestaurantDetailsAddressToJson(this);

  String? country;
}

@JsonSerializable()
class RestaurantDetailsDelivery {

	RestaurantDetailsDelivery();

	factory RestaurantDetailsDelivery.fromJson(Map<String, dynamic> json) => $RestaurantDetailsDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $RestaurantDetailsDeliveryToJson(this);

  String? provider;
}
