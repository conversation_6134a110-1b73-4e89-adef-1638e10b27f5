import 'package:connect/data/repository/restaurant_details_entity.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:dio/dio.dart';
import 'package:sprintf/sprintf.dart';
import '../summary_report_v1_entity.dart';
import '../../generated/json/base/json_convert_content.dart';
import '../../http/commom/http_uri.dart';
import '../../http/http_manager.dart';
import '../orders_entity.dart';
import '../summary_report_entity.dart';

class ReportRepository {
  static List<String> listParams = [
    "count",
    "passcode",
    "passcodeExt",
    "status",
    "createdAt",
    "updatedAt",
    "confirmedAt",
    "adjustments",
    "restaurant._id",
    "restaurant.name",
    "region._id",
    "bundle",
    "restRating",
    "doubt"
  ];

  static Future<RestaurantDetailsEntity?> restaurantDetails(
      String? restId) async {
    String? url = sprintf(HttpUri.RESTAURANTS_DETAILS, ["$restId"]);
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "report";
    Options requestOptions = new Options(extra: opExtras);
    var response = await HttpManager.instance!
        .getUri(url, option: requestOptions, withLoading: false);
    // LogUtil.v("response::+${response.toString()}");
    if (response != null) {
      return JsonConvert.fromJsonAsT<RestaurantDetailsEntity>(response.data);
    }
    return null;
  }

  static Future<List<SummaryReportEntity>> report(
      String restId, String from, String to,
      {loading = false}) async {
    var params = Map<String, dynamic>();
    params["from"] = from;
    params["to"] = to;
    var url = HttpUri.REPORT.replaceAll("{restaurant_id}", restId);
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "report";
    Options requestOptions = new Options(extra: opExtras);
    var response = await HttpManager.instance!
        .get(url, params: params, option: requestOptions, withLoading: loading);
    if (response != null) {
      return JsonConvert.fromJsonAsT<List<SummaryReportEntity>>(response.data) ?? [];
    }
    return [];
  }

  static Future<List<SummaryReportV1Entity>> reportV1(
      String restId, String from, String to,
      {loading = false}) async {
    var params = Map<String, dynamic>();
    params["from"] = from;
    params["to"] = to;
    var url = HttpUri.REPORT.replaceAll("{restaurant_id}", restId);
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "report";
    Options requestOptions = new Options(extra: opExtras);
    var response = await HttpManager.instance!
        .get(url, params: params, option: requestOptions, withLoading: loading);
    if (response != null) {
      return response.data
          .map<SummaryReportV1Entity>((e) => SummaryReportV1Entity.fromJson(e))
          .toList() as List<SummaryReportV1Entity>;
    }
    return [];
  }

  static Future<List<OrdersEntity>> adjustments(
      String restId, String from, String to,
      {int page = 1, loading = false}) async {
    var params = Map<String, dynamic>();
    params["f"] = listParams;
    params["from"] = from;
    params["to"] = to;
    params["page"] = page.toString();
    params["recipient"] = "restaurant";
    var url = HttpUri.ORDERS_LIST.replaceAll("{restaurant_id}", restId);
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "report";
    Options requestOptions = new Options(extra: opExtras);
    var response = await HttpManager.instance!.getUri(url,
        params: params, option: requestOptions, withLoading: loading);
    if (response != null) {
      return JsonConvert.fromJsonAsT<List<OrdersEntity>>(response.data) ?? [];
    }
    return [];
  }

  static Future<List<OrdersEntity>> orders(
      String restId, String from, String to,
      {int page = 1, loading = false, String? q}) async {
    var params = Map<String, dynamic>();
    params["f"] = listParams;
    params["from"] = from;
    params["to"] = to;
    params["page"] = page.toString();
    if (q != null) {
      params["q"] = q;
    }

    var url = HttpUri.ORDERS_LIST.replaceAll("{restaurant_id}", restId);
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "report";
    Options requestOptions = new Options(extra: opExtras);
    var response = await HttpManager.instance!.getUri(url,
        params: params, option: requestOptions, withLoading: loading);
    if (loading && LoadingUtils.isShowing()) {
      LoadingUtils.dismiss();
    }

    if (response != null) {
      return JsonConvert.fromJsonAsT<List<OrdersEntity>>(response.data) ?? [];
    }
    return [];
  }

  static Future<void> sendReport(
      String restId, String from, String to, String type,
      {loading = false}) async {
    var params = Map<String, dynamic>();
    params["from"] = from;
    params["to"] = to;
    params["type"] = type;
    params["fax"] = true;

    var url = HttpUri.REPORT.replaceAll("{restaurant_id}", restId);
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "report";
    Options requestOptions = new Options(extra: opExtras);
    var response = await HttpManager.instance!.post(url,
        params: params, option: requestOptions, withLoading: loading);
    if (loading && LoadingUtils.isShowing()) {
      LoadingUtils.dismiss();
    }

    if (response != null) {
      return;
    }
    return;
  }
}
