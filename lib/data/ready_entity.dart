import 'package:connect/generated/json/ready_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class ReadyEntity {

	ReadyEntity();

	factory ReadyEntity.fromJson(Map<String, dynamic> json) => $ReadyEntityFromJson(json);

	Map<String, dynamic> toJson() => $ReadyEntityToJson(this);

  @JSONField(name: "_id")
  String? orderId;
  String? comments;
  String? passcode;
  String? passcodeExt;
  ReadyRestaurant? restaurant;
  ReadyDestination? destination;
  ReadyDelivery? delivery;
  String? status;
  String? createdAt;
  String? updatedAt;
  bool? doubt;
  String? confirmedAt;
  String? message;
}

@JsonSerializable()
class ReadyRestaurant {

	ReadyRestaurant();

	factory ReadyRestaurant.fromJson(Map<String, dynamic> json) => $ReadyRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $ReadyRestaurantToJson(this);

  @JSONField(name: "_id")
  String? sId;
  ReadyRestaurantName? name;
  bool? manual;
  bool? fake;
  String? color;
  String? timezone;
  ReadyRestaurantAddress? address;
  String? language;
  double? tax;
  ReadyRestaurantDelivery? delivery;
}

@JsonSerializable()
class ReadyRestaurantName {

	ReadyRestaurantName();

	factory ReadyRestaurantName.fromJson(Map<String, dynamic> json) => $ReadyRestaurantNameFromJson(json);

	Map<String, dynamic> toJson() => $ReadyRestaurantNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class ReadyRestaurantAddress {

	ReadyRestaurantAddress();

	factory ReadyRestaurantAddress.fromJson(Map<String, dynamic> json) => $ReadyRestaurantAddressFromJson(json);

	Map<String, dynamic> toJson() => $ReadyRestaurantAddressToJson(this);

  String? number;
  String? street;
  String? unit;
  String? city;
  String? state;
  String? country;
  String? zipcode;
  String? formatted;
  ReadyRestaurantAddressLocation? location;
  String? note;
}

@JsonSerializable()
class ReadyRestaurantAddressLocation     {

	ReadyRestaurantAddressLocation();

	factory ReadyRestaurantAddressLocation.fromJson(Map<String, dynamic> json) => $ReadyRestaurantAddressLocationFromJson(json);

	Map<String, dynamic> toJson() => $ReadyRestaurantAddressLocationToJson(this);

  String? type;
  List<double?>? coordinates;
}

@JsonSerializable()
class ReadyRestaurantDelivery {

	ReadyRestaurantDelivery();

	factory ReadyRestaurantDelivery.fromJson(Map<String, dynamic> json) => $ReadyRestaurantDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $ReadyRestaurantDeliveryToJson(this);

  String? provider;
  num? prepare;
  ReadyRestaurantDeliveryFee? fee;
  int? minimum;
  ReadyRestaurantDeliveryEstimate? estimate;
  int? prepareTime;
}

@JsonSerializable()
class ReadyRestaurantDeliveryFee {

	ReadyRestaurantDeliveryFee();

	factory ReadyRestaurantDeliveryFee.fromJson(Map<String, dynamic> json) => $ReadyRestaurantDeliveryFeeFromJson(json);

	Map<String, dynamic> toJson() => $ReadyRestaurantDeliveryFeeToJson(this);

  int? factor;
  int? flat;
}

@JsonSerializable()
class ReadyRestaurantDeliveryEstimate     {

	ReadyRestaurantDeliveryEstimate();

	factory ReadyRestaurantDeliveryEstimate.fromJson(Map<String, dynamic> json) => $ReadyRestaurantDeliveryEstimateFromJson(json);

	Map<String, dynamic> toJson() => $ReadyRestaurantDeliveryEstimateToJson(this);

  String? deadline;
  int? max;
  int? min;
}

@JsonSerializable()
class ReadyDestination {

	ReadyDestination();

	factory ReadyDestination.fromJson(Map<String, dynamic> json) => $ReadyDestinationFromJson(json);

	Map<String, dynamic> toJson() => $ReadyDestinationToJson(this);

  String? address;
  String? formattedAddress;
}

@JsonSerializable()
class ReadyDelivery {

	ReadyDelivery();

	factory ReadyDelivery.fromJson(Map<String, dynamic> json) => $ReadyDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $ReadyDeliveryToJson(this);

  String? status;
}
