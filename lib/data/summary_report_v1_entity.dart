import 'package:json_annotation/json_annotation.dart';

part 'summary_report_v1_entity.g.dart';

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Entity {
  @Json<PERSON>ey(name: '_id')
  SummaryReportV1Id? id;

  @Json<PERSON>ey(name: 'number')
  int? number;

  @Json<PERSON>ey(name: 'metadata')
  SummaryReportV1Metadata? metadata;

  @JsonKey(name: 'subtotal')
  int? subtotal;

  @JsonKey(name: 'cost')
  SummaryReportV1Cost? cost;

  @JsonKey(name: 'fees')
  SummaryReportV1Fees? fees;

  @Json<PERSON>ey(name: 'total')
  int? total;

  @Json<PERSON>ey(name: 'deliveryTime')
  int? deliveryTime;

  @JsonKey(name: 'deliveryTimeStd')
  int? deliveryTimeStd;

  @Json<PERSON>ey(name: 'commission')
  SummaryReportV1Commission? commission;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'distribution')
  SummaryReportV1Distribution? distribution;

  @Json<PERSON>ey(name: 'adjustments')
  SummaryReportV1Adjustments? adjustments;

  SummaryReportV1Entity();

  static SummaryReportV1Entity fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1EntityFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1EntityToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Id {
  @JsonKey(name: 'delivery')
  bool? delivery;

  @JsonKey(name: 'provider')
  bool? provider;

  SummaryReportV1Id();

  static SummaryReportV1Id fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1IdFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1IdToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Metadata {
  @JsonKey(name: 'mktFee')
  int? mktFee;

  SummaryReportV1Metadata();

  static SummaryReportV1Metadata fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1MetadataFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1MetadataToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Cost {
  @JsonKey(name: 'subtotal')
  int? subtotal;

  @JsonKey(name: 'tax')
  int? tax;

  SummaryReportV1Cost();

  static SummaryReportV1Cost fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1CostFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1CostToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Fees {
  @JsonKey(name: 'delivery')
  int? delivery;

  @JsonKey(name: 'tip')
  SummaryReportV1Tip? tip;

  @JsonKey(name: 'tax')
  int? tax;

  @JsonKey(name: 'service')
  int? service;

  @JsonKey(name: 'credit')
  int? credit;

  @JsonKey(name: 'delta')
  int? delta;

  SummaryReportV1Fees();

  static SummaryReportV1Fees fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1FeesFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1FeesToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Tip {
  @JsonKey(name: 'amount')
  int? amount;

  SummaryReportV1Tip();

  static SummaryReportV1Tip fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1TipFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1TipToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Commission {
  @JsonKey(name: 'subtotal')
  int? subtotal;

  @JsonKey(name: 'total')
  int? total;

  @JsonKey(name: 'service')
  int? service;

  @JsonKey(name: 'totTax')
  int? totTax;

  @JsonKey(name: 'subTax')
  int? subTax;

  @JsonKey(name: 'adjustments')
  SummaryReportV1Adjustments? adjustments;

  SummaryReportV1Commission();

  static SummaryReportV1Commission fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1CommissionFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1CommissionToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Distribution {
  @JsonKey(name: 'restaurant')
  int? restaurant;

  @JsonKey(name: 'ricepo')
  SummaryReportV1Ricepo? ricepo;

  SummaryReportV1Distribution();

  static SummaryReportV1Distribution fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1DistributionFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1DistributionToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Ricepo {
  @JsonKey(name: 'tax')
  int? tax;

  SummaryReportV1Ricepo();

  static SummaryReportV1Ricepo fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1RicepoFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1RicepoToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SummaryReportV1Adjustments {
  @JsonKey(name: 'ricepo')
  int? ricepo;

  @JsonKey(name: 'restaurant')
  int? restaurant;

  @JsonKey(name: 'driver')
  int? driver;

  @JsonKey(name: 'customer')
  int? customer;

  @JsonKey(name: 'unionpay')
  int? unionpay;

  SummaryReportV1Adjustments();

  static SummaryReportV1Adjustments fromJson(Map<String, dynamic> srcJson) =>
      _$SummaryReportV1AdjustmentsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SummaryReportV1AdjustmentsToJson(this);
}
