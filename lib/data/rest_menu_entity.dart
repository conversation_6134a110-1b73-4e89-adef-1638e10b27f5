import 'package:connect/generated/json/rest_menu_entity.g.dart';

import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class RestMenuEntity {

	RestMenuEntity();

	factory RestMenuEntity.fromJson(Map<String, dynamic> json) => $RestMenuEntityFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuEntityToJson(this);

  List<RestMenuCategories?>? categories;
  List<RestMenuFood?>? food;
  List<RestMenuReward?>? reward;
}

@JsonSerializable()
class RestMenuCategories {

	RestMenuCategories();

	factory RestMenuCategories.fromJson(Map<String, dynamic> json) => $RestMenuCategoriesFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuCategoriesToJson(this);

  @JSONField(name: "_id")
  String? sId;
  MultiNameEntity? name;
  int? index;
  MultiNameEntity? description;
  RestMenuCategoriesRestaurant? restaurant;
  String? updatedAt;
  @JSONField(name: "__v")
  int? iV;
  bool? promotion;
  int? maxItem;
  int? limit;
  //move food to this place manual
  List<RestMenuFood?>? food = [];
  bool? isExpanded = false;
}

@JsonSerializable()
class RestMenuCategoriesRestaurant     {

	RestMenuCategoriesRestaurant();

	factory RestMenuCategoriesRestaurant.fromJson(Map<String, dynamic> json) => $RestMenuCategoriesRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuCategoriesRestaurantToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestMenuFood {

	RestMenuFood();

	factory RestMenuFood.fromJson(Map<String, dynamic> json) => $RestMenuFoodFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodToJson(this);

  @JSONField(name: "_id")
  String? foodId;
  MultiNameEntity? description;
  bool? available;
  MultiNameEntity? name;
  String? code;
  int? price;
  int? cost;
  List<dynamic>? date;
  int? index;
  String? label;
  dynamic limit;
  int? minimum;
  dynamic stock;
  RestMenuFoodCategory? category;
  List<RestMenuFoodOptions?>? options;
  @JSONField(name: "__v")
  int? iV;
  String? updatedAt;
  List<RestMenuFoodHours?>? hours;
  RestMenuFoodImage? image;
  RestMenuFoodCondition? condition;
  String? fullChar;
  int? popularity;
  bool? featured;
  int? originalPrice;
  bool? cover;
}

@JsonSerializable()
class RestMenuFoodOptions {

	RestMenuFoodOptions();

	factory RestMenuFoodOptions.fromJson(Map<String, dynamic> json) => $RestMenuFoodOptionsFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodOptionsToJson(this);

  MultiNameEntity? name;
  int? min;
  int? max;
  List<RestMenuFoodOptionsItems?>? items;
  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestMenuFoodOptionsItems {

	RestMenuFoodOptionsItems();

	factory RestMenuFoodOptionsItems.fromJson(Map<String, dynamic> json) => $RestMenuFoodOptionsItemsFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodOptionsItemsToJson(this);

  MultiNameEntity? name;
  int? price;
  RestMenuFoodOptionsItemsAnalytics? analytics;
  bool? available;
  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestMenuFoodOptionsItemsAnalytics     {

	RestMenuFoodOptionsItemsAnalytics();

	factory RestMenuFoodOptionsItemsAnalytics.fromJson(Map<String, dynamic> json) => $RestMenuFoodOptionsItemsAnalyticsFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodOptionsItemsAnalyticsToJson(this);

  int? popularity;
}

@JsonSerializable()
class RestMenuFoodAnalytics {

	RestMenuFoodAnalytics();

	factory RestMenuFoodAnalytics.fromJson(Map<String, dynamic> json) => $RestMenuFoodAnalyticsFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodAnalyticsToJson(this);

  List<int?>? history;
}

@JsonSerializable()
class RestMenuFoodName {

	RestMenuFoodName();

	factory RestMenuFoodName.fromJson(Map<String, dynamic> json) => $RestMenuFoodNameFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestMenuFoodCategory {

	RestMenuFoodCategory();

	factory RestMenuFoodCategory.fromJson(Map<String, dynamic> json) => $RestMenuFoodCategoryFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodCategoryToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestMenuFoodHours {

	RestMenuFoodHours();

	factory RestMenuFoodHours.fromJson(Map<String, dynamic> json) => $RestMenuFoodHoursFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodHoursToJson(this);

  int? dayOfWeek;
  int? start;
  int? end;
}

@JsonSerializable()
class RestMenuFoodImage {

	RestMenuFoodImage();

	factory RestMenuFoodImage.fromJson(Map<String, dynamic> json) => $RestMenuFoodImageFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodImageToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? url;
  String? createdAt;
  String? updatedAt;
}

@JsonSerializable()
class RestMenuFoodCondition {

	RestMenuFoodCondition();

	factory RestMenuFoodCondition.fromJson(Map<String, dynamic> json) => $RestMenuFoodConditionFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuFoodConditionToJson(this);

  int? minimum;
}

@JsonSerializable()
class RestMenuReward {

	RestMenuReward();

	factory RestMenuReward.fromJson(Map<String, dynamic> json) => $RestMenuRewardFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuRewardToJson(this);

  @JSONField(name: "_id")
  String? sId;
  RestMenuRewardCategory? category;
  int? index;
  RestMenuRewardName? name;
  String? color;
  int? price;
  dynamic image;
  dynamic description;
  dynamic label;
  bool? available;
  int? popularity;
  List<dynamic>? options;
  RestMenuRewardAnalytics? analytics;
  String? updatedAt;
  int? zscore;
  int? cost;
  @JSONField(name: "__v")
  int? iV;
  List<dynamic>? tag;
  RestMenuRewardTags? tags;
  RestMenuRewardScore? score;
  bool? reward;
  int? point;
  List<dynamic>? date;
  dynamic hours;
  int? last2WeekCount;
  String? fullChar;
  dynamic code;
  int? regionZscore;
  dynamic condition;
  dynamic stock;
  dynamic limit;
  int? originalPrice;
}

@JsonSerializable()
class RestMenuRewardCategory {

	RestMenuRewardCategory();

	factory RestMenuRewardCategory.fromJson(Map<String, dynamic> json) => $RestMenuRewardCategoryFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuRewardCategoryToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestMenuRewardName {

	RestMenuRewardName();

	factory RestMenuRewardName.fromJson(Map<String, dynamic> json) => $RestMenuRewardNameFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuRewardNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestMenuRewardAnalytics {

	RestMenuRewardAnalytics();

	factory RestMenuRewardAnalytics.fromJson(Map<String, dynamic> json) => $RestMenuRewardAnalyticsFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuRewardAnalyticsToJson(this);

  List<int?>? history;
}

@JsonSerializable()
class RestMenuRewardTags {

	RestMenuRewardTags();

	factory RestMenuRewardTags.fromJson(Map<String, dynamic> json) => $RestMenuRewardTagsFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuRewardTagsToJson(this);
}

@JsonSerializable()
class RestMenuRewardScore {

	RestMenuRewardScore();

	factory RestMenuRewardScore.fromJson(Map<String, dynamic> json) => $RestMenuRewardScoreFromJson(json);

	Map<String, dynamic> toJson() => $RestMenuRewardScoreToJson(this);

  int? restaurant;
}
