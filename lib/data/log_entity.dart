import 'package:connect/generated/json/base/json_field.dart';
import 'package:connect/generated/json/log_entity.g.dart';


@JsonSerializable()
class LogEntity {

	LogEntity();

	factory LogEntity.fromJson(Map<String, dynamic> json) => $LogEntityFromJson(json);

	Map<String, dynamic> toJson() => $LogEntityToJson(this);

  String? method;
  String? bodyParams;
  String? url;
  String? header;
  String? response;
  int? responseLength;
  int? statusCode;
}
