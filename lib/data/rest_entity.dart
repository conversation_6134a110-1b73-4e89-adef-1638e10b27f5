import 'package:connect/generated/json/rest_entity.g.dart';

import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class RestEntity {

	RestEntity();

	factory RestEntity.fromJson(Map<String, dynamic> json) => $RestEntityFromJson(json);

	Map<String, dynamic> toJson() => $RestEntityToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? color;
  String? timezone;
  double? zscore;
  bool? featured;
  bool? fake;
  bool? suspended;
  bool? manual;
  String? language;
  bool? canDeliver;
  bool? canPickup;
  String? acceptCredit;
  List<String?>? tags;
  List<RestContacts?>? contacts;
  List<RestHours?>? hours;
  double? tax;
  RestDelivery? delivery;
  RestCredit? credit;
  RestStripe? stripe;
  RestCommission? commission;
  RestPreferences? preferences;
  RestLocation? location;
  RestName? name;
  RestAddress? address;
  RestRegion? region;
  String? lastUpdate;
  String? createdAt;
  String? updatedAt;
  RestReward? reward;
  RestDiscount? discount;
  RestAnalytics? analytics;
  List<RestItems?>? items;
  String? transferedAt;
  RestBundleOpts? bundleOpts;
  RestServiceFee? serviceFee;
  RestMotd? motd;
  RestPromotion? promotion;
  RestExpireProp? expireProp;
  List<RestExtraFees?>? extraFees;
  RestPrepare? prepare;
  String? fullChar;
  RestClosed? closed;
  RestClosePeriod? closePeriod;
}

@JsonSerializable()
class RestClosePeriod {

	RestClosePeriod();

	factory RestClosePeriod.fromJson(Map<String, dynamic> json) => $RestClosePeriodFromJson(json);

	Map<String, dynamic> toJson() => $RestClosePeriodToJson(this);

  String? from;
  String? to;
  String? note;
}

@JsonSerializable()
class RestContacts {

	RestContacts();

	factory RestContacts.fromJson(Map<String, dynamic> json) => $RestContactsFromJson(json);

	Map<String, dynamic> toJson() => $RestContactsToJson(this);

  String? type;
  String? content;
  bool? report;
  bool? order;
}

@JsonSerializable()
class RestHours {

	RestHours();

	factory RestHours.fromJson(Map<String, dynamic> json) => $RestHoursFromJson(json);

	Map<String, dynamic> toJson() => $RestHoursToJson(this);

  String? type;
  int? dayOfWeek;
  int? start;
  int? end;
}

@JsonSerializable()
class RestDelivery {

	RestDelivery();

	factory RestDelivery.fromJson(Map<String, dynamic> json) => $RestDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryToJson(this);

  RestDeliveryZone? zone;
  String? provider;
  MultiNameEntity? note;
  RestDeliveryPickupEst? pickupEst;
}

@JsonSerializable()
class RestDeliveryZone {

	RestDeliveryZone();

	factory RestDeliveryZone.fromJson(Map<String, dynamic> json) => $RestDeliveryZoneFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryZoneToJson(this);

  String? type;
  List<RestDeliveryZoneFeatures?>? features;
}

@JsonSerializable()
class RestDeliveryZoneFeatures {

	RestDeliveryZoneFeatures();

	factory RestDeliveryZoneFeatures.fromJson(Map<String, dynamic> json) => $RestDeliveryZoneFeaturesFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryZoneFeaturesToJson(this);

  String? type;
  RestDeliveryZoneFeaturesGeometry? geometry;
  RestDeliveryZoneFeaturesProperties? properties;
}

@JsonSerializable()
class RestDeliveryZoneFeaturesGeometry     {

	RestDeliveryZoneFeaturesGeometry();

	factory RestDeliveryZoneFeaturesGeometry.fromJson(Map<String, dynamic> json) => $RestDeliveryZoneFeaturesGeometryFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryZoneFeaturesGeometryToJson(this);

  String? type;
}

@JsonSerializable()
class RestDeliveryZoneFeaturesProperties     {

	RestDeliveryZoneFeaturesProperties();

	factory RestDeliveryZoneFeaturesProperties.fromJson(Map<String, dynamic> json) => $RestDeliveryZoneFeaturesPropertiesFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryZoneFeaturesPropertiesToJson(this);

  int? minimum;
  RestDeliveryZoneFeaturesPropertiesFee? fee;
  RestDeliveryZoneFeaturesPropertiesEstimate? estimate;
  List<RestDeliveryZoneFeaturesPropertiesFees?>? fees;
  dynamic name;
  dynamic motd;
  dynamic promotion;
  bool? scheduled;
  dynamic batch;
  int? driverBonus;
  List<RestDeliveryZoneFeaturesPropertiesWindows?>? windows;
  String? provider;
}

@JsonSerializable()
class RestDeliveryZoneFeaturesPropertiesFee     {

	RestDeliveryZoneFeaturesPropertiesFee();

	factory RestDeliveryZoneFeaturesPropertiesFee.fromJson(Map<String, dynamic> json) => $RestDeliveryZoneFeaturesPropertiesFeeFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryZoneFeaturesPropertiesFeeToJson(this);

  int? flat;
  int? factor;
}

@JsonSerializable()
class RestDeliveryZoneFeaturesPropertiesEstimate     {

	RestDeliveryZoneFeaturesPropertiesEstimate();

	factory RestDeliveryZoneFeaturesPropertiesEstimate.fromJson(Map<String, dynamic> json) => $RestDeliveryZoneFeaturesPropertiesEstimateFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryZoneFeaturesPropertiesEstimateToJson(this);

  int? min;
  int? max;
}

@JsonSerializable()
class RestDeliveryZoneFeaturesPropertiesFees     {

	RestDeliveryZoneFeaturesPropertiesFees();

	factory RestDeliveryZoneFeaturesPropertiesFees.fromJson(Map<String, dynamic> json) => $RestDeliveryZoneFeaturesPropertiesFeesFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryZoneFeaturesPropertiesFeesToJson(this);

  int? minimum;
  int? delivery;
  RestDeliveryZoneFeaturesPropertiesFeesAdjustments? adjustments;
}

@JsonSerializable()
class RestDeliveryZoneFeaturesPropertiesFeesAdjustments     {

	RestDeliveryZoneFeaturesPropertiesFeesAdjustments();

	factory RestDeliveryZoneFeaturesPropertiesFeesAdjustments.fromJson(Map<String, dynamic> json) => $RestDeliveryZoneFeaturesPropertiesFeesAdjustmentsFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryZoneFeaturesPropertiesFeesAdjustmentsToJson(this);

  int? ricepo;
  String? reason;
  int? restaurant;
  int? customer;
}

@JsonSerializable()
class RestDeliveryZoneFeaturesPropertiesWindows     {

	RestDeliveryZoneFeaturesPropertiesWindows();

	factory RestDeliveryZoneFeaturesPropertiesWindows.fromJson(Map<String, dynamic> json) => $RestDeliveryZoneFeaturesPropertiesWindowsFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryZoneFeaturesPropertiesWindowsToJson(this);

  String? type;
  int? dayOfWeek;
  int? start;
  int? end;
}

@JsonSerializable()
class RestDeliveryPickupEst {

	RestDeliveryPickupEst();

	factory RestDeliveryPickupEst.fromJson(Map<String, dynamic> json) => $RestDeliveryPickupEstFromJson(json);

	Map<String, dynamic> toJson() => $RestDeliveryPickupEstToJson(this);

  int? duration;
  int? count;
}

@JsonSerializable()
class RestCredit {

	RestCredit();

	factory RestCredit.fromJson(Map<String, dynamic> json) => $RestCreditFromJson(json);

	Map<String, dynamic> toJson() => $RestCreditToJson(this);

  int? minimum;
  RestCreditFee? fee;
}

@JsonSerializable()
class RestCreditFee {

	RestCreditFee();

	factory RestCreditFee.fromJson(Map<String, dynamic> json) => $RestCreditFeeFromJson(json);

	Map<String, dynamic> toJson() => $RestCreditFeeToJson(this);

  int? flat;
  int? factor;
}

@JsonSerializable()
class RestStripe {

	RestStripe();

	factory RestStripe.fromJson(Map<String, dynamic> json) => $RestStripeFromJson(json);

	Map<String, dynamic> toJson() => $RestStripeToJson(this);

  String? id;
  bool? onboarding;
}

@JsonSerializable()
class RestCommission {

	RestCommission();

	factory RestCommission.fromJson(Map<String, dynamic> json) => $RestCommissionFromJson(json);

	Map<String, dynamic> toJson() => $RestCommissionToJson(this);

  RestCommissionSubtotal? subtotal;
  RestCommissionTotal? total;
}

@JsonSerializable()
class RestCommissionSubtotal {

	RestCommissionSubtotal();

	factory RestCommissionSubtotal.fromJson(Map<String, dynamic> json) => $RestCommissionSubtotalFromJson(json);

	Map<String, dynamic> toJson() => $RestCommissionSubtotalToJson(this);

  int? flat;
  double? factor;
}

@JsonSerializable()
class RestCommissionTotal {

	RestCommissionTotal();

	factory RestCommissionTotal.fromJson(Map<String, dynamic> json) => $RestCommissionTotalFromJson(json);

	Map<String, dynamic> toJson() => $RestCommissionTotalToJson(this);

  int? flat;
  int? factor;
}

@JsonSerializable()
class RestPreferences {

	RestPreferences();

	factory RestPreferences.fromJson(Map<String, dynamic> json) => $RestPreferencesFromJson(json);

	Map<String, dynamic> toJson() => $RestPreferencesToJson(this);

  bool? driverCopy;
  bool? dailyReport;
  bool? autoConfirm;
}

@JsonSerializable()
class RestLocation {

	RestLocation();

	factory RestLocation.fromJson(Map<String, dynamic> json) => $RestLocationFromJson(json);

	Map<String, dynamic> toJson() => $RestLocationToJson(this);

  String? type;
  List<double?>? coordinates;
}

@JsonSerializable()
class RestName {

	RestName();

	factory RestName.fromJson(Map<String, dynamic> json) => $RestNameFromJson(json);

	Map<String, dynamic> toJson() => $RestNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestAddress {

	RestAddress();

	factory RestAddress.fromJson(Map<String, dynamic> json) => $RestAddressFromJson(json);

	Map<String, dynamic> toJson() => $RestAddressToJson(this);

  String? number;
  String? street;
  String? unit;
  String? city;
  String? state;
  String? country;
  String? zipcode;
  String? formatted;
  RestAddressLocation? location;
  String? note;
}

@JsonSerializable()
class RestAddressLocation {

	RestAddressLocation();

	factory RestAddressLocation.fromJson(Map<String, dynamic> json) => $RestAddressLocationFromJson(json);

	Map<String, dynamic> toJson() => $RestAddressLocationToJson(this);

  String? type;
  List<double?>? coordinates;
}

@JsonSerializable()
class RestRegion {

	RestRegion();

	factory RestRegion.fromJson(Map<String, dynamic> json) => $RestRegionFromJson(json);

	Map<String, dynamic> toJson() => $RestRegionToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestReward {

	RestReward();

	factory RestReward.fromJson(Map<String, dynamic> json) => $RestRewardFromJson(json);

	Map<String, dynamic> toJson() => $RestRewardToJson(this);

  bool? enabled;
}

@JsonSerializable()
class RestDiscount {

	RestDiscount();

	factory RestDiscount.fromJson(Map<String, dynamic> json) => $RestDiscountFromJson(json);

	Map<String, dynamic> toJson() => $RestDiscountToJson(this);

  List<RestDiscountMenu?>? menu;
  List<RestDiscountRicepo?>? ricepo;
  List<RestDiscountVip?>? vip;
}

@JsonSerializable()
class RestDiscountMenu {

	RestDiscountMenu();

	factory RestDiscountMenu.fromJson(Map<String, dynamic> json) => $RestDiscountMenuFromJson(json);

	Map<String, dynamic> toJson() => $RestDiscountMenuToJson(this);

  int? minimum;
  RestDiscountMenuDiscount? discount;
}

@JsonSerializable()
class RestDiscountMenuDiscount {

	RestDiscountMenuDiscount();

	factory RestDiscountMenuDiscount.fromJson(Map<String, dynamic> json) => $RestDiscountMenuDiscountFromJson(json);

	Map<String, dynamic> toJson() => $RestDiscountMenuDiscountToJson(this);

  int? flat;
  int? factor;
}

@JsonSerializable()
class RestDiscountRicepo {

	RestDiscountRicepo();

	factory RestDiscountRicepo.fromJson(Map<String, dynamic> json) => $RestDiscountRicepoFromJson(json);

	Map<String, dynamic> toJson() => $RestDiscountRicepoToJson(this);

  int? minimum;
  RestDiscountRicepoDiscount? discount;
}

@JsonSerializable()
class RestDiscountRicepoDiscount {

	RestDiscountRicepoDiscount();

	factory RestDiscountRicepoDiscount.fromJson(Map<String, dynamic> json) => $RestDiscountRicepoDiscountFromJson(json);

	Map<String, dynamic> toJson() => $RestDiscountRicepoDiscountToJson(this);

  int? flat;
  int? factor;
}

@JsonSerializable()
class RestDiscountVip {

	RestDiscountVip();

	factory RestDiscountVip.fromJson(Map<String, dynamic> json) => $RestDiscountVipFromJson(json);

	Map<String, dynamic> toJson() => $RestDiscountVipToJson(this);

  int? minimum;
  RestDiscountVipDiscount? discount;
}

@JsonSerializable()
class RestDiscountVipDiscount {

	RestDiscountVipDiscount();

	factory RestDiscountVipDiscount.fromJson(Map<String, dynamic> json) => $RestDiscountVipDiscountFromJson(json);

	Map<String, dynamic> toJson() => $RestDiscountVipDiscountToJson(this);

  int? flat;
  double? factor;
}

@JsonSerializable()
class RestAnalytics {

	RestAnalytics();

	factory RestAnalytics.fromJson(Map<String, dynamic> json) => $RestAnalyticsFromJson(json);

	Map<String, dynamic> toJson() => $RestAnalyticsToJson(this);

  int? history;
}

@JsonSerializable()
class RestItems {

	RestItems();

	factory RestItems.fromJson(Map<String, dynamic> json) => $RestItemsFromJson(json);

	Map<String, dynamic> toJson() => $RestItemsToJson(this);

  @JSONField(name: "_id")
  String? sId;
  bool? available;
  List<dynamic>? options;
  RestItemsName? name;
  int? price;
  RestItemsImage? image;
  bool? cover;
  double? zscore;
  int? last2WeekCount;
  RestItemsSortMetrics? sortMetrics;
  int? originalPrice;
}

@JsonSerializable()
class RestItemsName {

	RestItemsName();

	factory RestItemsName.fromJson(Map<String, dynamic> json) => $RestItemsNameFromJson(json);

	Map<String, dynamic> toJson() => $RestItemsNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestItemsImage {

	RestItemsImage();

	factory RestItemsImage.fromJson(Map<String, dynamic> json) => $RestItemsImageFromJson(json);

	Map<String, dynamic> toJson() => $RestItemsImageToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? url;
  String? createdAt;
  String? updatedAt;
  bool? exclusive;
}

@JsonSerializable()
class RestItemsSortMetrics {

	RestItemsSortMetrics();

	factory RestItemsSortMetrics.fromJson(Map<String, dynamic> json) => $RestItemsSortMetricsFromJson(json);

	Map<String, dynamic> toJson() => $RestItemsSortMetricsToJson(this);

  bool? cover;
  bool? available;
  bool? highPrice;
  double? zscore;
}

@JsonSerializable()
class RestBundleOpts {

	RestBundleOpts();

	factory RestBundleOpts.fromJson(Map<String, dynamic> json) => $RestBundleOptsFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsToJson(this);

  List<String?>? include;
  List<RestBundleOptsPopular?>? popular;
}

@JsonSerializable()
class RestBundleOptsPopular {

	RestBundleOptsPopular();

	factory RestBundleOptsPopular.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularToJson(this);

  @JSONField(name: "_id")
  String? sId;
  RestBundleOptsPopularName? name;
  int? index;
  List<RestBundleOptsPopularBundleItems?>? bundleItems;
}

@JsonSerializable()
class RestBundleOptsPopularName {

	RestBundleOptsPopularName();

	factory RestBundleOptsPopularName.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularNameFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestBundleOptsPopularBundleItems     {

	RestBundleOptsPopularBundleItems();

	factory RestBundleOptsPopularBundleItems.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularBundleItemsFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularBundleItemsToJson(this);

  @JSONField(name: "_id")
  String? sId;
  dynamic code;
  dynamic description;
  dynamic label;
  bool? available;
  List<dynamic>? options;
  RestBundleOptsPopularBundleItemsName? name;
  int? price;
  int? cost;
  int? index;
  List<dynamic>? date;
  bool? reward;
  int? point;
  dynamic hours;
  RestBundleOptsPopularBundleItemsImage? image;
  dynamic stock;
  dynamic limit;
  RestBundleOptsPopularBundleItemsRestaurant? restaurant;
  RestBundleOptsPopularBundleItemsRegion? region;
  RestBundleOptsPopularBundleItemsCategory? category;
  RestBundleOptsPopularBundleItemsTags? tags;
  RestBundleOptsPopularBundleItemsScore? score;
  String? updatedAt;
  int? zscore;
  int? originalPrice;
}

@JsonSerializable()
class RestBundleOptsPopularBundleItemsName     {

	RestBundleOptsPopularBundleItemsName();

	factory RestBundleOptsPopularBundleItemsName.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularBundleItemsNameFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularBundleItemsNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestBundleOptsPopularBundleItemsImage     {

	RestBundleOptsPopularBundleItemsImage();

	factory RestBundleOptsPopularBundleItemsImage.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularBundleItemsImageFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularBundleItemsImageToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? url;
  String? createdAt;
  String? updatedAt;
}

@JsonSerializable()
class RestBundleOptsPopularBundleItemsRestaurant     {

	RestBundleOptsPopularBundleItemsRestaurant();

	factory RestBundleOptsPopularBundleItemsRestaurant.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularBundleItemsRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularBundleItemsRestaurantToJson(this);

  @JSONField(name: "_id")
  String? sId;
  RestBundleOptsPopularBundleItemsRestaurantName? name;
}

@JsonSerializable()
class RestBundleOptsPopularBundleItemsRestaurantName     {

	RestBundleOptsPopularBundleItemsRestaurantName();

	factory RestBundleOptsPopularBundleItemsRestaurantName.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularBundleItemsRestaurantNameFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularBundleItemsRestaurantNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestBundleOptsPopularBundleItemsRegion     {

	RestBundleOptsPopularBundleItemsRegion();

	factory RestBundleOptsPopularBundleItemsRegion.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularBundleItemsRegionFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularBundleItemsRegionToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestBundleOptsPopularBundleItemsCategory     {

	RestBundleOptsPopularBundleItemsCategory();

	factory RestBundleOptsPopularBundleItemsCategory.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularBundleItemsCategoryFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularBundleItemsCategoryToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestBundleOptsPopularBundleItemsTags     {

	RestBundleOptsPopularBundleItemsTags();

	factory RestBundleOptsPopularBundleItemsTags.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularBundleItemsTagsFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularBundleItemsTagsToJson(this);

  int? vegetable;
}

@JsonSerializable()
class RestBundleOptsPopularBundleItemsScore     {

	RestBundleOptsPopularBundleItemsScore();

	factory RestBundleOptsPopularBundleItemsScore.fromJson(Map<String, dynamic> json) => $RestBundleOptsPopularBundleItemsScoreFromJson(json);

	Map<String, dynamic> toJson() => $RestBundleOptsPopularBundleItemsScoreToJson(this);

  double? region;
  int? restaurant;
}

@JsonSerializable()
class RestServiceFee {

	RestServiceFee();

	factory RestServiceFee.fromJson(Map<String, dynamic> json) => $RestServiceFeeFromJson(json);

	Map<String, dynamic> toJson() => $RestServiceFeeToJson(this);

  double? factor;
  int? flat;
}

@JsonSerializable()
class RestMotd {

	RestMotd();

	factory RestMotd.fromJson(Map<String, dynamic> json) => $RestMotdFromJson(json);

	Map<String, dynamic> toJson() => $RestMotdToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestPromotion {

	RestPromotion();

	factory RestPromotion.fromJson(Map<String, dynamic> json) => $RestPromotionFromJson(json);

	Map<String, dynamic> toJson() => $RestPromotionToJson(this);

  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestExpireProp {

	RestExpireProp();

	factory RestExpireProp.fromJson(Map<String, dynamic> json) => $RestExpirePropFromJson(json);

	Map<String, dynamic> toJson() => $RestExpirePropToJson(this);
}

@JsonSerializable()
class RestExtraFees {

	RestExtraFees();

	factory RestExtraFees.fromJson(Map<String, dynamic> json) => $RestExtraFeesFromJson(json);

	Map<String, dynamic> toJson() => $RestExtraFeesToJson(this);

  MultiNameEntity? name;
  int? price;
  int? cost;
  bool? extraFee;
}

@JsonSerializable()
class RestExtraFeesName {

	RestExtraFeesName();

	factory RestExtraFeesName.fromJson(Map<String, dynamic> json) => $RestExtraFeesNameFromJson(json);

	Map<String, dynamic> toJson() => $RestExtraFeesNameToJson(this);

  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestPrepare {

	RestPrepare();

	factory RestPrepare.fromJson(Map<String, dynamic> json) => $RestPrepareFromJson(json);

	Map<String, dynamic> toJson() => $RestPrepareToJson(this);

  int? avg;
  int? std;
  RestPrepareFood? food;
  double? reliability;
}

@JsonSerializable()
class RestPrepareFood {

	RestPrepareFood();

	factory RestPrepareFood.fromJson(Map<String, dynamic> json) => $RestPrepareFoodFromJson(json);

	Map<String, dynamic> toJson() => $RestPrepareFoodToJson(this);

  @JSONField(name: "food_DHrWUfv77")
  int? foodDhrwufv77;
  @JSONField(name: "food_kZcar_oark")
  int? foodKzcarOark;
  @JSONField(name: "food_tKONSwtzU")
  int? foodTkonswtzu;
  @JSONField(name: "food_v5CtwGY0w")
  int? foodV5ctwgy0w;
}

@JsonSerializable()
class RestBundlePopular {

	RestBundlePopular();

	factory RestBundlePopular.fromJson(Map<String, dynamic> json) => $RestBundlePopularFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularToJson(this);

  @JSONField(name: "_id")
  String? sId;
  RestBundlePopularName? name;
  int? index;
  List<RestBundlePopularBundleItems?>? bundleItems;
}

@JsonSerializable()
class RestBundlePopularName {

	RestBundlePopularName();

	factory RestBundlePopularName.fromJson(Map<String, dynamic> json) => $RestBundlePopularNameFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestBundlePopularBundleItems     {

	RestBundlePopularBundleItems();

	factory RestBundlePopularBundleItems.fromJson(Map<String, dynamic> json) => $RestBundlePopularBundleItemsFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularBundleItemsToJson(this);

  @JSONField(name: "_id")
  String? sId;
  dynamic code;
  dynamic description;
  dynamic label;
  bool? available;
  List<dynamic>? options;
  RestBundlePopularBundleItemsName? name;
  int? price;
  int? cost;
  int? originalPrice;
  int? index;
  List<dynamic>? date;
  int? point;
  dynamic hours;
  RestBundlePopularBundleItemsImage? image;
  dynamic stock;
  dynamic limit;
  RestBundlePopularBundleItemsRestaurant? restaurant;
  RestBundlePopularBundleItemsRegion? region;
  RestBundlePopularBundleItemsCategory? category;
  RestBundlePopularBundleItemsTags? tags;
  String? updatedAt;
  RestBundlePopularBundleItemsScore? score;
  int? zscore;
  int? last2WeekCount;
  String? fullChar;
  List<String?>? pinyin;
}

@JsonSerializable()
class RestBundlePopularBundleItemsName     {

	RestBundlePopularBundleItemsName();

	factory RestBundlePopularBundleItemsName.fromJson(Map<String, dynamic> json) => $RestBundlePopularBundleItemsNameFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularBundleItemsNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestBundlePopularBundleItemsImage     {

	RestBundlePopularBundleItemsImage();

	factory RestBundlePopularBundleItemsImage.fromJson(Map<String, dynamic> json) => $RestBundlePopularBundleItemsImageFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularBundleItemsImageToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? url;
  String? createdAt;
  String? updatedAt;
}

@JsonSerializable()
class RestBundlePopularBundleItemsRestaurant     {

	RestBundlePopularBundleItemsRestaurant();

	factory RestBundlePopularBundleItemsRestaurant.fromJson(Map<String, dynamic> json) => $RestBundlePopularBundleItemsRestaurantFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularBundleItemsRestaurantToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestBundlePopularBundleItemsRegion     {

	RestBundlePopularBundleItemsRegion();

	factory RestBundlePopularBundleItemsRegion.fromJson(Map<String, dynamic> json) => $RestBundlePopularBundleItemsRegionFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularBundleItemsRegionToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestBundlePopularBundleItemsCategory     {

	RestBundlePopularBundleItemsCategory();

	factory RestBundlePopularBundleItemsCategory.fromJson(Map<String, dynamic> json) => $RestBundlePopularBundleItemsCategoryFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularBundleItemsCategoryToJson(this);

  @JSONField(name: "_id")
  String? sId;
}

@JsonSerializable()
class RestBundlePopularBundleItemsTags     {

	RestBundlePopularBundleItemsTags();

	factory RestBundlePopularBundleItemsTags.fromJson(Map<String, dynamic> json) => $RestBundlePopularBundleItemsTagsFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularBundleItemsTagsToJson(this);

  int? bbq;
}

@JsonSerializable()
class RestBundlePopularBundleItemsScore     {

	RestBundlePopularBundleItemsScore();

	factory RestBundlePopularBundleItemsScore.fromJson(Map<String, dynamic> json) => $RestBundlePopularBundleItemsScoreFromJson(json);

	Map<String, dynamic> toJson() => $RestBundlePopularBundleItemsScoreToJson(this);

  double? region;
  int? restaurant;
}

@JsonSerializable()
class RestPromoItems {

	RestPromoItems();

	factory RestPromoItems.fromJson(Map<String, dynamic> json) => $RestPromoItemsFromJson(json);

	Map<String, dynamic> toJson() => $RestPromoItemsToJson(this);

  @JSONField(name: "_id")
  String? sId;
  bool? available;
  List<dynamic>? options;
  RestPromoItemsName? name;
  int? price;
  int? originalPrice;
  RestPromoItemsImage? image;
  double? zscore;
  int? last2WeekCount;
}

@JsonSerializable()
class RestPromoItemsName {

	RestPromoItemsName();

	factory RestPromoItemsName.fromJson(Map<String, dynamic> json) => $RestPromoItemsNameFromJson(json);

	Map<String, dynamic> toJson() => $RestPromoItemsNameToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}

@JsonSerializable()
class RestPromoItemsImage {

	RestPromoItemsImage();

	factory RestPromoItemsImage.fromJson(Map<String, dynamic> json) => $RestPromoItemsImageFromJson(json);

	Map<String, dynamic> toJson() => $RestPromoItemsImageToJson(this);

  @JSONField(name: "_id")
  String? sId;
  String? url;
  String? createdAt;
  String? updatedAt;
}

@JsonSerializable()
class RestClosed {

	RestClosed();

	factory RestClosed.fromJson(Map<String, dynamic> json) => $RestClosedFromJson(json);

	Map<String, dynamic> toJson() => $RestClosedToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "zh-HK")
  String? zhHk;
  @JSONField(name: "en-US")
  String? enUs;
  String? user;
  String? reason;
}
