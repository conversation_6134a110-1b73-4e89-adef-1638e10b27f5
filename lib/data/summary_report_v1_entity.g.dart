// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'summary_report_v1_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SummaryReportV1Entity _$SummaryReportV1EntityFromJson(
        Map<String, dynamic> json) =>
    SummaryReportV1Entity()
      ..id = json['_id'] == null
          ? null
          : SummaryReportV1Id.fromJson(json['_id'] as Map<String, dynamic>)
      ..number = json['number'] as int?
      ..metadata = json['metadata'] == null
          ? null
          : SummaryReportV1Metadata.fromJson(
              json['metadata'] as Map<String, dynamic>)
      ..subtotal = json['subtotal'] as int?
      ..cost = json['cost'] == null
          ? null
          : SummaryReportV1Cost.fromJson(json['cost'] as Map<String, dynamic>)
      ..fees = json['fees'] == null
          ? null
          : SummaryReportV1Fees.fromJson(json['fees'] as Map<String, dynamic>)
      ..total = json['total'] as int?
      ..deliveryTime = json['deliveryTime'] as int?
      ..deliveryTimeStd = json['deliveryTimeStd'] as int?
      ..commission = json['commission'] == null
          ? null
          : SummaryReportV1Commission.fromJson(
              json['commission'] as Map<String, dynamic>)
      ..distribution = json['distribution'] == null
          ? null
          : SummaryReportV1Distribution.fromJson(
              json['distribution'] as Map<String, dynamic>)
      ..adjustments = json['adjustments'] == null
          ? null
          : SummaryReportV1Adjustments.fromJson(
              json['adjustments'] as Map<String, dynamic>);

Map<String, dynamic> _$SummaryReportV1EntityToJson(
        SummaryReportV1Entity instance) =>
    <String, dynamic>{
      '_id': instance.id?.toJson(),
      'number': instance.number,
      'metadata': instance.metadata?.toJson(),
      'subtotal': instance.subtotal,
      'cost': instance.cost?.toJson(),
      'fees': instance.fees?.toJson(),
      'total': instance.total,
      'deliveryTime': instance.deliveryTime,
      'deliveryTimeStd': instance.deliveryTimeStd,
      'commission': instance.commission?.toJson(),
      'distribution': instance.distribution?.toJson(),
      'adjustments': instance.adjustments?.toJson(),
    };

SummaryReportV1Id _$SummaryReportV1IdFromJson(Map<String, dynamic> json) =>
    SummaryReportV1Id()
      ..delivery = json['delivery'] as bool?
      ..provider = json['provider'] as bool?;

Map<String, dynamic> _$SummaryReportV1IdToJson(SummaryReportV1Id instance) =>
    <String, dynamic>{
      'delivery': instance.delivery,
      'provider': instance.provider,
    };

SummaryReportV1Metadata _$SummaryReportV1MetadataFromJson(
        Map<String, dynamic> json) =>
    SummaryReportV1Metadata()..mktFee = json['mktFee'] as int?;

Map<String, dynamic> _$SummaryReportV1MetadataToJson(
        SummaryReportV1Metadata instance) =>
    <String, dynamic>{
      'mktFee': instance.mktFee,
    };

SummaryReportV1Cost _$SummaryReportV1CostFromJson(Map<String, dynamic> json) =>
    SummaryReportV1Cost()
      ..subtotal = json['subtotal'] as int?
      ..tax = json['tax'] as int?;

Map<String, dynamic> _$SummaryReportV1CostToJson(
        SummaryReportV1Cost instance) =>
    <String, dynamic>{
      'subtotal': instance.subtotal,
      'tax': instance.tax,
    };

SummaryReportV1Fees _$SummaryReportV1FeesFromJson(Map<String, dynamic> json) =>
    SummaryReportV1Fees()
      ..delivery = json['delivery'] as int?
      ..tip = json['tip'] == null
          ? null
          : SummaryReportV1Tip.fromJson(json['tip'] as Map<String, dynamic>)
      ..tax = json['tax'] as int?
      ..service = json['service'] as int?
      ..credit = json['credit'] as int?
      ..delta = json['delta'] as int?;

Map<String, dynamic> _$SummaryReportV1FeesToJson(
        SummaryReportV1Fees instance) =>
    <String, dynamic>{
      'delivery': instance.delivery,
      'tip': instance.tip?.toJson(),
      'tax': instance.tax,
      'service': instance.service,
      'credit': instance.credit,
      'delta': instance.delta,
    };

SummaryReportV1Tip _$SummaryReportV1TipFromJson(Map<String, dynamic> json) =>
    SummaryReportV1Tip()..amount = json['amount'] as int?;

Map<String, dynamic> _$SummaryReportV1TipToJson(SummaryReportV1Tip instance) =>
    <String, dynamic>{
      'amount': instance.amount,
    };

SummaryReportV1Commission _$SummaryReportV1CommissionFromJson(
        Map<String, dynamic> json) =>
    SummaryReportV1Commission()
      ..subtotal = json['subtotal'] as int?
      ..total = json['total'] as int?
      ..service = json['service'] as int?
      ..totTax = json['totTax'] as int?
      ..subTax = json['subTax'] as int?
      ..adjustments = json['adjustments'] == null
          ? null
          : SummaryReportV1Adjustments.fromJson(
              json['adjustments'] as Map<String, dynamic>);

Map<String, dynamic> _$SummaryReportV1CommissionToJson(
        SummaryReportV1Commission instance) =>
    <String, dynamic>{
      'subtotal': instance.subtotal,
      'total': instance.total,
      'service': instance.service,
      'totTax': instance.totTax,
      'subTax': instance.subTax,
      'adjustments': instance.adjustments?.toJson(),
    };

SummaryReportV1Distribution _$SummaryReportV1DistributionFromJson(
        Map<String, dynamic> json) =>
    SummaryReportV1Distribution()
      ..restaurant = json['restaurant'] as int?
      ..ricepo = json['ricepo'] == null
          ? null
          : SummaryReportV1Ricepo.fromJson(
              json['ricepo'] as Map<String, dynamic>);

Map<String, dynamic> _$SummaryReportV1DistributionToJson(
        SummaryReportV1Distribution instance) =>
    <String, dynamic>{
      'restaurant': instance.restaurant,
      'ricepo': instance.ricepo?.toJson(),
    };

SummaryReportV1Ricepo _$SummaryReportV1RicepoFromJson(
        Map<String, dynamic> json) =>
    SummaryReportV1Ricepo()..tax = json['tax'] as int?;

Map<String, dynamic> _$SummaryReportV1RicepoToJson(
        SummaryReportV1Ricepo instance) =>
    <String, dynamic>{
      'tax': instance.tax,
    };

SummaryReportV1Adjustments _$SummaryReportV1AdjustmentsFromJson(
        Map<String, dynamic> json) =>
    SummaryReportV1Adjustments()
      ..ricepo = json['ricepo'] as int?
      ..restaurant = json['restaurant'] as int?
      ..driver = json['driver'] as int?
      ..customer = json['customer'] as int?
      ..unionpay = json['unionpay'] as int?;

Map<String, dynamic> _$SummaryReportV1AdjustmentsToJson(
        SummaryReportV1Adjustments instance) =>
    <String, dynamic>{
      'ricepo': instance.ricepo,
      'restaurant': instance.restaurant,
      'driver': instance.driver,
      'customer': instance.customer,
      'unionpay': instance.unionpay,
    };
