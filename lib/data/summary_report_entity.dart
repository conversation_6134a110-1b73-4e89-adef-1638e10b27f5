import 'package:connect/generated/json/summary_report_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class SummaryReportEntity {

	SummaryReportEntity();

	factory SummaryReportEntity.fromJson(Map<String, dynamic> json) => $SummaryReportEntityFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportEntityToJson(this);

  @JSONField(name: "_id")
  SummaryReportId? sId;
  double? subtotal;
  SummaryReportCost? cost;
  double? total;
  SummaryReportFees? fees;
  SummaryReportPostmates? postmates;
  SummaryReportDelivery? delivery;
  SummaryReportAdjustments? adjustments;
  SummaryReportCoupon? coupon;
  SummaryReportCommission? commission;
  SummaryReportDistribution? distribution;
  double? number;
  double? uniqueNumber;
  double? bundleNumber;
  double? subtotalAvg;
  double? totalAvg;
  SummaryReportPayment? payment;
}

@JsonSerializable()
class SummaryReportId {

	SummaryReportId();

	factory SummaryReportId.fromJson(Map<String, dynamic> json) => $SummaryReportIdFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportIdToJson(this);

  bool? delivery;
  bool? provider;
}

@JsonSerializable()
class SummaryReportCost {

	SummaryReportCost();

	factory SummaryReportCost.fromJson(Map<String, dynamic> json) => $SummaryReportCostFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportCostToJson(this);

  double? subtotal;
  double? tax;
}

@JsonSerializable()
class SummaryReportFees {

	SummaryReportFees();

	factory SummaryReportFees.fromJson(Map<String, dynamic> json) => $SummaryReportFeesFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportFeesToJson(this);

  double? delivery;
  SummaryReportFeesTip? tip;
  double? tax;
  double? service;
  double? credit;
  double? delta;
}

@JsonSerializable()
class SummaryReportFeesTip {

	SummaryReportFeesTip();

	factory SummaryReportFeesTip.fromJson(Map<String, dynamic> json) => $SummaryReportFeesTipFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportFeesTipToJson(this);

  double? amount;
}

@JsonSerializable()
class SummaryReportPostmates {

	SummaryReportPostmates();

	factory SummaryReportPostmates.fromJson(Map<String, dynamic> json) => $SummaryReportPostmatesFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportPostmatesToJson(this);

  SummaryReportPostmatesDelivery? delivery;
}

@JsonSerializable()
class SummaryReportPostmatesDelivery     {

	SummaryReportPostmatesDelivery();

	factory SummaryReportPostmatesDelivery.fromJson(Map<String, dynamic> json) => $SummaryReportPostmatesDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportPostmatesDeliveryToJson(this);

  double? fee;
}

@JsonSerializable()
class SummaryReportDelivery {

	SummaryReportDelivery();

	factory SummaryReportDelivery.fromJson(Map<String, dynamic> json) => $SummaryReportDeliveryFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportDeliveryToJson(this);

  double? fee;
}

@JsonSerializable()
class SummaryReportAdjustments {

	SummaryReportAdjustments();

	factory SummaryReportAdjustments.fromJson(Map<String, dynamic> json) => $SummaryReportAdjustmentsFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportAdjustmentsToJson(this);

  double? ricepo;
  double? restaurant;
  double? driver;
  double? customer;
  double? unionpay;
}

@JsonSerializable()
class SummaryReportCoupon {

	SummaryReportCoupon();

	factory SummaryReportCoupon.fromJson(Map<String, dynamic> json) => $SummaryReportCouponFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportCouponToJson(this);

  double? amount;
}

@JsonSerializable()
class SummaryReportCommission {

	SummaryReportCommission();

	factory SummaryReportCommission.fromJson(Map<String, dynamic> json) => $SummaryReportCommissionFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportCommissionToJson(this);

  double? subtotal;
  double? total;
  double? service;
  double? driver;
}

@JsonSerializable()
class SummaryReportDistribution {

	SummaryReportDistribution();

	factory SummaryReportDistribution.fromJson(Map<String, dynamic> json) => $SummaryReportDistributionFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportDistributionToJson(this);

  SummaryReportDistributionRicepo? ricepo;
  double? restaurant;
}

@JsonSerializable()
class SummaryReportDistributionRicepo     {

	SummaryReportDistributionRicepo();

	factory SummaryReportDistributionRicepo.fromJson(Map<String, dynamic> json) => $SummaryReportDistributionRicepoFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportDistributionRicepoToJson(this);

  double? tax;
}

@JsonSerializable()
class SummaryReportPayment {

	SummaryReportPayment();

	factory SummaryReportPayment.fromJson(Map<String, dynamic> json) => $SummaryReportPaymentFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportPaymentToJson(this);

  SummaryReportPaymentSale? sale;
  SummaryReportPaymentNumber? number;
}

@JsonSerializable()
class SummaryReportPaymentSale {

	SummaryReportPaymentSale();

	factory SummaryReportPaymentSale.fromJson(Map<String, dynamic> json) => $SummaryReportPaymentSaleFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportPaymentSaleToJson(this);

  double? stripe;
  double? alipay;
  double? wechat;
  double? applepay;
}

@JsonSerializable()
class SummaryReportPaymentNumber {

	SummaryReportPaymentNumber();

	factory SummaryReportPaymentNumber.fromJson(Map<String, dynamic> json) => $SummaryReportPaymentNumberFromJson(json);

	Map<String, dynamic> toJson() => $SummaryReportPaymentNumberToJson(this);

  double? stripe;
  double? alipay;
  double? wechat;
  double? applepay;
}
