import 'package:connect/generated/json/multi_name_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class MultiNameEntity {

	MultiNameEntity();

	factory MultiNameEntity.fromJson(Map<String, dynamic> json) => $MultiNameEntityFromJson(json);

	Map<String, dynamic> toJson() => $MultiNameEntityToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "en-US")
  String? enUs;
  @JSONField(name: "zh-HK")
  String? zhHk;
}
