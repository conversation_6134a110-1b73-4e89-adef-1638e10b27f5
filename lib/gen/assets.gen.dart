/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// ignore_for_file: directives_ordering,unnecessary_import

import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/services.dart';

class $AssetsConfigGen {
  const $AssetsConfigGen();

  /// File path: assets/config/cancel_order_en.json
  String get cancelOrderEn => 'assets/config/cancel_order_en.json';

  /// File path: assets/config/cancel_order_hk.json
  String get cancelOrderHk => 'assets/config/cancel_order_hk.json';

  /// File path: assets/config/cancel_order_zh.json
  String get cancelOrderZh => 'assets/config/cancel_order_zh.json';

  /// File path: assets/config/tags_en.json
  String get tagsEn => 'assets/config/tags_en.json';

  /// File path: assets/config/tags_hk.json
  String get tagsHk => 'assets/config/tags_hk.json';

  /// File path: assets/config/tags_zh.json
  String get tagsZh => 'assets/config/tags_zh.json';
}

class $AssetsHeatmapGen {
  const $AssetsHeatmapGen();

  /// File path: assets/heatmap/index.html
  String get indexHtml => 'assets/heatmap/index.html';

  /// File path: assets/heatmap/index.js
  String get indexJs => 'assets/heatmap/index.js';

  /// File path: assets/heatmap/lodash.js
  String get lodash => 'assets/heatmap/lodash.js';

  /// File path: assets/heatmap/style.css
  String get style => 'assets/heatmap/style.css';
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/delivery.png
  AssetGenImage get delivery =>
      const AssetGenImage('assets/images/delivery.png');

  $AssetsImagesDriverTabGen get driverTab => const $AssetsImagesDriverTabGen();
  $AssetsImagesIconsGen get icons => const $AssetsImagesIconsGen();

  /// File path: assets/images/launch_image.jpg
  AssetGenImage get launchImage =>
      const AssetGenImage('assets/images/launch_image.jpg');

  /// File path: assets/images/loading.gif
  AssetGenImage get loading => const AssetGenImage('assets/images/loading.gif');

  /// File path: assets/images/loading_faster.gif
  AssetGenImage get loadingFaster =>
      const AssetGenImage('assets/images/loading_faster.gif');

  /// File path: assets/images/maps.png
  AssetGenImage get maps => const AssetGenImage('assets/images/maps.png');

  /// File path: assets/images/marker_black.png
  AssetGenImage get markerBlack =>
      const AssetGenImage('assets/images/marker_black.png');

  /// File path: assets/images/marker_red.png
  AssetGenImage get markerRed =>
      const AssetGenImage('assets/images/marker_red.png');

  $AssetsImagesMoreGen get more => const $AssetsImagesMoreGen();
  $AssetsImagesOrderGen get order => const $AssetsImagesOrderGen();

  /// File path: assets/images/plate.png
  AssetGenImage get plate => const AssetGenImage('assets/images/plate.png');

  /// File path: assets/images/plate_holder.png
  AssetGenImage get plateHolder =>
      const AssetGenImage('assets/images/plate_holder.png');

  $AssetsImagesReportGen get report => const $AssetsImagesReportGen();
  $AssetsImagesRestGen get rest => const $AssetsImagesRestGen();
  $AssetsImagesTabGen get tab => const $AssetsImagesTabGen();
}

class $AssetsImagesDriverTabGen {
  const $AssetsImagesDriverTabGen();

  /// File path: assets/images/driver_tab/ico_delivery_default.svg
  SvgGenImage get icoDeliveryDefault =>
      const SvgGenImage('assets/images/driver_tab/ico_delivery_default.svg');

  /// File path: assets/images/driver_tab/ico_delivery_select.svg
  SvgGenImage get icoDeliverySelect =>
      const SvgGenImage('assets/images/driver_tab/ico_delivery_select.svg');

  /// File path: assets/images/driver_tab/ico_more_default.svg
  SvgGenImage get icoMoreDefault =>
      const SvgGenImage('assets/images/driver_tab/ico_more_default.svg');

  /// File path: assets/images/driver_tab/ico_more_select.svg
  SvgGenImage get icoMoreSelect =>
      const SvgGenImage('assets/images/driver_tab/ico_more_select.svg');

  /// File path: assets/images/driver_tab/ico_notice.svg
  SvgGenImage get icoNotice =>
      const SvgGenImage('assets/images/driver_tab/ico_notice.svg');

  /// File path: assets/images/driver_tab/ico_order_default.svg
  SvgGenImage get icoOrderDefault =>
      const SvgGenImage('assets/images/driver_tab/ico_order_default.svg');

  /// File path: assets/images/driver_tab/ico_order_select.svg
  SvgGenImage get icoOrderSelect =>
      const SvgGenImage('assets/images/driver_tab/ico_order_select.svg');

  /// File path: assets/images/driver_tab/ico_report_default.svg
  SvgGenImage get icoReportDefault =>
      const SvgGenImage('assets/images/driver_tab/ico_report_default.svg');

  /// File path: assets/images/driver_tab/ico_report_select.svg
  SvgGenImage get icoReportSelect =>
      const SvgGenImage('assets/images/driver_tab/ico_report_select.svg');

  /// File path: assets/images/driver_tab/ico_schedule_default.svg
  SvgGenImage get icoScheduleDefault =>
      const SvgGenImage('assets/images/driver_tab/ico_schedule_default.svg');

  /// File path: assets/images/driver_tab/ico_schedule_select.svg
  SvgGenImage get icoScheduleSelect =>
      const SvgGenImage('assets/images/driver_tab/ico_schedule_select.svg');

  /// File path: assets/images/driver_tab/ico_service_default.svg
  SvgGenImage get icoServiceDefault =>
      const SvgGenImage('assets/images/driver_tab/ico_service_default.svg');

  /// File path: assets/images/driver_tab/ico_service_select.svg
  SvgGenImage get icoServiceSelect =>
      const SvgGenImage('assets/images/driver_tab/ico_service_select.svg');

  /// File path: assets/images/driver_tab/profile_avatar.svg
  SvgGenImage get profileAvatar =>
      const SvgGenImage('assets/images/driver_tab/profile_avatar.svg');
}

class $AssetsImagesIconsGen {
  const $AssetsImagesIconsGen();

  /// File path: assets/images/icons/left_arrow.png
  AssetGenImage get leftArrow =>
      const AssetGenImage('assets/images/icons/left_arrow.png');

  $AssetsImagesIconsOrderGen get order => const $AssetsImagesIconsOrderGen();

  /// File path: assets/images/icons/search.png
  AssetGenImage get search =>
      const AssetGenImage('assets/images/icons/search.png');
}

class $AssetsImagesMoreGen {
  const $AssetsImagesMoreGen();

  /// File path: assets/images/more/help.png
  AssetGenImage get help => const AssetGenImage('assets/images/more/help.png');

  /// File path: assets/images/more/identity.png
  AssetGenImage get identity =>
      const AssetGenImage('assets/images/more/identity.png');

  /// File path: assets/images/more/language.png
  AssetGenImage get language =>
      const AssetGenImage('assets/images/more/language.png');

  /// File path: assets/images/more/notification.png
  AssetGenImage get notification =>
      const AssetGenImage('assets/images/more/notification.png');

  /// File path: assets/images/more/password.png
  AssetGenImage get password =>
      const AssetGenImage('assets/images/more/password.png');

  /// File path: assets/images/more/print.png
  AssetGenImage get print =>
      const AssetGenImage('assets/images/more/print.png');

  /// File path: assets/images/more/support.png
  AssetGenImage get support =>
      const AssetGenImage('assets/images/more/support.png');

  /// File path: assets/images/more/user.png
  AssetGenImage get user => const AssetGenImage('assets/images/more/user.png');
}

class $AssetsImagesOrderGen {
  const $AssetsImagesOrderGen();

  /// File path: assets/images/order/crown.png
  AssetGenImage get crown =>
      const AssetGenImage('assets/images/order/crown.png');
}

class $AssetsImagesReportGen {
  const $AssetsImagesReportGen();

  /// File path: assets/images/report/calc.png
  AssetGenImage get calc =>
      const AssetGenImage('assets/images/report/calc.png');

  /// File path: assets/images/report/down.png
  AssetGenImage get down =>
      const AssetGenImage('assets/images/report/down.png');

  /// File path: assets/images/report/send.png
  AssetGenImage get send =>
      const AssetGenImage('assets/images/report/send.png');
}

class $AssetsImagesRestGen {
  const $AssetsImagesRestGen();

  /// File path: assets/images/rest/rice_delivery.png
  AssetGenImage get riceDelivery =>
      const AssetGenImage('assets/images/rest/rice_delivery.png');
}

class $AssetsImagesTabGen {
  const $AssetsImagesTabGen();

  /// File path: assets/images/tab/more.png
  AssetGenImage get more => const AssetGenImage('assets/images/tab/more.png');

  /// File path: assets/images/tab/more_select.png
  AssetGenImage get moreSelect =>
      const AssetGenImage('assets/images/tab/more_select.png');

  /// File path: assets/images/tab/order.png
  AssetGenImage get order => const AssetGenImage('assets/images/tab/order.png');

  /// File path: assets/images/tab/order_select.png
  AssetGenImage get orderSelect =>
      const AssetGenImage('assets/images/tab/order_select.png');

  /// File path: assets/images/tab/report.png
  AssetGenImage get report =>
      const AssetGenImage('assets/images/tab/report.png');

  /// File path: assets/images/tab/report_select.png
  AssetGenImage get reportSelect =>
      const AssetGenImage('assets/images/tab/report_select.png');

  /// File path: assets/images/tab/restaurant.png
  AssetGenImage get restaurant =>
      const AssetGenImage('assets/images/tab/restaurant.png');

  /// File path: assets/images/tab/restaurant_select.png
  AssetGenImage get restaurantSelect =>
      const AssetGenImage('assets/images/tab/restaurant_select.png');
}

class $AssetsImagesIconsOrderGen {
  const $AssetsImagesIconsOrderGen();

  /// File path: assets/images/icons/order/search.png
  AssetGenImage get search =>
      const AssetGenImage('assets/images/icons/order/search.png');

  /// File path: assets/images/icons/order/setting.png
  AssetGenImage get setting =>
      const AssetGenImage('assets/images/icons/order/setting.png');
}

class Assets {
  Assets._();

  static const $AssetsConfigGen config = $AssetsConfigGen();
  static const $AssetsHeatmapGen heatmap = $AssetsHeatmapGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const String order = 'assets/order.mp3';
}

class AssetGenImage extends AssetImage {
  const AssetGenImage(String assetName) : super(assetName);

  Image image({
    Key? key,
    ImageFrameBuilder? frameBuilder,
    ImageLoadingBuilder? loadingBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? width,
    double? height,
    Color? color,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    FilterQuality filterQuality = FilterQuality.low,
  }) {
    return Image(
      key: key,
      image: this,
      frameBuilder: frameBuilder,
      loadingBuilder: loadingBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      width: width,
      height: height,
      color: color,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      filterQuality: filterQuality,
    );
  }

  String get path => assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName);

  final String _assetName;

  SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    Color? color,
    BlendMode colorBlendMode = BlendMode.srcIn,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    Clip clipBehavior = Clip.hardEdge,
  }) {
    return SvgPicture.asset(
      _assetName,
      key: key,
      matchTextDirection: matchTextDirection,
      bundle: bundle,
      package: package,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      color: color,
      colorBlendMode: colorBlendMode,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      clipBehavior: clipBehavior,
    );
  }

  String get path => _assetName;
}
