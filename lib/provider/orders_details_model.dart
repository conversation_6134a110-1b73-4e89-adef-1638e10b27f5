import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:sprintf/sprintf.dart';
import 'package:connect/data/orders_entity.dart';

class OrdersDetailsModel extends ChangeNotifier {
  static Future<Response?> orderDetails(String? orderId) async {
    var url = sprintf(HttpUri.ORDERS_DETAILS, ["$orderId"]);
    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "report";
    Options requestOptions = new Options(extra: opExtras);
    return await HttpManager.instance!
        .getUri(url, option: requestOptions, withLoading: false);
  }

  OrdersEntity? ordersEntity;

  Future<OrdersEntity?> orderCancel(String? orderId, String? message,
      {loading = true}) async {
    var restId = ConnectCache.getCurrentRole()!.scope;
    var url = sprintf(HttpUri.ORDERS_CANCEL, ["$restId", "$orderId"]);
    var params = Map<String, dynamic>();
    params["message"] = "$message";
    params["status"] = "cancelled";
    var response = await HttpManager.instance!
        .put(url, data: params, withLoading: loading);
    if (response != null) {
      ordersEntity = JsonConvert.fromJsonAsT<OrdersEntity>(response.data);
      notifyListeners();
      return ordersEntity;
    }
    return null;
  }
}
