import 'package:connect/common/global.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/data/repository/order_repository.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class PreOrderViewModel extends ChangeNotifier {
  List<OrdersEntity> items = [];

  notify() {
    notifyListeners();
  }

  Future requestPreOrder(
      BuildContext context, bool isExpanded, String from, String to,
      {String? text,
      bool isShowLoading = false,
      bool option = false,
      Success? success,
      Error? error}) async {
    LogUtil.v("preOrder start");
    return await OrderRepository.requestOrder(context,
            from: from,
            to: to,
            error: error,
            option: option,
            isShowLoading: isShowLoading)
        .then((value) {
      if (value != null) {
        LogUtil.v("preOrder success");
        var responseItems =
            JsonConvert.fromJsonAsT<List<OrdersEntity>>(value.data) ?? [];
        Orientation orientation = MediaQuery.of(context).orientation;
        if (orientation == Orientation.landscape) {
          if (responseItems != null && responseItems.length > 0) {
            LogUtil.v("preOrder refresh order details");
            bool haSelected = false;
            items.forEach((element) {
              if (element.isSelected == true) {
                responseItems.forEach((responseElement) {
                  if (responseElement.orderId == element.orderId) {
                    haSelected = true;
                    responseElement.isSelected = true;
                    GlobalConfig.eventBus
                        .fire(ResSyncOrderStatus(responseElement));
                  }
                });
              }
            });
            if (!haSelected &&
                responseItems != null &&
                responseItems.length > 0) {
              LogUtil.v("refresh order details");
              responseItems[0].isSelected = true;
              //refresh order details when screen is landscape on tablet
              GlobalConfig.eventBus.fire(ResSyncOrderStatus(responseItems[0]));
            }
          } else {
            GlobalConfig.eventBus.fire(ResSyncOrderStatus(null));
          }
        }
        items = responseItems;
        LogUtil.v("ScheduledPage notifyListeners ${items.length}");
        notifyListeners();
      }
    }).catchError((e) {
      Sentry.captureException("preOrder_list_exception::${e.toString()}");
    });
  }

  setItems(List<OrdersEntity> items) {
    this.items = items;
  }
}
