import 'package:connect/data/rest_entity.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:sprintf/sprintf.dart';

class RestModel extends ChangeNotifier {
  RestEntity? restEntity;

  Future<void> restInfo() async {
    var restId = ConnectCache.getCurrentRole()!.scope;
    LogUtil.v("restId::$restId");
    var url = sprintf(HttpUri.RESTAURANTS_DETAILS, ["$restId"]);

    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "rest";
    Options requestOptions = new Options(extra: opExtras);

    var response = await HttpManager.instance!
        .get(url, option: requestOptions, withLoading: false);
    if (response != null) {
      restEntity = JsonConvert.fromJsonAsT<RestEntity>(response.data);
      notifyListeners();
    }
  }

  ///request restaurant close
  Future<void> closed(Map closed) async {
    var restId = ConnectCache.getCurrentRole()!.scope;
    LogUtil.v("restId::$restId");
    var url = sprintf(HttpUri.RESTAURANTS_DETAILS, ["$restId"]);
    var data = Map<String, dynamic>();
    data["closed"] = closed;
    var response =
        await HttpManager.instance!.put(url, data: data, withLoading: true);
    if (response != null) {
      restEntity = JsonConvert.fromJsonAsT<RestEntity>(response.data);
      notifyListeners();
    }
  }

  ///request restaurant open
  Future<void> open() async {
    var restId = ConnectCache.getCurrentRole()!.scope;
    LogUtil.v("restId::$restId");
    var url = sprintf(HttpUri.RESTAURANTS_DETAILS, ["$restId"]);
    var data = Map<String, dynamic>();
    data["closed"] = null;
    var response =
        await HttpManager.instance!.put(url, data: data, withLoading: true);
    if (response != null) {
      restEntity = JsonConvert.fromJsonAsT<RestEntity>(response.data);
      notifyListeners();
    }
  }
}
