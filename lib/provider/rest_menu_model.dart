import 'dart:convert';

import 'package:connect/data/rest_menu_entity.dart';
import 'package:connect/data/rest_menu_toggle_entity.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_error.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:sprintf/sprintf.dart';

class RestMenuModel extends ChangeNotifier {
  RestMenuEntity? restMenuEntity;

  Future<void> requestMenu() async {
    try {
      var scope = ConnectCache.getCurrentRole()!.scope;
      String? url = sprintf(HttpUri.REST_MENU_LIST, [scope]);
      var response = await HttpManager.instance!.get(url, withLoading: false);
      if (response != null) {
        RestMenuEntity? result =
            JsonConvert.fromJsonAsT<RestMenuEntity>(response.data);
        //keep menu item expanded status
        if (restMenuEntity != null) {
          restMenuEntity!.categories?.forEach((element) {
            result?.categories?.forEach((elementNew) {
              if (elementNew!.sId == element!.sId) {
                elementNew.isExpanded = element.isExpanded;
              }
            });
          });
        }
        restMenuEntity = result;
        notifyListeners();
      }
    } catch (e) {
      MessageDialog.messageAlert(e.toString(), ok: () {});
    }
  }

  Future<RestMenuToggleEntity?> requestMenuToggle(
      bool available, String? foodId,
      {loading = true, ValueChanged? error}) async {
    try {
      var scope = ConnectCache.getCurrentRole()!.scope;
      var url = sprintf(HttpUri.REST_MENU_TOGGLE, [scope, foodId]);
      var data = Map<String, dynamic>();
      data["available"] = available;

      Options? requestOptions;
      LogUtil.v("loading::$loading");
      if (!loading) {
        var opExtras = Map<String, dynamic>();
        opExtras["type"] = "rest";
        requestOptions = new Options(extra: opExtras);
      }

      var response = await HttpManager.instance!.put(url,
          data: data,
          option: requestOptions,
          withLoading: loading,
          callback: error);
      if (response != null) {
        RestMenuToggleEntity? toggleEntity =
            JsonConvert.fromJsonAsT<RestMenuToggleEntity>(response.data);
        return toggleEntity;
      }
    } catch (e) {
      if (e is DioError) {
        MessageDialog.messageAlert(HttpError.errorHandler(e));
      } else {
        MessageDialog.messageAlert(e.toString(), ok: () {});
      }
    }
    return null;
  }

  Future<RestMenuToggleEntity?> requestMenuOptionsOneToggle(
      Map? data, String? foodId) async {
    try {
      var scope = ConnectCache.getCurrentRole()!.scope;
      String? url = sprintf(HttpUri.REST_MENU_TOGGLE, [scope, foodId]);
      var response =
          await HttpManager.instance!.put(url, data: data, withLoading: true);
      if (response != null) {
        RestMenuToggleEntity? toggleEntity =
            JsonConvert.fromJsonAsT<RestMenuToggleEntity>(response.data);
        return toggleEntity;
      }
    } catch (e) {
      MessageDialog.messageAlert(e.toString(), ok: () {});
    }
    return null;
  }

  Future<RestMenuToggleEntity?> requestMenuOptionsAllToggle(
      String options, String? foodId) async {
    try {
      var scope = ConnectCache.getCurrentRole()!.scope;
      var url = sprintf(HttpUri.REST_MENU_TOGGLE, [scope, foodId]);
      var data = Map<String, dynamic>();
      data["options"] = jsonDecode(options);

      var opExtras = Map<String, dynamic>();
      opExtras["type"] = "rest";
      Options requestOptions = new Options(extra: opExtras);

      var response = await HttpManager.instance!
          .put(url, data: data, option: requestOptions, withLoading: false);
      if (response != null) {
        RestMenuToggleEntity? toggleEntity =
            JsonConvert.fromJsonAsT<RestMenuToggleEntity>(response.data);
        return toggleEntity;
      }
    } catch (e) {
      if (e is DioError) {
        MessageDialog.messageAlert(HttpError.errorHandler(e));
      } else {
        MessageDialog.messageAlert(e.toString(), ok: () {});
      }
    }
    return null;
  }
}
