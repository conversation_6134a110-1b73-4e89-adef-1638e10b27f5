import 'package:connect/utils/regex_util.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';

class LoginProvider with ChangeNotifier {
  bool _isValidEmail = false;
  bool _isValidPassword = false;
  bool _isValidPhone = false;
  bool _isValidSmsCode = false;
  String _regionCode = "+1";
  String _email = "";
  String _password = "";
  String _phone = "";
  String _smsCode = "";
  int _tabIndex = 0;

  FocusNode? _passwordNodeFocus;
  FocusNode? _phoneNodeFocus;

  void setTabIndex(int tabIndex) {
    this._tabIndex = tabIndex;
  }

  get tabIndex => _tabIndex;

  void setPasswordNodeFocus(FocusNode passwordNodeFocus) {
    this._passwordNodeFocus = passwordNodeFocus;
  }

  get passwordNodeFocus => _passwordNodeFocus;

  void setPhoneNodeFocus(FocusNode phoneNodeFocus) {
    this._phoneNodeFocus = phoneNodeFocus;
  }

  get phoneNodeFocus => _phoneNodeFocus;

  bool loginHighlightByPassword() {
    return _isValidEmail && _isValidPassword;
  }

  bool loginHighlightByPhone() {
    return _isValidPhone && _isValidSmsCode;
  }

  void notify() {
    notifyListeners();
  }

  void setPhone(String phone,
      {bool notify = true}) {
    this._phone = phone;
    if (phone.length >= 9) {
      final phoneNumber = PhoneNumber.parse(_regionCode + phone);
      _isValidPhone = phoneNumber.isValid();
      if (notify) {
        notifyListeners();
      }
      // .then((value) {
      //   _isValidPhone = true;
      // }, onError: (e) {
      //   _isValidPhone = false;
      // }).whenComplete(() {
      //   if (notify) notifyListeners();
      // });
    } else {
      _isValidPhone = false;
      if (notify) notifyListeners();
    }
  }

  get isValidPhone => _isValidPhone;

  get isValidSmsCode => _isValidSmsCode;

  get phone => _phone;

  void setSmsCode(String smsCode, {bool notify = true}) {
    this._smsCode = smsCode;
    if (smsCode.length == 4) {
      this._isValidSmsCode = true;
    } else {
      this._isValidSmsCode = false;
    }
    if (notify) {
      notifyListeners();
    }
  }

  get smsCode => _smsCode;

  void setEmail(String email) {
    this._email = email;
    this._isValidEmail = RegexUtil.isEmail(email);
    notifyListeners();
  }

  get email => _email;

  get isValidEmail => _isValidEmail;

  void setPassword(String password) {
    this._password = password;
    if (password.length >= 6) {
      this._isValidPassword = true;
    } else {
      this._isValidPassword = false;
    }
    // LogUtil.v("LoginIn::setPassword");
    notifyListeners();
  }

  get password => _password;

  get isValidPassword => _isValidPassword;

  void setRegionCode(String regionCode) {
    this._regionCode = regionCode;
    notifyListeners();
  }

  get regionCode => _regionCode;
}
