import 'package:connect/data/repository/report_repository.dart';
import 'package:connect/data/repository/restaurant_details_entity.dart';
import 'package:connect/data/summary_report_entity.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/ui/home/<USER>/food/report_food_entity.dart';
import 'package:flutter/cupertino.dart';
import '../data/orders_entity.dart';
import '../data/summary_report_v1_entity.dart';
import '../ui/view/loading.dart';
import '../utils/connect_cache.dart';

class ReportModel extends ChangeNotifier {
  RestaurantDetailsEntity? restaurantEntity;
  List<SummaryReportEntity> summaryList = [];
  List<SummaryReportV1Entity> summaryV1List = [];
  List<OrdersEntity> adjustList = [];
  List<OrdersEntity> orderList = [];
  List<OrdersEntity> searchList = [];

  reset() {
    foodItems = null;
    restaurantEntity = null;
    summaryList = [];
    summaryV1List = [];
    adjustList = [];
    orderList = [];
    searchList = [];
  }

  Future<RestaurantDetailsEntity?> restaurantDetails(
      {bool isFirst = true, String? from, String? to}) async {
    var restId = ConnectCache.getCurrentRole()!.scope;
    restaurantEntity = await ReportRepository.restaurantDetails(restId);
    return restaurantEntity;
  }

  ///when date selected
  Future<void> reportAndAdjustments(String from, String to,
      {loading = false}) async {
    if (loading) LoadingUtils.show();
    var restId = ConnectCache.getCurrentRole()!.scope!;
    summaryList =
        await ReportRepository.report(restId, from, to, loading: loading);
    adjustList =
        await ReportRepository.adjustments(restId, from, to, loading: loading);
    if (loading) LoadingUtils.dismiss();
    notifyListeners();
  }

  Future<void> requestReport(String from, String to, {loading = false}) async {
    if (loading) LoadingUtils.show();
    var restId = ConnectCache.getCurrentRole()!.scope!;
    summaryV1List =
        await ReportRepository.reportV1(restId, from, to, loading: loading);
    if (loading) LoadingUtils.dismiss();
    notifyListeners();
  }

  List<ReportFoodEntity>? foodItems;

  Future<List<ReportFoodEntity>?> reportFood(String from, String to,
      {loading = true}) async {
    var restId = ConnectCache.getCurrentRole()!.scope!;
    var params = Map<String, dynamic>();
    params["from"] = from;
    params["to"] = to;
    params["groupBy"] = "food";
    var url = HttpUri.REPORT.replaceAll("{restaurant_id}", restId);
    var response = await HttpManager.instance!
        .get(url, params: params, withLoading: loading);
    notifyListeners();
    if (response != null) {
      foodItems =
          JsonConvert.fromJsonAsT<List<ReportFoodEntity>>(response.data);
      return foodItems;
    }
    return null;
  }

  Future<void> requestAdjustments(String from, String to,
      {loading = false}) async {
    if (loading) LoadingUtils.show();
    var restId = ConnectCache.getCurrentRole()!.scope!;
    adjustList =
        await ReportRepository.adjustments(restId, from, to, loading: loading);
    if (loading) LoadingUtils.dismiss();
    notifyListeners();
  }

  ///up to load
  Future adjustments(int p, String from, String to, {loading = false}) async {
    var restId = ConnectCache.getCurrentRole()!.scope!;
    List<OrdersEntity>? newAdjustList = await ReportRepository.adjustments(
        restId, from, to,
        page: p, loading: loading);
    if (p > 1) {
      adjustList.addAll(newAdjustList);
    } else {
      adjustList = newAdjustList;
    }
    notifyListeners();
  }

  ///up to load
  Future refreshOrders(String from, String to, {loading = false}) async {
    var restId = ConnectCache.getCurrentRole()!.scope!;
    List<OrdersEntity>? newOrderList =
        await ReportRepository.orders(restId, from, to, loading: loading);
    orderList = newOrderList;
    notifyListeners();
  }

  Future loadOrders(int p, String from, String to, {loading = false}) async {
    var restId = ConnectCache.getCurrentRole()!.scope!;
    List<OrdersEntity>? newAdjustList = await ReportRepository.orders(
        restId, from, to,
        page: p, loading: loading);
    if (p > 1) {
      orderList.addAll(newAdjustList);
    } else {
      orderList = newAdjustList;
    }
    notifyListeners();
  }

  Future searchOrders(String q, String from, String to,
      {loading = false}) async {
    var restId = ConnectCache.getCurrentRole()!.scope!;
    List<OrdersEntity>? newOrderList =
        await ReportRepository.orders(restId, from, to, loading: loading, q: q);
    searchList = newOrderList;
    notifyListeners();
  }

  Future loadSearchOrders(String q, int p, String from, String to,
      {loading = false}) async {
    var restId = ConnectCache.getCurrentRole()!.scope!;
    List<OrdersEntity>? newAdjustList = await ReportRepository.orders(
        restId, from, to,
        page: p, loading: loading, q: q);
    if (p > 1) {
      searchList.addAll(newAdjustList);
    } else {
      searchList = newAdjustList;
    }
    notifyListeners();
  }

  Future sendReport(String from, String to, String type,
      {loading = false}) async {
    var restId = ConnectCache.getCurrentRole()!.scope!;
    await ReportRepository.sendReport(restId, from, to, type, loading: loading);
    notifyListeners();
  }
}
