import 'package:connect/data/orders_entity.dart';
import 'package:connect/data/repository/order_repository.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:flutter/material.dart';

class SearchViewModel extends ChangeNotifier {
  List<OrdersEntity> _items = [];

  get items => _items;

  Future searchOrder(BuildContext context,
      {String? text, Success? success, Error? error}) async {
    await OrderRepository.requestOrder(context,
            text: text, success: success, error: error)
        .then((value) {
      if (value != null) {
        _items = JsonConvert.fromJsonAsT<List<OrdersEntity>>(value.data) ?? [];
        notifyListeners();
      }
    }).catchError((e) {});
  }

  setItems(List<OrdersEntity> items, {bool notify = false}) {
    _items = items;
    if (notify) {
      notifyListeners();
    }
  }
}
