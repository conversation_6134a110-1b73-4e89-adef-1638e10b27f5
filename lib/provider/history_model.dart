import 'package:connect/common/global.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/data/repository/order_repository.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class HistoryViewModel extends ChangeNotifier {
  List<OrdersEntity> items = [];

  notify() {
    notifyListeners();
  }

  Future requestHistory(BuildContext context, String from, String to, int page,
      {String? text,
      bool isShowLoading = false,
      bool option = false,
      Success? success,
      Error? error}) async {
    LogUtil.v("history start");
    return await OrderRepository.requestOrder(context,
            from: from,
            to: to,
            page: page,
            error: error,
            option: option,
            isShowLoading: isShowLoading)
        .then((value) {
      if (value != null) {
        LogUtil.v("history success");
        List<OrdersEntity> responseItems =
            JsonConvert.fromJsonAsT<List<OrdersEntity>>(value.data) ?? [];
        if (page == 1) {
          Orientation orientation = MediaQuery.of(context).orientation;
          if (orientation == Orientation.landscape) {
            bool haSelected = false;
            items.forEach((element) {
              if (element.isSelected == true) {
                responseItems.forEach((responseElement) {
                  if (responseElement.orderId == element.orderId) {
                    haSelected = true;
                    responseElement.isSelected = true;
                    GlobalConfig.eventBus
                        .fire(ResSyncOrderStatus(responseElement));
                  }
                });
              }
            });
            if (!haSelected &&
                responseItems != null &&
                responseItems.length > 0) {
              LogUtil.v("history refresh order details");
              responseItems[0].isSelected = true;
              //refresh order details when screen is landscape on tablet
              GlobalConfig.eventBus.fire(ResSyncOrderStatus(responseItems[0]));
            }
          }
          items = responseItems;
        } else if (page > 1) {
          items.addAll(responseItems);
        }
        LogUtil.v("HistoryPage notifyListeners ${items.length}");
        notifyListeners();
      }
    }).catchError((e) {
      Sentry.captureException("history_list_exception::${e.toString()}");
    });
  }

  setItems(List<OrdersEntity> items) {
    this.items = items;
  }
}
