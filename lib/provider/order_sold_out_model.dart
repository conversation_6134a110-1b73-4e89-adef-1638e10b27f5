import 'package:connect/data/rest_sold_out_bean.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/http/http_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:sprintf/sprintf.dart';

class OrderSoldOutModel extends ChangeNotifier {
  ///foodId,foodName
  Map<String?, String?> allFood = {};

  ///foodId,RestSoldOutBean
  Map<String?, RestSoldOutBean> foodBeans = {};

  ///foodId,RestSoldOutBean
  Map<String?, List<RestSoldOutBean>> optionsBeans = {};

  bool refreshFlag = false;

  resetSoldOutBean() {
    allFood = {};
    foodBeans = {};
    optionsBeans = {};
  }

  setAllFood(String? foodId, String foodName) {
    allFood[foodId] = foodName;
  }

  setFoodBean(String? foodId, String foodName) {
    var bean = RestSoldOutBean();
    bean.foodId = foodId;
    bean.foodName = foodName;
    foodBeans[foodId] = bean;

    if (optionsBeans.containsKey(foodId)) {
      optionsBeans.remove(foodId);
    }
    notifyListeners();
  }

  removeFoodBean(String? foodId) {
    foodBeans.remove(foodId);
    notifyListeners();
  }

  setOptionsBean(String? foodId, String? optionId, String optionName) {
    var bean = RestSoldOutBean();
    bean.foodId = foodId;
    bean.optionId = optionId;
    bean.optionName = optionName;

    if (!optionsBeans.containsKey(foodId)) {
      List<RestSoldOutBean> optionsList = [];
      optionsList.add(bean);
      optionsBeans[foodId] = optionsList;
    } else {
      var list = optionsBeans[foodId];
      list?.add(bean);
    }

    notifyListeners();
  }

  removeOptionsBean(String? foodId, String? optionId) {
    var bean;
    optionsBeans.forEach((key, value) {
      if (key == foodId) {
        value.forEach((element) {
          if (element.optionId == optionId) {
            bean = element;
          }
        });
        if (bean != null) value.remove(bean);
      }
    });
    notifyListeners();
  }

  Future<void> requestFoodSelfOff(
      String? foodId, String from, String to, String? note,
      {loading = true, ValueChanged? error}) async {
    var scope = ConnectCache.getCurrentRole()!.scope;
    var url = sprintf(HttpUri.REST_MENU_TOGGLE, [scope, foodId]);
    var data = Map<String, dynamic>();

    var closePeriodObj = Map<String, dynamic>();
    closePeriodObj["from"] = from;
    closePeriodObj["to"] = to;
    closePeriodObj["note"] = note;

    data["closePeriod"] = closePeriodObj;

    Options? requestOptions;
    if (!loading) {
      var opExtras = Map<String, dynamic>();
      opExtras["type"] = "rest";
      requestOptions = new Options(extra: opExtras);
    }

    await HttpManager.instance!.put(url,
        data: data,
        option: requestOptions,
        withLoading: loading,
        callback: error);
  }

  Future<Response?> requestSelfOff(
      String foodId, String optionId, String from, String to, String note,
      {ValueChanged? error}) async {
    var scope = ConnectCache.getCurrentRole()!.scope;
    var url = sprintf(HttpUri.REST_MENU_OPTIONS, [scope, foodId, optionId]);

    var data = Map<String, dynamic>();

    var closePeriodObj = Map<String, dynamic>();
    closePeriodObj["from"] = from;
    closePeriodObj["to"] = to;
    closePeriodObj["note"] = note;

    data["closePeriod"] = closePeriodObj;

    var opExtras = Map<String, dynamic>();
    opExtras["type"] = "rest";
    Options requestOptions = new Options(extra: opExtras);

    var response = await HttpManager.instance!.put(url,
        data: data,
        option: requestOptions,
        withLoading: false,
        callback: error);
    if (response != null) {
      return response;
    }

    return null;
  }
}
