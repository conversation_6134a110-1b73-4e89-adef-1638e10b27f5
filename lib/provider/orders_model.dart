import 'package:connect/common/global.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/data/repository/order_repository.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/ui/home/<USER>/order_status_manager.dart';
import 'package:connect/utils/audio_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class OrdersViewModel extends ChangeNotifier {
  List<OrdersEntity>? items = [];
  List<OrdersEntity> itemsWaitConfirm = [];
  List<OrdersEntity?> itemsWaitReady = [];
  List<OrdersEntity?> itemsWaitCourierCome = [];

  reset() {
    items = [];
    itemsWaitConfirm = [];
    itemsWaitReady = [];
    itemsWaitCourierCome = [];
  }

  void confirmToReady(OrdersEntity? entity) {
    itemsWaitConfirm.remove(entity);
    itemsWaitReady.insert(0, entity);
    notifyListeners();
  }

  void readyToWaitCourierCome(OrdersEntity? entity) {
    itemsWaitReady.remove(entity);
    itemsWaitCourierCome.insert(0, entity);
    notifyListeners();
  }

  Future requestOrder(BuildContext context,
      {String? text,
      bool showMsg = true,
      bool option = false,
      Success? success,
      Error? error,
      ValueChanged? callback}) async {
    await OrderRepository.requestOrder(context,
            text: text,
            showMsg: showMsg,
            option: option,
            success: success,
            error: error,
            callback: callback)
        .then((value) {
      // LogUtil.v("ordersList_value::$value");
      if (value != null) {
        var responseItems =
            JsonConvert.fromJsonAsT<List<OrdersEntity>>(value.data);
        Orientation orientation =
            MediaQuery.of(GlobalConfig.navigatorKey.currentContext!)
                .orientation;
        if (orientation == Orientation.landscape &&
            GlobalConfig.currentTabIndex == 0) {
          // if (GlobalConfig.currentLabelIndex == 0) {
          // LogUtil.v("refresh order list-----------------------0");
          bool haSelected = false;
          items!.forEach((element) {
            if (element.isSelected == true) {
              responseItems?.forEach((responseElement) {
                if (responseElement.orderId == element.orderId) {
                  LogUtil.v(
                      "order list passcode ${responseElement.passcode} selected");
                  haSelected = true;
                  responseElement.isSelected = true;
                  if (GlobalConfig.currentLabelIndex == 0) {
                    //avoid order status sync failed when order cancelled
                    GlobalConfig.eventBus
                        .fire(ResSyncOrderStatus(responseElement));
                  }
                }
              });
            }
          });
          LogUtil.v(
              "refresh order list-----------------------,haSelected,$haSelected");
          if (!haSelected &&
              responseItems != null &&
              responseItems.length > 0) {
            // LogUtil.v("refresh order list-----------------------2");
            responseItems[0].isSelected = true;
            //refresh order details when screen is landscape on tablet
            GlobalConfig.eventBus.fire(ResSyncOrderStatus(responseItems[0]));
            // context.read<OrdersDetailsRefreshProvider>().setOrders(responseItems[0]);
          }
        }
        items = responseItems;
        itemsWaitConfirm.clear();
        itemsWaitReady.clear();
        itemsWaitCourierCome.clear();
        if (items != null && items!.length > 0) {
          items!.forEach((element) {
            if (OrderStatusManager.isNewOrder(element)) {
              itemsWaitConfirm.add(element);
            } else if (OrderStatusManager.restaurantConfirmedButNotChucan(
                element)) {
              itemsWaitReady.add(element);
            } else if (OrderStatusManager.matchCourierToRestaurant(element) ||
                OrderStatusManager.courierAtPickup(element)) {
              itemsWaitCourierCome.add(element);
            }
          });
        }

        if (orientation == Orientation.landscape &&
            GlobalConfig.currentTabIndex == 0 &&
            GlobalConfig.currentLabelIndex == 1) {
          if (itemsWaitConfirm.isEmpty) {
            GlobalConfig.eventBus.fire(ResSyncOrderStatus(null));
          } else {
            bool hasSelected = false;
            itemsWaitConfirm.forEach((element) {
              LogUtil.v(
                  "new,passcode::${element.passcode},isSelected::${element.isSelected}");
              if (element.isSelected!) {
                hasSelected = true;
              }
            });
            if (!hasSelected) {
              itemsWaitConfirm[0].isSelected = true;
              GlobalConfig.eventBus
                  .fire(ResSyncOrderStatus(itemsWaitConfirm[0]));
            }
          }
        }

        if (orientation == Orientation.landscape &&
            GlobalConfig.currentTabIndex == 0 &&
            GlobalConfig.currentLabelIndex == 2) {
          if (itemsWaitReady.isEmpty) {
            GlobalConfig.eventBus.fire(ResSyncOrderStatus(null));
          } else {
            bool hasSelected = false;
            itemsWaitReady.forEach((element) {
              LogUtil.v(
                  "progress,passcode::${element!.passcode},isSelected::${element.isSelected}");
              if (element.isSelected!) {
                hasSelected = true;
              }
            });
            if (!hasSelected) {
              itemsWaitReady[0]!.isSelected = true;
              GlobalConfig.eventBus.fire(ResSyncOrderStatus(itemsWaitReady[0]));
            }
          }
        }
        notifyListeners();
        if (success != null) success(items);
        if (ConnectCache.getRingTone()!) {
          playAudio();
        }
      }
    }).catchError((e) {
      Sentry.captureException("order_list_exception::${e.toString()}");
    });
  }

  setItems(List<OrdersEntity> items, {bool notify = false}) {
    this.items = items;
    if (notify) {
      notifyListeners();
    }
  }

  notify() {
    notifyListeners();
  }

  playAudio() {
    bool onlyPreOrder = false;
    bool hasNewOrder = false;
    OrdersEntity? preOrderEntity;
    if (items != null && items!.isNotEmpty) {
      for (int i = 0; i < items!.length; i++) {
        OrdersEntity element = items![i];
        var preOrder = OrderStatusManager.isNewPreOrder(element);
        if (preOrder) {
          preOrderEntity = element;
          onlyPreOrder = true;
        }
        if (OrderStatusManager.isNewOrderWithoutPreOrder(element)) {
          hasNewOrder = true;
        }
      }
    }

    if (hasNewOrder) {
      //play audio if there are new orders
      AudioManager.instance!.play();
    } else {
      if (onlyPreOrder && preOrderEntity != null) {
        // LogUtil.v(
        //     "different:${DateTime.now().difference(DateUtil.getDateTime(preOrderEntity.restaurant.delivery.window.start).toLocal()).inMinutes}");
        if (DateTime.now()
                .difference(DateUtil.getDateTime(
                        preOrderEntity.restaurant!.delivery!.window!.start!)!
                    .toLocal())
                .inMinutes <
            60) {
          //if all orders are preOrder,and less than one hour
          AudioManager.instance!.play();
        }
      }
    }
  }
}
