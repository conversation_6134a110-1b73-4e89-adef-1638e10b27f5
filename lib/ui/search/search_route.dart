import 'dart:async';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/http/http_error.dart';
import 'package:connect/provider/orders_model.dart';
import 'package:connect/provider/search_model.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/widget/order_card.dart';
import 'package:connect/ui/home/<USER>/search_cancel.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:timezone/timezone.dart' as tz;

import '../../common/MyStyles.dart';
import '../../data/orders_entity.dart';
import '../../data/repository/restaurant_details_entity.dart';
import '../../provider/report_model.dart';
import '../../utils/date_util.dart';
import '../../utils/server_multi_lan.dart';
import '../home/<USER>/orders_details_route.dart';
import '../home/<USER>/widget/order_card_details.dart';

class SearchRoute extends StatefulWidget {
  static String tag = "search_route";

  SearchRoute({Key? key}) : super(key: key);

  final GlobalKey<SearchRightWidgetState> searchRightKey = GlobalKey();

  @override
  _SearchRouteState createState() => _SearchRouteState();
}

class _SearchRouteState extends State<SearchRoute> {
  Timer? _timerDelaySearch;
  TextEditingController _searchController = TextEditingController();
  bool isRefresh = false;

  Future _onRefresh({bool loading = false}) async {
    if (loading) {
      LoadingUtils.show();
    }
    isRefresh = true;
    if (widget.searchRightKey.currentState != null) {
      widget.searchRightKey.currentState!
          .onStatusChange(isRefresh, _searchController.text);
    }
    await context
        .read<SearchViewModel>()
        .searchOrder(context, text: _searchController.text, error: (code, msg) {
      if (code == HttpError.UNKNOWN) {
        ToastUtils.show(msg);
      }
    }).whenComplete(() {
      isRefresh = false;
      if (widget.searchRightKey.currentState != null) {
        widget.searchRightKey.currentState!
            .onStatusChange(isRefresh, _searchController.text);
      }
      if (loading) {
        LoadingUtils.dismiss();
      }
    });
  }

  void _textChange(String text) {
    if (text.isEmpty) {
      _clearList();
    } else {
      if (widget.searchRightKey.currentState != null) {
        widget.searchRightKey.currentState!.onStatusChange(isRefresh, text);
      }
      if (_timerDelaySearch != null) {
        _timerDelaySearch!.cancel();
        _timerDelaySearch = null;
      }
      _timerDelaySearch = new Timer(Duration(milliseconds: 500), () {
        _onRefresh();
      });
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<SearchViewModel>().setItems([]);
    GlobalConfig.eventBus.on<SearchRefreshOrders>().listen((event) async {
      if (mounted) await _onRefresh(loading: true);
    });

    TrackingUtils.instance!.tracking(TConstants.R_SEARCH_PAGE_ENTER);
  }

  @override
  void deactivate() {
    super.deactivate();
    LogUtil.v("deactivate");
    if (GlobalConfig.currentTabIndex == 0) {
      GlobalConfig.eventBus.fire(ResRefreshOrders());
    } else if (GlobalConfig.currentTabIndex == 1) {
      GlobalConfig.eventBus.fire(ResRefreshHistoryOrders());
    } else if (GlobalConfig.currentTabIndex == 2) {
      GlobalConfig.eventBus.fire(ResRefreshScheduledOrders());
    }
  }

  @override
  void dispose() {
    super.dispose();
    LogUtil.v("SearchRoute::dispose");
    if (_timerDelaySearch != null) {
      _timerDelaySearch!.cancel();
      _timerDelaySearch = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    context.watch<OrdersViewModel>();
    return Scaffold(
      backgroundColor: Colors.grey[200],
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.light,
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: Colors.black87,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        elevation: 0,
        title: _buildSearch(),
      ),
      body: context.watch<SearchViewModel>().items.isEmpty
          ? EmptyView("")
          : ListView.builder(
              // itemScrollController: itemScrollController,
              itemBuilder: (context, index) => OrderCard(
                  context.watch<SearchViewModel>().items[index],
                  OrderCardFrom.search),
              itemCount: context.watch<SearchViewModel>().items.length,
            ),
    );
  }

  Widget _buildSearch() {
    return Container(
      margin: const EdgeInsets.only(right: Dimens.dp_40),
      padding: const EdgeInsets.only(right: Dimens.dp_20),
      decoration: BoxDecoration(
          color: Colors.black12,
          borderRadius: BorderRadius.circular(Dimens.dp_30)),
      height: Dimens.dp_35,
      child: Row(
        children: [
          Expanded(
            child: TextField(
              autofocus: true,
              controller: _searchController,
              onChanged: (v) {
                LogUtil.v("onChanged::$v");
                _textChange(v);
              },
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.only(
                    left: Dimens.dp_20, right: Dimens.dp_20),
                hintStyle: TextStyle(
                  color: RColors.gray_99,
                  fontSize: Dimens.sp_14,
                ),
                hintText: S.of(context).search_label,
                border: OutlineInputBorder(
                  //for hintText show in center
                  borderSide: BorderSide(
                    color: Colors.transparent,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Colors.transparent,
                  ),
                ),
                disabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Colors.transparent,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Colors.transparent,
                  ),
                ),
              ),
            ),
            flex: 1,
          ),
          SearchRightWidget(
            widget.searchRightKey,
            searchClear: () {
              _clearList();
            },
          ),
        ],
      ),
    );
  }

  _clearList() {
    //why not invoke onChanged method
    _searchController.text = "";
    //clear list
    context.read<SearchViewModel>().setItems([], notify: true);
    //hide clear icon
    isRefresh = false;
    widget.searchRightKey.currentState!.onStatusChange(isRefresh, "");
  }
}
