import 'package:flutter/material.dart';

class SplashPage extends StatefulWidget {
  SplashPage({Key? key, this.title}) : super(key: key);

  final String? title;

  @override
  State<StatefulWidget> createState() {
    return _SplashPageState();
  }
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    // Future.delayed(Duration(seconds: 2)).then((value) => null)
    // Timer t = Timer(Duration(seconds: 2), () {});
  }

  @override
  Widget build(BuildContext context) {
    return new Material(
      child: new Stack(
        children: <Widget>[
          _buildSplashBg(),
        ],
      ),
    );
  }

  Widget _buildSplashBg() {
    return new Image.asset(
      'assets/images/launch_image.jpg',
      width: double.infinity,
      fit: BoxFit.cover,
      height: double.infinity,
    );
  }
}
