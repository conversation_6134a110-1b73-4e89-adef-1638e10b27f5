import 'package:connect/common/t_constants.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/switch_button.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';

class Ringtone extends StatefulWidget {
  Ringtone({Key? key}) : super(key: key);

  @override
  _RingtoneState createState() => _RingtoneState();
}

class _RingtoneState extends State<Ringtone> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
            height: Dimens.dp_45,
            color: Colors.white,
            padding:
                const EdgeInsets.only(left: Dimens.dp_20, right: Dimens.dp_20),
            child: Row(
              children: [
                Image(
                  image: AssetImage("assets/images/more/notification.png"),
                  width: 16,
                  height: 16,
                ),
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Text(
                      S.of(context).ringtone,
                      style: TextStyle(color: Colors.black87),
                    ),
                  ),
                ),
                SwitchButton((b) {
                  LogUtil.v("RingTone::$b");
                  ConnectCache.saveRingTone(b);

                  var params = Map<String, dynamic>();
                  params["ringtone"] = "$b";
                  TrackingUtils.instance!.tracking(
                      TConstants.R_MORE_RINGTONE_CLICK,
                      value: params);
                }, ConnectCache.getRingTone() == true)
              ],
            )),
      ],
    );
  }
}
