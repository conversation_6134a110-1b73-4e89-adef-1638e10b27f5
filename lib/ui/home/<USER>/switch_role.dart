import 'package:connect/data/user_entity.dart';
import 'package:connect/driver/driver_home_route.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/switch_role_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>';
import 'package:connect/ui/home/<USER>/ondemanddelivery/submit_button.dart';
import 'package:connect/ui/home/<USER>/switch_role_item.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/roles_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:provider/provider.dart';
import 'package:sprintf/sprintf.dart';

class SwitchRole extends StatefulWidget {
  SwitchRole({Key? key}) : super(key: key);

  static String tag = "switch_role_route";

  @override
  _SwitchRoleState createState() => _SwitchRoleState();
}

class _SwitchRoleState extends State<SwitchRole> {
  List<UserRole?>? roles = [];
  List<UserRole?> matchRoles = [];
  String? currentSelectId;
  String? originSelectId;

  @override
  void initState() {
    super.initState();
    context.read<SwitchRoleModel>().roleId = ConnectCache.getCurrentRoleId();
    currentSelectId = ConnectCache.getCurrentRoleId();
    originSelectId = ConnectCache.getCurrentRoleId();
    roles = ConnectCache.getUser()?.roles;
    matchRoles = roles!
        .where((element) =>
            element!.name!.contains("driver") ||
            element.name!.contains("restaurant"))
        .toList();
    //set true if id matched
    matchRoles.forEach((element) {
      if (element!.sId == currentSelectId) {
        element.isSelected = true;
      } else {
        element.isSelected = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    //find driver or restaurant

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        centerTitle: true,
        title: Text(
          S.of(context).switch_role,
          style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_17),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0, top: 12, bottom: 12),
            child: Center(
                child: SubmitButton(
              text: S.of(context).confirm,
              onSubmit: () {
                if (currentSelectId == null) return;
                DriverDialog.show(
                    context,
                    sprintf(S.of(context).switching_role_tips, [
                      roles
                          ?.firstWhere(
                              (element) => element?.sId == currentSelectId)
                          ?.nameAndDesc()
                    ]), () async {
                  SmartDialog.showLoading(
                      msg: S.of(context).switching_role,
                      maskColor: Colors.black54);
                  context.read<SwitchRoleModel>().switchRole(currentSelectId!);
                  ConnectCache.saveCurrentRoleId(currentSelectId!);
                  await Future.delayed(Duration(seconds: 1));
                  SmartDialog.dismiss();
                  await Future.delayed(Duration(milliseconds: 300));
                  Navigator.of(context).pushNamedAndRemoveUntil(
                    RolesManager.isSwitchDriverRole(currentSelectId!)
                        ? DriverHomeRoute.tag
                        : HomeRoute.tag,
                    (route) => route == null,
                  );
                });
              },
              enable: currentSelectId != originSelectId,
              radius: 8,
              disableColor: Colors.grey[500],
            )),
          )
        ],
      ),
      backgroundColor: Colors.white,
      body: Column(
        children: [
          _buildRole(),
          SizedBox(
            height: Dimens.dp_15,
          ),
        ],
      ),
    );
  }

  Widget _buildRole() {
    return ListView.builder(
      scrollDirection: Axis.vertical,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: matchRoles.length,
      itemBuilder: (context, index) {
        return SwitchRoleItem(matchRoles[index], currentSelectId, () {
          setState(() {
            currentSelectId = matchRoles[index]?.sId;
          });
        });
      },
    );
  }
}
