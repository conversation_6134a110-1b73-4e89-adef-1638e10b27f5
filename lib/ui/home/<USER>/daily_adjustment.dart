import 'package:connect/common/MyStyles.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../data/orders_entity.dart';
import '../../../data/repository/restaurant_details_entity.dart';
import '../../../provider/report_model.dart';
import '../../../utils/server_multi_lan.dart';
import '../../view/gif_header.dart';
import '../orders/orders_details_route.dart';
import '../report_route_v2.dart';

class DailyAdjustment extends StatefulWidget {
  const DailyAdjustment({Key? key}) : super(key: key);

  @override
  _DailyAdjustmentState createState() => _DailyAdjustmentState();
}

class _DailyAdjustmentState extends State<DailyAdjustment>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;
  int page = 1;

  @override
  void initState() {
    super.initState();

    _refreshController = RefreshController(initialRefresh: true);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var reportModel = context.watch<ReportModel>();
    var restaurant = context.watch<ReportModel>().restaurantEntity;
    return Container(
      color: MyColors.bg,
      child: SmartRefresher(
        child: ListView.separated(
          itemCount: reportModel.adjustList.length,
          //列表项构造器
          itemBuilder: (BuildContext context, int index) {
            final widget =
                _buildRow(context, reportModel.adjustList[index], restaurant);
            if (index == 0) {
              return Container(
                child: widget,
                padding: EdgeInsets.only(top: 20),
              );
            }
            return widget;
          },
          //分割器构造器
          separatorBuilder: (BuildContext context, int index) {
            return SizedBox(
              height: 16,
            );
          },
        ),
        controller: _refreshController,
        enablePullUp: true,
        physics: AlwaysScrollableScrollPhysics(),
        header: GifHeader(),
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        footer: ClassicFooter(
          loadStyle: LoadStyle.ShowWhenLoading,
        ),
      ),
    );
  }

  Widget _buildRow(BuildContext context, OrdersEntity order,
      RestaurantDetailsEntity? restaurant) {
    var country = "";
    if (restaurant != null) {
      country = restaurant.address!.country!;
    }
    final restAdjustment = order.adjustments?.restaurant ?? 0;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20),
      padding: EdgeInsets.fromLTRB(20, 16, 20, 16),
      decoration: BoxDecoration(
        color: MyColors.W,
        borderRadius: BorderRadius.circular(8),
      ),
      child: GestureDetector(
        onTap: () {
          Navigator.of(context)
              .pushNamed(OrdersDetailsRoute.tag, arguments: order);
        },
        child: _buildRowItem(order, restAdjustment, country),
      ),
    );
  }

  Row _buildRowItem(OrdersEntity order, num restAdjustment, String country) {
    return Row(children: [
      Expanded(
        child: Text.rich(
          TextSpan(
            children: [
              TextSpan(text: "${order.passcode}"),
              TextSpan(
                text:
                    "  ${ServerMultiLan.multiAdapt(order.restaurant?.name)}  ",
                style: MyStyles.r13.copyWith(color: MyColors.color333),
              ),
              TextSpan(
                text: "${order.adjustments?.reason}",
                style: MyStyles.r12.copyWith(color: MyColors.color999),
              ),
            ],
          ),
          style: MyStyles.r13.copyWith(color: MyColors.color999),
          overflow: TextOverflow.fade,
          softWrap: true,
          maxLines: 3,
        ),
      ),
      SizedBox(
        width: 4,
      ),
      Text(
        restAdjustment > 0
            ? ServerMultiLan.coinSymbolCountry(country, restAdjustment / 100)
            : "(${ServerMultiLan.coinSymbolCountry(country, restAdjustment / 100)})",
        style: MyStyles.r13.copyWith(color: MyColors.color999),
      )
    ]);
  }

  void _onRefresh() async {
    try {
      var reportModel = context.read<ReportModel>();

      var value = reportModel.restaurantEntity;
      if (value == null) value = await reportModel.restaurantDetails();
      if (value == null) {
        _refreshController.refreshCompleted();
        return;
      }

      final args =
          ModalRoute.of(context)!.settings.arguments as ReportRouteV2Arguments;
      final from = args.from;
      final to = args.to;
      await reportModel.requestAdjustments(from, to, loading: false);
      _refreshController.refreshCompleted();
      page = 1;
    } catch (e) {
      _refreshController.refreshCompleted();
    }
  }

  void _onLoading() async {
    page++;
    final args =
        ModalRoute.of(context)!.settings.arguments as ReportRouteV2Arguments;
    final from = args.from;
    final to = args.to;
    context
        .read<ReportModel>()
        .adjustments(page, from, to)
        .whenComplete(() => _refreshController.loadComplete());
  }
}
