import 'package:connect/common/constants.dart';
import 'package:connect/data/rest_menu_entity.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/menu/rest_menu_options_category_sub_item_toggle_dialog.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class RestMenuOptionsCategorySubItem extends StatefulWidget {
  RestMenuOptionsCategorySubItem(
      this.item, this.options, this.foodId, this.updateOne, this.updateAll,
      {Key? key})
      : super(key: key);

  final ValueChanged updateOne;
  final ValueChanged updateAll;
  final RestMenuFoodOptionsItems? item;
  final List<RestMenuFoodOptions?>?
      options; //this is request params for update all
  final String? foodId;

  @override
  _RestMenuOptionsCategorySubItemState createState() =>
      _RestMenuOptionsCategorySubItemState();
}

class _RestMenuOptionsCategorySubItemState
    extends State<RestMenuOptionsCategorySubItem> {
  bool? available = false;

  @override
  void initState() {
    super.initState();
    available = widget.item!.available;
  }

  @override
  Widget build(BuildContext context) {
    var restEntity = context.read<RestModel>().restEntity;
    var country;
    if (restEntity != null && restEntity.address != null) {
      country = restEntity.address!.country;
    }
    var item = widget.item!;
    return Container(
      padding: const EdgeInsets.symmetric(
          vertical: Dimens.dp_15, horizontal: Dimens.dp_20),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                Text(ServerMultiLan.multiAdapt(item.name)),
                Text(" ${item.name!.enUs}"),
              ],
            ),
          ),
          Row(
            children: [
              SizedBox(
                width: Dimens.dp_15,
              ),
              Text(
                  "+${ServerMultiLan.coinSymbolCountry(country, item.price! / 100)}"),
              SizedBox(
                width: Dimens.dp_15,
              ),
              CupertinoSwitch(
                  value: available!,
                  activeColor: RColors.r_orange,
                  onChanged: (bool) {
                    change(bool, ServerMultiLan.multiAdapt2(item.name));
                  }),
            ],
          )
        ],
      ),
    );
  }

  change(bool b, String optionName) async {
    LogUtil.v("RestMenuOptionsItem::$b");
    showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return RestMenuCategorySubItemToggleDialog(optionName, available,
              (index) async {
            var data = Map<String, dynamic>();
            data["optionFoodId"] = widget.item!.sId;
            data["optionFoodName"] =
                ServerMultiLan.multiAdapt2(widget.item!.name);
            data["available"] = b;
            data["index"] = index;
            if (Constants.UPDATE_ONE == index) {
              setState(() {
                available = b;
                widget.updateOne(data);
              });
            } else if (Constants.UPDATE_ALL == index) {
              widget.updateAll(data);
            }
          });
        });
  }
}
