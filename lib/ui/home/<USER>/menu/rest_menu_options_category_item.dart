import 'package:connect/data/rest_menu_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/menu/rest_menu_options_category_sub_item.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';

class RestMenuOptionsCategoryItem extends StatefulWidget {
  RestMenuOptionsCategoryItem(
      this.option, this.food, this.updateOne, this.updateAll,
      {Key? key})
      : super(key: key);

  final ValueChanged updateOne;
  final ValueChanged updateAll;
  final RestMenuFoodOptions option;
  final RestMenuFood food;

  @override
  _RestMenuOptionsCategoryItemState createState() =>
      _RestMenuOptionsCategoryItemState();
}

class _RestMenuOptionsCategoryItemState
    extends State<RestMenuOptionsCategoryItem> {
  bool isExpand = false;

  @override
  Widget build(BuildContext context) {
    int optionsLength = 0;
    var optionsItems = widget.option.items;
    if (optionsItems != null) {
      optionsLength = optionsItems.length;
    }

    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              isExpand = !isExpand;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(Dimens.dp_20),
            color: Colors.grey[100],
            child: Row(
              children: [
                Text(ServerMultiLan.multiAdapt(widget.option.name),
                    style: TextStyle(
                      fontSize: Dimens.sp_15,
                    )),
                Text(
                  " ${widget.option.name!.enUs} (${widget.option.min}-${widget.option.max})",
                  style:
                      TextStyle(fontSize: Dimens.sp_13, color: Colors.black54),
                ),
              ],
            ),
          ),
        ),
        isExpand
            ? ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return RestMenuOptionsCategorySubItem(
                      widget.option.items![index]!,
                      widget.food.options,
                      widget.food.foodId,
                      widget.updateOne,
                      widget.updateAll);
                },
                itemCount: optionsLength,
              )
            : EmptyContainer()
      ],
    );
  }
}
