import 'package:connect/generated/json/base/json_field.dart';
import 'package:connect/generated/json/update_one_params_entity.g.dart';

import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/data/rest_menu_entity.dart';

@JsonSerializable()
class UpdateOneParamsEntity {

	UpdateOneParamsEntity();

	factory UpdateOneParamsEntity.fromJson(Map<String, dynamic> json) => $UpdateOneParamsEntityFromJson(json);

	Map<String, dynamic> toJson() => $UpdateOneParamsEntityToJson(this);

  MultiNameEntity? name;
  dynamic description;
  dynamic code;
  int? price;
  int? cost;
  int? index;
  dynamic label;
  List<RestMenuFoodOptions?>? options;
  dynamic hours;
  dynamic image;
  List<dynamic?>? date;
  int? minimum;
  dynamic stock;
  dynamic limit;
}
