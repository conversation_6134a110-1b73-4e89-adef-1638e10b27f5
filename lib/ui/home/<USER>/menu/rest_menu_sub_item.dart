import 'dart:convert';
import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/rest_menu_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_menu_model.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/menu/rest_menu_options_category_dialog.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:provider/provider.dart';
import 'package:sprintf/sprintf.dart';

import '../menu_view.dart';

class RestMenuSubItem extends StatefulWidget {
  RestMenuSubItem(this.food, {Key? key}) : super(key: key);

  final RestMenuFood? food;

  @override
  _RestMenuSubItemState createState() => _RestMenuSubItemState();
}

class _RestMenuSubItemState extends State<RestMenuSubItem> {
  bool available = false;
  String? foodId;

  @override
  void initState() {
    super.initState();


  }

  @override
  Widget build(BuildContext context) {
    available = widget.food!.available!;
    var restEntity = context.read<RestModel>().restEntity;
    var country;
    if (restEntity != null && restEntity.address != null) {
      country = restEntity.address!.country;
    }
    var food = widget.food!;
    var description = food.description;
    foodId = food.foodId;

    bool hasOptionsFood = food.options != null && food.options!.length > 0;

    var url = "";
    if (food.image != null && food.image!.url != null) {
      url = food.image!.url!;
    }
    return InkWell(
      onTap: () {
        if (hasOptionsFood) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return RestMenuOptionsCategoryDialog(food, (op) async {
                  var updateAllMap = Map<String, dynamic>();
                  var updateAllList = [];
                  var read = context.read<RestMenuModel>().restMenuEntity;
                  //update all
                  if (op is Map) {
                    var optionFoodName = op["optionFoodName"];
                    LogUtil.v("optionFoodName::$optionFoodName");
                    var available = op["available"];
                    var index = op["index"];
                    var spendMenuFoodName = "";
                    //match option food id
                    read!.food!.forEach((elementFood) {
                      elementFood!.options!.forEach((element) {
                        element!.items!.forEach((elementOp) {
                          // LogUtil.v("elementOp::${ServerMultiLan.multiAdapt2(elementOp.name)}");
                          if (ServerMultiLan.multiAdapt2(elementOp!.name) ==
                              optionFoodName) {
                            elementOp.available = available;
                            updateAllMap[elementFood.foodId!] =
                                elementFood.options;
                            updateAllList.add(elementFood.foodId);
                            spendMenuFoodName =
                                "$spendMenuFoodName,${ServerMultiLan.multiAdapt2(elementFood.name)}";
                          }
                        });
                      });
                    });
                    if (Constants.UPDATE_ALL == index) {
                      try {
                        LoadingUtils.show();
                        //update option food toggle status
                        await Future.forEach(updateAllList,
                            (dynamic foodId) async {
                          var optionsStr = jsonEncode(updateAllMap[foodId]);
                          //change menu options toggle
                          await GlobalConfig.navigatorKey.currentContext!
                              .read<RestMenuModel>()
                              .requestMenuOptionsAllToggle(optionsStr, foodId);
                        });
                        LoadingUtils.dismiss();
                        MessageDialog.messageAlert(
                            sprintf(
                                S.of(context).connect_update_all_successful, [
                              "${updateAllMap.length}",
                              optionFoodName,
                              spendMenuFoodName.replaceFirst(",", "")
                            ]), ok: () {
                          Navigator.pop(context);
                        });

                        var params = Map<String, dynamic>();
                        params["optionName"] = "$optionFoodName";
                        params["allFood"] = "$spendMenuFoodName";
                        TrackingUtils.instance!.tracking(
                            TConstants.R_MENU_OPTION_UPDATE_ALL_CLICK,
                            value: params);
                      } catch (e) {
                        LogManager.instance!.log("RestMenuSubItem", "",
                            "request update all::${e.toString()}");
                      }
                    }
                  }
                });
              });
        }
      },
      child: Opacity(
        opacity: available ? 1.0 : 0.3,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: Dimens.dp_15),
          color: Colors.white,
          alignment: Alignment.center,
          child: Row(
            children: [
              Container(
                margin: const EdgeInsets.only(
                  right: Dimens.dp_10,
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Image(
                        image: AssetImage(url.isEmpty
                            ? "assets/images/plate_holder.png"
                            : "assets/images/plate.png")),
                    url.isEmpty
                        ? EmptyContainer()
                        : Positioned(
                            left: 3,
                            top: 8,
                            child: Container(
                              child: Image.network(url),
                              width: 72,
                              height: 72,
                            )),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Stack(
                      alignment: AlignmentDirectional.topEnd,
                      children: [
                        Text(
                            "${ServerMultiLan.multiAdapt2(widget.food!.name)}   ",
                            maxLines: 2),
                        hasOptionsFood
                            ? Text("${widget.food!.options!.length}",
                                style: TextStyle(
                                    fontSize: Dimens.sp_12, color: RColors.mr),
                                maxLines: 2)
                            : EmptyContainer(),
                      ],
                    ),
                    Text(
                      ServerMultiLan.multiAdapt2(widget.food!.name),
                      maxLines: 2,
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                    description == null
                        ? EmptyContainer()
                        : Text(
                            "${ServerMultiLan.multiAdapt2(widget.food!.description)} ${widget.food!.description!.enUs}",
                            maxLines: 2),
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                                "${ServerMultiLan.coinSymbolCountry(country, widget.food!.cost! / 100)}"),
                          ),
                          SizedBox(
                            width: Dimens.dp_15,
                          ),
                          Switch(
                              value: available,
                              activeColor: RColors.r_orange,
                              onChanged: (bool) {
                                change(bool);
                              }),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  change(bool b) async {
    LogUtil.v("b::$b");
    //change menu toggle
    var requestMenuToggle =
        await context.read<RestMenuModel>().requestMenuToggle(b, foodId);
    if (requestMenuToggle != null) {

      // setState(() {
      //   available = requestMenuToggle.available!;
      //   widget.food?.available = requestMenuToggle.available!;
      // });

      var params = Map<String, dynamic>();
      params["available"] = "${requestMenuToggle.available}";
      params["food"] = "${widget.food!.name!.zhCn}";
      TrackingUtils.instance!
          .tracking(TConstants.R_MENU_AVAILABLE_CLICK, value: params);

      menuGlobalKey.currentState?.refresh();

    }
  }
}
