import 'package:connect/common/constants.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';

class RestMenuCategorySubItemToggleDialog extends Dialog {
  RestMenuCategorySubItemToggleDialog(
      this.optionName, this.available, this.callback,
      {Key? key})
      : super(key: key);

  final ValueChanged callback;
  final String? optionName;
  final bool? available;

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: Dimens.dp_320,
          height: Dimens.dp_160,
          alignment: Alignment.center,
          padding: const EdgeInsets.all(Dimens.dp_15),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(Dimens.dp_10),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: Dimens.dp_10,
              ),
              Expanded(
                  child: Text(
                "${sprintf(available! ? S.of(context).connect_turn_off : S.of(context).connect_turn_on, [
                      "$optionName"
                    ])}",
                style: TextStyle(
                  fontSize: Dimens.sp_15,
                ),
                textAlign: TextAlign.center,
              )),
              SizedBox(
                height: Dimens.dp_10,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                      child: TextButton(
                    style: ButtonStyle(
                        backgroundColor:
                            MaterialStateProperty.all(Color(0xFFFFFF))),
                    onPressed: () {
                      Future.delayed(Duration(milliseconds: 300))
                          .then((value) => callback(Constants.UPDATE_ONE));
                      Navigator.pop(context);
                    },
                    child: Text(
                      S.of(context).connect_update_one,
                      style: TextStyle(
                        color: Colors.black87,
                        fontSize: Dimens.sp_16,
                      ),
                    ),
                  )),
                  Expanded(
                      child: TextButton(
                    style: ButtonStyle(
                        backgroundColor:
                            MaterialStateProperty.all(Color(0xFF2196F3))),
                    onPressed: () async {
                      Future.delayed(Duration(milliseconds: 300))
                          .then((value) => callback(Constants.UPDATE_ALL));
                      Navigator.pop(context);
                    },
                    child: Text(
                      S.of(context).connect_update_all,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: Dimens.sp_16,
                      ),
                    ),
                  )),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
