import 'package:connect/data/rest_menu_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/menu/rest_menu_sub_item.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';

class RestMenuItem extends StatefulWidget {
  RestMenuItem(this.categories, {Key? key}) : super(key: key);

  final RestMenuCategories categories;

  @override
  _RestMenuItemState createState() => _RestMenuItemState();
}

class _RestMenuItemState extends State<RestMenuItem> {
  bool isExpand = false;

  @override
  Widget build(BuildContext context) {
    var categories = widget.categories;
    var description = categories.description;
    var food = categories.food;

    //available foods
    var availableFoods =
        food!.where((element) => element!.available == true).toList();

    // LogUtil.v("${categories.name.zhCn}");
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: food.isEmpty
                ? null
                : () {
                    setState(() {
                      categories.isExpanded = !categories.isExpanded!;
                    });
                  },
            child: Container(
              padding: const EdgeInsets.symmetric(
                  vertical: Dimens.dp_15, horizontal: Dimens.dp_15),
              width: MediaQuery.of(context).size.width,
              color: Colors.grey[100],
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${ServerMultiLan.multiAdapt(categories.name)} (${availableFoods.length} / ${food.length})",
                          style: TextStyle(fontSize: Dimens.sp_16),
                        ),
                        SizedBox(
                          height: Dimens.dp_3,
                        ),
                        Text(
                          categories.name!.enUs!,
                          style: TextStyle(color: Colors.grey),
                        ),
                        SizedBox(
                          height: Dimens.dp_3,
                        ),
                        description == null
                            ? EmptyContainer()
                            : Text(
                                ServerMultiLan.multiAdapt2(description),
                                style: TextStyle(color: Colors.grey),
                              ),
                      ],
                    ),
                  ),
                  categories.isExpanded!
                      ? Icon(Icons.arrow_drop_up_outlined)
                      : Icon(Icons.arrow_drop_down_outlined),
                ],
              ),
            ),
          ),
         ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    return RestMenuSubItem(food[index]!);
                  },
                  itemCount: food.length,
                )
        ],
      ),
    );
  }
}
