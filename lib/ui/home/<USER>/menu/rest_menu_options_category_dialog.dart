import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/rest_menu_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_menu_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/menu/rest_menu_options_category_item.dart';
import 'package:connect/ui/home/<USER>/menu/update_one_params_entity.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class RestMenuOptionsCategoryDialog extends Dialog {
  RestMenuOptionsCategoryDialog(this.food, this.callback, {Key? key})
      : super(key: key);

  final ValueChanged callback;
  final RestMenuFood food;
  var toggleMap = Map<String, dynamic>();

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: Dimens.dp_420,
          child: Container(
            margin: const EdgeInsets.all(Dimens.dp_15),
            padding: const EdgeInsets.fromLTRB(
                Dimens.dp_0, Dimens.dp_15, Dimens.dp_0, Dimens.dp_15),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(Dimens.dp_15),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.fromLTRB(
                      Dimens.dp_15, Dimens.dp_0, Dimens.dp_15, Dimens.dp_15),
                  color: Colors.white,
                  alignment: Alignment.center,
                  height: Dimens.dp_40,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Text(
                                S.of(context).cancel,
                                style: TextStyle(
                                    color: Colors.blue, fontSize: Dimens.sp_15),
                              ),
                            ),
                            SizedBox(
                              width: Dimens.dp_5,
                            ),
                            Expanded(
                                child: Container(
                              child: Text(ServerMultiLan.multiAdapt2(food.name),
                                  maxLines: 1,
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      color: Colors.black87,
                                      fontSize: Dimens.sp_16,
                                      fontWeight: FontWeight.bold)),
                            )),
                            InkWell(
                              onTap: () async {
                                // var optionFoodId = op["optionFoodId"];
                                // var available = op["available"];
                                var optionName;
                                if (toggleMap != null && toggleMap.length > 0) {
                                  toggleMap.forEach((key, value) {
                                    //match option food id
                                    food.options!.forEach((element) {
                                      element!.items!.forEach((elementOp) {
                                        if (elementOp!.sId == key) {
                                          elementOp.available = value;
                                          optionName = elementOp.name!.zhCn;
                                        }
                                      });
                                    });
                                  });
                                }

                                //update on params object
                                var updateOneParamsEntity =
                                    UpdateOneParamsEntity();
                                updateOneParamsEntity.code = food.code;
                                updateOneParamsEntity.cost = food.cost;
                                updateOneParamsEntity.date = food.date;
                                updateOneParamsEntity.description =
                                    food.description;
                                updateOneParamsEntity.hours = food.hours;
                                updateOneParamsEntity.image = food.image;
                                updateOneParamsEntity.index = food.index;
                                updateOneParamsEntity.label = food.label;
                                updateOneParamsEntity.limit = food.limit;
                                updateOneParamsEntity.minimum = food.minimum;
                                updateOneParamsEntity.name = food.name;
                                updateOneParamsEntity.options = food.options;
                                updateOneParamsEntity.price = food.price;
                                updateOneParamsEntity.stock = food.stock;

                                //change menu options toggle
                                var restMenuToggleEntity = GlobalConfig
                                    .navigatorKey.currentContext!
                                    .read<RestMenuModel>()
                                    .requestMenuOptionsOneToggle(
                                        updateOneParamsEntity.toJson(),
                                        food.foodId);
                                if (restMenuToggleEntity != null) {
                                  var params = Map<String, dynamic>();
                                  params["oneFood"] = "${food.name!.zhCn}";
                                  params["oneOption"] = "$optionName";
                                  TrackingUtils.instance!.tracking(
                                      TConstants.R_MENU_OPTION_UPDATE_ONE_CLICK,
                                      value: params);

                                  toggleMap.clear();
                                  Navigator.pop(context);
                                }
                              },
                              child: Text(
                                S.of(context).connect_save,
                                style: TextStyle(
                                    color: Colors.blue, fontSize: Dimens.sp_15),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(
                  height: Dimens.dp_1,
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(Dimens.dp_20),
                          child: Text(
                            S.of(context).connect_options,
                            style: TextStyle(
                                color: Colors.black,
                                fontSize: Dimens.sp_17,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                        ListView.separated(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              return RestMenuOptionsCategoryItem(
                                  food.options![index]!, food, (op) {
                                if (op is Map) {
                                  toggleMap[op["optionFoodId"]] =
                                      op["available"];
                                }
                              }, callback);
                            },
                            separatorBuilder: (context, index) {
                              return SizedBox(
                                height: Dimens.dp_20,
                              );
                            },
                            itemCount: food.options!.length)
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
