import 'package:connect/common/MyStyles.dart';
import 'package:connect/data/summary_report_v1_entity.dart';
import 'package:connect/generated/l10n.dart' as L10n;

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../data/user_entity.dart';
import '../../../provider/report_model.dart';
import '../../../res/colors.dart';
import '../../../utils/connect_cache.dart';
import '../../../utils/server_multi_lan.dart';
import '../../view/gif_header.dart';
import '../report_route_v2.dart';

enum DailyCardItemType {
  titleWithDivider,
  title,
  subtitle,
}

class DailyCardItem {
  DailyCardItemType type;
  String title;
  String value;
  Color? color;

  DailyCardItem(this.type, this.title, this.value);
}

class DailyCardGroup {
  DailyCardItem? title;
  List<DailyCardItem> items;
  DailyCardGroup(this.title, this.items);
}

class DailyDistribution extends StatefulWidget {
  const DailyDistribution({Key? key}) : super(key: key);

  @override
  _DailyDistributionState createState() => _DailyDistributionState();
}

class _DailyDistributionState extends State<DailyDistribution>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;

  @override
  void initState() {
    super.initState();

    _refreshController = RefreshController(initialRefresh: true);
  }

  SummaryReportV1Entity reduceSummary(
      SummaryReportV1Entity value1, SummaryReportV1Entity element) {
    var value = SummaryReportV1Entity.fromJson(value1.toJson());
    //number
    value.number = (value.number ?? 0) + (element.number ?? 0);
    //summary.cost
    var cost = value.cost;
    cost?.subtotal = (cost.subtotal ?? 0) + (element.cost?.subtotal ?? 0);
    value.cost = cost;
    //summary.commission
    var commission = value.commission;
    commission?.subtotal =
        (commission.subtotal ?? 0) + (element.commission?.subtotal ?? 0);
    commission?.total =
        (commission.total ?? 0) + (element.commission?.total ?? 0);
    commission?.service =
        (commission.service ?? 0) + (element.commission?.service ?? 0);
    commission?.totTax =
        (commission.totTax ?? 0) + (element.commission?.totTax ?? 0);
    commission?.subTax =
        (commission.subTax ?? 0) + (element.commission?.subTax ?? 0);
    var commissionAdj = value.commission?.adjustments;
    commissionAdj?.ricepo = (commissionAdj.ricepo ?? 0) +
        (element.commission?.adjustments?.ricepo ?? 0);
    commissionAdj?.restaurant = (commissionAdj.restaurant ?? 0) +
        (element.commission?.adjustments?.restaurant ?? 0);
    commissionAdj?.driver = (commissionAdj.driver ?? 0) +
        (element.commission?.adjustments?.driver ?? 0);
    commissionAdj?.customer = (commissionAdj.customer ?? 0) +
        (element.commission?.adjustments?.customer ?? 0);
    commissionAdj?.unionpay = (commissionAdj.unionpay ?? 0) +
        (element.commission?.adjustments?.unionpay ?? 0);
    commission?.adjustments = commissionAdj;
    value.commission = commission;
    //summary.adjustments
    var adjustments = value.adjustments;
    adjustments?.ricepo =
        (adjustments.ricepo ?? 0) + (element.adjustments?.ricepo ?? 0);
    adjustments?.restaurant =
        (adjustments.restaurant ?? 0) + (element.adjustments?.restaurant ?? 0);
    adjustments?.driver =
        (adjustments.driver ?? 0) + (element.adjustments?.driver ?? 0);
    adjustments?.customer =
        (adjustments.customer ?? 0) + (element.adjustments?.customer ?? 0);
    adjustments?.unionpay =
        (adjustments.unionpay ?? 0) + (element.adjustments?.unionpay ?? 0);
    value.adjustments = adjustments;
    //summary.distribution
    var distribution = value.distribution;
    distribution?.restaurant = (distribution.restaurant ?? 0) +
        (element.distribution?.restaurant ?? 0);
    var disRicepo = value.distribution?.ricepo;
    disRicepo?.tax =
        (disRicepo.tax ?? 0) + (element.distribution?.ricepo?.tax ?? 0);
    distribution?.ricepo = disRicepo;
    value.distribution = distribution;
    //summary.metadata
    value.metadata?.mktFee =
        (value.metadata?.mktFee ?? 0) + (element.metadata?.mktFee ?? 0);
    return value;
  }

  List<DailyCardGroup> processData(
      BuildContext context, ReportModel reportModel) {
    if (reportModel.summaryV1List.length == 0) return [];

    final summary = reportModel.summaryV1List.reduce(reduceSummary);
    final restaurant = reportModel.restaurantEntity;
    var country = "";
    if (restaurant != null) {
      country = restaurant.address!.country!;
    }

    List<DailyCardGroup> groups = [];

    List<DailyCardItem> items1 = [];
    var orders = DailyCardItem(
        DailyCardItemType.titleWithDivider,
        L10n.S.of(context).dailyReport_distribution_orders,
        "${summary.number}");
    items1.add(orders);
    if (summary.cost?.subtotal != null) {
      var subtotal = DailyCardItem(
          DailyCardItemType.subtitle,
          L10n.S.of(context).dailyReport_distribution_subtotal,
          "${ServerMultiLan.coinSymbolCountry(country, summary.cost!.subtotal! / 100)}");
      items1.add(subtotal);
    }
    if (summary.commission?.adjustments?.restaurant != null) {
      var restDiscount = DailyCardItem(
          DailyCardItemType.subtitle,
          L10n.S.of(context).dailyReport_distribution_restDiscount,
          "-${ServerMultiLan.coinSymbolCountry(country, summary.commission!.adjustments!.restaurant! / 100)}");
      items1.add(restDiscount);
    }
    //subtotal-after-discount
    //vm.sum.cost.subtotal + vm.sum.commission.adjustments.restaurant
    final subtotal = summary.cost?.subtotal ?? 0;
    final comissionAdjRest = summary.commission?.adjustments?.restaurant ?? 0;
    var afterDiscount = DailyCardItem(
        DailyCardItemType.title,
        L10n.S.of(context).dailyReport_distribution_subtotalAfterDiscount,
        "${ServerMultiLan.coinSymbolCountry(country, (subtotal + comissionAdjRest) / 100)}");
    items1.add(afterDiscount);
    var group1 = DailyCardGroup(null, items1);
    groups.add(group1);

    // commission group
    List<DailyCardItem> items2 = [];
    // commission
    // vm.sum.commission.subtotal+vm.sum.commission.total-vm.sum.commission.totTax-vm.sum.commission.subTax
    final commissionSubtotal = summary.commission?.subtotal ?? 0;
    final commissionTotal = summary.commission?.total ?? 0;
    final commissionTotTax = summary.commission?.totTax ?? 0;
    final commissionSubTax = summary.commission?.subTax ?? 0;
    final commissionPrice = commissionSubtotal +
        commissionTotal -
        commissionTotTax -
        commissionSubTax;
    final vatTaxPrice = commissionTotTax + commissionSubTax;
    var commission = DailyCardItem(
        DailyCardItemType.subtitle,
        L10n.S.of(context).dailyReport_distribution_commission,
        "${ServerMultiLan.coinSymbolCountry(country, commissionPrice / 100)}");
    items2.add(commission);

    var vatTax = DailyCardItem(
        DailyCardItemType.subtitle,
        L10n.S.of(context).dailyReport_distribution_vatTax,
        "${ServerMultiLan.coinSymbolCountry(country, vatTaxPrice / 100)}");
    items2.add(vatTax);

    // adjustments
    final adjustmentsJson = summary.adjustments?.toJson();
    UserRole? role = ConnectCache.getCurrentRole();
    final recipient = role?.name?.split(".").first;
    if (recipient != null) {
      var adj = adjustmentsJson?[recipient] ?? 0;
      double commissionAdj = 0;
      if (recipient.toLowerCase() == "restaurant") {
        commissionAdj =
            summary.commission?.adjustments?.restaurant?.toDouble() ?? 0;
      }

      String commissionFlag = (adj - commissionAdj) < 0 ? "-" : "";
      var commission = DailyCardItem(
          DailyCardItemType.subtitle,
          L10n.S.of(context).dailyReport_distribution_adjustment,
          commissionFlag +
              "${ServerMultiLan.coinSymbolCountry(country, (adj - commissionAdj) / 100)}");
      if ((adj - commissionAdj) < 0) {
        commission.color = RColors.mr;
      }
      items2.add(commission);
    }
    var group2 = DailyCardGroup(null, items2);
    groups.add(group2);

    //distribution
    var distJson = summary.distribution?.toJson();
    final dist = distJson?[recipient] ?? 0;
    var distribution = DailyCardItem(
        DailyCardItemType.title,
        L10n.S.of(context).dailyReport_distribution_distribution,
        "${ServerMultiLan.coinSymbolCountry(country, dist / 100)}");
    var group3 = DailyCardGroup(null, [distribution]);
    groups.add(group3);

    //mkt-fee
    final mktFee = summary.metadata?.mktFee ?? 0;
    var mkt = DailyCardItem(
        DailyCardItemType.title,
        L10n.S.of(context).dailyReport_distribution_mkt,
        "-${ServerMultiLan.coinSymbolCountry(country, mktFee / 100)}");
    mkt.color = RColors.mr;
    var group4 = DailyCardGroup(null, [mkt]);
    groups.add(group4);

    {
      // List<DailyCardItem> items4 = [];
      // String title = "";
      // if (summary.id?.delivery != true) {
      //   title = L10n.S.of(context).dailyReport_distribution_deliveryPickup;
      // } else {
      //   title =
      //       L10n.S.of(context).dailyReport_distribution_deliveryRiceDelivery;
      // }
      // String value = "";
      // if (summary.deliveryTime != null && summary.deliveryTimeStd != null) {
      //   value = "${summary.deliveryTime! / summary.deliveryTimeStd!} min | ";
      // }
      // value = value + "${summary.number}";
      // var item = DailyCardItem(DailyCardItemType.subtitle, title, "$value");
      // items4.add(item);
      //
      // //subtotal
      // //type.subtotal
      // var subtotal = DailyCardItem(
      //     DailyCardItemType.subtitle,
      //     L10n.S.of(context).dailyReport_distribution_deliverySubtotal,
      //     "${ServerMultiLan.coinSymbolCountry(country, (summary.subtotal?.toDouble() ?? 0) / 100)}");
      // items4.add(subtotal);
      //
      // //reach-minimun
      // var reachMinimun = DailyCardItem(
      //     DailyCardItemType.subtitle,
      //     L10n.S.of(context).dailyReport_distribution_deliveryReachMini,
      //     "${ServerMultiLan.coinSymbolCountry(country, (summary.fees?.delta?.toDouble() ?? 0) / 100)}");
      // items4.add(reachMinimun);
      //
      // //delivery.fee
      // var deliveryFee = DailyCardItem(
      //     DailyCardItemType.subtitle,
      //     L10n.S.of(context).dailyReport_distribution_deliveryFee,
      //     "${ServerMultiLan.coinSymbolCountry(country, (summary.fees?.delivery?.toDouble() ?? 0) / 100)}");
      // items4.add(deliveryFee);
      //
      // //tip
      // var tip = DailyCardItem(
      //     DailyCardItemType.subtitle,
      //     L10n.S.of(context).dailyReport_distribution_deliveryTip,
      //     "${ServerMultiLan.coinSymbolCountry(country, (summary.fees?.tip?.amount?.toDouble() ?? 0) / 100)}");
      // items4.add(tip);
      //
      // //total
      // var total = DailyCardItem(
      //     DailyCardItemType.subtitle,
      //     L10n.S.of(context).dailyReport_distribution_deliveryTotal,
      //     "${ServerMultiLan.coinSymbolCountry(country, (summary.total?.toDouble() ?? 0) / 100)}");
      // items4.add(total);
      //
      // //commission
      // var commitionValue = (summary.commission?.subtotal ?? 0) +
      //     (summary.commission?.total ?? 0);
      // var commission = DailyCardItem(
      //     DailyCardItemType.subtitle,
      //     L10n.S.of(context).dailyReport_distribution_deliveryCommission,
      //     "${ServerMultiLan.coinSymbolCountry(country, commitionValue / 100)}");
      // items4.add(commission);
      //
      // //service-fee
      // var serviceFeeValue = (summary.commission?.service ?? 0);
      // var serviceFee = DailyCardItem(
      //     DailyCardItemType.subtitle,
      //     L10n.S.of(context).dailyReport_distribution_deliveryServiceFee,
      //     "${ServerMultiLan.coinSymbolCountry(country, serviceFeeValue / 100)}");
      // items4.add(serviceFee);
      //
      // //adjustments
      // var adjustmentsValue = (adjustmentsJson?[recipient] ?? 0);
      // var adjustments = DailyCardItem(
      //     DailyCardItemType.subtitle,
      //     L10n.S.of(context).dailyReport_distribution_deliveryAdjustment,
      //     "${ServerMultiLan.coinSymbolCountry(country, adjustmentsValue / 100)}");
      // items4.add(adjustments);
      //
      // var first = items4.removeAt(0);
      // var group5 = DailyCardGroup(first, items4);
      // groups.add(group5);
    }

    return groups;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var reportModel = context.watch<ReportModel>();
    final groups = processData(context, reportModel);

    List<Widget> widgets = [];
    groups.forEachIndexed((group, index) {
      if (index != 0) {
        widgets.add(SizedBox(
          height: 16,
        ));
      }

      if (group.title == null) {
        widgets.add(_buildCard(context, group));
      } else {
        widgets.add(_buildExpandedCard(context, group));
      }
    });

    return Container(
      color: MyColors.bg,
      child: SmartRefresher(
        child: ListView(
          shrinkWrap: true,
          padding: const EdgeInsets.all(20.0),
          children: widgets,
        ),
        controller: _refreshController,
        enablePullUp: false,
        physics: AlwaysScrollableScrollPhysics(),
        header: GifHeader(),
        onRefresh: _onRefresh,
      ),
    );
  }

  Widget _buildCard(BuildContext context, DailyCardGroup group) {
    List<Widget> widgets = [];
    for (var item in group.items) {
      switch (item.type) {
        case DailyCardItemType.titleWithDivider:
          if (widgets.length > 0) widgets.add(SizedBox(height: 12));
          widgets.add(_buildTitleWithDivider(context, item));
          break;

        case DailyCardItemType.title:
          if (widgets.length > 0) widgets.add(SizedBox(height: 12));
          widgets.add(_buildTitle(context, item));
          break;

        case DailyCardItemType.subtitle:
          if (widgets.length > 0) widgets.add(SizedBox(height: 12));
          widgets.add(_buildSubtitle(context, item));
          break;

        default:
      }
    }

    return Container(
      padding: EdgeInsets.fromLTRB(20, 16, 20, 16),
      decoration: BoxDecoration(
        color: MyColors.W,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgets,
      ),
    );
  }

  Widget _buildTitleWithDivider(BuildContext context, DailyCardItem item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(context, item),
        Divider(
          thickness: 0.5,
          color: MyColors.line,
        ),
      ],
    );
  }

  Widget _buildTitle(BuildContext context, DailyCardItem item) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          item.title,
          style: MyStyles.m13.copyWith(color: MyColors.color333),
        ),
        Text(
          item.value,
          style: MyStyles.m13.copyWith(color: MyColors.color333),
        ),
      ],
    );
  }

  Widget _buildSubtitle(BuildContext context, DailyCardItem item) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          item.title,
          style: MyStyles.m13.copyWith(color: MyColors.color999),
        ),
        Text(
          item.value,
          style: MyStyles.m13.copyWith(color: item.color ?? MyColors.color999),
        ),
      ],
    );
  }

  Widget _buildExpandedCard(BuildContext context, DailyCardGroup group) {
    List<Widget> widgets = [];
    for (var item in group.items) {
      switch (item.type) {
        case DailyCardItemType.titleWithDivider:
          if (widgets.length > 0) widgets.add(SizedBox(height: 12));
          widgets.add(_buildTitleWithDivider(context, item));
          break;

        case DailyCardItemType.title:
          if (widgets.length > 0) widgets.add(SizedBox(height: 12));
          widgets.add(_buildTitle(context, item));
          break;

        case DailyCardItemType.subtitle:
          if (widgets.length > 0) widgets.add(SizedBox(height: 12));
          widgets.add(_buildSubtitle(context, item));
          break;

        default:
      }
    }

    return Container(
      padding: EdgeInsets.fromLTRB(20, 0, 20, 0),
      decoration: BoxDecoration(
        color: MyColors.W,
        borderRadius: BorderRadius.circular(20),
      ),
      child: ExpansionTile(
        iconColor: RColors.gray_99,
        collapsedIconColor: RColors.gray_99,
        backgroundColor: RColors.gray_f6,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              group.title?.title ?? "",
              style: MyStyles.m13.copyWith(color: MyColors.color999),
            ),
            Row(
              children: [
                Text(
                  group.title?.value ?? "",
                  style: MyStyles.m13.copyWith(color: MyColors.color999),
                ),
              ],
            ),
          ],
        ),
        tilePadding: EdgeInsets.all(0),
        childrenPadding: EdgeInsets.only(bottom: 16),
        initiallyExpanded: false,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: widgets,
              ),
            ],
          )
        ],
      ),
    );
  }

  void _onRefresh() async {
    try {
      var reportModel = context.read<ReportModel>();

      var value = reportModel.restaurantEntity;
      if (value == null) value = await reportModel.restaurantDetails();
      if (value == null) {
        _refreshController.refreshCompleted();
        return;
      }

      final args =
          ModalRoute.of(context)!.settings.arguments as ReportRouteV2Arguments;
      final from = args.from;
      final to = args.to;
      await reportModel.requestReport(from, to, loading: false);
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshCompleted();
    }
  }
}

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but the callback has index as second argument
  Iterable<T> mapIndexed<T>(T Function(E e, int i) f) {
    var i = 0;
    return map((e) => f(e, i++));
  }

  void forEachIndexed(void Function(E e, int i) f) {
    var i = 0;
    forEach((e) => f(e, i++));
  }
}
