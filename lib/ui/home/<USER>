import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/ui/more/driver_payment.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/call_support_sheet.dart';
import 'package:connect/ui/home/<USER>/help_center_route.dart';
import 'package:connect/ui/home/<USER>/setting_item.dart';
import 'package:connect/ui/home/<USER>/switch_role.dart';
import 'package:connect/ui/home/<USER>/update_pwd_route.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/roles_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'more/copy_print.dart';
import 'more/log_out.dart';
import 'more/multi_language_route.dart';
import 'more/ringtone.dart';
import 'more/sentry_test.dart';
import 'more/version.dart';
import 'package:provider/provider.dart';

class MoreRoute extends StatefulWidget {
  @override
  MoreRouteState createState() => MoreRouteState();
}

class MoreRouteState extends State<MoreRoute>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    GlobalConfig.eventBus.on<DriverSetupPaymentRefresh>().listen((event) async {
      GlobalConfig.paymentRefresh = false;
      if (!mounted) return;
      LoadingUtils.show();
      await context.read<DriverModel>().checkOnline();
      LoadingUtils.dismiss();

      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colors.white,
      body: _buildMore(),
    );
  }

  Widget _buildMore() {
    var isDriverRole = RolesManager.isDriverRole();
    var role = ConnectCache.getUser()?.roles?.firstWhere(
        (element) => element?.sId == ConnectCache.getCurrentRoleId());
    var roleName = "${role?.name} - ${role?.description}";
    return SingleChildScrollView(
      child: Column(
        children: [
          Column(
            children: [
              Container(
                color: Colors.white,
                padding: EdgeInsets.all(16),
                width: double.infinity,
                child: Text(
                  S.of(context).title_more,
                  style: TextStyle(
                      color: Colors.black87,
                      fontSize: Dimens.sp_26,
                      fontWeight: FontWeight.bold),
                ),
              ),
              SettingItem(
                  tittle: S.of(context).select_language,
                  subTitle: ConnectCache.getLocaleStr(),
                  imagePath: "assets/images/more/language.png",
                  onTap: () {
                    TrackingUtils.instance!
                        .tracking(TConstants.D_MORE_SELECT_LANGUAGE_CLICK);
                    Navigator.of(context).pushNamed(MultiLanguageRoute.tag);
                  }),
              SettingItem(
                  tittle: S.of(context).update_password,
                  imagePath: "assets/images/more/password.png",
                  onTap: () {
                    TrackingUtils.instance!
                        .tracking(TConstants.D_MORE_PASSWORD_CLICK);
                    Navigator.of(context).pushNamed(UpdatePwdRoute.tag);
                  }),
              buildDivider(),
              isDriverRole ? EmptyContainer() : Ringtone(),
              SettingItem(
                  tittle: S.of(context).call_support,
                  imagePath: "assets/images/more/support.png",
                  onTap: () {
                    TrackingUtils.instance!
                        .tracking(TConstants.D_MORE_CALL_SUPPORT_CLICK);
                    CallSupportSheet.show(context);
                  }),
              isDriverRole
                  ? SettingItem(
                      tittle: S.of(context).driver_help_center,
                      imagePath: "assets/images/more/help.png",
                      onTap: () {
                        Navigator.of(context).pushNamed(HelpCenterRoute.tag);
                      })
                  : EmptyContainer(),
              buildDivider(),
              isDriverRole ? EmptyContainer() : CopyPrint(),
              DriverPayment(),
              SettingItem(
                  tittle: S.of(context).switch_role,
                  subTitle: roleName,
                  imagePath: "assets/images/more/user.png",
                  onTap: () {
                    Navigator.of(context).pushNamed(SwitchRole.tag);
                  }),
              GlobalConfig.isRelease ? EmptyContainer() : SentryTest(),
              SizedBox(
                height: Dimens.dp_60,
              ),
              LogOut(),
            ],
          ),
          Version(),
        ],
      ),
    );
  }

  Widget buildDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Divider(height: 1),
    );
  }
}
