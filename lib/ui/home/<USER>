import 'package:connect/ui/home/<USER>/daily_report.dart';
import 'package:connect/ui/home/<USER>/SelectedButton.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:connect/generated/l10n.dart' as L10n;
import 'package:provider/provider.dart';
import 'package:timezone/timezone.dart' as tz;

import '../../common/MyStyles.dart';
import '../../common/constants.dart';
import '../../data/repository/restaurant_details_entity.dart';
import '../../gen/assets.gen.dart';
import '../../provider/report_model.dart';
import '../../utils/date_util.dart';
import '../../utils/format_utils.dart';
import '../view/message_dialog.dart';
import 'report/daily_food_report.dart';

enum DateFormatType {
  startOfDay,
  endOfDay,
}

class ReportRouteV2Arguments {
  final String from;
  final String to;
  final String shortFrom;
  final String shortTo;

  ReportRouteV2Arguments(this.from, this.to, this.shortFrom, this.shortTo);
}

enum ReportType { distribution, food }

class ReportRouteV2 extends StatefulWidget {
  const ReportRouteV2({Key? key}) : super(key: key);

  @override
  _ReportRouteV2State createState() => _ReportRouteV2State();
}

class _ReportRouteV2State extends State<ReportRouteV2>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  var from;
  var to;
  RestaurantDetailsEntity? restaurant;
  var tzNow;
  var detroit;
  ReportType reportType = ReportType.distribution;

  @override
  void initState() {
    super.initState();

    // Create anonymous function:
    () async {
      await context.read<ReportModel>().restaurantDetails();
      setState(() {
        restaurant = context.read<ReportModel>().restaurantEntity;
        if (restaurant != null) {
          detroit = tz.getLocation("${restaurant!.timezone}");
          tz.setLocalLocation(detroit);
          tzNow = tz.TZDateTime.now(detroit);
          var from = restDateformate(tzNow, DateFormatType.startOfDay, null);
          var to = restDateformate(tzNow, DateFormatType.endOfDay, null);
          this.from = tz.TZDateTime.parse(detroit, from);
          this.to = tz.TZDateTime.parse(detroit, to);
        }
      });
    }();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    restaurant = context.watch<ReportModel>().restaurantEntity;
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: false,
        title: Text(
          L10n.S.of(context).title_report,
          style: TextStyle(
              color: Colors.black87, fontSize: 20, fontWeight: FontWeight.w500),
        ),
      ),
      backgroundColor: MyColors.W,
      body: Padding(
        padding: const EdgeInsets.fromLTRB(20, 32, 20, 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildFrom(context),
            SizedBox(height: 20),
            _buildTo(context),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SelectedButton(
                  title: L10n.S.of(context).dailyReport_button_distribution,
                  isSelected: reportType == ReportType.distribution,
                  onPressed: () => {
                    this.setState(() {
                      reportType = ReportType.distribution;
                    })
                  },
                ),
                SizedBox(width: 20),
                SelectedButton(
                  title: L10n.S.of(context).dailyReport_button_food,
                  isSelected: reportType == ReportType.food,
                  onPressed: () => {
                    this.setState(() {
                      reportType = ReportType.food;
                    })
                  },
                ),
              ],
            ),
            Spacer(),
            Container(
              height: 40,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  shadowColor: Colors.transparent,
                  backgroundColor: MyColors.portal,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(L10n.S.of(context).dailyReport_getReport,
                    style: MyStyles.m15.copyWith(color: MyColors.W)),
                onPressed: _getReport,
              ),
            ),
            SizedBox(
              height: 40,
            ),
          ],
        ),
      ),
    );
  }

  void _getReport() {
    if (this.from == null || this.to == null) {
      MessageDialog.messageAlert(
          L10n.S.of(context).dailyReport_invalidDateRange,
          ok: () {});
      return;
    }

    var from = restDateformate(this.from, DateFormatType.startOfDay, null);
    var to = restDateformate(this.to, DateFormatType.endOfDay, null);
    final shortFrom = DateUtil.formatDate(this.from,
        format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
    final shortTo = DateUtil.formatDate(this.to,
        format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
    if (from.isEmpty || to.isEmpty || !this.from.isBefore(this.to)) {
      MessageDialog.messageAlert(
          L10n.S.of(context).dailyReport_invalidDateRange,
          ok: () {});
      return;
    }

    final arguments = ReportRouteV2Arguments(from, to, shortFrom, shortTo);
    if (reportType == ReportType.distribution) {
      Navigator.pushNamed(context, DailyReport.tag, arguments: arguments);
    } else {
      Navigator.pushNamed(context, DailyFoodReport.tag, arguments: arguments);
    }
  }

  Widget _buildFrom(BuildContext context) {
    var dateTextFrom = DateUtil.formatDate(from,
        format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
    return Container(
      padding: EdgeInsets.fromLTRB(16, 10, 16, 10),
      decoration: BoxDecoration(
        border: Border.all(
          color: MyColors.line,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Assets.images.report.calc.image(width: 16),
          SizedBox(width: 4),
          Text(L10n.S.of(context).dailyReport_from,
              style: MyStyles.r15.copyWith(color: MyColors.color333)),
          Spacer(),
          InkWell(
            onTap: () {
              var detroit = tz.getLocation("${restaurant!.timezone}");

              showDatePicker(
                context: context,
                initialDate: tzNow,
                firstDate: DateTime.parse("1970-01-01"),
                lastDate: tzNow,
              ).then((value) {
                if (value != null) {
                  setState(() {
                    final timezone = FormatUtils.format(tzNow);
                    var valueStr = restDateformate(
                        value, DateFormatType.startOfDay, timezone);
                    var date = tz.TZDateTime.parse(detroit, valueStr);
                    this.from = date;
                  });
                }
              });
            },
            child: Row(
              children: [
                Text(dateTextFrom,
                    style: MyStyles.m13.copyWith(color: MyColors.portal)),
                Assets.images.report.down.image(width: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTo(BuildContext context) {
    var dateTextTo = DateUtil.formatDate(to,
        format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
    return Container(
      padding: EdgeInsets.fromLTRB(16, 10, 16, 10),
      decoration: BoxDecoration(
        border: Border.all(
          color: MyColors.line,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Assets.images.report.calc.image(width: 16),
          SizedBox(width: 4),
          Text(L10n.S.of(context).dailyReport_to,
              style: MyStyles.r15.copyWith(color: MyColors.color333)),
          Spacer(),
          InkWell(
            onTap: () {
              showDatePicker(
                context: context,
                initialDate: tzNow,
                firstDate: DateTime.parse("1970-01-01"),
                lastDate: tzNow,
              ).then((value) {
                if (value != null) {
                  setState(() {
                    final timezone = FormatUtils.format(tzNow);
                    var valueStr = restDateformate(
                        value, DateFormatType.endOfDay, timezone);
                    var date = tz.TZDateTime.parse(detroit, valueStr);
                    final nowOffset = (tzNow as tz.TZDateTime).timeZoneOffset.inHours;
                    final selectOffset = date.timeZoneOffset.inHours;
                    if (selectOffset > nowOffset) {
                      date = date.subtract(Duration(hours: (selectOffset - nowOffset)));
                    }
                    this.to = date;
                  });
                }
              });
            },
            child: Row(
              children: [
                Text(dateTextTo,
                    style: MyStyles.m13.copyWith(color: MyColors.portal)),
                Assets.images.report.down.image(width: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String restDateformate(DateTime dt, DateFormatType type, String? tz) {
    var timezone = tz ?? FormatUtils.format(dt);

    var result = "";
    switch (type) {
      case DateFormatType.startOfDay:
        result = formatDate(
                dt, [yyyy, '-', mm, '-', dd, 'T', '00', ':', '00', ':', '00']) +
            timezone;
        break;
      case DateFormatType.endOfDay:
        result = formatDate(
                dt, [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
            timezone;
        break;
      default:
    }

    return result;
  }
}
