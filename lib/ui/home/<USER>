import 'package:connect/common/t_constants.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/roles_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../common/global.dart';
import '../../utils/push_permission.dart';
import 'restaurant_route.dart';
import 'more_route.dart';
import 'orders/orders_route.dart';

class HomeRoute extends StatefulWidget {
  static String tag = "home_route";

  @override
  HomeRouteState createState() => HomeRouteState();
}

class TabData {
  String imageResource;
  String selectResource;
  String tittle;
  String track;
  int index;

  TabData({
    required this.imageResource,
    required this.selectResource,
    required this.tittle,
    required this.track,
    required this.index,
  });
}

class HomeRouteState extends State<HomeRoute> with WidgetsBindingObserver {
  late int _selectedIndex = 0;
  late List<Widget> _pages;
  var _pageController = PageController();

  List<TabData> _tabs = [];

  void _pageChanged(int index) {
    setState(() {
      if (_selectedIndex != index) _selectedIndex = index;
    });
    if (index == 2) {
      restaurantGlobalKey.currentState?.onChanged();
    }
  }

  void _onTabTapped(TabData select) {
    _pageController.jumpToPage(select.index);
    TrackingUtils.instance!.tracking(select.track);
  }

  void listenNotification() {
    submitPushToken();
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      LogUtil.d('Got a message whilst in the foreground!');
      LogUtil.d('Message data: ${message.data}');
      if (message.notification == null) return;
      LogUtil.d(
          'Message also contained a notification: ${message.notification}');
      if (!GlobalConfig.isBackground) return;
      var body = message.notification?.body;
      var title = message.notification?.title;
      if (title != null && body != null) {
        showNotificationDialog(Text(title), Text(body), context);
      }
    });
  }

  @override
  void initState() {
    LogUtil.v("HomeRoute::initState");
    if (RolesManager.isRestaurantManagerRole()) {
      _pages = [
        OrdersRoute(),
        ReportRouteV2(),
        RestaurantRoute(key: restaurantGlobalKey),
        MoreRoute(),
      ];
    } else {
      _pages = [
        OrdersRoute(),
        MoreRoute(),
      ];
    }
    super.initState();
    listenNotification();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.resumed:
        GlobalConfig.isBackground = false;
        break;
      case AppLifecycleState.paused:
        GlobalConfig.isBackground = true;
        break;
      case AppLifecycleState.detached:
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  void buildTabs() {
    if (RolesManager.isRestaurantManagerRole()) {
      _tabs = <TabData>[
        TabData(
            imageResource: "assets/images/tab/order.png",
            selectResource: "assets/images/tab/order_select.png",
            tittle: S.of(context).title_orders,
            track: TConstants.R_ORDERS_TAB_CLICK,
            index: 0),
        TabData(
            imageResource: "assets/images/tab/report.png",
            selectResource: "assets/images/tab/report_select.png",
            tittle: S.of(context).title_report,
            track: TConstants.R_REPORT_TAB_CLICK,
            index: 1),
        TabData(
            imageResource: "assets/images/tab/restaurant.png",
            selectResource: "assets/images/tab/restaurant_select.png",
            tittle: S.of(context).title_restaurant,
            track: TConstants.R_RESTAURANT_TAB_CLICK,
            index: 2),
        TabData(
            imageResource: "assets/images/tab/more.png",
            selectResource: "assets/images/tab/more_select.png",
            tittle: S.of(context).title_more,
            track: TConstants.R_MORE_TAB_CLICK,
            index: 3),
      ];
    } else {
      _tabs = <TabData>[
        TabData(
            imageResource: "assets/images/tab/order.png",
            selectResource: "assets/images/tab/order_select.png",
            tittle: S.of(context).title_orders,
            track: TConstants.R_ORDERS_TAB_CLICK,
            index: 0),
        TabData(
            imageResource: "assets/images/tab/more.png",
            selectResource: "assets/images/tab/more_select.png",
            tittle: S.of(context).title_more,
            track: TConstants.R_MORE_TAB_CLICK,
            index: 1),
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("HomeRoute::build");
    buildTabs();
    return Scaffold(
      backgroundColor: Colors.grey[200],
      appBar: PreferredSize(
          child: AppBar(
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle.light,
            backgroundColor: Colors.white,
          ),
          preferredSize: Size.fromHeight(0.0)),
      body: PageView.builder(
        itemBuilder: (context, index) => _pages[index],
        controller: _pageController,
        physics: NeverScrollableScrollPhysics(),
        itemCount: _pages.length,
        onPageChanged: _pageChanged,
      ),
      bottomNavigationBar: BottomNavigationBar(
        selectedFontSize: Dimens.sp_12,
        unselectedFontSize: Dimens.sp_12,
        currentIndex: _selectedIndex,
        onTap: (index) {
          _onTabTapped(_tabs[index]);
        },
        type: BottomNavigationBarType.fixed,
        fixedColor: RColors.mr,
        items: _tabs
            .map((e) => BottomNavigationBarItem(
                  icon: SizedBox(
                      width: 24,
                      height: 24,
                      child: Image(
                        image: AssetImage((_selectedIndex == e.index)
                            ? e.selectResource
                            : e.imageResource),
                      )),
                  label: e.tittle,
                ))
            .toList(),
      ),
    );
  }
}
