import 'package:connect/common/t_constants.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/provider/report_model.dart';
import 'package:connect/ui/home/<USER>/date/report_date_group.dart';
import 'package:connect/ui/home/<USER>/food/report_food_entity.dart';
import 'package:connect/ui/home/<USER>/food/report_food_item.dart';
import 'package:connect/ui/home/<USER>/report_adjustments.dart';
import 'package:connect/ui/home/<USER>/report_category.dart';
import 'package:connect/ui/home/<USER>/report_summary.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:connect/utils/tz_date.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:timezone/data/latest.dart' as tz;
import '../../generated/l10n.dart';
import '../view/gif_header.dart';

class ReportRoute extends StatefulWidget {
  @override
  ReportRouteState createState() => ReportRouteState();
}

class ReportRouteState extends State<ReportRoute>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;
  late RefreshController _refreshControllerFood;
  int page = 1;
  String from = "";
  String to = "";
  bool isSummary = true;

  void _onRefresh() async {
    try {
      if (isSummary) {
        page = 1;
        var reportModel = context.read<ReportModel>();

        var value = await reportModel.restaurantDetails();
        if (value == null) return;
        LogUtil.v("timezone::${value.timezone}");

        if (from.isEmpty) {
          from = TzDate.tzFrom("${value.timezone}");
        }
        if (to.isEmpty) {
          to = TzDate.tzTo("${value.timezone}");
        }

        await reportModel.reportAndAdjustments(from, to, loading: false);
        _refreshController.refreshCompleted();
      } else {
        await context.read<ReportModel>().reportFood(from, to, loading: false);
        if (_refreshControllerFood != null) {
          _refreshControllerFood.refreshCompleted();
        }
      }
    } catch (e) {
      if (isSummary) {
        _refreshController.refreshCompleted();
      } else {
        if (_refreshControllerFood != null) {
          _refreshControllerFood.refreshCompleted();
        }
      }
    }
  }

  void _onLoading() {
    if (isSummary) {
      page++;
      context
          .read<ReportModel>()
          .adjustments(page, from, to)
          .whenComplete(() => _refreshController.loadComplete());
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<ReportModel>().reset();
    tz.initializeTimeZones();
    _refreshController = RefreshController(initialRefresh: true);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var model = context.watch<ReportModel>();
    var items = model.adjustList;
    var foodItems = model.foodItems;
    return Scaffold(
      backgroundColor: Colors.grey[200],
      body: Column(
        children: [
          ReportDateGroup((f, t) {
            page = 1;
            from = f;
            to = t;
            var params = Map<String, dynamic>();
            params["from"] = "$f";
            params["to"] = "$t";
            TrackingUtils.instance!
                .tracking(TConstants.R_REPORT_DATE_OK_CLICK, value: params);

            if (isSummary) {
              context
                  .read<ReportModel>()
                  .reportAndAdjustments(from, to, loading: true);
            } else {
              context.read<ReportModel>().reportFood(from, to);
            }
          }, (type) {
            if (type == S.of(context).report_food) {
              isSummary = false;
              if (_refreshControllerFood == null) {
                _refreshControllerFood = RefreshController();
              }
              context.read<ReportModel>().reportFood(from, to);
            } else {
              isSummary = true;
              context
                  .read<ReportModel>()
                  .reportAndAdjustments(from, to, loading: true);
            }
          }),
          // ReportNode(),
          Expanded(
              child: SmartRefresher(
            controller: isSummary ? _refreshController : _refreshControllerFood,
            enablePullUp: isSummary ? true : false,
            physics: AlwaysScrollableScrollPhysics(),
            header: GifHeader(),
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            footer: ClassicFooter(
              loadStyle: LoadStyle.ShowWhenLoading,
            ),
            child: isSummary ? _reportSummary(items) : _reportFood(foodItems),
          )),
        ],
      ),
    );
  }

  Widget _reportSummary(List<OrdersEntity> items) {
    return items == null || items.isEmpty
        ? EmptyView(S.of(context).no_report_data)
        : ListView.builder(
            itemBuilder: (context, index) {
              if (index == 0) {
                return ReportSummary(S.of(context).summary_total_distribution);
              } else if (index == 1) {
                return ReportCategory();
              } else {
                return ReportAdjustments();
              }
            },
            itemCount: 3,
          );
  }

  Widget _reportFood(List<ReportFoodEntity>? foodItems) {
    return foodItems == null || foodItems.isEmpty
        ? EmptyView(S.of(context).no_report_data)
        : ListView.separated(
            itemBuilder: (context, index) {
              return ReportFoodItem(foodItems[index], index);
            },
            separatorBuilder: (context, index) {
              return Divider(
                height: 1,
              );
            },
            itemCount: foodItems.length,
          );
  }
}
