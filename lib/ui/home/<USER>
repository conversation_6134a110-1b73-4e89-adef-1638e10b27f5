import 'package:connect/common/MyStyles.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/contact_view.dart';
import 'package:connect/ui/home/<USER>/delivery_view.dart';
import 'package:connect/ui/home/<USER>/info_view.dart';
import 'package:connect/ui/home/<USER>/menu_view.dart';
import 'package:flutter/material.dart';

GlobalKey<_RestaurantRouteState> restaurantGlobalKey = GlobalKey();


class RestaurantRoute extends StatefulWidget {
  RestaurantRoute({Key? key}) : super(key: key);

  @override
  _RestaurantRouteState createState() => _RestaurantRouteState();
}

class _RestaurantRouteState extends State<RestaurantRoute>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  @override
  bool get wantKeepAlive => true;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 4, initialIndex: 0, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        onChanged();
      }
    });
  }

  void onChanged() {
    if (_tabController.index == 3) {
      menuGlobalKey.currentState?.onPageVisible();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: MyColors.W,
        centerTitle: true,
        title: Text(
          S.of(context).title_restaurant,
          style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_17),
        ),
        bottom: TabBar(
          labelColor: MyColors.portal,
          indicatorColor: MyColors.portal,
          unselectedLabelColor: Colors.black54,
          controller: _tabController,
          tabs: [
            Tab(
              text: S.of(context).merchants,
            ),
            Tab(
              text: S.of(context).setting,
            ),
            Tab(
              text: S.of(context).driver,
            ),
            Tab(
              text: S.of(context).menu,
            ),
          ],
        ),
      ),
      backgroundColor: MyColors.bg,
      body: TabBarView(
        controller: _tabController,
        children: [
          InfoView(),
          ContactView(),
          DeliveryView(),
          MenuView(key: menuGlobalKey),
        ],
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _tabController.dispose();

  }
}
