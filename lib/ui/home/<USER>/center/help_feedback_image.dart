import 'dart:io';
import 'package:connect/driver/delivery/driver_camera_sheet.dart';
import 'package:connect/driver/provider/driver_feedback_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';
import 'package:provider/provider.dart';

class HelpFeedbackImage extends StatefulWidget {
  HelpFeedbackImage({Key? key}) : super(key: key);

  @override
  _HelpFeedbackImageState createState() => _HelpFeedbackImageState();
}

class _HelpFeedbackImageState extends State<HelpFeedbackImage> {
  List<AssetEntity> _imageFiles = [];
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(S.of(context).driver_upload_max,
                style: TextStyle(
                  color: Colors.black87,
                  fontSize: Dimens.sp_16,
                )),
            SizedBox(height: 16.0),
            AlignedGridView.count(
              shrinkWrap: true,
              mainAxisSpacing: 4,
              crossAxisSpacing: 4,
              crossAxisCount: 3,
              itemCount: _imageFiles.length == 5
                  ? _imageFiles.length
                  : _imageFiles.length + 1,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (BuildContext context, int index) {
                if (_imageFiles.length < 5 && index == 0) {
                  return InkWell(
                    onTap: () {
                      FocusScope.of(context).requestFocus(FocusNode());

                      DriverCameraSheet.show(context, (value) async {
                        if (value == 0) {
                          _onCameraImage();
                        } else if (value == 1) {
                          _onPickImage();
                        }
                      });
                    },
                    child: Container(
                      height: 100,
                      padding: EdgeInsets.all(5),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5.0),
                          color: Color(0xFFF6F7F8),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.add,
                            color: Color(0xFFB4B4B4),
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                  );
                } else {
                  int imgIndex = _imageFiles.length < 5 ? index - 1 : index;
                  return Stack(
                    children: [
                      Container(
                        height: 100,
                        padding: EdgeInsets.all(5),
                        child: InkWell(
                          child: FutureBuilder<File?>(
                            future: _imageFiles[imgIndex].file,
                            builder: (context, snapshot) {
                              return snapshot.connectionState ==
                                      ConnectionState.done
                                  ? Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.rectangle,
                                        borderRadius:
                                            BorderRadius.circular(6.0),
                                        image: DecorationImage(
                                          image: FileImage(snapshot.data!),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    )
                                  : Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.rectangle,
                                        borderRadius:
                                            BorderRadius.circular(6.0),
                                        color: Color(0xFFF6F7F8),
                                      ),
                                      child: Center(
                                        child: CupertinoActivityIndicator(),
                                      ),
                                    );
                            },
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Align(
                          child: InkWell(
                            onTap: () => _deleteImage(
                                _imageFiles.length < 5 ? index - 1 : index),
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(99.0),
                                color: Colors.black,
                              ),
                              padding: EdgeInsets.all(2.0),
                              child: Icon(
                                Icons.close,
                                size: 12.0,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          alignment: Alignment.topRight,
                        ),
                      ),
                    ],
                  );
                }
              },
              // staggeredTileBuilder: (int index) =>
              //     new StaggeredTile.count(1, 1),
            ),
          ],
        ),
      ),
    );
  }

  /// select pictures
  _onPickImage() async {
    List<AssetEntity>? assets = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        maxAssets: 5 - _imageFiles.length,
        themeColor: Theme.of(context).primaryColor,
        requestType: RequestType.image,
        previewThumbnailSize: ThumbnailSize.square(150),
        gridThumbnailSize: ThumbnailSize.square(80),
      ),
    );

    if (assets == null || assets.length <= 0) return;
    setState(() {
      _imageFiles.addAll(assets);
      context.read<DriverFeedbackModel>().setAssetEntity(_imageFiles);
    });
  }

  /// camera
  _onCameraImage() async {
    final AssetEntity? asset = await CameraPicker.pickFromCamera(context);
    if (asset == null) return;
    setState(() {
      _imageFiles.add(asset);
      context.read<DriverFeedbackModel>().setAssetEntity(_imageFiles);
    });
  }

  /// delete picture
  _deleteImage(int index) {
    if (_imageFiles == null || _imageFiles.length <= index) return;
    setState(() {
      _imageFiles.removeAt(index);
      if (_imageFiles.length == 0) {
        context.read<DriverFeedbackModel>().setAssetEntity([]);
      }
    });
  }
}
