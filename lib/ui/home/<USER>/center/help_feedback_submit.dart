import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/driver/provider/driver_feedback_model.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/provider/driver_tickets.dart';
import 'package:connect/driver/widget/driver_one_dialog.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class HelpFeedbackSubmit extends StatefulWidget {
  HelpFeedbackSubmit({Key? key}) : super(key: key);

  @override
  _HelpFeedbackSubmitState createState() => _HelpFeedbackSubmitState();
}

class _HelpFeedbackSubmitState extends State<HelpFeedbackSubmit> {
  bool isClickable = false;
  String? type;
  String? info;

  @override
  void initState() {
    super.initState();
    context.read<DriverFeedbackModel>().imagesEntity = [];

    GlobalConfig.eventBus.on<DriverFeedbackType>().listen((event) {
      if (!mounted) return;
      type = event.type;
      LogUtil.v("feedback type::$type");
    });

    GlobalConfig.eventBus.on<DriverFeedbackInfo>().listen((event) {
      if (!mounted) return;
      setState(() {
        info = event.info;
        LogUtil.v("feedback info::$info");
        if (info == null || info!.isEmpty) {
          isClickable = false;
        } else {
          isClickable = true;
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    DriverFeedbackModel model = context.watch<DriverFeedbackModel>();
    var images = model.imagesEntity;
    return Container(
        margin: const EdgeInsets.fromLTRB(
            Dimens.dp_25, Dimens.dp_20, Dimens.dp_25, Dimens.dp_20),
        alignment: Alignment.center,
        child: SizedBox(
          width: double.infinity,
          height: Dimens.dp_40,
          child: ElevatedButton(
            style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(
                    isClickable ? RColors.mr : RColors.r_orange_alpha),
                shape: MaterialStateProperty.all(RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(Dimens.dp_20)))),
            child: Text(S.of(context).driver_submit,
                style: TextStyle(color: Colors.white, fontSize: Dimens.sp_16)),
            onPressed: isClickable
                ? () async {
                    FocusScope.of(context).requestFocus(FocusNode());
                    //upload picture first
                    List<String> photosUrls = [];
                    if (images != null && images.length > 0) {
                      LoadingUtils.show();
                      await Future.forEach(images, (AssetEntity element) async {
                        var uint8list = await element.thumbnailDataWithSize(ThumbnailSize.square(300), quality: 70);
                        var file = await element.file;
                        if (uint8list != null && file != null) {
                          var photosUrl = await context
                              .read<DriverModel>()
                              .uploadFile(
                                  uint8list.toList(), file.path, "ticket");
                          photosUrls.add(photosUrl!);
                        }
                      }).catchError((e) {
                        LoadingUtils.dismiss();
                      });
                    }

                    //submmit feedback info
                    var user = ConnectCache.getUser();
                    String subject = "";
                    String typeParam = "";
                    if (S.of(context).driver_feedback_and_salary == type) {
                      typeParam = "paymentissue-adjustments";
                      subject =
                          "[$typeParam]: ${user!.email} from ${user.roles![0]!.description} has payment issue in adjustments\n     $info";
                    } else if (S.of(context).driver_feedback_and_tron == type) {
                      typeParam = "tron-issue";
                      subject =
                          "[$typeParam]: ${user!.email} from ${user.roles![0]!.description} has issue\n     $info";
                    } else if (S.of(context).driver_feedback_and_shift ==
                        type) {
                      typeParam = "request-more-shifts";
                      subject =
                          "[$typeParam]: ${user!.email} from ${user.roles![0]!.description} has issue\n     $info";
                    } else {
                      typeParam = "driver-feedback";
                      subject =
                          "[$typeParam]: ${user!.email} from ${user.roles![0]!.description} has issue\n     $info";
                    }

                    var params = Map<String, dynamic>();
                    params["type"] = "$typeParam";
                    params["des"] = "$subject";
                    TrackingUtils.instance!.tracking(
                        TConstants.D_MORE_FEEDBACK_SUBMIT_CLICK,
                        value: params);

                    await context
                        .read<DriverTickets>()
                        .postTickets(subject, photosUrls, type: typeParam)
                        .then((value) {
                      if (value != null) {
                        DriverOneDialog.show(
                            context, S.of(context).driver_feedback_successfully,
                            callback: () {
                          //finish current route
                          Navigator.pop(context);
                        });
                      }
                    });
                  }
                : null,
          ),
        ));
  }
}
