import 'package:connect/common/global.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class HelpFeedbackInfo extends StatefulWidget {
  HelpFeedbackInfo({Key? key}) : super(key: key);

  @override
  _HelpFeedbackInfoState createState() => _HelpFeedbackInfoState();
}

class _HelpFeedbackInfoState extends State<HelpFeedbackInfo> {
  TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
          vertical: Dimens.dp_25, horizontal: Dimens.dp_15),
      child: Column(
        children: [
          Row(
            children: [
              Text(S.of(context).driver_feedback_issue,
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: Dimens.sp_16,
                  )),
            ],
          ),
          Row(
            children: [
              Container(
                alignment: Alignment.topLeft,
                width: MediaQuery.of(context).size.width - Dimens.dp_30,
                margin: const EdgeInsets.only(top: Dimens.dp_25),
                child: Text<PERSON>ield(
                  controller: _controller,
                  autofocus: true,
                  maxLines: 5,
                  onChanged: (v) {
                    GlobalConfig.eventBus.fire(DriverFeedbackInfo(v));
                  },
                  decoration: InputDecoration(
                      hintText: S.of(context).driver_feedback_des_hint,
                      filled: true,
                      fillColor: Colors.grey[200],
                      border: InputBorder.none,
                      focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                          borderSide: BorderSide.none),
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 12.0, horizontal: 15)),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
