import 'package:connect/common/global.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';

class HelpFeedbackType extends StatefulWidget {
  HelpFeedbackType({Key? key}) : super(key: key);

  @override
  _HelpFeedbackTypeState createState() => _HelpFeedbackTypeState();
}

class _HelpFeedbackTypeState extends State<HelpFeedbackType> {
  var selectedValue;

  List<DropdownMenuItem> generateItemList() {
    List<DropdownMenuItem> items = [];
    DropdownMenuItem salary = DropdownMenuItem(
      child: Text(S.of(context).driver_feedback_and_salary),
      value: S.of(context).driver_feedback_and_salary,
    );
    DropdownMenuItem feedback = DropdownMenuItem(
      child: Text(S.of(context).driver_feedback_and_suggest),
      value: S.of(context).driver_feedback_and_suggest,
    );
    DropdownMenuItem tron = DropdownMenuItem(
      child: Text(S.of(context).driver_feedback_and_tron),
      value: S.of(context).driver_feedback_and_tron,
    );
    DropdownMenuItem shift = DropdownMenuItem(
      child: Text(S.of(context).driver_feedback_and_shift),
      value: S.of(context).driver_feedback_and_shift,
    );
    items.add(salary);
    items.add(feedback);
    items.add(tron);
    items.add(shift);
    return items;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    selectedValue = S.of(context).driver_feedback_and_suggest;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
          vertical: Dimens.dp_15, horizontal: Dimens.dp_15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            S.of(context).driver_feedback_type,
            style: TextStyle(
              color: Colors.black87,
              fontSize: Dimens.sp_16,
            ),
          ),
          DropdownButtonHideUnderline(
            child: DropdownButton(
              items: generateItemList(),
              value: selectedValue,
              onChanged: (dynamic T) {
                setState(() {
                  selectedValue = T;
                  LogUtil.v("selectedValue::$selectedValue");
                  GlobalConfig.eventBus.fire(DriverFeedbackType(selectedValue));
                });
              },
              elevation: 24,
              style: TextStyle(color: RColors.mr, fontWeight: FontWeight.bold),
              isDense: false,
              iconSize: 40.0,
            ),
          )
        ],
      ),
    );
  }
}
