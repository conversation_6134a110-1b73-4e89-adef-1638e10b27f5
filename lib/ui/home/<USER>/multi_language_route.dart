import 'package:connect/common/constants.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/provider/connect_locale.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:provider/provider.dart';

class MultiLanguageRoute extends StatefulWidget {
  static String tag = "multi_language";

  MultiLanguageRoute({Key? key}) : super(key: key);

  @override
  _MultiLanguageRouteState createState() => _MultiLanguageRouteState();
}

class _MultiLanguageRouteState extends State<MultiLanguageRoute> {
  String? currentLanguage = "";
  var labelStr = {
    Constants.zh_CN: false,
    Constants.zh_HK: false,
    Constants.en: false,
    Constants.es: false,
    Constants.fr: false,
  };

  List<Widget> labelWidget = [];

  labelStatus() {
    labelWidget.clear();
    labelWidget
        .addAll(labelStr.keys.map((e) => buildItem(e, labelStr[e]!, (label) {
              LogUtil.v("label::$label");
              currentLanguage = label;
              setState(() {
                labelStr.forEach((key, value) {
                  if (key == label) {
                    labelStr[key] = true;
                  } else {
                    labelStr[key] = false;
                  }
                });
              });
            })));
  }

  @override
  void initState() {
    super.initState();
    currentLanguage = ConnectCache.getLocaleStr();
    labelStr.forEach((key, value) {
      if (key == currentLanguage) {
        labelStr[key] = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    labelStatus();
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        centerTitle: true,
        title: Text(S.of(context).language,
            style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_16)),
        actions: [
          Center(
            child: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: ElevatedButton(
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all(
                      ConnectCache.getLocaleStr() == currentLanguage
                          ? Colors.grey[350]
                          : RColors.mr),
                  padding: MaterialStateProperty.all(const EdgeInsets.only(
                      left: Dimens.dp_5,
                      right: Dimens.dp_5,
                      top: Dimens.dp_0,
                      bottom: Dimens.dp_0)),
                ),
                child: Text(
                  S.of(context).done,
                  style: TextStyle(
                      color: ConnectCache.getLocaleStr() == currentLanguage
                          ? Colors.grey[500]
                          : Colors.white,
                      fontSize: Dimens.sp_15),
                ),
                onPressed: ConnectCache.getLocaleStr() == currentLanguage
                    ? null
                    : () async {
                        SmartDialog.showLoading(
                            msg: S.of(context).lan_setting,
                            maskColor: Colors.black54);
                        ConnectCache.saveLocaleStr(currentLanguage!);
                        Provider.of<ConnectLocale>(context, listen: false)
                            .setLocale(ConnectCache.getLocale());

                        //tracking
                        TrackingUtils.instance!.tracking(
                            TConstants.D_MORE_LANGUAGE_DONE_CLICK,
                            value: {
                              "language":
                                  ConnectCache.getLocale().toLanguageTag()
                            });

                        await Future.delayed(Duration(seconds: 1));
                        SmartDialog.dismiss();
                        await Future.delayed(Duration(milliseconds: 500));
                        Navigator.of(context).pop();
                      },
              ),
            ),
          )
        ],
      ),
      body: Column(
        children: [
          Expanded(child: _buildLanguage()),
        ],
      ),
    );
  }

  Widget _buildLanguage() {
    return ListView.builder(
      scrollDirection: Axis.vertical,
      itemCount: labelWidget.length,
      itemBuilder: (context, index) {
        return labelWidget[index];
      },
    );
  }

  Widget buildItem(String lan, bool isSelected, ValueChanged onTap) {
    return GestureDetector(
      onTap: () {
        onTap(lan);
      },
      child: Container(
        color: Colors.white,
        height: Dimens.dp_45,
        padding: const EdgeInsets.only(left: Dimens.dp_20, right: Dimens.dp_20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              lan,
              style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_16),
            ),
            Radio(
                value: lan,
                groupValue: currentLanguage,
                onChanged: (onChanged) {
                  onTap(lan);
                }),
          ],
        ),
      ),
    );
  }
}
