import 'package:connect/common/MyStyles.dart';
import 'package:connect/data/repository/more_respository.dart';
import 'package:connect/data/repository/rest_repo.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/Quote.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/autocomplete_list.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/debouncer.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/edit_item.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/phone_edit_item.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/phone_select_sheet.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/place_repo.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/submit_button.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:uuid/uuid.dart';

class OnDemandDeliveryEvaluate extends StatefulWidget {
  final UIRestaurant _uiRestaurant;

  OnDemandDeliveryEvaluate(this._uiRestaurant);

  @override
  State<StatefulWidget> createState() =>
      _OnDemandDeliveryEvaluateState(_uiRestaurant);
}

class _OnDemandDeliveryEvaluateState extends State {
  late final TextEditingController _addressController = TextEditingController();
  late final TextEditingController _roomController = TextEditingController();
  late final TextEditingController _codeController = TextEditingController();
  late final TextEditingController _phoneController = TextEditingController();
  late final TextEditingController _noteController = TextEditingController();
  final Debouncer deBouncer = Debouncer();
  final PlaceApiProvider _placeApiProvider = PlaceApiProvider(Uuid().v4());
  final ValueNotifier<List<Suggestion>> _suggestions = ValueNotifier([]);
  ValueNotifier<CountryInfo> _countryInfo =
      ValueNotifier(CountryInfo.getSpain());
  // final FlutterLibphonenumber libPhone = FlutterLibphonenumber();
  final UIRestaurant _uiRestaurant;
  Address? _currentPlace;
  String btnText = S.current.delivery_quote;
  String? secondBtnText = null;
  bool btnEnable = false;
  bool quoteResult = false;

  _OnDemandDeliveryEvaluateState(this._uiRestaurant);

  late final List<Widget> _list = [
    Container(
      child: Text(
        S.of(context).order_now,
        style: MyStyles.sh4,
      ),
      alignment: Alignment.topLeft,
      margin: EdgeInsets.only(left: 16, top: 24, bottom: 16),
    ),
    EditItem(
        title: S.of(context).street,
        hint: S.of(context).street,
        controller: _addressController,
        onChange: (address) {
          searchAddress();
          checkState();
        }),
    AutoCompleteList(_suggestions, (suggestion) {
      _addressController.text = suggestion.description;
      checkState();
      fetchDetail(suggestion);
    }),
    EditItem(
        title: S.of(context).unit,
        hint: S.of(context).unit,
        controller: _roomController,
        onChange: (room) {}),
    if (_uiRestaurant.country == CountryInfo.getUk().countryCode)
      EditItem(
          title: S.of(context).zipcode,
          hint: S.of(context).zipcode,
          controller: _codeController,
          onChange: (code) {
          }),
    PhoneEditItem(
      title: S.of(context).phone,
      hint: S.of(context).phone,
      controller: _phoneController,
      onChange: (phone) {
        checkState();
      },
      countryInfo: _countryInfo,
    ),
    EditItem(
        title: S.of(context).note,
        hint: S.of(context).note,
        controller: _noteController,
        onChange: (note) {}),
  ];

  searchAddress({Function? onStart, Function? onEnd}) {
    onStart?.call();
    deBouncer.run(() {
      _placeApiProvider
          .fetchSuggestions(
              _addressController.text, _uiRestaurant.country ?? "")
          .then((value) => _suggestions.value = value)
          .whenComplete(() => onEnd?.call());
    });
  }

  fetchDetail(Suggestion suggestion) {
    LoadingUtils.show();
    _placeApiProvider.getPlaceDetailFromId(suggestion).then((value) {
      _suggestions.value = List.empty();
      _codeController.text = value.zipcode ?? "";
      _currentPlace = value;
      quote();
    }).catchError((error, stackTrace) {
      _codeController.text = "";
      LoadingUtils.dismiss();
    });
  }

  checkState() {
    // libPhone
    //     .parse(_countryInfo.value.area + _phoneController.text)
    //     .then((value) => buttonState(_addressController.text.isNotEmpty &&
    //         _addressController.text == _currentPlace?.formatted &&
    //         quoteResult))
    //     .onError((error, stackTrace) => buttonState(false));

    buttonState(_addressController.text.isNotEmpty &&
        _addressController.text == _currentPlace?.formatted &&
        quoteResult
        && _phoneController.text.length >= 6);
  }

  buttonState(enable) {
    setState(() {
      btnEnable = enable;
    });
  }

  @override
  void initState() {
    countrys.forEach((element) {
      if(element.countryCode == _uiRestaurant.country) {
        _countryInfo.value = element;
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          ..._list,
          Expanded(
            child: Container(
              alignment: Alignment.bottomCenter,
              padding:
                  EdgeInsets.only(top: 40, left: 16, right: 16, bottom: 40),
              child: SubmitButton(
                  text: btnText,
                  secnondText: secondBtnText,
                  enable: btnEnable,
                  width: double.infinity,
                  onSubmit: () {
                    evaluate();
                  }),
            ),
          )
        ],
      ),
    );
  }

  @override
  void dispose() {
    _addressController.dispose();
    _roomController.dispose();
    _codeController.dispose();
    _phoneController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  evaluate() {
    /**
     *  check data before evaluate
     */
    if (_codeController.text.isNotEmpty &&
        _codeController.text != _currentPlace?.zipcode) {
      _addressController.text =
          "${_currentPlace?.formatted} ${_codeController.text}";
      searchAddress(onStart: () {
        LoadingUtils.show();
      }, onEnd: () {
        LoadingUtils.dismiss();
        MessageDialog.messageAlert(
          S.of(context).alert_postcode_change,
        );
      });
      return;
    }
    placeOrder();
  }

  quote({Function(Map<String, dynamic>)? onNext}) {
    MoreRepository.evaluateDelivery(
        data: deliveryData(),
        countryCode: _uiRestaurant.country,
        onSuccess: (evaluate) {
          setState(() {
            btnText = evaluate;
            secondBtnText = S.of(context).order_confirm;
          });
          quoteResult = true;
          checkState();
        },
        onNext: onNext,
        onError: (error) {
          quoteResult = false;
          MessageDialog.messageAlert(error);
        });
  }

  placeOrder() {
    if (!btnEnable) {
      return;
    }
    quote(onNext: (quoteResult) {
      MoreRepository.placeOrder(
          CreateOrder(
            comments: _noteController.text,
            phone: _countryInfo.value.area + _phoneController.text,
            quote: quoteResult,
          ), () {
        MessageDialog.messageAlert(S.of(context).delivery_order_created,
            ok: () {
          Navigator.of(context).pop();
        });
      });
    });
  }

  Quote deliveryData() =>
      Quote(address: _currentPlace?.copyWith(unit: _roomController.text));
}
