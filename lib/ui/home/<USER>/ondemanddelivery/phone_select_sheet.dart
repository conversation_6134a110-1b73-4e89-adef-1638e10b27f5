
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

void showPhoneBottomSheet(BuildContext context, Function(CountryInfo) onSelect) {
  FocusScope.of(context).requestFocus(FocusNode());
  showModalBottomSheet(
      builder: (BuildContext context) {
        return buildBottomSheetWidget(context, onSelect);
      },
      context: context);
}

Widget PhonePopBtn(BuildContext context, Function(CountryInfo) onSelect) {
  FocusScope.of(context).requestFocus(FocusNode());
  return PopupMenuButton(
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    padding: EdgeInsets.all(0),
    offset: Offset(-25, 30),
    icon: Icon(Icons.arrow_drop_down_sharp),
    itemBuilder: (context) {
      return List.generate(countrys.length, (index) => PopupMenuItem(
        value: index,
        child: buildItem(countrys[index].area, context,onTap: (code) {
          onSelect(countrys[index]);
        }),
      ));
    },
  );
}

class CountryInfo {
  String area;
  int digit;
  String name;
  String? countryCode;
  CountryInfo({required this.area, required this.digit, required this.name, this.countryCode});
  static CountryInfo getSpain() => CountryInfo(area: "+34", digit: 9, name: "Spain",countryCode: "ES");
  static CountryInfo getUk() =>  CountryInfo(area: "+44", digit: 11, name: "United Kingdom", countryCode: "GB");
  static CountryInfo getIr() => CountryInfo(area: "+353", digit: 10, name: "Ireland", countryCode: "IE");
  static CountryInfo getUS() => CountryInfo(area: "+1", digit: 10, name: "United Stated",countryCode: "US");
  static CountryInfo getChina() => CountryInfo(area: "+86", digit: 11, name: "China",countryCode: "CN");
  static CountryInfo getFr() => CountryInfo(area: "+33", digit: 10, name: "France",countryCode: "FR");
  static CountryInfo getGe() => CountryInfo(area: "+49", digit: 10, name: "Germany",countryCode: "DE");
  static CountryInfo getIt() => CountryInfo(area: "+39", digit: 10, name: "Italy",countryCode: "IT");
}

List<CountryInfo> countrys = [
  CountryInfo.getSpain(),
  CountryInfo.getUk(),
  CountryInfo.getIr(),
  CountryInfo.getUS(),
  CountryInfo.getChina(),
  CountryInfo.getFr(),
  CountryInfo.getGe(),
  CountryInfo.getIt(),
];

Widget buildBottomSheetWidget(BuildContext context,  Function(CountryInfo) onSelect) {

  return Container(
    child: Column(
      children: [
        ...countrys.map((e) =>
            buildItem(e.area, context,onTap: (code) {
              onSelect(e);
            }),
        )
      ],
    ),
  );
}

Widget buildItem(String title, BuildContext context, {Function(String)? onTap}) {
  return InkWell(
    onTap: () {
      Navigator.of(context).pop();
      if (onTap != null) {
        // _phoneController.text = "";
        onTap(title);
      }
    },
    child: Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.only(left: Dimens.dp_20),
      height: Dimens.dp_50,
      child: Text(
        title,
      ),
    ),
  );
}
