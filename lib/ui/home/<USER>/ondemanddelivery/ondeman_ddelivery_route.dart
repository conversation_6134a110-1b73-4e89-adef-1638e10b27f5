
import 'package:connect/data/repository/rest_repo.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/ondemand_delivery_evaluate.dart';
import 'package:flutter/material.dart';

import '../../../../res/dimens.dart';

class OnDemandDeliveryRoute extends StatefulWidget {

  static String tag = "ondemand_delivery_route";

  OnDemandDeliveryRoute({Key? key}) : super(key: key);

  @override
  _OnDemandDeliveryState createState() => _OnDemandDeliveryState();

}

class _OnDemandDeliveryState extends State<OnDemandDeliveryRoute> with AutomaticKeepAliveClientMixin{

  List<Tab> tabs = [
    Tab(
      icon: Icon(Icons.delivery_dining),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final args = ModalRoute.of(context)!.settings.arguments! as UIRestaurant;
    return DefaultTab<PERSON>ontroller(
        length: tabs.length,
        child: Scaffold(
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.white,
            foregroundColor:  Colors.black87,
            centerTitle: true,
            title:  Text(
              S.of(context).tittle_on_demand_delivery,
              style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_17),
            ),
          ),
          backgroundColor: Colors.white,
          body: OnDemandDeliveryEvaluate(args),
        ));
  }


  @override
  bool get wantKeepAlive => true;
}