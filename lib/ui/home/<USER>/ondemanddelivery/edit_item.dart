import 'package:connect/common/MyStyles.dart';
import 'package:flutter/material.dart';

class EditItem extends StatelessWidget {
  @override
  Key? get key => super.key;

  final String title;
  final String hint;
  final Function(String)? onChange;
  final TextEditingController? controller;

  EditItem({required this.hint, required this.title, this.onChange, this.controller});

  @override
  Widget build(BuildContext context) {

    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(title),
              Container(width: 10),
              Flexible(
                child: TextField(
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: hint,
                    hintStyle: TextStyle(color: MyColors.hint)
                  ),
                  controller: controller,
                  textAlign: TextAlign.end,
                  style: Theme.of(context).textTheme.bodyLarge,
                  onChanged: onChange,
                ),
              ),
            ],
          ),
          Divider(height: 2,)
        ],
      ),
    );
  }

}
