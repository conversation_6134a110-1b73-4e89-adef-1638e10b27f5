import 'package:connect/common/MyStyles.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/phone_select_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PhoneEditItem extends StatefulWidget {
  @override
  Key? get key => super.key;

  final String title;
  final String hint;
  final Function(String)? onChange;
  final TextEditingController? controller;
  final ValueNotifier<CountryInfo> countryInfo;

  PhoneEditItem(
      {required this.hint,
      required this.title,
      this.onChange,
      this.controller,
      required this.countryInfo});

  @override
  State<StatefulWidget> createState() => PhoneEditItemState(
      hint: hint,
      title: title,
      onChange: onChange,
      controller: controller,
      countryInfo: countryInfo);
}

class PhoneEditItemState extends State {
  final String title;
  final String hint;
  final Function(String)? onChange;
  final ValueNotifier<CountryInfo> countryInfo;
  final TextEditingController? controller;

  PhoneEditItemState(
      {required this.hint,
      required this.title,
      this.onChange,
      this.controller,
      required this.countryInfo});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(title),
              SizedBox(width: 50),
              Row(
                children: [
                  Text(
                    countryInfo.value.area,
                  ),
                  PhonePopBtn(context, (info) {
                    setState(() {
                      countryInfo.value = info;
                    });
                  })
                ],
              ),
              SizedBox(width: 10),
              Flexible(
                child: TextField(
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: hint,
                    hintStyle: TextStyle(color: MyColors.hint)
                  ),
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  keyboardType: TextInputType.number,
                  controller: controller,
                  textAlign: TextAlign.end,
                  style: Theme.of(context).textTheme.bodyLarge,
                  onChanged: onChange,
                ),
              ),
            ],
          ),
          Divider(
            height: 2,
          )
        ],
      ),
    );
  }
}
