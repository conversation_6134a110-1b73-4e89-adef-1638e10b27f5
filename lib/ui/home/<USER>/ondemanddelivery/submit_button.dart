import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

import '../../../../res/colors.dart';

class SubmitButton extends StatelessWidget {
  final String text;
  final String? secnondText;
  final Function onSubmit;
  final bool enable;
  double? width;
  double? radius;
  Color? enableColor;
  Color? disableColor;

  SubmitButton(
      {required this.text,
        this.secnondText,
      required this.onSubmit,
      required this.enable,
      this.width,
      this.radius,
      this.enableColor,
      this.disableColor});

  @override
  Widget build(BuildContext context) {
    return Container(
        child: AnimatedOpacity(
      opacity: enable ? 1 : 0.5,
      duration: Duration(milliseconds: 300),
      child: SizedBox(
        height: Dimens.dp_40,
        width: width,
        child: ElevatedButton(
          style: ButtonStyle(
              overlayColor: MaterialStateProperty.resolveWith(
                  (states) => enable ? null : Colors.transparent),
              elevation: MaterialStateProperty.resolveWith((states) => 0),
              backgroundColor: MaterialStateProperty.all(enable
                  ? enableColor ?? RColors.mr
                  : disableColor ?? RColors.mr),
              shape: MaterialStateProperty.all(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(radius ?? 20)))),
          child: secnondText != null ? Row(
            children: [
              Expanded(
                child: _buildBtnText(text),
              ),
              _buildBtnText(secnondText!),
            ],
          ) : Center(child: _buildBtnText(text)),
          onPressed: () {
            if (enable) {
              onSubmit();
            }
          },
        ),
      ),
    ));
  }

  Widget _buildBtnText(String text) {
    return Text(text,
        style: TextStyle(color: Colors.white, fontSize: Dimens.sp_16));
  }
}
