import 'dart:convert';
import 'dart:io';

import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:http/http.dart';

import 'Quote.dart';

class Suggestion {
  final String placeId;
  final String description;

  Suggestion(this.placeId, this.description);

  @override
  String toString() {
    return 'Suggestion(description: $description, placeId: $placeId)';
  }
}

const MAP_BASE_URL = "https://maps.googleapis.com/maps/api/place";

class PlaceApiProvider {
  final client = Client();
  PlaceApiProvider(this.sessionToken);
  final sessionToken;
  static final String androidKey = 'AIzaSyBWD0m5J2dmbyY44wIwhig_waHG0xOu-oI';
  static final String iosKey = 'AIzaSyBWD0m5J2dmbyY44wIwhig_waHG0xOu-oI';
  final apiKey = Platform.isAndroid ? androidKey : iosKey;

  Future<List<Suggestion>> fetchSuggestions(
      String input, String country) async {
    final language = "${ConnectCache.getLocale().languageCode}";
    final request =
        '$MAP_BASE_URL/autocomplete/json?input=$input&types=geocode&language=$language&components=country:$country&key=$apiKey&sessiontoken=$sessionToken';

    final response = await client.get(Uri.parse(request));

    if (response.statusCode == 200) {
      final result = json.decode(response.body);
      if (result['status'] == 'OK') {
        // compose suggestions in a list
        LogUtil.e("success");
        return result['predictions']
            .map<Suggestion>((p) => Suggestion(p['place_id'], p['description']))
            .toList();
      }
      if (result['status'] == 'ZERO_RESULTS') {
        return [];
      }
      throw Exception(result['error_message']);
    } else {
      throw Exception('Failed to fetch suggestion');
    }
  }

  /// const compMap = {
  //   street_number:                'number',
  //   route:                        'street',
  //   locality:                     'city',
  //   administrative_area_level_1:  'state',
  //   country:                      'country',
  //   postal_code:                  'zipcode'
  // };
  ///
  Future<Address> getPlaceDetailFromId(Suggestion suggestion) async {
    final request =
        '$MAP_BASE_URL/details/json?place_id=${suggestion.placeId}&fields=address_component,geometry&key=$apiKey&sessiontoken=$sessionToken';
    final response = await client.get(Uri.parse(request));
    if (response.statusCode == 200) {
      final result = json.decode(response.body);
      LogUtil.e("location result: $result");
      if (result['status'] == 'OK') {
        final components =
            result['result']['address_components'] as List<dynamic>;
        // build result
        final place = Address();
        components.forEach((c) {
          final List type = c['types'];
          if (type.contains('locality')) {
            place.city = c['long_name'];
          }
          if (type.contains('administrative_area_level_1')) {
            place.state = c['long_name'];
          }
          if (type.contains('country')) {
            place.country = c['short_name'];
          }
          if (type.contains('postal_code')) {
            place.zipcode = c['long_name'];
          }
          place.formatted = suggestion.description;
        });
        final location =
            result['result']['geometry']['location'] as Map<String, dynamic>?;
        if (location != null &&
            location.containsKey('lat') &&
            location.containsKey('lng')) {
          place.location = Location(
              type: "Point", coordinates: [location["lng"]!, location["lat"]!]);
        }
        return place;
      }
      throw Exception(result['error_message']);
    } else {
      throw Exception('Failed to fetch suggestion');
    }
  }
}
