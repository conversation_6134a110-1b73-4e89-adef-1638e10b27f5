/// address : {"city":"Barcelona","state":"CT","country":"ES","formatted":"Barcelona, Spain","location":{"type":"Point","coordinates":[2.168568,41.3873974]},"zipcode":null,"unit":null}

class Quote {
  Quote({
    this.address,
  });

  Quote.fromJson(dynamic json) {
    address =
        json['address'] != null ? Address.fromJson(json['address']) : null;
  }

  Address? address;

  Quote copyWith({
    Address? address,
  }) =>
      Quote(
        address: address ?? this.address,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (address != null) {
      map['address'] = address?.toJson();
    }
    return map;
  }
}

/// city : "Barcelona"
/// state : "CT"
/// country : "ES"
/// formatted : "Barcelona, Spain"
/// location : {"type":"Point","coordinates":[2.168568,41.3873974]}
/// zipcode : null
/// unit : null

class Address {
  Address({
    this.city,
    this.state,
    this.country,
    this.formatted,
    this.location,
    this.zipcode,
    this.unit,
  });

  Address.fromJson(dynamic json) {
    city = json['city'];
    state = json['state'];
    country = json['country'];
    formatted = json['formatted'];
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
    zipcode = json['zipcode'];
    unit = json['unit'];
  }

  String? city;
  String? state;
  String? country;
  String? formatted;
  Location? location;
  dynamic zipcode;
  dynamic unit;

  Address copyWith({
    String? city,
    String? state,
    String? country,
    String? formatted,
    Location? location,
    dynamic zipcode,
    dynamic unit,
  }) =>
      Address(
        city: city ?? this.city,
        state: state ?? this.state,
        country: country ?? this.country,
        formatted: formatted ?? this.formatted,
        location: location ?? this.location,
        zipcode: zipcode ?? this.zipcode,
        unit: unit ?? this.unit,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['city'] = city;
    map['state'] = state;
    map['country'] = country;
    map['formatted'] = formatted;
    if (location != null) {
      map['location'] = location?.toJson();
    }
    map['zipcode'] = zipcode;
    map['unit'] = unit;
    return map;
  }
}

/// type : "Point"
/// coordinates : [2.168568,41.3873974]

class Location {
  Location({
    this.type,
    this.coordinates,
  });

  Location.fromJson(dynamic json) {
    type = json['type'];
    coordinates =
        json['coordinates'] != null ? json['coordinates'].cast<double>() : [];
  }

  String? type;
  List<double>? coordinates;

  Location copyWith({
    String? type,
    List<double>? coordinates,
  }) =>
      Location(
        type: type ?? this.type,
        coordinates: coordinates ?? this.coordinates,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['type'] = type;
    map['coordinates'] = coordinates;
    return map;
  }
}

class CreateOrder {
  String? comments;
  String? phone;
  Map<String, dynamic>? quote;

  CreateOrder({this.comments, this.phone, this.quote});

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['comments'] = comments;
    map['phone'] = phone;
    map['quote'] = quote;
    return map;
  }

}
