import 'package:connect/ui/home/<USER>/ondemanddelivery/place_repo.dart';
import 'package:flutter/material.dart';

class AutoCompleteList extends StatefulWidget {

  final ValueNotifier<List<Suggestion>> suggestions;

  final Function(Suggestion) _onSelect;

  AutoCompleteList(this.suggestions, this._onSelect);

  @override
  State<StatefulWidget> createState() => AutoCompleteListState(suggestions, _onSelect);
}

class AutoCompleteListState extends State {
  final Function(Suggestion) _onSelect;
  final ValueNotifier<List<Suggestion>> suggestions;
  AutoCompleteListState(this.suggestions, this._onSelect);
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: ValueListenableBuilder(
          valueListenable: suggestions,
          builder: (BuildContext context, List<Suggestion> value, Widget? child) {
        return ListView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: suggestions.value.length,
          itemBuilder: (_, index) => GestureDetector(
            onTap: () {
              _onSelect(suggestions.value[index]);
            },
            child: Container(
              alignment: Alignment.center,
              child: AutoCompleteItem(suggestions.value[index]),
            ),
          ),
        );
      }),
    );
  }
}

class AutoCompleteItem extends StatelessWidget {
  final Suggestion _suggestin;

  AutoCompleteItem(this._suggestin);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
            padding: const EdgeInsets.all(16),
            width: double.infinity,
            child: Text(_suggestin.description,
                maxLines: 3, textAlign: TextAlign.end)),
        Divider(
          height: 1,
        )
      ],
    );
  }
}
