import 'package:connect/common/MyStyles.dart';
import 'package:connect/gen/assets.gen.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:connect/generated/l10n.dart' as L10n;

import '../../../provider/report_model.dart';
import '../../../res/colors.dart';
import '../../../res/dimens.dart';
import '../report_route_v2.dart';

class DailyHeader extends StatefulWidget {
  const DailyHeader({
    Key? key,
    this.shouldShowReport = true,
  }) : super(key: key);

  final bool shouldShowReport;

  @override
  _DailyHeaderState createState() => _DailyHeaderState();
}

class _DailyHeaderState extends State<DailyHeader> {
  @override
  Widget build(BuildContext context) {
    final args =
        ModalRoute.of(context)!.settings.arguments as ReportRouteV2Arguments;
    final from = args.shortFrom;
    final to = args.shortTo;

    var detail = L10n.S.of(context).dailyReport_send_detail;
    var distribution = L10n.S.of(context).dailyReport_send_distribution;
    var items = [
      detail,
      distribution,
    ];

    var reportModel = context.watch<ReportModel>();

    return Container(
      height: Dimens.dp_50,
      color: Colors.white,
      child: Row(
        children: [
          InkWell(
            child: Assets.images.icons.leftArrow.image(width: 30),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          Container(
            padding: const EdgeInsets.fromLTRB(
                Dimens.dp_12, Dimens.dp_5, Dimens.dp_12, Dimens.dp_5),
            decoration: BoxDecoration(
                color: MyColors.bg,
                borderRadius: BorderRadius.circular(Dimens.dp_4)),
            child: Row(
              children: [
                Text(
                  from,
                  style: MyStyles.r12.copyWith(color: MyColors.color666),
                ),
              ],
            ),
          ),
          SizedBox(
            width: 6,
          ),
          Text(
            "-",
            style: TextStyle(color: RColors.gray_66, fontSize: Dimens.sp_10),
          ),
          SizedBox(
            width: 6,
          ),
          Container(
            padding: const EdgeInsets.fromLTRB(
                Dimens.dp_12, Dimens.dp_5, Dimens.dp_12, Dimens.dp_5),
            decoration: BoxDecoration(
                color: MyColors.bg,
                borderRadius: BorderRadius.circular(Dimens.dp_4)),
            child: Row(
              children: [
                Text(
                  to,
                  style: MyStyles.r12.copyWith(color: MyColors.color666),
                ),
              ],
            ),
          ),
          Spacer(),
          widget.shouldShowReport
              ? DropdownButton(
                  underline: SizedBox(),
                  icon: Assets.images.report.send.image(width: 20),
                  items: items.map((String items) {
                    return DropdownMenuItem(
                      value: items,
                      child: Text(
                        items,
                        style: MyStyles.r12.copyWith(color: MyColors.color666),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue == detail) {
                      // send detail report
                      reportModel.sendReport(args.from, args.to, "detail",
                          loading: true);
                    } else if (newValue == distribution) {
                      // send distribution report
                      reportModel.sendReport(args.from, args.to, "distribution",
                          loading: true);
                    }
                  },
                )
              : EmptyContainer(),
        ],
      ),
    );
  }
}
