import 'package:connect/data/repository/more_respository.dart';
import 'package:connect/http/http_error.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class UpdatePwdRoute extends StatefulWidget {
  static String tag = "update_pwd_route";

  UpdatePwdRoute({Key? key}) : super(key: key);

  @override
  _UpdatePwdRouteState createState() => _UpdatePwdRouteState();
}

class _UpdatePwdRouteState extends State<UpdatePwdRoute> {
  TextEditingController _firstController = TextEditingController();
  TextEditingController _secondController = TextEditingController();
  late Size textSize;

  @override
  void initState() {
    textSize =
        boundingTextSize(S.current.confirm_new_pass, TextStyle(), maxLines: 1);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          centerTitle: true,
          title: Text(S.of(context).update_password,
              style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_16)),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 20.0),
              child: Center(
                child: ElevatedButton(
                  style: ButtonStyle(
                    backgroundColor: MaterialStateProperty.all(
                        !canConfirm() ? Colors.grey[350] : RColors.r_orange),
                    padding: MaterialStateProperty.all(const EdgeInsets.only(
                        left: Dimens.dp_5,
                        right: Dimens.dp_5,
                        top: Dimens.dp_0,
                        bottom: Dimens.dp_0)),
                  ),
                  child: Text(
                    S.of(context).done,
                    style: TextStyle(
                        color: !canConfirm() ? Colors.grey[500] : Colors.white,
                        fontSize: Dimens.sp_15),
                  ),
                  onPressed: !canConfirm()
                      ? null
                      : () {
                          FocusScope.of(context).requestFocus(FocusNode());
                          MoreRepository.updatePwd(_firstController.text)
                              .then((value) {
                            ToastUtils.show(S.of(context).update_success);
                            Navigator.of(context).pop();
                          }).catchError((e) {});
                        },
                ),
              ),
            ),
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            children: [
              _buildItem(_firstController, S.of(context).new_pass,
                  S.of(context).new_pass_hint),
              Divider(height: 1),
              _buildItem(_secondController, S.of(context).confirm_new_pass,
                  S.of(context).confirm_new_pass_hint),
              Divider(height: 1),
            ],
          ),
        ));
  }

  bool canConfirm() {
    return _firstController.text.isNotEmpty &&
        _secondController.text.isNotEmpty &&
        _firstController.text == _secondController.text;
  }

  Widget _buildItem(
    TextEditingController controller,
    String title,
    String hint,
  ) {
    return Row(
      children: [
        Container(child: Text(title), width: textSize.width + 20),
        Expanded(
            child: Container(
          alignment: Alignment.center,
          color: Colors.white,
          height: Dimens.dp_50,
          child: TextField(
            obscureText: true,
            enableSuggestions: false,
            autocorrect: false,
            controller: controller,
            autofocus: true,
            onChanged: (v) {
              setState(() {});
            },
            decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hint,
                contentPadding: const EdgeInsets.only(left: Dimens.dp_15)),
          ),
        )),
        controller.text.isEmpty
            ? EmptyContainer()
            : InkWell(
                child: Container(
                  alignment: Alignment.center,
                  width: Dimens.dp_50,
                  height: Dimens.dp_50,
                  color: Colors.white,
                  child: Icon(
                    Icons.cancel,
                    color: Colors.black54,
                    size: Dimens.dp_20,
                  ),
                ),
                onTap: () {
                  setState(() {
                    controller.text = "";
                  });
                },
              ),
      ],
    );
  }
}

Size boundingTextSize(String text, TextStyle style,
    {int maxLines = 2 ^ 31, double maxWidth = double.infinity}) {
  if (text.isEmpty) {
    return Size.zero;
  }
  final TextPainter textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      text: TextSpan(text: text, style: style),
      maxLines: maxLines)
    ..layout(maxWidth: maxWidth);
  return textPainter.size;
}
