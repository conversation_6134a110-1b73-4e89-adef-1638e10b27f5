import 'package:connect/res/dimens.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:flutter/material.dart';

class SentryTest extends StatelessWidget {
  SentryTest({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () async {
            // try {
            //   String s = null;
            //   s.length;
            // } catch (exception, stackTrace) {
            //   ToastUtils.show(exception.toString());
            //   await Sentry.captureException(
            //     exception,
            //     stackTrace: stackTrace,
            //   );
            // }
            ConnectCache.firstDisclosure(true);
            // ToastUtils.show("exception.toString()");
            // FirebaseCrashlytics.instance.crash();
          },
          child: Container(
              height: Dimens.dp_45,
              color: Colors.white,
              padding: const EdgeInsets.only(
                  left: Dimens.dp_20, right: Dimens.dp_20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "sentry test ${ConnectCache.getUser()!.email}",
                    style: TextStyle(color: Colors.black87),
                  ),
                  Icon(
                    Icons.keyboard_arrow_right,
                    color: Colors.black54,
                    size: Dimens.dp_20,
                  ),
                ],
              )),
        )
      ],
    );
  }
}
