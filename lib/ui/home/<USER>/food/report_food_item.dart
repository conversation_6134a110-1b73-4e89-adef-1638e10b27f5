import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/report_model.dart';
import 'package:connect/ui/home/<USER>/food/report_food_entity.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ReportFoodItem extends StatefulWidget {
  ReportFoodItem(this.entity, this.index, {Key? key}) : super(key: key);

  final ReportFoodEntity entity;
  final int index;

  @override
  _ReportFoodItemState createState() => _ReportFoodItemState();
}

class _ReportFoodItemState extends State<ReportFoodItem> {
  @override
  Widget build(BuildContext context) {
    var entity = widget.entity;
    String optionsStr = "";
    String? country = "";
    MultiNameEntity? foodName;
    var restaurantEntity = context.read<ReportModel>().restaurantEntity;
    if (restaurantEntity != null && restaurantEntity.address != null) {
      country = restaurantEntity.address!.country;
    }
    if (widget.index > 0 && entity.rId != null) {
      foodName = entity.rId!.food!.name;
      var options = entity.rId!.food!.options;
      if (options != null && options.length > 0) {
        options.forEach((element) {
          optionsStr += "${ServerMultiLan.multiAdapt2(element)},";
        });
        if (optionsStr.isNotEmpty) {
          optionsStr =
              "options: ${optionsStr.substring(0, optionsStr.length - 1)}";
        }
      }
    }
    return widget.index == 0
        ? _buildTitle()
        : Container(
            padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("${ServerMultiLan.multiAdapt2(foodName)}"),
                      optionsStr.isNotEmpty
                          ? Text("$optionsStr")
                          : EmptyContainer(),
                    ],
                  ),
                  flex: 2,
                ),
                SizedBox(
                  width: 30,
                ),
                Expanded(
                  flex: 1,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text("  ${entity.number}"),
                      ),
                      Expanded(
                        child: Text(
                          "${ServerMultiLan.coinSymbolCountry(country, entity.price! / 100)}",
                          textAlign: TextAlign.end,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
  }

  Widget _buildTitle() {
    var textStyle = TextStyle(fontWeight: FontWeight.bold);
    return Container(
      padding: const EdgeInsets.all(10),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: Text(
              S.of(context).report_food_items,
              style: textStyle,
            ),
            flex: 2,
          ),
          SizedBox(
            width: 30,
          ),
          Expanded(
            flex: 1,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.of(context).report_food_count,
                  style: textStyle,
                ),
                Text(
                  S.of(context).report_food_sales,
                  style: textStyle,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
