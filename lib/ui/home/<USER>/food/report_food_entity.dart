import 'package:connect/generated/json/report_food_entity.g.dart';

import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class ReportFoodEntity {

	ReportFoodEntity();

	factory ReportFoodEntity.fromJson(Map<String, dynamic> json) => $ReportFoodEntityFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodEntityToJson(this);

  @JSONField(name: "_id")
  ReportFoodId? rId;
  num? number;
  num? price;
  double? subtotal;
  ReportFoodCost? cost;
  ReportFoodFees? fees;
  double? total;
  ReportFoodCommission? commission;
  ReportFoodDistribution? distribution;
  ReportFoodAdjustments? adjustments;
}

@JsonSerializable()
class ReportFoodId {

	ReportFoodId();

	factory ReportFoodId.fromJson(Map<String, dynamic> json) => $ReportFoodIdFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodIdToJson(this);

  ReportFoodIdFood? food;
}

@JsonSerializable()
class ReportFoodIdFood {

	ReportFoodIdFood();

	factory ReportFoodIdFood.fromJson(Map<String, dynamic> json) => $ReportFoodIdFoodFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodIdFoodToJson(this);

  @JSONField(name: "_id")
  String? sId;
  MultiNameEntity? name;
  dynamic code;
  List<MultiNameEntity?>? options;
}

@JsonSerializable()
class ReportFoodCost {

	ReportFoodCost();

	factory ReportFoodCost.fromJson(Map<String, dynamic> json) => $ReportFoodCostFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodCostToJson(this);

  double? subtotal;
  double? tax;
}

@JsonSerializable()
class ReportFoodFees {

	ReportFoodFees();

	factory ReportFoodFees.fromJson(Map<String, dynamic> json) => $ReportFoodFeesFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodFeesToJson(this);

  double? delivery;
  ReportFoodFeesTip? tip;
  double? tax;
  double? service;
  double? credit;
  double? delta;
}

@JsonSerializable()
class ReportFoodFeesTip {

	ReportFoodFeesTip();

	factory ReportFoodFeesTip.fromJson(Map<String, dynamic> json) => $ReportFoodFeesTipFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodFeesTipToJson(this);

  double? amount;
}

@JsonSerializable()
class ReportFoodCommission {

	ReportFoodCommission();

	factory ReportFoodCommission.fromJson(Map<String, dynamic> json) => $ReportFoodCommissionFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodCommissionToJson(this);

  double? subtotal;
  double? total;
  double? service;
}

@JsonSerializable()
class ReportFoodDistribution {

	ReportFoodDistribution();

	factory ReportFoodDistribution.fromJson(Map<String, dynamic> json) => $ReportFoodDistributionFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodDistributionToJson(this);

  double? restaurant;
  ReportFoodDistributionRicepo? ricepo;
}

@JsonSerializable()
class ReportFoodDistributionRicepo     {

	ReportFoodDistributionRicepo();

	factory ReportFoodDistributionRicepo.fromJson(Map<String, dynamic> json) => $ReportFoodDistributionRicepoFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodDistributionRicepoToJson(this);

  double? tax;
}

@JsonSerializable()
class ReportFoodAdjustments {

	ReportFoodAdjustments();

	factory ReportFoodAdjustments.fromJson(Map<String, dynamic> json) => $ReportFoodAdjustmentsFromJson(json);

	Map<String, dynamic> toJson() => $ReportFoodAdjustmentsToJson(this);

  double? restaurant;
}
