import 'package:connect/data/rest_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/gif_header.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:provider/provider.dart';
import 'contact/rest_contact_item.dart';

class ContactView extends StatefulWidget {
  ContactView({Key? key}) : super(key: key);

  @override
  _ContactViewState createState() => _ContactViewState();
}

class _ContactViewState extends State<ContactView>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;

  void _onRefresh() {
    if (context != null) {
      context.read<RestModel>().restInfo().whenComplete(() {
        _refreshController.refreshCompleted();
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: false);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    LogUtil.v("ContactView build");
    var restEntity = context.watch<RestModel>().restEntity;
    List<RestContacts?>? contacts;
    if (restEntity != null) {
      contacts = restEntity.contacts;
    }

    return SmartRefresher(
      controller: _refreshController,
      physics: AlwaysScrollableScrollPhysics(),
      header: GifHeader(),
      onRefresh: _onRefresh,
      child: (restEntity == null || (contacts == null || contacts.isEmpty))
          ? EmptyView(S.of(context).no_data)
          : ListView.builder(
              itemBuilder: (context, index) {
                return RestContactItem(contacts![index]);
              },
              itemCount: contacts.length,
            ),
    );
  }
}
