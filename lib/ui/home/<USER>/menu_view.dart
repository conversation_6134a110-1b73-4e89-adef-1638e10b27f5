import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_menu_model.dart';
import 'package:connect/ui/view/double_scroll_view.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/gif_header.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

GlobalKey<_MenuViewState> menuGlobalKey = GlobalKey();

class MenuView extends StatefulWidget {
  MenuView({Key? key}) : super(key: key);

  @override
  _MenuViewState createState() => _MenuViewState();
}

class _MenuViewState extends State<MenuView>
    with AutomaticKeepAliveClientMixin, WidgetsBindingObserver, RouteAware {

  bool _isVisible = true;
  late RouteObserver<PageRoute> _routeObserver;

  @override
  void didPush() {
    // 路由被推入时
    _isVisible = true;
    // onPageVisible();
  }

  @override
  void didPopNext() {
    // 返回到此页面时
    _isVisible = true;
    onPageVisible();
  }

  @override
  void didPushNext() {
    // 新路由被推入时
    _isVisible = false;
  }

  @override
  void didPop() {
    // 路由被弹出时
    _isVisible = false;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        if (!_isVisible) {
          _isVisible = true;
          onPageVisible();
        }
        break;
      case AppLifecycleState.paused:
        _isVisible = false;
        break;
      default:
        break;
    }
  }

  void onPageVisible() {
    if (!mounted) return;
    // _refreshController.requestRefresh();
    _onRefresh();
  }

  void refresh() {
    isFirstLoad = true;
    _onRefresh();
  }


  @override
  bool get wantKeepAlive => true;

  late RefreshController _refreshController;
  late ScrollController controller;
  late bool isFirstLoad = true;

  void _onRefresh() {
    if (context != null) {
      if (isFirstLoad) {
        LoadingUtils.show();
      }
      context.read<RestMenuModel>().requestMenu().whenComplete(() {
        _refreshController.refreshCompleted();
        if (isFirstLoad) {
          isFirstLoad = false;
          LoadingUtils.dismiss();
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);
    _routeObserver = RouteObserver<PageRoute>();

    context.read<RestMenuModel>().restMenuEntity = null;
    _refreshController = RefreshController(initialRefresh: false);
    controller = ScrollController();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final tabController = DefaultTabController.of(context);
      tabController.addListener(() {
        if (!tabController.indexIsChanging) {
          // 判断是否切换到了当前页面
          if (tabController.index == 3) {
            _onRefresh();
          }
        }
      });
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final modalRoute = ModalRoute.of(context);
    if (modalRoute is PageRoute) {
      _routeObserver.subscribe(this, modalRoute);
    }
  }

  @override
  void dispose() {
    super.dispose();
    _routeObserver.unsubscribe(this);
    WidgetsBinding.instance.removeObserver(this);
    _refreshController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    bool isEmpty = false;
    var watch = context.watch<RestMenuModel>().restMenuEntity;
    if (watch == null ||
        (watch.categories == null || watch.categories!.isEmpty)) {
      isEmpty = true;
    } else {
      isEmpty = false;
      //put food to category
      watch.categories!.forEach((elementC) {
        elementC!.food = [];
        watch.food!.forEach((elementF) {
          if (elementF!.category != null) {
            if (elementC.sId == elementF.category!.sId) {
              elementC.food!.add(elementF);
            }
          }
        });
      });
    }
    return isEmpty
        ? SmartRefresher(
            controller: _refreshController,
            physics: AlwaysScrollableScrollPhysics(),
            header: GifHeader(),
            onRefresh: _onRefresh,
            child: EmptyView(S.of(context).no_data))
        : DoubleScrollView((watch!.categories!));
  }
}
