import 'package:connect/common/MyStyles.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import 'order_card_details.dart';
import 'order_card_food.dart';

class OrderCardSubtotal extends StatefulWidget {
  OrderCardSubtotal(
    this.entity, {
    Key? key,
    this.showExpand = true,
    this.from,
  }) : super(key: key);

  final OrdersEntity? entity;
  final bool showExpand;
  final OrderCardFrom? from;

  @override
  _OrderCardSubtotalState createState() => _OrderCardSubtotalState();
}

class _OrderCardSubtotalState extends State<OrderCardSubtotal> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.from == OrderCardFrom.portrait ||
                widget.from == OrderCardFrom.details
            ? EmptyContainer()
            : Divider(),
        _buildNote(widget.entity!),
        OrderCardFood(widget.entity, showExpand: widget.showExpand),
      ],
    );
  }

  Widget _buildNote(OrdersEntity entity) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        (entity.comments == null || entity.comments!.isEmpty)
            ? EmptyContainer()
            : Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.branding_watermark,
                    color: MyColors.portal,
                  ),
                  SizedBox(
                    width: Dimens.dp_8,
                  ),
                  Expanded(
                    child: Text(
                      sprintf(S.of(context).note_product, [entity.comments]),
                      style: MyStyles.m15.copyWith(color: MyColors.portal),
                    ),
                  ),
                ],
              ),
        SizedBox(
          height: Dimens.dp_8,
        ),
        Text(
          sprintf(S.of(context).product, [entity.items!.length]),
          style: MyStyles.m15.copyWith(color: MyColors.color333),
        ),
      ],
    );
  }
}
