import 'package:connect/common/t_constants.dart';
import 'package:connect/provider/orders_model.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../common/MyStyles.dart';

///secondary menu
class Label extends StatefulWidget {
  Label(this.currentLabel, {Key? key}) : super(key: key);

  final ValueChanged currentLabel;

  @override
  State<StatefulWidget> createState() => LabelState();
}

class LabelState extends State<Label> {
  var labelStr = [];

  List<Widget> labelWidget = [];
  var color = Colors.white;
  bool isExpanded = false;
  int currentSelectedIndex = 0;

  buildLabels() {
    labelStr = [
      S.of(context).label_all,
      S.of(context).wait_confirm_receiving,
      S.of(context).wait_chucan_confirmed,
      S.of(context).wait_courier_come,
    ];
  }

  labelStatus() {
    labelWidget.clear();
    labelWidget.addAll(labelStr.map((e) => _buildLabelButton(e, (label) {
          // LogUtil.v("label::$label");
          setState(() {
            for (int i = 0; i < labelStr.length; i++) {
              if (labelStr[i] == label) {
                currentSelectedIndex = i;
              }
            }
          });
          widget.currentLabel(label);
        })));
  }

  @override
  Widget build(BuildContext context) {
    buildLabels();
    labelStatus();
    return Container(
      alignment: Alignment.center,
      height: Dimens.dp_40,
      padding: const EdgeInsets.fromLTRB(
          Dimens.dp_20, Dimens.dp_7, Dimens.dp_20, Dimens.dp_7),
      color: MyColors.bg,
      child: Row(
        children: [
          Expanded(
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: labelWidget.length,
              itemBuilder: (context, index) {
                return labelWidget[index];
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  width: Dimens.dp_12,
                );
              },
            ),
            flex: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildLabelButton(String label, ValueChanged onTap) {
    return InkWell(
      onTap: () {
        onTap(label);
        if (label == S.of(context).label_all) {
          TrackingUtils.instance!.tracking(TConstants.R_IN_PROGRESS_ALL_CLICK);
        } else if (label == S.of(context).wait_confirm_receiving) {
          TrackingUtils.instance!.tracking(TConstants.R_IN_PROGRESS_NEW_CLICK);
        } else if (label == S.of(context).wait_chucan_confirmed) {
          TrackingUtils.instance!
              .tracking(TConstants.R_IN_PROGRESS_PROGRESS_CLICK);
        } else if (label == S.of(context).wait_courier_come) {
          TrackingUtils.instance!
              .tracking(TConstants.R_IN_PROGRESS_READY_CLICK);
        }
      },
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.fromLTRB(12, 3, 12, 3),
        decoration: BoxDecoration(
            color: label == labelStr[currentSelectedIndex]
                ? MyColors.portal
                : MyColors.f4,
            borderRadius: BorderRadius.circular(Dimens.dp_20)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              label,
              textAlign: TextAlign.center,
              style: MyStyles.r12.copyWith(
                color: label == labelStr[currentSelectedIndex]
                    ? Colors.white
                    : MyColors.color666,
              ),
            ),
            Container(
              margin:
                  const EdgeInsets.only(left: Dimens.dp_2, top: Dimens.dp_1),
              child: Text(
                label == S.of(context).wait_confirm_receiving
                    ? context
                                .watch<OrdersViewModel>()
                                .itemsWaitConfirm
                                .length ==
                            0
                        ? ""
                        : context
                            .watch<OrdersViewModel>()
                            .itemsWaitConfirm
                            .length
                            .toString()
                    : label == S.of(context).wait_chucan_confirmed
                        ? context
                                    .watch<OrdersViewModel>()
                                    .itemsWaitReady
                                    .length ==
                                0
                            ? ""
                            : context
                                .watch<OrdersViewModel>()
                                .itemsWaitReady
                                .length
                                .toString()
                        : label == S.of(context).wait_courier_come
                            ? context
                                        .watch<OrdersViewModel>()
                                        .itemsWaitCourierCome
                                        .length ==
                                    0
                                ? ""
                                : context
                                    .watch<OrdersViewModel>()
                                    .itemsWaitCourierCome
                                    .length
                                    .toString()
                            : context.watch<OrdersViewModel>().items!.length ==
                                    0
                                ? ""
                                : context
                                    .watch<OrdersViewModel>()
                                    .items!
                                    .length
                                    .toString(),
                style: MyStyles.r12.copyWith(
                  color: label == labelStr[currentSelectedIndex]
                      ? Colors.white
                      : MyColors.color666,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
