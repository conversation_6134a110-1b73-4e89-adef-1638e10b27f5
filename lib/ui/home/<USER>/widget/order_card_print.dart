import 'dart:io';
import 'package:connect/http/http_manager.dart';
import 'package:connect/common/constants.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/circle_progress.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/button_style_manager.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/print_manager.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:printing/printing.dart';
import '../order_status_manager.dart';

class OrderCardPrint extends StatefulWidget {
  OrderCardPrint(this.entity, {Key? key}) : super(key: key);

  final OrdersEntity entity;

  @override
  _OrderCardPrintState createState() => _OrderCardPrintState();
}

class _OrderCardPrintState extends State<OrderCardPrint> {
  bool showLoading = false;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _buildPrint(widget.entity),
      ],
    );
  }

  Widget _buildPrint(OrdersEntity entity) {
    return Row(
      children: [
        Opacity(
          opacity: showLoading ? 0.8 : 1.0,
          child: Container(
            alignment: Alignment.center,
            child: ElevatedButton(
              style: ButtonStyleManager.buttonStyleAllPadding(
                  const EdgeInsets.symmetric(horizontal: 30)),
              child: Row(
                children: [
                  Text(
                    S.of(context).order_print,
                    style:
                        TextStyle(fontSize: Dimens.sp_14, color: Colors.white),
                  ),
                  showLoading ? CircleProgress() : EmptyContainer(),
                ],
              ),
              onPressed: showLoading
                  ? null
                  : () async {
                      if (!OrderStatusManager.cancelOrder(entity)) {
                        if (entity.status == Constants.CONFIRMED) {
                          var isSunmi = await PrintManager.instance.isSunmi();
                          LogUtil.v("isSunmi::$isSunmi");
                          if (isSunmi) {
                            LogManager.instance!.log("OrderCardPrint", "",
                                "sunmiPrint click, orderId::${widget.entity.orderId}, passcode::${widget.entity.passcode},doublePrint::${ConnectCache.getDoublePrint()}");
                            PrintManager.instance.sunmiPrint(widget.entity);
                            if (ConnectCache.getDoublePrint()!) {
                              PrintManager.instance.sunmiPrint(widget.entity);
                            }
                          } else {
                            _getH5();
                          }
                        } else {
                          ToastUtils.show(S.of(context).confirmed_before_print);
                        }
                      }
                    },
            ),
          ),
        ),
      ],
    );
  }

  //h5 print
  _getH5() async {
    setState(() {
      showLoading = true;
    });
    LogUtil.v("print_orderId::${widget.entity.orderId}");
    // order_2aKECzGZd4
    var url = HttpUri.PRINT_URL + '${widget.entity.orderId}';
    try {
      var response = await HttpManager.instance!.get(url);
      // LogUtil.v("statusCode::${response.statusCode}");
      if (response != null && response.statusCode == HttpStatus.ok) {
        var json = response.data;
        Printing.layoutPdf(
            onLayout: (PdfPageFormat format) async =>
                await Printing.convertHtml(
                  format: format,
                  html: json.toString(),
                )).whenComplete(() => setState(() {
              if (showLoading != null) showLoading = false;
            }));
      } else {
        PrintManager.instance.printException();
        setState(() {
          showLoading = false;
        });
      }
    } catch (exception) {
      PrintManager.instance.printException();
      setState(() {
        showLoading = false;
      });
    }
  }
}
