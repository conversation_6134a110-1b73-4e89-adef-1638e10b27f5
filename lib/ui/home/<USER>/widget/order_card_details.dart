import 'package:connect/data/orders_entity.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';
import '../order_status_manager.dart';
import 'order_card_delivery.dart';
import 'order_card_fee.dart';
import 'order_card_menu_time.dart';
import 'order_card_phone.dart';
import 'order_card_status.dart';
import 'order_card_subtotal.dart';

enum OrderCardFrom {
  portrait,
  details,

  // used in `OrderCard`
  search,
  progress,
  history,
  scheduled,
}

class OrderCardDetails extends StatefulWidget {
  OrderCardDetails(this.entity, this.from, {Key? key}) : super(key: key);

  final OrdersEntity? entity;
  final OrderCardFrom from;

  @override
  _OrderCardDetailsState createState() => _OrderCardDetailsState();
}

class _OrderCardDetailsState extends State<OrderCardDetails> {
  @override
  Widget build(BuildContext context) {
    // widget.entity.status = Constants.CANCELLED;
    return Opacity(
      opacity: OrderStatusManager.cancelOrder(widget.entity!) ? 0.2 : 1.0,
      child: Column(
        children: [
          _buildItem(OrderCardStatus(widget.entity)),
          _buildItem(OrderCardPhone(widget.entity)),
          OrderCardMenuTimeState.shouldShow(widget.entity)
              ? _buildItem(OrderCardMenuTime(widget.entity, false, widget.from))
              : EmptyContainer(),
          widget.entity?.shouldShowDelivery() == true
              ? _buildItem(OrderCardDelivery(widget.entity, from: widget.from))
              : EmptyContainer(),
          _buildItem(OrderCardSubtotal(
            widget.entity,
            showExpand: false,
            from: widget.from,
          )),
          _buildItem(OrderCardFee(widget.entity, from: widget.from)),
        ],
      ),
    );
  }

  Widget _buildItem(StatefulWidget content) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.fromLTRB(20, 10, 20, 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
        child: content,
      ),
    );
  }
}
