import 'dart:math';

import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:connect/utils/flutter_device_type.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

class OrderCardPhone extends StatefulWidget {
  OrderCardPhone(this.entity, {Key? key}) : super(key: key);

  final OrdersEntity? entity;

  @override
  _OrderCardPhoneState createState() => _OrderCardPhoneState();
}

class _OrderCardPhoneState extends State<OrderCardPhone> {
  @override
  Widget build(BuildContext context) {
    return _buildPhone(widget.entity!);
  }

  _getDistance(double lat1, double lng1, double lat2, double lng2) {
    double def = 6378137.0;
    double radLat1 = _rad(lat1);
    double radLat2 = _rad(lat2);
    double a = radLat1 - radLat2;
    double b = _rad(lng1) - _rad(lng2);
    double s = 2 *
        asin(sqrt(pow(sin(a / 2), 2) +
            cos(radLat1) * cos(radLat2) * pow(sin(b / 2), 2)));
    return ((s * def).roundToDouble() / 1000).toStringAsPrecision(2);
  }

  double _rad(double d) {
    return d * pi / 180.0;
  }

  Widget _buildPhone(OrdersEntity entity) {
    bool orderStatus = (entity.status == Constants.DECLINED ||
        entity.status == Constants.CANCELLED);

    String? distance = "";

    var resAddress = entity.restaurant?.address?.location?.coordinates;
    var deliveryAddress = entity.delivery?.address?.location?.coordinates;
    if (resAddress != null && deliveryAddress != null) {
      distance = _getDistance(resAddress[1], resAddress[0], deliveryAddress[1],
              deliveryAddress[0]) +
          "km";
    }

    String customerPhoneSuffix = "";
    if (entity.customer != null) {
      String phone = entity.customer!.phone!;
      customerPhoneSuffix = phone.substring(phone.length - 4, phone.length);
    }

    String? formatted = "";
    String? unit = "";
    if (entity.delivery != null && entity.delivery!.address != null) {
      formatted = entity.delivery!.address!.formatted;
      unit = entity.delivery!.address!.unit;
    }

    bool isSelfPickup = false;
    String? position = "";
    if (entity.delivery == null) {
      isSelfPickup = true;
    } else {
      isSelfPickup = false;
      if (entity.delivery!.provider == null) {
        position = "$distance | $formatted #$unit";
      } else {
        position = distance;
      }
    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(S.of(context).tail_number + " " + customerPhoneSuffix,
                      style: TextStyle(
                          fontSize: Dimens.sp_16,
                          color: Colors.black87,
                          fontWeight: FontWeight.bold)),
                  SizedBox(
                    height: Dimens.dp_5,
                  ),
                  InkWell(
                    onTap: () {
                      if (formatted!.isNotEmpty) {
                        Clipboard.setData(ClipboardData(text: formatted)).then(
                            (value) => ToastUtils.show(
                                S.of(context).copy_to_clipboard));
                      }
                    },
                    child: isSelfPickup
                        ? Container(
                            width: Dimens.dp_0,
                            height: Dimens.dp_0,
                          )
                        : Text(position!,
                            style: TextStyle(
                                fontSize: Dimens.sp_14, color: Colors.black87)),
                  )
                ],
              ),
            ),
            orderStatus
                ? Container(
                    width: Dimens.dp_0,
                    height: Dimens.dp_0,
                  )
                : Column(
                    children: [
                      InkWell(
                        onTap: () {
                          if (entity.customer != null) {
                            if (GlobalConfig.isSunmi ||
                                Device.get().isTablet!) {
                              MessageDialog.messageAlert(
                                  "${S.of(context).sunmi_not_support_calls}\n\n${entity.customer!.phone}");
                            } else {
                              launch("tel:${entity.customer!.phone}");
                            }
                          } else {
                            ToastUtils.show("Phone exception");
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(Dimens.dp_5),
                          decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius:
                                  BorderRadius.circular(Dimens.dp_20)),
                          child: Icon(
                            Icons.phone,
                            color: Colors.white,
                          ),
                        ),
                      )
                    ],
                  ),
          ],
        ),
      ],
    );
  }
}
