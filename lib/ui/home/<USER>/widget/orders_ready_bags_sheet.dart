import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/material.dart';

class OrdersReadyBagsSheet {
  static show(BuildContext context, {ValueChanged<int>? callback}) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context, callback);
        },
        context: context);
  }

  static Widget buildBottomSheetWidget(BuildContext context, callback) {
    return Container(
      height: Dimens.dp_330,
      child: Column(
        children: [
          buildItem(context, S.of(context).rest_ready_bags_title),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, "1", onTap: () async {
            if (callback != null) callback(1);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, "2", onTap: () async {
            if (callback != null) callback(2);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, "3", onTap: () async {
            if (callback != null) callback(3);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, "4", onTap: () async {
            if (callback != null) callback(4);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, "5", onTap: () async {
            if (callback != null) callback(5);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, "6", onTap: () async {
            if (callback != null) callback(6);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(context, S.of(context).cancel),
        ],
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_40,
        child: Text(
          title,
          style: TextStyle(
              color: S.of(context).rest_ready_bags_title == title
                  ? Colors.black87
                  : Colors.blue,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
