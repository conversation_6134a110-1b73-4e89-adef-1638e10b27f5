import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/material.dart';

class OrderCardExpand extends StatefulWidget {
  OrderCardExpand(this.callback, {Key? key}) : super(key: key);

  final VoidCallback callback;

  @override
  _OrderCardExpandState createState() => _OrderCardExpandState();
}

class _OrderCardExpandState extends State<OrderCardExpand> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(bottom: Dimens.dp_8),
        height: Dimens.dp_36,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(S.of(context).expand_orders,
                style:
                    TextStyle(fontSize: Dimens.sp_16, color: Colors.black87)),
            SizedBox(
              width: Dimens.dp_8,
            ),
            Icon(
              Icons.keyboard_arrow_down,
              color: Colors.black87,
            ),
          ],
        ),
      ),
      onTap: () {
        widget.callback();
      },
    );
  }
}
