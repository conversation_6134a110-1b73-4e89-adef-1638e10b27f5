import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/material.dart';

class OrderCardCollapse extends StatefulWidget {
  OrderCardCollapse(this.callback, {Key? key}) : super(key: key);

  final VoidCallback callback;

  @override
  _OrderCardCollapseState createState() => _OrderCardCollapseState();
}

class _OrderCardCollapseState extends State<OrderCardCollapse> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(bottom: Dimens.dp_8),
        height: Dimens.dp_40,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(S.of(context).collapse_orders,
                style:
                    TextStyle(fontSize: Dimens.sp_16, color: Colors.black87)),
            SizedBox(
              width: Dimens.dp_8,
            ),
            Icon(
              Icons.keyboard_arrow_up,
              color: Colors.black87,
            ),
          ],
        ),
      ),
      onTap: () {
        widget.callback();
      },
    );
  }
}
