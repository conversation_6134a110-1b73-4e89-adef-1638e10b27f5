import 'dart:convert';
import 'package:connect/common/MyStyles.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';

import '../../../../common/global.dart';

class OrderCardFood extends StatefulWidget {
  OrderCardFood(
    this.entity, {
    Key? key,
    this.isExpand = false,
    this.showExpand = true,
  }) : super(key: key);

  final OrdersEntity? entity;
  final bool isExpand;
  final bool showExpand;

  @override
  _OrderCardFoodState createState() => _OrderCardFoodState(isExpand);
}

class _OrderCardFoodState extends State<OrderCardFood> {
  _OrderCardFoodState(
    this.isExpanded,
  );
  bool isExpanded;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: _buildTextFood(widget.entity!),
        ),
        SizedBox(
          height: Dimens.dp_8,
        ),
        _buildSubTotal(widget.entity!),
      ],
    );
  }

  bool showExpandedRow() {
    return !GlobalConfig.isSunmi && widget.showExpand;
  }

  bool isExpandedWapper() {
    if (!showExpandedRow()) {
      return true;
    } else {
      return isExpanded;
    }
  }

  Widget _buildSubTotal(OrdersEntity entity) {
    TextStyle style = TextStyle(fontSize: Dimens.sp_14, color: Colors.black87);
    num? subtotal = 0;
    if (entity.subtotal != null) {
      subtotal = entity.subtotal;
    }
    return Stack(
      alignment: AlignmentDirectional.center,
      children: [
        showExpandedRow() ? _buildExpandedRow() : EmptyContainer(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Spacer(),
            Text(S.of(context).sub_total, style: style),
            SizedBox(width: 4),
            Text(
                ServerMultiLan.coinSymbol(
                    entity.restaurant, "${subtotal! / 100}"),
                style: style),
          ],
        ),
      ],
    );
  }

  Widget _buildExpandedRow() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            isExpandedWapper()
                ? S.of(context).orders_button_collapse
                : S.of(context).orders_button_expand,
            style: MyStyles.r13.copyWith(
              color: MyColors.color999,
            ),
          ),
          SizedBox(
            width: 4,
          ),
          Icon(
            isExpandedWapper()
                ? Icons.keyboard_arrow_up_rounded
                : Icons.keyboard_arrow_down_rounded,
            color: MyColors.color999,
            size: 20,
          ),
        ],
      ),
      onTap: () => setState(() {
        isExpanded = !isExpanded;
      }),
    );
  }

  List<Widget> _buildTextFood(OrdersEntity entity) {
    Map<String, int> foodMaps = {};
    List<Widget> foods = [];
    entity.items?.forEach((element) {
      var jsonStr = jsonEncode(element);
      if (foodMaps[jsonStr] == null) {
        foodMaps[jsonStr] = 1;
      } else {
        foodMaps[jsonStr] = foodMaps[jsonStr]! + 1;
      }
    });

    foodMaps.forEach((key, value) {
      foods.add(_buildFood(
          JsonConvert.fromJsonAsT<OrdersItem>(jsonDecode(key)), value));
    });

    return isExpandedWapper() ? foods : [foods.first];
  }

  Widget _buildFood(OrdersItem? element, int size) {
    if (element == null) return EmptyContainer();
    num foodPrice = 0;
    if (element.price != null) {
      foodPrice =
          size > 1 ? (element.price! * size) / 100 : element.price! / 100;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: Dimens.dp_8,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text.rich(TextSpan(children: [
                      TextSpan(
                        text: "$size x ",
                        style: MyStyles.r15.copyWith(
                            color:
                                size > 1 ? MyColors.portal : MyColors.color999),
                      ),
                      TextSpan(
                        text: ServerMultiLan.multiAdapt(element.name),
                        style: MyStyles.m13.copyWith(color: MyColors.color333),
                      ),
                    ])),
                    Text(
                      ServerMultiLan.coinSymbol(
                          widget.entity!.restaurant, "$foodPrice"),
                      style: MyStyles.m13.copyWith(color: MyColors.color333),
                    ),
                  ],
                ),
                SizedBox(
                  height: Dimens.dp_5,
                ),
                Text(
                  element.name!.enUs!,
                  style: MyStyles.m13.copyWith(color: MyColors.color333),
                ),
                SizedBox(
                  height: Dimens.dp_5,
                ),
                (element.options != null && element.options!.length > 0)
                    ? Column(
                        children: _buildOptions(element),
                      )
                    : EmptyContainer()
              ],
            )),
          ],
        ),
      ],
    );
  }

  List<Widget> _buildOptions(OrdersItem element) {
    Map<String, int> foodOptionsMaps = {};
    List<Widget> foodsOptions = [];
    element.options!.forEach((elementOp) {
      var jsonStr = jsonEncode(elementOp);
      if (foodOptionsMaps[jsonStr] == null) {
        foodOptionsMaps[jsonStr] = 1;
      } else {
        foodOptionsMaps[jsonStr] = foodOptionsMaps[jsonStr]! + 1;
      }
    });

    foodOptionsMaps.forEach((key, value) {
      foodsOptions.add(_buildOption(
          JsonConvert.fromJsonAsT<OrdersItemOptions>(jsonDecode(key)), value));
    });

    return foodsOptions;
  }

  Widget _buildOption(OrdersItemOptions? elementOptions, int count) {
    if (elementOptions == null) return EmptyContainer();
    num price = count > 1
        ? (elementOptions.price! * count) / 100
        : elementOptions.price! / 100;
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  width: Dimens.dp_120,
                  child: Text.rich(TextSpan(children: [
                    TextSpan(
                      text: count > 1 ? count.toString() + "x" : "",
                      style: MyStyles.m13.copyWith(
                          color:
                              count > 1 ? MyColors.portal : MyColors.color999),
                    ),
                    TextSpan(
                        text: ServerMultiLan.multiAdapt(elementOptions.name)),
                  ])),
                ),
                Text(
                  elementOptions.name!.enUs!,
                  style: MyStyles.m13.copyWith(color: MyColors.color333),
                ),
              ],
            ),
            elementOptions.price == 0
                ? Container(
                    width: Dimens.dp_0,
                    height: Dimens.dp_0,
                  )
                : Text(
                    ServerMultiLan.coinSymbol(
                        widget.entity!.restaurant, "$price"),
                    style: MyStyles.m13.copyWith(color: MyColors.color333),
                  ),
          ],
        ),
        SizedBox(
          height: Dimens.dp_3,
        ),
      ],
    );
  }
}
