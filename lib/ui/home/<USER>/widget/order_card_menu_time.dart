import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/data/ready_entity.dart';
import 'package:connect/data/repository/order_repository.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/http/commom/http_uri.dart';
import 'package:connect/provider/order_status_provider.dart';
import 'package:connect/provider/orders_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/circle_progress.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/print_manager.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import '../../../../http/http_error.dart';
import '../../../view/message_dialog.dart';
import '../order_status_manager.dart';
import '../orders_prepare_sheet.dart';
import 'menu_time.dart';
import 'order_card_details.dart';
import 'orders_ready_bags_sheet.dart';

class OrderCardMenuTime extends StatefulWidget {
  OrderCardMenuTime(this.entity, this.belongOrderList, this.from, {Key? key})
      : super(key: key);

  final OrdersEntity? entity;
  final bool belongOrderList;
  final OrderCardFrom from;

  @override
  OrderCardMenuTimeState createState() => OrderCardMenuTimeState();
}

class OrderCardMenuTimeState extends State<OrderCardMenuTime> {
  bool showLoading = false;
  String? confirmedAt = "";
  var isConfirm = false;
  var confirmOrReadyColor;
  bool confirmOrReadyVisible = true;
  bool isExpanded = false;

  static bool shouldShow(OrdersEntity? entity) {
    if (entity == null) return false;
    bool isCancelled = OrderStatusManager.cancelOrder(entity);

    var courierAtDropOff = OrderStatusManager.isCourierAtDropOff(entity);
    var courierDeliveryCompleted =
        OrderStatusManager.isCourierDeliveryCompleted(entity);
    if (isCancelled || courierAtDropOff || courierDeliveryCompleted) {
      return false;
    } else {
      return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    var entity = widget.entity!;
    return !shouldShow(entity) ? EmptyContainer() : _buildMenuTime(entity);
  }

  Widget _buildMenuTime(OrdersEntity entity) {
    // prepareTime will be present when ready 完成备餐 clicked
    if (OrderStatusManager.isNewOrder(entity)) {
      isConfirm = false;
      confirmOrReadyColor = Colors.green[400];
      confirmOrReadyVisible = true;
    } else {
      if (entity.restaurant!.delivery!.prepareTime == null &&
          entity.status == Constants.CONFIRMED) {
        confirmedAt = entity.confirmedAt;
        isConfirm = true;
        confirmOrReadyColor = MyColors.portal;
        confirmOrReadyVisible = true;
      } else {
        isConfirm = true;
        confirmOrReadyVisible = false;
      }
    }

    if (!isConfirm &&
        ConnectCache.getAutoReceiveStatus()! &&
        !showLoading &&
        OrderCardFrom.progress == widget.from) {
      //order auto confirmed
      if (!GlobalConfig.autoConfirmedMapId.contains("${entity.orderId}")) {
        LogManager.instance!.log("OrderCardMenuTime", "",
            "sunmi auto print, ${entity.orderId}, ${entity.passcode}");
        GlobalConfig.autoConfirmedMapId.add("${entity.orderId}");
        request();
      }
    }

    return Column(
      children: [
        widget.from == OrderCardFrom.portrait ||
                widget.from == OrderCardFrom.details
            ? EmptyContainer()
            : Divider(
                height: Dimens.dp_15,
              ),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: MenuTime(entity),
            ),
            _buildReady(isConfirm, confirmOrReadyColor),
          ],
        ),
        SizedBox(
          height: Dimens.dp_3,
        ),
      ],
    );
  }

  Widget _buildReady(bool isConfirm, confirmOrReadyColor) {
    return confirmOrReadyVisible
        ? Row(
            children: [
              Opacity(
                opacity: showLoading ? 0.5 : 1.0,
                child: Container(
                  alignment: Alignment.center,
                  child: ElevatedButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          isConfirm ? MyColors.portal : Colors.blue),
                      shape: MaterialStateProperty.all(RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(Dimens.dp_8))),
                    ),
                    child: Row(
                      children: [
                        Text(
                          isConfirm
                              ? S.of(context).ready_meal
                              : S.of(context).confirm_order,
                          style: MyStyles.m15.copyWith(color: Colors.white),
                        ),
                        showLoading ? CircleProgress() : EmptyContainer(),
                      ],
                    ),
                    onPressed: showLoading
                        ? null
                        : () async {
                            var connectivityResult =
                                await (Connectivity().checkConnectivity());
                            if (connectivityResult == ConnectivityResult.none) {
                              // LogUtil.v("checkNetWork");
                              ToastUtils.show(S.of(context).network_check);
                            } else {
                              setState(() {
                                request();
                              });
                            }
                          },
                  ),
                ),
              ),
            ],
          )
        : EmptyContainer();
  }

  request() {
    Orientation orientation = MediaQuery.of(context).orientation;
    if (orientation == Orientation.landscape) {
      LoadingUtils.show();
    }
    String? restaurantId = widget.entity!.restaurant!.restaurantId;
    String? orderId = widget.entity!.orderId;
    if (!isConfirm) {
      //confirm order
      if (ConnectCache.getAutoReceiveStatus()! && GlobalConfig.isSunmi) {
        //auto confirm order
        showLoading = true;
        _confirmOrder(restaurantId!, orderId!, orientation);
      } else {
        //confirm order manual
        OrdersPrepareSheet.show(context, callback: (value) {
          setState(() {
            showLoading = true;
          });

          var params = Map<String, dynamic>();
          params["prepare"] = "$value";
          TrackingUtils.instance!
              .tracking(TConstants.R_CONFIRM_ORDER_CLICK, value: params);

          _confirmOrder(restaurantId!, orderId!, orientation, prepare: value);
        });
      }
    } else {
      //confirm order ready
      TrackingUtils.instance!.tracking(TConstants.R_READY_ORDER_CLICK);

      var showBagsDialog =
          OrderStatusManager.restaurantDeliverySelf(widget.entity!) ||
              OrderStatusManager.customerPickupSelf(widget.entity!);
      if (showBagsDialog) {
        showLoading = true;
        _readyOrder(restaurantId!, orderId!, orientation, 0);
      } else {
        OrdersReadyBagsSheet.show(context, callback: (bags) {
          setState(() {
            showLoading = true;
          });
          _readyOrder(restaurantId!, orderId!, orientation, bags);
        });
      }
    }
  }

  _readyOrder(String restaurantId, String orderId, Orientation orientation,
      int bags) async {
    OrderRepository.requestReady(restaurantId, orderId, bags: bags, error: (e) {
      var params = Map<String, dynamic>();
      params["fail_reason"] = "${e.toString()}";
      TrackingUtils.instance!.tracking(TConstants.R_READY_ORDER_FAIL);
    }).then((value) async {
      if (value != null) {
        TrackingUtils.instance!.tracking(TConstants.R_READY_ORDER_SUCCESS);

        Orientation orientation = MediaQuery.of(context).orientation;
        if (orientation == Orientation.landscape) {
          if (OrderCardFrom.progress == widget.from &&
              GlobalConfig.currentTabIndex == 0) {
            //list logic
            LoadingUtils.show();
            var read = context.read<OrdersViewModel>();
            if (GlobalConfig.currentLabelIndex == 2) {
              read.items = [];
            }
            await read.requestOrder(context, option: true);
            LoadingUtils.dismiss();
          } else if (OrderCardFrom.details == widget.from &&
              GlobalConfig.currentTabIndex == 0) {
            //in progress details tap confirm button
            LogUtil.v("ready,details");
            LoadingUtils.show();
            var read = context.read<OrdersViewModel>();
            if (GlobalConfig.currentLabelIndex == 2) {
              read.items = [];
            }
            await read.requestOrder(context, option: true);
            LoadingUtils.dismiss();
          } else if ((OrderCardFrom.history == widget.from ||
                  OrderCardFrom.details == widget.from) &&
              GlobalConfig.currentTabIndex == 1) {
            GlobalConfig.eventBus.fire(ResRefreshHistoryOrders());
          } else if ((OrderCardFrom.scheduled == widget.from ||
                  OrderCardFrom.details == widget.from) ||
              GlobalConfig.currentTabIndex == 2) {
            GlobalConfig.eventBus.fire(ResRefreshScheduledOrders());
          } else {
            //search or search details
            FocusScope.of(context).requestFocus(FocusNode());
            ReadyEntity? readyEntity =
                JsonConvert.fromJsonAsT<ReadyEntity>(value.data);
            widget.entity!.restaurant!.delivery!.prepareTime =
                readyEntity?.restaurant?.delivery?.prepareTime;
            widget.entity!.status = readyEntity?.status;
            if (readyEntity?.delivery == null) {
              widget.entity!.delivery = null;
            } else {
              widget.entity!.delivery!.status = readyEntity?.delivery?.status;
            }
            context
                .read<OrdersViewModel>()
                .readyToWaitCourierCome(widget.entity);
            context.read<OrderStatusProvider>().notifyConfirmedOrder();
          }
        } else {
          ReadyEntity? readyEntity =
              JsonConvert.fromJsonAsT<ReadyEntity>(value.data);
          widget.entity!.restaurant!.delivery!.prepareTime =
              readyEntity?.restaurant?.delivery?.prepareTime;
          widget.entity!.status = readyEntity?.status;
          if (readyEntity?.delivery == null) {
            widget.entity!.delivery = null;
          } else {
            widget.entity!.delivery!.status = readyEntity?.delivery?.status;
          }
          LogUtil.v("readyEntity_status::${readyEntity?.status}");
          LogUtil.v("readyEntity_from::${widget.from}");
          context.read<OrdersViewModel>().readyToWaitCourierCome(widget.entity);
          context.read<OrderStatusProvider>().notifyConfirmedOrder();
        }
        //OrdersDetailsSplitScreenRoute receive this event for sync order status
      }
    }).catchError((e) {
      Sentry.captureException("ready_exception::${e.toString()}");
      if (orientation == Orientation.landscape) {
        LoadingUtils.dismiss();
      }
    }).whenComplete(() {
      if (mounted) {
        if (showLoading) {
          setState(() {
            showLoading = false;
          });
        }
      }
    });
  }

  _confirmOrder(String restaurantId, String orderId, Orientation orientation,
      {int prepare = 0}) async {
    OrderRepository.requestConfirmOrder(restaurantId, orderId, prepare: prepare,
        error: (e) {
      //request failed
      var params = Map<String, dynamic>();
      params["fail_reason"] = "${e.toString()}";
      TrackingUtils.instance!.tracking(TConstants.R_CONFIRM_ORDER_FAIL);
    }).then((value) async {
      // Confirm that state of order should be confirmed.
      if (value == null) return;
      ReadyEntity? readyEntity =
          JsonConvert.fromJsonAsT<ReadyEntity>(value.data);
      if (readyEntity?.status?.toLowerCase() != "confirmed") {
        return;
      }

      // context.read<OrderStatusProvider>().notifyConfirmedOrder();
      Orientation orientation = MediaQuery.of(context).orientation;
      if (orientation == Orientation.landscape) {
        //list,else details
        if (OrderCardFrom.progress == widget.from &&
            GlobalConfig.currentTabIndex == 0) {
          //list logic
          LoadingUtils.show();
          var read = context.read<OrdersViewModel>();
          if (GlobalConfig.currentLabelIndex == 1) {
            read.items = [];
          }
          await read.requestOrder(context, option: true);
          LoadingUtils.dismiss();
        } else if (OrderCardFrom.details == widget.from &&
            GlobalConfig.currentTabIndex == 0) {
          //in progress details tap confirm button
          LoadingUtils.show();
          var read = context.read<OrdersViewModel>();
          if (GlobalConfig.currentLabelIndex == 1) {
            read.items = [];
          }
          await read.requestOrder(context, option: true);
          LoadingUtils.dismiss();
        } else if ((OrderCardFrom.history == widget.from ||
                OrderCardFrom.details == widget.from) &&
            GlobalConfig.currentTabIndex == 1) {
          GlobalConfig.eventBus.fire(ResRefreshHistoryOrders());
        } else if ((OrderCardFrom.scheduled == widget.from ||
                OrderCardFrom.details == widget.from) ||
            GlobalConfig.currentTabIndex == 2) {
          GlobalConfig.eventBus.fire(ResRefreshScheduledOrders());
        } else {
          //search
          FocusScope.of(context).requestFocus(FocusNode());
          ReadyEntity? readyEntity =
              JsonConvert.fromJsonAsT<ReadyEntity>(value.data);
          widget.entity!.restaurant!.delivery!.prepareTime =
              readyEntity?.restaurant?.delivery?.prepareTime;
          widget.entity!.status = readyEntity?.status;
          widget.entity!.restaurant!.delivery!.prepare =
              readyEntity?.restaurant?.delivery!.prepare;
          widget.entity!.confirmedAt = readyEntity?.confirmedAt;
          context.read<OrdersViewModel>().confirmToReady(widget.entity);
          context.read<OrderStatusProvider>().notifyConfirmedOrder();
        }
      } else {
        ReadyEntity? readyEntity =
            JsonConvert.fromJsonAsT<ReadyEntity>(value.data);
        widget.entity!.restaurant!.delivery!.prepareTime =
            readyEntity?.restaurant?.delivery?.prepareTime;
        widget.entity!.status = readyEntity?.status;
        widget.entity!.restaurant!.delivery!.prepare =
            readyEntity?.restaurant?.delivery!.prepare;
        widget.entity!.confirmedAt = readyEntity?.confirmedAt;
        LogUtil.v("readyEntity_confirm_from::${widget.from}");
        context.read<OrdersViewModel>().confirmToReady(widget.entity);
        context.read<OrderStatusProvider>().notifyConfirmedOrder();
      }

      TrackingUtils.instance!.tracking(TConstants.R_CONFIRM_ORDER_SUCCESS);
      //print order receipt
      var isSunmi = await PrintManager.instance.isSunmi();
      LogUtil.v("isSunmi::$isSunmi");
      if (isSunmi) {
        LogManager.instance!.log("DriverPhone", "",
            "sunmiPrint pre, orderId::$orderId, restaurantId::$restaurantId,doublePrint::${ConnectCache.getDoublePrint()}");
        PrintManager.instance.sunmiPrint(widget.entity!);
        if (ConnectCache.getDoublePrint()!) {
          PrintManager.instance.sunmiPrint(widget.entity!);
        }
      }
    }).catchError((e) {
      var data = Map<String, dynamic>();
      data["status"] = "confirmed";
      data["prepare"] = int.parse(ConnectCache.getPrepareTime()!);
      var confirmUrl = HttpUri.ORDERS_STATUS
          .replaceAll("{restaurant_id}", restaurantId)
          .replaceAll("{order_id}", orderId);
      Sentry.captureException(
          "confirm_exception::${e.toString()},isAutoPrint::${ConnectCache.getAutoReceiveStatus()},params::${data.toString()},url::$confirmUrl");
    }).whenComplete(() {
      if (orientation == Orientation.landscape) {
        LoadingUtils.dismiss();
      }
      if (mounted) {
        if (showLoading) {
          setState(() {
            showLoading = false;
          });
        }
      }
    });
  }
}
