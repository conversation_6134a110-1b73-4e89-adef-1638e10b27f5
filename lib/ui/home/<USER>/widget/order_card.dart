import 'package:connect/common/global.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/provider/history_model.dart';
import 'package:connect/provider/orders_model.dart';
import 'package:connect/provider/pre_order_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import '../order_status_manager.dart';
import '../orders_details_route.dart';
import 'order_card_details.dart';
import 'order_card_menu_time.dart';
import 'order_card_status.dart';
import 'order_card_subtotal.dart';
import 'package:provider/provider.dart';

class OrderCard extends StatefulWidget {
  OrderCard(this.entity, this.from, {Key? key}) : super(key: key);

  final OrdersEntity? entity;
  final OrderCardFrom from;

  @override
  _OrderCardState createState() => _OrderCardState();
}

class _OrderCardState extends State<OrderCard> {
  @override
  Widget build(BuildContext context) {
    // widget.entity.status = Constants.CANCELLED;
    var waitConfirm = OrderStatusManager.isNewOrder(widget.entity!);
    var waitReady =
        OrderStatusManager.restaurantConfirmedButNotChucan(widget.entity!);
    // LogUtil.v(
    //     "waitConfirm::$waitConfirm，waitReady：：$waitReady,passcode::${widget.entity.passcode},status::${widget.entity.status}");
    return Opacity(
      opacity: OrderStatusManager.cancelOrder(widget.entity!) ? 0.2 : 1.0,
      child: InkWell(
        child: Card(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
          elevation: 0,
          shadowColor: Colors.transparent,
          color: widget.entity!.isSelected! ? Colors.blue[200] : Colors.white,
          margin: const EdgeInsets.fromLTRB(
              Dimens.dp_20, Dimens.dp_8, Dimens.dp_20, Dimens.dp_8),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
            child: Column(
              children: [
                OrderCardStatus(widget.entity),
                (waitConfirm || waitReady)
                    ? OrderCardMenuTime(widget.entity, true, widget.from)
                    : EmptyContainer(),
                waitConfirm
                    ? OrderCardSubtotal(widget.entity)
                    : EmptyContainer(),
              ],
            ),
          ),
        ),
        onTap: () {
          LogUtil.v("from::${widget.from}");
          if (OrderCardFrom.search == widget.from) {
            FocusScope.of(context).requestFocus(FocusNode());
            Navigator.of(context)
                .pushNamed(OrdersDetailsRoute.tag, arguments: widget.entity);
          } else {
            Orientation orientation = MediaQuery.of(context).orientation;
            if (orientation == Orientation.landscape) {
              LogUtil.v("currentTabIndex::${GlobalConfig.currentTabIndex}");
              if (GlobalConfig.currentTabIndex == 0) {
                OrdersViewModel model = context.read<OrdersViewModel>();
                model.items!.forEach((element) {
                  element.isSelected = false;
                });
                widget.entity!.isSelected = true;
                model.notify();
              } else if (GlobalConfig.currentTabIndex == 1) {
                HistoryViewModel model = context.read<HistoryViewModel>();
                model.items.forEach((element) {
                  element.isSelected = false;
                });
                widget.entity!.isSelected = true;
                model.notify();
              } else {
                PreOrderViewModel model = context.read<PreOrderViewModel>();
                model.items.forEach((element) {
                  element.isSelected = false;
                });
                widget.entity!.isSelected = true;
                model.notify();
              }
              // sync order details page
              // context.read<OrdersDetailsRefreshProvider>().setOrders(widget.entity);
              GlobalConfig.eventBus.fire(ResNotifyOrderDetails(widget.entity));
            } else {
              Navigator.of(context)
                  .pushNamed(OrdersDetailsRoute.tag, arguments: widget.entity);
            }
          }
        },
      ),
    );
  }
}
