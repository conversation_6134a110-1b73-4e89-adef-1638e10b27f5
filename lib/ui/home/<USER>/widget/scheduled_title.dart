import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/material.dart';

import '../orders_setting.dart';

class ScheduledTitle extends StatefulWidget {
  ScheduledTitle(this.expand, {Key? key}) : super(key: key);

  final ValueChanged expand;

  @override
  _ScheduledTitleState createState() => _ScheduledTitleState();
}

class _ScheduledTitleState extends State<ScheduledTitle> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Dimens.dp_40,
      padding: const EdgeInsets.only(left: Dimens.dp_20, right: Dimens.dp_20),
      color: Colors.grey[300],
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                "将在预定送达时间90分钟前提醒您",
                style: TextStyle(color: Colors.black87),
              ),
              Divider(thickness: 1),
              SizedBox(
                width: Dimens.dp_5,
              ),
              Text(
                "|",
                style: TextStyle(color: Colors.black87),
              ),
              SizedBox(
                width: Dimens.dp_5,
              ),
              InkWell(
                onTap: () {
                  Navigator.of(context).pushNamed(OrdersSetting.tag);
                },
                child: Text(
                  "设置 >",
                  style: TextStyle(color: Colors.black87),
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildExpandOrder(() {
                isExpanded = !isExpanded;
                widget.expand(isExpanded);
              }),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExpandOrder(VoidCallback expand) {
    return InkWell(
      onTap: () {
        expand();
      },
      child: Container(
        child: Row(
          children: [
            Icon(
              isExpanded ? Icons.unfold_less : Icons.unfold_more,
            ),
            Text(
                isExpanded
                    ? S.of(context).collapse_orders
                    : S.of(context).expand_orders,
                style:
                    TextStyle(color: Colors.black87, fontSize: Dimens.sp_13)),
          ],
        ),
      ),
    );
  }
}
