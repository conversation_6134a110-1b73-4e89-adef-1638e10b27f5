import 'package:connect/common/MyStyles.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/date_util.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import 'dart:async';
import '../order_status_manager.dart';

class MenuTime extends StatefulWidget {
  MenuTime(this.entity, {Key? key}) : super(key: key);

  final OrdersEntity entity;

  @override
  _MenuTimeState createState() => _MenuTimeState();
}

class _MenuTimeState extends State<MenuTime> {
  String orderTextStatus = "";
  String orderTextTime = "";
  Timer? _timer;
  int localPrepareTime = 0;
  bool isTimeout = false;

  void startTime() {
    isTimeout = false;
    localPrepareTime = 0;
    var isNewOrder = OrderStatusManager.isNewOrder(widget.entity);
    var restaurantConfirmedButNotChucan =
        OrderStatusManager.restaurantConfirmedButNotChucan(widget.entity);
    if (isNewOrder) {
      if (_timer != null) {
        _timer?.cancel();
        _timer = null;
      }
      orderTextStatus = S.of(context).confirm_receiving;
      orderTextTime = S.of(context).manual_confirm_receiving;
    } else if (restaurantConfirmedButNotChucan) {
      var prepare = widget.entity.restaurant!.delivery!.prepare;
      if (prepare != null && widget.entity.confirmedAt != null) {
        var recommendTimeDate =
            DateUtil.getDateTime(widget.entity.confirmedAt!)!
                .toLocal()
                .add(Duration(minutes: prepare.toInt()));
        String recommendTime = formatDate(recommendTimeDate, [HH, ':', nn]);

        orderTextStatus = time();
        orderTextTime =
            sprintf(S.of(context).recommended_time, [recommendTime]);
        if (_timer == null) {
          _timer = Timer.periodic(Duration(seconds: 1), (Timer t) {
            setState(() {
              orderTextStatus = time();
              orderTextTime =
                  sprintf(S.of(context).recommended_time, [recommendTime]);
            });
          });
        }
      }
    } else {
      //waitCourierToRestaurant || isCourierAtDropOff || sCourierDeliveryCompleted
      if (_timer != null) {
        _timer?.cancel();
        _timer = null;
      }
      orderTextStatus = S.of(context).restaurant_completed;
      var prepareTime = widget.entity.restaurant!.delivery!.prepareTime;
      // LogUtil.v("prepareTime:: $prepareTime");
      if (prepareTime != null &&
          prepareTime > int.parse(ConnectCache.getPrepareTime()!)) {
        isTimeout = true;
        int timeout =
            prepareTime - int.parse(ConnectCache.getPrepareTime()!) as int;
        orderTextTime = sprintf(S.of(context).meal_preparation_time_timeout, [
          _printDuration(Duration(seconds: prepareTime * 60 as int)),
          _printDuration(Duration(seconds: timeout * 60))
        ]);
      } else if (prepareTime != null) {
        isTimeout = false;
        orderTextTime = sprintf(S.of(context).meal_preparation_time,
            [_printDuration(Duration(seconds: prepareTime * 60 as int))]);

        // LogUtil.v(
        //     "Duration:: ${Duration(seconds: prepareTime * 60).toString()}");
      }
    }
  }

  String time() {
    var dateTime = DateUtil.getDateTime(widget.entity.confirmedAt!)!.toLocal();
    Duration difference = DateTime.now().difference(dateTime);
    localPrepareTime = difference.inSeconds;

    return sprintf(
        S.of(context).meal_preparation_time, [_printDuration(difference)]);
  }

  String _printDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }

  @override
  void dispose() {
    super.dispose();
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // LogUtil.v("MenuTime build");
    startTime();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          orderTextStatus,
          style: MyStyles.m13.copyWith(
            color: OrderStatusManager.restaurantConfirmedButNotChucan(
                    widget.entity)
                ? localPrepareTime >
                        int.parse(ConnectCache.getPrepareTime()!) * 60
                    ? MyColors.portal
                    : MyColors.color333
                : MyColors.color333,
          ),
        ),
        SizedBox(
          height: Dimens.dp_5,
        ),
        Text(
          orderTextTime,
          style: MyStyles.r13
              .copyWith(color: isTimeout ? MyColors.portal : MyColors.color666),
        ),
      ],
    );
  }
}
