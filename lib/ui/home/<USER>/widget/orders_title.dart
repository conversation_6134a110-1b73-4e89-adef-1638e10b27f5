import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/gen/assets.gen.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/orders_setting.dart';
import 'package:connect/ui/home/<USER>/search_cancel.dart';
import 'package:connect/ui/search/search_route.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:flutter/material.dart';

import '../../../../utils/print_manager.dart';

class OrdersTitle extends StatefulWidget {
  final GlobalKey<SearchRightWidgetState> searchRightKey = GlobalKey();

  OrdersTitle({Key? key}) : super(key: key);

  @override
  _OrdersTitleState createState() => _OrdersTitleState();
}

class _OrdersTitleState extends State<OrdersTitle> {
  @override
  void initState() {
    super.initState();
    GlobalConfig.eventBus.on<AutoConfirmed>().listen((event) {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return _buildTitle();
  }

  Widget _buildTitle() {
    var isSunmi = GlobalConfig.isSunmi;
    return Container(
      padding: const EdgeInsets.only(left: Dimens.dp_15, right: Dimens.dp_15),
      height: Dimens.dp_50,
      color: MyColors.W,
      child: Row(
        children: [
          isSunmi
              ? Container(
                  padding: const EdgeInsets.fromLTRB(
                      Dimens.dp_8, Dimens.dp_4, Dimens.dp_8, Dimens.dp_4),
                  decoration: BoxDecoration(
                      color: MyColors.FFF1F1,
                      borderRadius: BorderRadius.circular(Dimens.dp_20)),
                  child: Row(
                    children: [
                      Container(
                        margin: const EdgeInsets.only(left: Dimens.dp_5),
                        width: Dimens.dp_8,
                        height: Dimens.dp_8,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle, color: MyColors.portal),
                      ),
                      SizedBox(width: 4),
                      Text(
                        ConnectCache.getAutoReceiveStatus()!
                            ? S.of(context).auto_confirm
                            : S.of(context).manual_handle,
                        style: TextStyle(
                            color: MyColors.portal, fontSize: Dimens.sp_14),
                      ),
                    ],
                  ),
                )
              : EmptyContainer(),
          Spacer(),
          InkWell(
            onTap: () {
              Navigator.pushNamed(context, SearchRoute.tag);
            },
            child: Assets.images.icons.order.search.image(
              width: 20,
              height: 20,
            ),
          ),
          SizedBox(
            width: 20,
          ),
          InkWell(
            child: Container(
              margin: const EdgeInsets.only(
                left: Dimens.dp_10,
              ),
              child: Assets.images.icons.order.setting.image(
                width: 20,
                height: 20,
              ),
              height: Dimens.dp_50,
            ),
            onTap: () {
              Navigator.pushNamed(context, OrdersSetting.tag);
            },
          )
        ],
      ),
    );
  }
}
