import 'package:connect/data/orders_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/widget/order_card_print.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';
import '../order_status_manager.dart';
import 'order_card_cancel.dart';
import 'order_card_details.dart';

class OrderCardFee extends StatefulWidget {
  OrderCardFee(
    this.entity, {
    Key? key,
    this.from,
  }) : super(key: key);

  final OrdersEntity? entity;
  final OrderCardFrom? from;

  @override
  _OrderCardFeeState createState() => _OrderCardFeeState();
}

class _OrderCardFeeState extends State<OrderCardFee> {
  @override
  Widget build(BuildContext context) {
    return _buildFee(widget.entity!);
  }

  Widget _buildFee(OrdersEntity entity) {
    TextStyle style = TextStyle(fontSize: Dimens.sp_14, color: Colors.black87);

    num tax = 0;
    if (entity.fees != null) {
      tax = entity.fees!.tax! / 100;
    }

    num tip = 0;
    bool isRestaurantDelivery =
        OrderStatusManager.restaurantDeliverySelf(entity);
    if (isRestaurantDelivery) {
      if (entity.fees != null && entity.fees!.tip != null) {
        tip = entity.fees!.tip!.amount! / 100;
      }
    }
    // LogUtil.v("isRestaurantDelivery::$isRestaurantDelivery");

    // String role = ConnectCache.getCurrentRole().name;
    // bool showPreTotal = false;
    // if (Constants.MANAGER == role) {
    //   showPreTotal = true;
    // }

    num? adjRestaurant = entity.adjustments!.restaurant;

    return Column(
      children: [
        widget.from == OrderCardFrom.portrait ||
                widget.from == OrderCardFrom.details
            ? EmptyContainer()
            : Divider(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(S.of(context).tax, style: style),
            Text(ServerMultiLan.coinSymbol(entity.restaurant, "$tax"),
                style: style),
          ],
        ),
        isRestaurantDelivery
            ? Column(
                children: [
                  SizedBox(
                    height: Dimens.dp_8,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(S.of(context).tip, style: style),
                      Text(ServerMultiLan.coinSymbol(entity.restaurant, "$tip"),
                          style: style),
                    ],
                  ),
                  SizedBox(
                    height: Dimens.dp_8,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(S.of(context).reach_minimum, style: style),
                      Text(
                          (entity.fees != null && entity.fees!.delta != null)
                              ? ServerMultiLan.coinSymbol(entity.restaurant,
                                  "${entity.fees!.delta! / 100}")
                              : "",
                          style: style),
                    ],
                  ),
                  SizedBox(
                    height: Dimens.dp_8,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(S.of(context).delivery_fee, style: style),
                      Text(
                          (entity.fees != null && entity.fees!.delivery != null)
                              ? ServerMultiLan.coinSymbol(entity.restaurant,
                                  "${entity.fees!.delivery! / 100}")
                              : "",
                          style: style),
                    ],
                  ),
                ],
              )
            : Container(
                width: Dimens.dp_0,
                height: Dimens.dp_0,
              ),
        (adjRestaurant != null && adjRestaurant > 0)
            ? Column(
                children: [
                  SizedBox(
                    height: Dimens.dp_8,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child:
                            Text("${entity.adjustments!.reason}", style: style),
                      ),
                      SizedBox(
                        width: Dimens.dp_10,
                      ),
                      Text(
                          adjRestaurant > 0
                              ? ServerMultiLan.coinSymbolCountry(
                                  entity.restaurant!.address!.country,
                                  adjRestaurant / 100)
                              : "(${ServerMultiLan.coinSymbolCountry(entity.restaurant!.address!.country, adjRestaurant / 100)})",
                          style: style),
                    ],
                  ),
                ],
              )
            : EmptyContainer(),
        // showPreTotal
        //     ? Column(
        //         children: [
        //           SizedBox(
        //             height: Dimens.dp_8,
        //           ),
        //           Row(
        //             mainAxisAlignment: MainAxisAlignment.end,
        //             children: [
        //               Text.rich(TextSpan(children: [
        //                 TextSpan(
        //                   text: S.of(context).pre_income,
        //                 ),
        //                 TextSpan(
        //                     text: entity.distribution == null ||
        //                             entity.distribution.restaurant == null
        //                         ? ""
        //                         : ServerMultiLan.coinSymbol(entity.restaurant,
        //                             "${entity.distribution.restaurant / 100}"),
        //                     style: TextStyle(color: Colors.red)),
        //               ])),
        //             ],
        //           ),
        //         ],
        //       )
        //     : Container(
        //         width: Dimens.dp_0,
        //         height: Dimens.dp_0,
        //       ),
        Divider(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(S.of(context).order_number, style: style),
            Text("${entity.passcode}", style: style),
          ],
        ),
        SizedBox(
          height: Dimens.dp_10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(S.of(context).order_time, style: style),
            Text("${entity.createdAtTime()}", style: style),
          ],
        ),
        SizedBox(
          height: Dimens.dp_10,
        ),
        entity.pickupTime() != null
            ? Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(S.of(context).pickup_time, style: style),
                  Text("${entity.pickupTime()}", style: style),
                ],
              )
            : EmptyContainer(),
        entity.pickupTime() != null
            ? SizedBox(
                height: Dimens.dp_10,
              )
            : EmptyContainer(),
        Row(
          children: [
            OrderCardCancel(entity),
            Expanded(
              child: OrderCardPrint(entity),
            ),
          ],
        )
      ],
    );
  }
}
