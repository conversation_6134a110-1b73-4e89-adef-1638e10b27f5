import 'package:connect/data/orders_entity.dart';
import 'package:connect/provider/order_sold_out_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class OrderCardSoldOutOptionItem extends StatefulWidget {
  OrderCardSoldOutOptionItem(this.option, this.foodId, this.isFoodChecked,
      {Key? key})
      : super(key: key);

  final OrdersItemOptions? option;
  final String? foodId;
  final bool? isFoodChecked;

  @override
  _OrderCardSoldOutOptionItemState createState() =>
      _OrderCardSoldOutOptionItemState();
}

class _OrderCardSoldOutOptionItemState
    extends State<OrderCardSoldOutOptionItem> {
  bool? isChecked = false;

  @override
  Widget build(BuildContext context) {
    var optionName = ServerMultiLan.multiAdapt2(widget.option?.name);
    var optionId = widget.option!.optionId;

    if (widget.isFoodChecked!) isChecked = false;

    return Opacity(
      opacity: widget.isFoodChecked! ? 0.2 : 1,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: Dimens.dp_20),
        color: Colors.white,
        child: Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Text(optionName),
                ],
              ),
            ),
            Row(
              children: [
                SizedBox(
                  width: Dimens.dp_15,
                ),
                Checkbox(
                    value: isChecked,
                    activeColor: Colors.blue,
                    onChanged: (checked) {
                      if (widget.isFoodChecked!) return;
                      var model = context.read<OrderSoldOutModel>();
                      if (checked!) {
                        model.setOptionsBean(
                            widget.foodId, optionId, optionName);
                      } else {
                        model.removeOptionsBean(widget.foodId, optionId);
                      }
                      setState(() {
                        isChecked = checked;
                      });
                    }),
              ],
            )
          ],
        ),
      ),
    );
  }
}
