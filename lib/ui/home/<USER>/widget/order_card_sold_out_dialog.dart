import 'package:connect/data/orders_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/order_sold_out_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'order_card_sold_out_dialog_confirm.dart';
import 'order_card_sold_out_item.dart';

class OrderCardSoldOutDialog extends Dialog {
  OrderCardSoldOutDialog(this.entity, this.valueCall, {Key? key})
      : super(key: key);

  final OrdersEntity entity;
  final ValueChanged<String> valueCall;

  @override
  Widget build(BuildContext context) {
    var items = entity.items!;
    items.forEach((element) {
      context.read<OrderSoldOutModel>().setAllFood(
          element!.foodId, ServerMultiLan.multiAdapt2(element.name));
    });
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: Dimens.dp_420,
          child: Container(
            margin: const EdgeInsets.all(Dimens.dp_15),
            padding: const EdgeInsets.fromLTRB(
                Dimens.dp_0, Dimens.dp_15, Dimens.dp_0, Dimens.dp_15),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(Dimens.dp_15),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.fromLTRB(
                      Dimens.dp_15, Dimens.dp_0, Dimens.dp_15, Dimens.dp_15),
                  color: Colors.white,
                  alignment: Alignment.center,
                  height: Dimens.dp_40,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Text(
                                S.of(context).cancel,
                                style: TextStyle(
                                    color: Colors.blue, fontSize: Dimens.sp_15),
                              ),
                            ),
                            SizedBox(
                              width: Dimens.dp_5,
                            ),
                            Expanded(
                                child: Container(
                              child: Text(S.of(context).rest_sold_out,
                                  maxLines: 1,
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      color: Colors.black87,
                                      fontSize: Dimens.sp_16,
                                      fontWeight: FontWeight.bold)),
                            )),
                            OrderCardSoldOutDialogConfirm((message) {
                              Navigator.of(context).pop();
                              valueCall(message);
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(
                  height: Dimens.dp_1,
                ),
                Expanded(
                  child: ListView.builder(
                      itemBuilder: (context, index) {
                        return OrderCardSoldOutItem(items[index]);
                      },
                      itemCount: items.length),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
