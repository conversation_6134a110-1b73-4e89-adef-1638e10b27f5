import 'package:connect/common/MyStyles.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/provider/order_status_provider.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/date_util.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import 'package:provider/provider.dart';

import '../order_status_manager.dart';

class OrderCardStatus extends StatefulWidget {
  OrderCardStatus(this.entity, {Key? key}) : super(key: key);

  final OrdersEntity? entity;

  @override
  _OrderCardStatusState createState() => _OrderCardStatusState();
}

class _OrderCardStatusState extends State<OrderCardStatus> {
  @override
  Widget build(BuildContext context) {
    // LogUtil.v("OrderCardStatus::build");
    context.watch<OrderStatusProvider>().isConfirmedOrder;
    return _buildStatus(widget.entity!);
  }

  Widget _buildStatus(OrdersEntity entity) {
    String? mPassCode = "";
    var mPassCodeExt = "";
    if (entity.passcode != null && entity.passcode!.isNotEmpty) {
      mPassCode = entity.passcode;
    }
    if (entity.passcodeExt != null && entity.passcodeExt!.isNotEmpty) {
      mPassCodeExt = "-${entity.passcodeExt}";
    }

    // prepareTime will be present when ready 完成备餐 clicked
    var orderStatus = "";
    if (OrderStatusManager.cancelOrder(entity)) {
      orderStatus = S.of(context).order_cancel;
    } else if (OrderStatusManager.isNewOrderWithoutPreOrder(entity)) {
      orderStatus = S.of(context).wait_confirm_receiving;
    } else if (OrderStatusManager.matchCourierToRestaurant(entity)) {
      orderStatus = S.of(context).wait_courier_come;
    } else if (OrderStatusManager.courierAtPickup(entity)) {
      orderStatus = S.of(context).courier_at_pickup;
    } else if (OrderStatusManager.courierPickupCompleted(entity)) {
      orderStatus = S.of(context).courier_pickup_completed;
    } else if (OrderStatusManager.isCourierAtDropOff(entity)) {
      orderStatus = S.of(context).courier_at_dropoff;
    } else if (OrderStatusManager.isCourierDeliveryCompleted(entity)) {
      orderStatus = S.of(context).courier_completed;
    } else if (OrderStatusManager.restaurantConfirmedButNotChucan(entity)) {
      orderStatus = S.of(context).wait_chucan_confirmed;
    } else if (OrderStatusManager.preOrder(entity)) {
      orderStatus = S.of(context).pre_order;
    }

    String deliveryTime = "";
    String deliveryStr = "";
    if (OrderStatusManager.customerPickupSelf(entity)) {
      deliveryStr = S.of(context).pickup_by_myself;
    } else if (OrderStatusManager.restaurantDeliverySelf(entity)) {
      deliveryStr = S.of(context).restaurant_delivery;
    } else if ((orderStatus == S.of(context).wait_confirm_receiving ||
            orderStatus == S.of(context).wait_chucan_confirmed ||
            orderStatus == S.of(context).wait_courier_come ||
            orderStatus == S.of(context).courier_at_pickup ||
            orderStatus == S.of(context).courier_pickup_completed ||
            orderStatus == S.of(context).courier_at_dropoff ||
            orderStatus == S.of(context).order_cancel) &&
        !OrderStatusManager.customerPickupSelf(entity)) {
      if (entity.delivery != null && entity.delivery!.finishAt != null) {
        deliveryTime = formatDate(
            DateUtil.getDateTimeWitTimeZone(
                entity.delivery!.finishAt!, entity.restaurant?.timezone)!,
            [HH, ':', nn, ':', ss]);
        deliveryStr = sprintf(S.of(context).immediately_delivery,
            [deliveryTime.split(".")[0]]).toString();
      } else if (entity.delivery != null &&
          entity.delivery!.estimate != null &&
          entity.delivery!.estimate!.max != null) {
        if (entity.createdAt != null) {
          deliveryTime = formatDate(
              DateUtil.getDateTimeWitTimeZone(
                      entity.createdAt!, entity.restaurant?.timezone)!
                  .add(Duration(
                      minutes: entity.delivery!.estimate!.max!.toInt())),
              [HH, ':', nn, ':', ss]);
          deliveryStr = sprintf(S.of(context).immediately_delivery,
              [deliveryTime.split(".")[0]]).toString();
        }
      }
    } else if (orderStatus == S.of(context).courier_completed &&
        entity.confirmedAt != null &&
        entity.delivery != null) {
      deliveryTime = DateUtil.getDateTimeWitTimeZone(
              entity.confirmedAt!, entity.restaurant?.timezone)!
          .add(Duration(minutes: entity.delivery!.time!.toInt()))
          .toString();
      deliveryStr = sprintf(
              S.of(context).delivery_completed, [deliveryTime.split(".")[0]])
          .toString();
    } else if (entity.restaurant != null &&
        entity.restaurant!.delivery != null &&
        entity.restaurant!.delivery!.window != null) {
      deliveryTime = formatDate(
          DateUtil.getDateTimeWitTimeZone(
              entity.restaurant!.delivery!.window!.start!,
              entity.restaurant?.timezone)!,
          [HH, ':', nn, ':', ss]);
      deliveryStr =
          sprintf(S.of(context).pre_order_time, [deliveryTime.split(".")[0]])
              .toString();
    }

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text.rich(TextSpan(children: [
              TextSpan(
                text: (entity.count != null) ? "${entity.count}#" : "",
                style: MyStyles.m13.copyWith(color: MyColors.color333),
              ),
              TextSpan(
                text: mPassCode! + mPassCodeExt,
                style: MyStyles.m13.copyWith(color: MyColors.color333),
              ),
            ])),
            Text(
              orderStatus,
              style: MyStyles.m13.copyWith(color: MyColors.color333),
            ),
          ],
        ),
        deliveryStr.isEmpty
            ? EmptyContainer()
            : Column(
                children: [
                  SizedBox(
                    height: Dimens.dp_5,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          deliveryStr,
                          style:
                              MyStyles.m13.copyWith(color: MyColors.color333),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
      ],
    );
  }
}
