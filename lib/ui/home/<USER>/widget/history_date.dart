import 'package:connect/common/MyStyles.dart';
import 'package:connect/data/history_total_report_entity.dart';
import 'package:connect/data/repository/history_repository.dart';
import 'package:connect/data/repository/rest_repo.dart';
import 'package:connect/gen/assets.gen.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/format_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/roles_manager.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';

import '../../../../common/constants.dart';

typedef Date<T, S> = Function(String from, String to);

class HistoryDate extends StatefulWidget {
  HistoryDate(this.selectedDate, {Key? key}) : super(key: key);

  final Date selectedDate;

  @override
  _HistoryDateState createState() => _HistoryDateState();
}

class _HistoryDateState extends State<HistoryDate> {
  List<HistoryTotalReportEntity> _items = [];
  num number = 0;
  num totalMoney = 0;
  String dateText = "";
  String symbolWithMoney = "";

  @override
  void initState() {
    super.initState();
    dateText = DateUtil.formatDate(DateTime.now(),
        format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
    RestaurantRepo.restaurantDetails().then((value) => setState(() {
      symbolWithMoney = ServerMultiLan.coinSymbolCountry(value?.country, totalMoney / 100);
    }));
    WidgetsBinding.instance!.addPostFrameCallback((_) {
      if (mounted) {
        if (RolesManager.isRestaurantManagerRole()) {
          HistoryRepository.historyReport().then((value) {
            LogUtil.v("number::success");
            if (value != null) {
              _items = JsonConvert.fromJsonAsT<List<HistoryTotalReportEntity>>(
                  value.data) ?? [];
              setState(() {
                _items.forEach((element) {
                  if (element.number != null) {
                    number += element.number!;
                  }
                  if (element.distribution != null) {
                    totalMoney += (element.distribution!.restaurant!);
                  }
                  // LogUtil.v("number::$number");
                });
              });
            }
          });
        }
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    LogUtil.v("HistoryDate::dispose");
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("HistoryDate::build");
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 6, 20, 6),
      color: MyColors.W,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          RolesManager.isRestaurantManagerRole()
              ? _buildProfits()
              : EmptyContainer(),
          _buildDatePickerButton(),
        ],
      ),
    );
  }

  Widget _buildProfits() {
    return Expanded(
      child: Row(
        children: [
          Assets.images.order.crown.image(width: 20, height: 20),
          SizedBox(width: 2),
          Expanded(
            child: Text(
              sprintf(S.of(context).pre_total,
                  ["${number.toInt()}",
                    "$symbolWithMoney"]),
              maxLines: 10,
              softWrap: true,
              style: MyStyles.m13.copyWith(color: MyColors.color666),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatePickerButton() {
    return InkWell(
      onTap: () {
        var date = DateTime.now();
        showDatePicker(
          context: context,
          initialDate: date,
          firstDate: DateTime.parse("1970-01-01"),
          lastDate: date,
        ).then((value) {
          if (value != null) {
            setState(() {
              dateText = DateUtil.formatDate(value,
                  format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
            });
            var fromFormat = formatDate(
                value, [yyyy, '-', mm, '-', dd, 'T', HH, ':', nn, ':', ss]);
            var toFormat = formatDate(value,
                [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']);
            var formatTimeZone = FormatUtils.format(value);
            String from = fromFormat + formatTimeZone;
            String to = toFormat + formatTimeZone;
            widget.selectedDate(from, to);
            LogUtil.v("formatDate::$from,to::$to");
          }
        });
      },
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.0),
              color: MyColors.bg,
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(8, 5, 8, 5),
              child: Text(
                dateText,
                style: MyStyles.r12.copyWith(color: MyColors.color666),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
