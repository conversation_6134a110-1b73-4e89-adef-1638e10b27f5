import 'package:connect/common/global.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/provider/order_sold_out_model.dart';
import 'package:connect/provider/orders_details_model.dart';
import 'package:connect/provider/orders_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/button_style_manager.dart';
import 'package:connect/utils/format_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import '../order_status_manager.dart';
import 'order_card_cancel_sheet.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'dart:convert';
import 'order_card_sold_out_dialog.dart';

class OrderCardCancel extends StatefulWidget {
  OrderCardCancel(this.entity, {Key? key}) : super(key: key);

  final OrdersEntity entity;

  @override
  _OrderCardCancelState createState() => _OrderCardCancelState();
}

class _OrderCardCancelState extends State<OrderCardCancel> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _buildCancel(widget.entity),
      ],
    );
  }

  Widget _buildCancel(OrdersEntity entity) {
    //监听饭店已经确认接单
    context.watch<OrdersViewModel>();

    String? language = "";
    bool isNewOrder = false;
    if (entity != null) {
      isNewOrder = OrderStatusManager.isNewOrder(entity);
    }

    if (entity != null && entity.customer != null) {
      language = entity.customer!.language;
    }

    return isNewOrder
        ? Container(
            alignment: Alignment.center,
            child: ElevatedButton(
              style: ButtonStyleManager.buttonStyleAll(),
              child: Text(
                S.of(context).order_cancel_action,
                style: TextStyle(fontSize: Dimens.sp_14, color: Colors.white),
              ),
              onPressed: () async {
                OrderCardCancelSheet.show(context, callback: (key) async {
                  if ("message_items_sold_out" == key) {
                    //重置数据集合
                    context.read<OrderSoldOutModel>().resetSoldOutBean();
                    //菜品售罄弹框
                    showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return OrderCardSoldOutDialog(entity,
                              (message) async {
                            LoadingUtils.show();
                            var dateTime = DateTime.now();
                            var fromFormat = formatDate(dateTime, [
                              yyyy,
                              '-',
                              mm,
                              '-',
                              dd,
                              'T',
                              HH,
                              ':',
                              nn,
                              ':',
                              ss
                            ]);
                            var toFormat = formatDate(dateTime, [
                              yyyy,
                              '-',
                              mm,
                              '-',
                              dd,
                              'T',
                              '23',
                              ':',
                              '59',
                              ':',
                              '59'
                            ]);
                            var formatTimeZone = FormatUtils.format(dateTime);
                            String from = fromFormat + formatTimeZone;
                            String to = toFormat + formatTimeZone;
                            String note = await paramsGenerate(key, language);

                            //下架food
                            bool offShelf = true;
                            var food = this
                                .context
                                .read<OrderSoldOutModel>()
                                .foodBeans;
                            if (food != null && food.length > 0) {
                              var foodIds = food.keys.toList();
                              await Future.forEach(foodIds,
                                  (dynamic foodId) async {
                                LogUtil.v("foodIds::$foodIds");
                                await this
                                    .context
                                    .read<OrderSoldOutModel>()
                                    .requestFoodSelfOff(foodId, from, to, note,
                                        loading: false, error: (e) {
                                  offShelf = false;
                                  ToastUtils.show(S
                                      .of(this.context)
                                      .rest_sold_out_update_failed);
                                });
                              });
                            }

                            //下架options
                            if (offShelf) {
                              var options = this
                                  .context
                                  .read<OrderSoldOutModel>()
                                  .optionsBeans;
                              if (options != null && options.length > 0) {
                                var listBeans = options.values.toList();
                                await Future.forEach(listBeans,
                                    (dynamic list) async {
                                  await Future.forEach(list,
                                      (dynamic bean) async {
                                    LogUtil.v(
                                        "foodIds::${bean.foodId},options::${bean.optionId}");
                                    await this
                                        .context
                                        .read<OrderSoldOutModel>()
                                        .requestSelfOff(
                                            bean.foodId,
                                            bean.optionId,
                                            from,
                                            to,
                                            note, error: (e) {
                                      offShelf = false;
                                      ToastUtils.show(S
                                          .of(this.context)
                                          .rest_sold_out_update_failed);
                                    });
                                  });
                                });
                              }
                            }

                            if (offShelf)
                              ToastUtils.show(S
                                  .of(this.context)
                                  .rest_sold_out_update_successful);

                            if (offShelf) {
                              //取消订单
                              await cancelOrder(
                                  entity,
                                  sprintf(
                                      S.of(this.context).rest_sold_out_message,
                                      [message]),
                                  true);

                              var params = Map<String, dynamic>();
                              params["sold_out"] = "$message";
                              TrackingUtils.instance!
                                  .tracking(key, value: params);
                            }

                            LoadingUtils.dismiss();
                          });
                        });
                  } else {
                    TrackingUtils.instance!.tracking(key);

                    var message = await paramsGenerate(key, language);
                    LogUtil.v("message::$message language::$language");
                    cancelOrder(entity, message, false);
                  }
                });
              },
            ),
          )
        : EmptyContainer();
  }

  cancelOrder(OrdersEntity entity, String message, bool isSoldOut) async {
    var response = await context.read<OrdersDetailsModel>().orderCancel(
        entity.orderId, message,
        loading: isSoldOut ? false : true);
    if (response != null) {
      ToastUtils.show("Order #${entity.passcode} has got cancelled");
      //refresh order list
      GlobalConfig.eventBus.fire(ResRefreshOrders());
    }

    //设置详情页返回键退回到订单列表页后，进行刷新标识
    // context.read<OrderSoldOutModel>().refreshFlag = true;
  }

  Future<String> paramsGenerate(String key, String? language) async {
    var tagsStr;
    if ("zh-CN" == language) {
      tagsStr =
          await rootBundle.loadString('assets/config/cancel_order_zh.json');
    } else if ("zh-HK" == language) {
      tagsStr =
          await rootBundle.loadString('assets/config/cancel_order_hk.json');
    } else {
      tagsStr =
          await rootBundle.loadString('assets/config/cancel_order_en.json');
    }
    var tagsJson = jsonDecode(tagsStr);

    return tagsJson[key];
  }
}
