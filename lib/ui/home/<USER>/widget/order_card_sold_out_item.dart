import 'package:connect/data/orders_entity.dart';
import 'package:connect/provider/order_sold_out_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';
import 'order_card_sold_out_option_item.dart';
import 'package:provider/provider.dart';

class OrderCardSoldOutItem extends StatefulWidget {
  OrderCardSoldOutItem(this.item, {Key? key}) : super(key: key);

  final OrdersItem? item;

  @override
  _OrderCardSoldOutItemState createState() => _OrderCardSoldOutItemState();
}

class _OrderCardSoldOutItemState extends State<OrderCardSoldOutItem> {
  bool isChecked = false;
  bool hasOptions = false;

  @override
  Widget build(BuildContext context) {
    var item = widget.item!;
    if (item.options != null && item.options!.length > 0) {
      hasOptions = true;
    } else {
      hasOptions = false;
    }
    var foodId = widget.item!.foodId;
    var foodName = ServerMultiLan.multiAdapt2(widget.item?.name);
    return Column(
      children: [
        InkWell(
          onTap: () {},
          child: Container(
            padding: const EdgeInsets.symmetric(
                horizontal: Dimens.dp_20, vertical: Dimens.dp_5),
            color: Colors.grey[200],
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(foodName,
                    style: TextStyle(
                      fontSize: Dimens.sp_15,
                    )),
                Checkbox(
                    value: isChecked,
                    activeColor: Colors.blue,
                    onChanged: (checked) {
                      var model = context.read<OrderSoldOutModel>();
                      if (checked!) {
                        model.setFoodBean(foodId, foodName);
                      } else {
                        model.removeFoodBean(foodId);
                      }
                      setState(() {
                        isChecked = checked;
                      });
                    }),
              ],
            ),
          ),
        ),
        hasOptions
            ? ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return OrderCardSoldOutOptionItem(
                      item.options![index], foodId, isChecked);
                  ;
                },
                itemCount: item.options!.length,
              )
            : EmptyContainer()
      ],
    );
  }
}
