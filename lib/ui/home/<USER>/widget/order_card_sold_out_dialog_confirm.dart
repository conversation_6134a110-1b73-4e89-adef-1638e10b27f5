import 'package:connect/data/rest_sold_out_bean.dart';
import 'package:connect/driver/widget/driver_dialog.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/order_sold_out_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class OrderCardSoldOutDialogConfirm extends StatefulWidget {
  OrderCardSoldOutDialogConfirm(this.valueCall, {Key? key}) : super(key: key);

  final ValueChanged<String> valueCall;

  @override
  _OrderCardSoldOutDialogConfirmState createState() =>
      _OrderCardSoldOutDialogConfirmState();
}

class _OrderCardSoldOutDialogConfirmState
    extends State<OrderCardSoldOutDialogConfirm> {
  @override
  Widget build(BuildContext context) {
    bool isClickable = false;

    var watch = context.watch<OrderSoldOutModel>();

    if (watch.foodBeans.length > 0) {
      isClickable = true;
    }

    watch.optionsBeans.forEach((key, value) {
      if (value != null && value.length > 0) {
        isClickable = true;
      }
    });

    return InkWell(
      onTap: isClickable
          ? () {
              String message = "";
              var allFood = context.read<OrderSoldOutModel>().allFood;
              var food = context.read<OrderSoldOutModel>().foodBeans;
              var options = context.read<OrderSoldOutModel>().optionsBeans;

              if (food.isNotEmpty && options.isEmpty) {
                food.forEach((key, value) {
                  message += "${value.foodName},";
                });
                message = message.substring(0, message.length - 1);
              } else if (food.isEmpty && options.isNotEmpty) {
                allFood.forEach((key, value) {
                  options.forEach((oKey, oValue) {
                    if (key == oKey) {
                      String optionsStr = "";
                      oValue.forEach((element) {
                        optionsStr += "${element.optionName},";
                      });
                      message +=
                          "${allFood[key]}[${optionsStr.substring(0, optionsStr.length - 1)}],";
                    }
                  });
                });
                message = message.substring(0, message.length - 1);
              } else if (food.isNotEmpty && options.isNotEmpty) {
                allFood.forEach((key, value) {
                  options.forEach((oKey, oValue) {
                    if (key == oKey) {
                      String optionsStr = "";
                      oValue.forEach((element) {
                        optionsStr += "${element.optionName},";
                      });
                      message +=
                          "${allFood[key]}[${optionsStr.substring(0, optionsStr.length - 1)}],";
                    }
                  });
                });

                //过滤出没有options的菜品
                Map<String?, RestSoldOutBean> foodTemp = {};
                foodTemp.addAll(food);

                options.forEach((oKey, oValue) {
                  foodTemp.removeWhere((key, value) => key == oKey);
                });

                foodTemp.forEach((key, value) {
                  message += "${value.foodName},";
                });

                message = message.substring(0, message.length - 1);
              }

              LogUtil.v("message::$message");

              DriverDialog.show(
                  context, "${S.of(context).rest_sold_out_confirm}\n$message",
                  () {
                widget.valueCall(message);
              });
            }
          : null,
      child: Text(
        S.of(context).confirm,
        style: TextStyle(
            color: isClickable ? Colors.blue : Colors.blue[100],
            fontSize: Dimens.sp_15),
      ),
    );
  }
}
