import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/material.dart';

class OrderCardCancelSheet {
  static show(BuildContext context, {ValueChanged<String>? callback}) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context, callback);
        },
        context: context);
  }

  static Widget buildBottomSheetWidget(BuildContext context, callback) {
    return Container(
      height: Dimens.dp_370,
      child: Column(
        children: [
          buildItem(context, S.of(context).order_cancel_reason),
          Divider(
            height: Dimens.dp_1,
          ),
          Expanded(
            // Order #1052 has got cancelled
            child: ListView(
              children: [
                buildItem(context, S.of(context).order_cancel_too_far,
                    onTap: () async {
                  if (callback != null) callback("message_too_far");
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context, S.of(context).order_cancel_customer_request,
                    onTap: () async {
                  if (callback != null) callback("message_customer_request");
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(
                    context, S.of(context).order_cancel_cannot_reach_restaurant,
                    onTap: () async {
                  if (callback != null)
                    callback("message_cannot_reach_restaurant");
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context, S.of(context).order_cancel_duplicate_order,
                    onTap: () async {
                  if (callback != null) callback("message_duplicate_order");
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context, S.of(context).order_cancel_items_sold_out,
                    onTap: () async {
                  if (callback != null) callback("message_items_sold_out");
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(
                    context, S.of(context).order_cancel_kitchen_closed_early,
                    onTap: () async {
                  if (callback != null)
                    callback("message_kitchen_closed_early");
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(
                    context, S.of(context).order_cancel_restaurant_closed_today,
                    onTap: () async {
                  if (callback != null)
                    callback("message_restaurant_closed_today");
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context, S.of(context).cancel),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        Navigator.of(context).pop();
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_40,
        child: Text(
          title,
          style: TextStyle(
              color: S.of(context).order_cancel_reason == title
                  ? Colors.black87
                  : Colors.blue,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
