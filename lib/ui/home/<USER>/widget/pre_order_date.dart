import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/widget/pre_order/pre_date_picker.dart';
import 'package:connect/ui/view/device_utils.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/format_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';

typedef Date<T, S> = Function(String from, String to);

class PreOrderDate extends StatefulWidget {
  PreOrderDate(this.selectedDate, {Key? key}) : super(key: key);

  final Date selectedDate;

  @override
  _PreOrderDateState createState() => _PreOrderDateState();
}

class _PreOrderDateState extends State<PreOrderDate> {
  bool isCan = true;

  @override
  Widget build(BuildContext context) {
    double width = (MediaQuery.of(context).size.width - 6) / 7;
    Orientation orientation = MediaQuery.of(context).orientation;
    if (orientation == Orientation.landscape && DeviceUtils.isTablet()) {
      width = (MediaQuery.of(context).size.width * 2 / 5 - 6) / 7;
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        PreDatePicker(
          DateTime.now(),
          locale: ConnectCache.getLocale().toLanguageTag(),
          height: 88,
          width: width,
          initialSelectedDate: DateTime.now(),
          selectionColor: MyColors.portal,
          selectedTextColor: MyColors.W,
          dateTextStyle: MyStyles.m16,
          dayTextStyle: MyStyles.r12,
          daysCount: 7,
          onDateChange: (date) {
            Orientation orientation = MediaQuery.of(context).orientation;
            if (orientation == Orientation.landscape) {
              GlobalConfig.eventBus.fire(ResSyncOrderStatus(null));
            }
            var today =
                DateUtil.isToday(DateUtil.getDateMsByTimeStr(date.toString()));
            var fromFormat;
            if (today) {
              fromFormat = formatDate(DateTime.now(),
                  [yyyy, '-', mm, '-', dd, 'T', HH, ':', nn, ':', ss]);
            } else {
              fromFormat = formatDate(
                  date, [yyyy, '-', mm, '-', dd, 'T', HH, ':', nn, ':', ss]);
            }
            var toFormat = formatDate(date,
                [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']);
            var formatTimeZone = FormatUtils.format(date);
            String from = fromFormat + formatTimeZone;
            String to = toFormat + formatTimeZone;
            widget.selectedDate(from, to);
            LogUtil.v("formatDate::$from,to::$to");
          },
        ),
      ],
    );
  }
}
