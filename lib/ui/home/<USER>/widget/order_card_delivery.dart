import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/flutter_device_type.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import 'package:url_launcher/url_launcher.dart';

import 'order_card_details.dart';

class OrderCardDelivery extends StatefulWidget {
  OrderCardDelivery(
    this.entity, {
    Key? key,
    this.from,
  }) : super(key: key);

  final OrdersEntity? entity;
  final OrderCardFrom? from;

  @override
  _OrderCardDeliveryState createState() => _OrderCardDeliveryState();
}

class _OrderCardDeliveryState extends State<OrderCardDelivery> {
  @override
  Widget build(BuildContext context) {
    OrdersEntity entity = widget.entity!;
    bool showDelivery = entity.shouldShowDelivery();
    return showDelivery ? _buildDelivery(widget.entity!) : EmptyContainer();
  }

  Widget _buildDelivery(OrdersEntity entity) {
    String courier = "";
    if (entity.delivery != null &&
        entity.delivery!.courier != null &&
        entity.delivery!.courier!.email != null) {
      courier = entity.delivery!.courier!.email!.split("@")[0];
      if (courier.length > 3) {
        courier = courier.substring(0, 3).toUpperCase();
      } else {
        courier = courier.toUpperCase();
      }
    }

    String? deliveryStr = "";
    //scheduled,en-route-to-pickup,at-pickup all of them contains courier matched
    if (entity.delivery != null &&
        (entity.delivery!.status == Constants.SCHEDULED ||
            entity.delivery!.status == Constants.EN_ROUTE_TO_PICKUP ||
            entity.delivery!.status == Constants.AT_PICKUP)) {
      deliveryStr = S.of(context).match_courier;
    } else if (entity.delivery != null &&
        (entity.delivery!.status == Constants.PICKUP_COMPLETED ||
            entity.delivery!.status == Constants.EN_ROUTE_TO_DROPOFF ||
            entity.delivery!.status == Constants.AT_DROPOFF ||
            entity.delivery!.status == Constants.COMPLETED)) {
      //pickup completed
      if (entity.delivery!.stats != null) {
        num? pickupCompleted = entity.delivery!.stats!.pickupCompleted;
        if (pickupCompleted != null) {
          var dateTime = DateUtil.getDateTimeWitTimeZone(
              entity.confirmedAt!, entity.restaurant?.timezone)!;
          String pickupTime = formatDate(
              dateTime.add(Duration(minutes: pickupCompleted as int)),
              [HH, ':', nn]);
          deliveryStr = sprintf(S.of(context).courier_picked_up, [pickupTime]);
        }
      }
    }

    return Column(
      children: [
        widget.from == OrderCardFrom.portrait ||
                widget.from == OrderCardFrom.details
            ? EmptyContainer()
            : Divider(
                height: Dimens.dp_15,
              ),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(deliveryStr,
                      style: TextStyle(
                          fontSize: Dimens.sp_16,
                          color: Colors.black87,
                          fontWeight: FontWeight.bold)),
                  SizedBox(
                    height: Dimens.dp_5,
                  ),
                  courier.isEmpty
                      ? Container(
                          width: Dimens.dp_0,
                          height: Dimens.dp_0,
                        )
                      : Text(courier,
                          style: TextStyle(
                              fontSize: Dimens.sp_14, color: Colors.black87)),
                ],
              ),
            ),
            Column(
              children: [
                InkWell(
                  onTap: () {
                    String? phone = "";
                    if (entity.delivery != null &&
                        entity.delivery!.courier != null) {
                      phone = entity.delivery!.courier!.phone;
                      if (phone != null && phone.isNotEmpty) {
                        if (GlobalConfig.isSunmi || Device.get().isTablet!) {
                          MessageDialog.messageAlert(
                              "${S.of(context).sunmi_not_support_calls}\n\n$phone");
                        } else {
                          launch("tel:$phone");
                        }
                      } else {
                        ToastUtils.show("Phone exception");
                      }
                    } else {
                      ToastUtils.show("Unknown exception");
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(Dimens.dp_5),
                    decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(Dimens.dp_20)),
                    child: Icon(
                      Icons.phone,
                      color: Colors.white,
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
      ],
    );
  }
}
