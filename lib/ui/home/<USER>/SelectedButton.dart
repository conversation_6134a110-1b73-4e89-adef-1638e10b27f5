import 'package:flutter/material.dart';

import '../../../common/MyStyles.dart';

class SelectedButton extends StatefulWidget {
  const SelectedButton({
    Key? key,
    this.onPressed,
    this.selectedColor = MyColors.portal,
    this.selectedBgColor = MyColors.FFF1F1,
    this.normalColor = MyColors.color666,
    this.normalBgColor = MyColors.f4,
    this.isSelected = false,
    required this.title,
  }) : super(key: key);

  final VoidCallback? onPressed;
  final Color selectedColor;
  final Color selectedBgColor;
  final Color normalColor;
  final Color normalBgColor;
  final bool isSelected;
  final String title;

  @override
  _SelectedButtonState createState() => _SelectedButtonState();
}

class _SelectedButtonState extends State<SelectedButton> {
  @override
  Widget build(BuildContext context) {
    var borderColor =
        widget.isSelected ? widget.selectedColor : Colors.transparent;
    var textColor =
        widget.isSelected ? widget.selectedColor : widget.normalColor;
    var bgColor =
        widget.isSelected ? widget.selectedBgColor : widget.normalBgColor;
    return Expanded(
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          elevation: 0.0,
          shadowColor: Colors.transparent,
          backgroundColor: bgColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          side: BorderSide(color: borderColor),
        ),
        child: Text(
          widget.title,
          style: MyStyles.r15.copyWith(
            color: textColor,
          ),
        ),
        onPressed: widget.onPressed,
      ),
    );
  }
}
