import 'package:connect/common/global.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/order_status_provider.dart';
import 'package:connect/provider/orders_details_model.dart';
import 'package:connect/ui/home/<USER>/widget/order_card_details.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/gif_header.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:provider/provider.dart';

class OrdersDetailsSplitScreenRoute extends StatefulWidget {
  static String tag = "orders_details_split_route";

  OrdersDetailsSplitScreenRoute({Key? key}) : super(key: key);

  @override
  _OrdersDetailsSplitScreenRouteState createState() =>
      _OrdersDetailsSplitScreenRouteState();
}

class _OrdersDetailsSplitScreenRouteState
    extends State<OrdersDetailsSplitScreenRoute> {
  late RefreshController _refreshController;
  OrdersEntity? entity;
  OrdersEntity? data;

  void _onRefresh({bool loading = false}) {
    LogUtil.v("---_onRefresh---");
    if (entity != null) {
      LogUtil.v("---_onRefresh2---${entity!.orderId}");
      // SmartDialog.showLoading(msg: "loading", background: Colors.black54);
      if (loading) {
        LoadingUtils.show();
      }
      OrdersDetailsModel.orderDetails(entity!.orderId)
          .then((value) {
            setState(() {
              data = JsonConvert.fromJsonAsT<OrdersEntity>(value!.data);
            });
          })
          .catchError((e) {})
          .whenComplete(() {
            if (loading) {
              LoadingUtils.dismiss();
            } else {
              _refreshController.refreshCompleted();
            }
          });
    } else {
      if (loading) {
        LoadingUtils.dismiss();
      } else {
        _refreshController.refreshCompleted();
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: false);
    GlobalConfig.eventBus.on<ResNotifyOrderDetails>().listen((event) {
      LogUtil.v("ResNotifyOrderDetails");
      if (mounted) {
        setState(() {
          data = null;
        });
        entity = event.responseItem;
        _onRefresh(loading: true);
      }
    });
    GlobalConfig.eventBus.on<ResSyncOrderStatus>().listen((event) {
      LogUtil.v("ResSyncOrderStatus receive mounted $mounted");
      if (mounted) {
        setState(() {
          entity = event.responseItem;
          data = event.responseItem;
        });
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    entity = null;
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("OrdersDetailsSplitScreenRoute::build");
    // entity = context.watch<OrdersDetailsRefreshProvider>().entity;
    context.watch<OrderStatusProvider>().isConfirmedOrder;
    return Scaffold(
      backgroundColor: Colors.grey[200],
      appBar: null,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: SmartRefresher(
              controller: _refreshController,
              physics: AlwaysScrollableScrollPhysics(),
              header: GifHeader(),
              onRefresh: _onRefresh,
              child: data == null
                  ? EmptyView(S.of(context).no_data)
                  : ListView.builder(
                      itemBuilder: (context, index) {
                        return OrderCardDetails(data, OrderCardFrom.details);
                      },
                      itemCount: 1,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
