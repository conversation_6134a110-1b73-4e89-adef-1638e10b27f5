import 'package:connect/common/t_constants.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/switch_button.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/print_manager.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';

class CopyPrint extends StatefulWidget {
  CopyPrint({Key? key}) : super(key: key);

  @override
  _CopyPrintState createState() => _CopyPrintState();
}

class _CopyPrintState extends State<CopyPrint> {
  bool isSunmi = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance!.addPostFrameCallback((timeStamp) {
      PrintManager.instance.isSunmi().then((value) {
        setState(() {
          isSunmi = value;
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return isSunmi
        ? Column(
            children: [
              Container(
                  height: Dimens.dp_45,
                  color: Colors.white,
                  padding: const EdgeInsets.only(
                      left: Dimens.dp_20, right: Dimens.dp_20),
                  child: Row(
                    children: [
                      Image(
                          width: 16,
                          height: 16,
                          image: AssetImage("assets/images/more/print.png")),
                      Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Text(
                            S.of(context).copy_print,
                            style: TextStyle(color: Colors.black87),
                          ),
                        ),
                      ),
                      SwitchButton((b) {
                        ConnectCache.saveDoublePrint(b);

                        var params = Map<String, dynamic>();
                        params["double_print"] = "$b";
                        TrackingUtils.instance!.tracking(
                            TConstants.R_MORE_DOUBLE_PRINT_CLICK,
                            value: params);
                      }, ConnectCache.getDoublePrint() == true)
                    ],
                  )),
            ],
          )
        : EmptyContainer();
  }
}
