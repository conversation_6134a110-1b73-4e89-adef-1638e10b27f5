import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/date/report_date.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tz_date.dart';
import 'package:flutter/material.dart';

typedef Date<T, S> = Function(String from, String to);

class ReportDateGroup extends StatefulWidget {
  ReportDateGroup(this.dateSelected, this.typeCall, {Key? key})
      : super(key: key);

  final Date dateSelected;
  final ValueChanged<String?> typeCall;

  @override
  _ReportDateGroupState createState() => _ReportDateGroupState();
}

class _ReportDateGroupState extends State<ReportDateGroup> {
  var selectedValue;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    selectedValue = S.of(context).report_summary;
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("ReportDate::build");
    return Container(
      color: Colors.grey[300],
      child: Row(
        children: [
          Expanded(
            child: ReportDate(
              (from, to, timezone) {
                if (from.isEmpty) {
                  from = TzDate.tzFrom(timezone!);
                }
                if (to.isEmpty) {
                  to = TzDate.tzTo(timezone!);
                }
                LogUtil.v(
                    "ReportDate::from::$from,to::$to,timezone::$timezone");
                widget.dateSelected(from, to);
              },
            ),
          ),
          _buildType(),
        ],
      ),
    );
  }

  Widget _buildType() {
    return Row(
      children: [
        DropdownButtonHideUnderline(
          child: DropdownButton(
            items: generateItemList(),
            value: selectedValue,
            onChanged: (dynamic T) {
              setState(() {
                selectedValue = T;
                LogUtil.v("selectedValue::$selectedValue");
                widget.typeCall(selectedValue);
              });
            },
            elevation: 24,
            style: TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.bold,
                fontSize: Dimens.sp_13),
            isDense: false,
            icon: Icon(Icons.arrow_drop_down),
            iconSize: 30.0,
          ),
        )
      ],
    );
  }

  List<DropdownMenuItem> generateItemList() {
    List<DropdownMenuItem> items = [];
    DropdownMenuItem regular = DropdownMenuItem(
      child: Text(S.of(context).report_summary),
      value: S.of(context).report_summary,
    );
    DropdownMenuItem food = DropdownMenuItem(
      child: Text(S.of(context).report_food),
      value: S.of(context).report_food,
    );
    items.add(regular);
    items.add(food);
    return items;
  }
}
