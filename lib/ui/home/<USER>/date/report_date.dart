import 'package:connect/data/repository/restaurant_details_entity.dart';
import 'package:connect/provider/report_model.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/log_util.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import '../../../../common/constants.dart';
import '../../../../generated/l10n.dart';
import '../../../../res/dimens.dart';
import '../../../../utils/format_utils.dart';
import 'package:provider/provider.dart';
import 'package:timezone/timezone.dart' as tz;

typedef Date<T, S, M> = Function(String from, String to, String? timezone);

class ReportDate extends StatefulWidget {
  ReportDate(this.selectedDate, {Key? key}) : super(key: key);

  final Date selectedDate;

  @override
  _ReportDateState createState() => _ReportDateState();
}

class _ReportDateState extends State<ReportDate> {
  String dateTextFromFormat = "";
  String dateTextToFormat = "";
  String dateTextFrom = "";
  String dateTextTo = "";
  RestaurantDetailsEntity? restaurant;
  var detroit;
  var from = "";
  var to = "";
  var tzNow;
  bool isFromClick = false;
  bool isToClick = false;

  _makeDateText(BuildContext context) {
    if (restaurant != null) {
      // dateText = DateUtil.formatDate(tz.TZDateTime.now(detroit), format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
      dateTextFromFormat = sprintf(S.of(context).from, [dateTextFrom]);
      dateTextToFormat = sprintf(S.of(context).to, [dateTextTo]);
    }
  }

  @override
  void initState() {
    super.initState();
    dateTextFromFormat = "";
    dateTextToFormat = "";
    dateTextFrom = "";
    dateTextTo = "";
    detroit = null;
    tzNow = null;
    from = "";
    to = "";
    isFromClick = false;
    isToClick = false;
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("ReportDate::build");
    restaurant = context.watch<ReportModel>().restaurantEntity;
    if (restaurant != null) {
      detroit = tz.getLocation("${restaurant!.timezone}");
      // detroit = tz.getLocation("Asia/Magadan");
      tz.setLocalLocation(detroit);
      tzNow = tz.TZDateTime.now(detroit);
      if (!isFromClick) {
        dateTextFrom = DateUtil.formatDate(tzNow,
            format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
      }
      if (!isToClick) {
        dateTextTo = DateUtil.formatDate(tzNow,
            format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
      }
    }
    _makeDateText(context);
    return Row(
      children: [
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _fromDate(),
            ],
          ),
          flex: 1,
        ),
        SizedBox(
          width: Dimens.dp_1,
          height: Dimens.dp_15,
          child: DecoratedBox(
            decoration: BoxDecoration(color: Colors.grey),
          ),
        ),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _toDate(),
            ],
          ),
          flex: 1,
        ),
      ],
    );
  }

  Widget _fromDate() {
    return InkWell(
      onTap: () {
        if (tzNow == null) {
          return;
        }
        // var tzNow = DateTime.now();
        showDatePicker(
          context: context,
          initialDate: tzNow,
          firstDate: DateTime.parse("1970-01-01"),
          lastDate: tzNow,
        ).then((value) {
          if (value != null) {
            LogUtil.v("value:${value.toString()}");

            setState(() {
              isFromClick = true;
              dateTextFrom = DateUtil.formatDate(value,
                  format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
            });
            var formatTimeZone = FormatUtils.format(tzNow);
            LogUtil.v("timeZoneOffset:${tzNow.timeZoneOffset}");
            from = formatDate(value,
                    [yyyy, '-', mm, '-', dd, 'T', '00', ':', '00', ':', '00']) +
                formatTimeZone;
            widget.selectedDate(from, to, restaurant?.timezone);
          }
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: Dimens.dp_5),
        height: Dimens.dp_40,
        child: Row(
          children: [
            Text(
              dateTextFromFormat,
              style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_13),
            ),
            Icon(
              Icons.date_range,
              color: Colors.black87,
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  Widget _toDate() {
    return InkWell(
      onTap: () {
        // var tzNow = DateTime.now();
        if (tzNow == null) {
          return;
        }
        showDatePicker(
          context: context,
          initialDate: tzNow,
          firstDate: DateTime.parse("1970-01-01"),
          lastDate: tzNow,
        ).then((value) {
          if (value != null) {
            setState(() {
              isToClick = true;
              dateTextTo = DateUtil.formatDate(value,
                  format: DateFormatterConstants.YYYY_SLASH_MM_SLASH_DD);
            });
            var formatTimeZone = FormatUtils.format(tzNow);
            to = formatDate(value,
                    [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
                formatTimeZone;
            widget.selectedDate(from, to, restaurant?.timezone);
          }
        });
      },
      child: Container(
        margin: const EdgeInsets.only(left: Dimens.dp_5),
        height: Dimens.dp_40,
        child: Row(
          children: [
            Text(
              dateTextToFormat,
              style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_13),
            ),
            Icon(Icons.date_range, color: Colors.black87, size: 18),
          ],
        ),
      ),
    );
  }
}
