import 'package:connect/res/dimens.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';

typedef SearchClear = Function();

class SearchRightWidget extends StatefulWidget {
  SearchRightWidget(Key key, {this.searchClear}) : super(key: key);

  final SearchClear? searchClear;

  @override
  State<StatefulWidget> createState() {
    return SearchRightWidgetState();
  }
}

class SearchRightWidgetState extends State<SearchRightWidget>
    with SingleTickerProviderStateMixin {
  bool _refresh = false;
  bool _searchTextEmpty = true;
  late AnimationController controller;

  @override
  void initState() {
    super.initState();
    LogUtil.v("SearchRightWidget::initState");
    controller =
        AnimationController(duration: const Duration(seconds: 1), vsync: this);
    controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        controller.reset();
        controller.forward();
      }
    });
  }

  @override
  void dispose() {
    LogUtil.v("SearchRightWidget::dispose");
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("SearchRightWidget::build");
    if (!_refresh && _searchTextEmpty) {
      //show nothing
      LogUtil.v("SearchRightWidget::show nothing");
      return Container(
        width: 0,
        height: 0,
      );
    } else if (_refresh) {
      //show refresh icon
      LogUtil.v("SearchRightWidget::show _refresh");
      controller.forward();
      return RotationTransition(
        alignment: Alignment.center,
        turns: controller,
        child: Icon(
          Icons.autorenew,
          color: Colors.black54,
          size: Dimens.dp_20,
        ),
      );
    } else {
      //show delete icon
      LogUtil.v("onStatusChange::show delete icon");
      return InkWell(
        onTap: widget.searchClear,
        child: Container(
          height: Dimens.dp_50,
          child: Icon(
            Icons.cancel,
            color: Colors.black54,
            size: Dimens.dp_20,
          ),
        ),
      );
    }
  }

  void onStatusChange(bool isRefresh, String searchText) {
    setState(() {
      _refresh = isRefresh;
      _searchTextEmpty = searchText.isEmpty;
      LogUtil.v("onStatusChange::$_refresh,$_searchTextEmpty");
    });
  }
}
