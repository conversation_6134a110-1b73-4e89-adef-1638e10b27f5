import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/repository/rest_repo.dart';
import 'package:connect/ui/home/<USER>/ondemanddelivery/ondeman_ddelivery_route.dart';
import 'package:connect/ui/home/<USER>/search_cancel.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'orders_details_split_screen_route.dart';
import 'tab/orders_tab.dart';
import 'widget/orders_title.dart';

class OrdersRoute extends StatefulWidget {
  final GlobalKey<SearchRightWidgetState> searchRightKey = GlobalKey();

  @override
  OrdersRouteState createState() => OrdersRouteState();
}

class OrdersRouteState extends State<OrdersRoute>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  UIRestaurant? uiRestaurant;

  @override
  void initState() {
    super.initState();
    TrackingUtils.instance!.tracking(TConstants.R_ORDERS_ENTER);
    RestaurantRepo.restaurantDetails().then((value) => setState(() {
          uiRestaurant = value;
        }));
  }

  @override
  void dispose() {
    super.dispose();
    LogUtil.v("OrdersRoute::dispose");
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("OrdersRoute::build");
    super.build(context);
    bool isLandscape = false;
    Orientation orientation = MediaQuery.of(context).orientation;
    if (orientation == Orientation.landscape) {
      isLandscape = true;
    }

    var button = (uiRestaurant?.enableConnect == true)
        ? FloatingActionButton(
            onPressed: () {
              Navigator.of(context).pushNamed(OnDemandDeliveryRoute.tag,
                  arguments: uiRestaurant);
            },
            child: SvgPicture.asset(
              'assets/images/delivery.svg'
            ),
          )
        : null;
    LogUtil.v("isLandscape::$isLandscape");
    return Scaffold(
      backgroundColor: MyColors.bg,
      floatingActionButton: button,
      body: isLandscape
          ? Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      OrdersTitle(),
                      Expanded(
                        child: OrdersTab(),
                      ),
                    ],
                  ),
                  flex: 2,
                ),
                Expanded(
                  child: OrdersDetailsSplitScreenRoute(),
                  flex: 3,
                ),
              ],
            )
          : Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                OrdersTitle(),
                Expanded(flex: 1, child: OrdersTab()),
              ],
            ),
    );
  }
}
