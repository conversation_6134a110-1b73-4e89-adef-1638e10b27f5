import 'package:connect/common/MyStyles.dart';
import 'package:connect/ui/home/<USER>/daily_orders.dart';
import 'package:flutter/material.dart';
import 'package:connect/generated/l10n.dart' as L10n;

import 'daily_adjustment.dart';
import 'daily_distribution.dart';
import 'daily_header.dart';

class DailyReport extends StatefulWidget {
  static String tag = "daily_report";
  const DailyReport({Key? key}) : super(key: key);

  @override
  _DailyReportState createState() => _DailyReportState();
}

class _DailyReportState extends State<DailyReport>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  List<Tab> tabs = [
    Tab(
      text: L10n.S.current.dailyReport_title_distribution,
    ),
    Tab(
      text: L10n.S.current.dailyReport_title_adjustment,
    ),
    Tab(
      text: L10n.S.current.dailyReport_title_orders,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return DefaultTabController(
        length: tabs.length,
        child: Scaffold(
          appBar: AppBar(
            leading: null,
            automaticallyImplyLeading: false,
            elevation: 0,
            backgroundColor: Colors.white,
            centerTitle: true,
            title: DailyHeader(),
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(40),
              child: Align(
                alignment: Alignment.centerLeft,
                child: TabBar(
                  labelStyle: MyStyles.m12.copyWith(color: MyColors.color666),
                  labelColor: MyColors.portal,
                  indicatorColor: MyColors.portal,
                  unselectedLabelColor: MyColors.color666,
                  tabs: tabs,
                  indicatorSize: TabBarIndicatorSize.label,
                  indicatorWeight: 2,
                ),
              ),
            ),
          ),
          backgroundColor: Colors.grey[200],
          body: TabBarView(
            children: [
              DailyDistribution(),
              DailyAdjustment(),
              DailyOrders(),
            ],
          ),
        ));
  }
}
