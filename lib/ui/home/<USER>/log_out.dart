import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/log_out_dialog.dart';
import 'package:flutter/material.dart';

class LogOut extends StatefulWidget {
  LogOut({Key? key}) : super(key: key);

  @override
  _LogOutState createState() => _LogOutState();
}

class _LogOutState extends State<LogOut> {
  @override
  Widget build(BuildContext context) {
    return _buildLogOut(context);
  }

  Widget _buildLogOut(BuildContext context) {
    return Container(
        margin: const EdgeInsets.fromLTRB(
            Dimens.dp_25, Dimens.dp_0, Dimens.dp_25, Dimens.dp_0),
        alignment: Alignment.center,
        child: SizedBox(
            width: double.infinity,
            height: Dimens.dp_40,
            child: OutlinedButton(
              onPressed: () {
                LogOutDialog.show(context);
              },
              style: ButtonStyle(
                shape: MaterialStateProperty.all(RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0))),
              ),
              child: Text(S.of(context).log_out,
                  style:
                      TextStyle(color: Colors.black38, fontSize: Dimens.sp_16)),
            )));
  }
}
