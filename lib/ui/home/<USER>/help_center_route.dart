import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';
import 'help_driver_handbook.dart';
import 'help_feedback.dart';

class HelpCenterRoute extends StatefulWidget {
  static String tag = "HelpCenterRoute";

  HelpCenterRoute({Key? key}) : super(key: key);

  @override
  _HelpCenterRouteState createState() => _HelpCenterRouteState();
}

class _HelpCenterRouteState extends State<HelpCenterRoute> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: Colors.black,
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          S.of(context).driver_help_center,
          style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_17),
        ),
      ),
      backgroundColor: Colors.white,
      body: _buildCenter(),
    );
  }

  Widget _buildCenter() {
    return Column(
      children: [
        <PERSON><PERSON>B<PERSON>(
          height: Dimens.dp_15,
        ),
        HelpFeedback(),
        HelpDriverHandbook(),
      ],
    );
  }
}
