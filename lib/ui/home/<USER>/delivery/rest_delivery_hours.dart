import 'package:connect/data/rest_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/ui/home/<USER>/widget.dart';
import 'package:flutter/material.dart';

class RestDeliveryHours extends StatefulWidget {
  final List<RestHours?>? hours;

  RestDeliveryHours(this.hours, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _RestDeliveryHoursState(hours: hours);
}

class _RestDeliveryHoursState extends State<RestDeliveryHours> {
  final List<RestHours?>? hours;

  _RestDeliveryHoursState({this.hours});

  String _printDuration(Duration duration) {
    var inMinutes = duration.inMinutes;
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(inMinutes.remainder(60));
    var formatStr = "${twoDigits(duration.inHours)}:$twoDigitMinutes";
    if (inMinutes >= 720) {
      return "$formatStr PM";
    } else {
      return "$formatStr AM";
    }
  }

  String getDuration(RestHours hours) {
    String durationStart = _printDuration(Duration(minutes: hours.start!));
    String durationEnd = _printDuration(Duration(minutes: hours.end!));
    return "$durationStart - $durationEnd";
  }

  final times = <String>["0 AM", "6 AM", "12 AM", "18 PM", "24 PM"];

  final barHeight = 240.0;
  final minOfDay = 1440;

  var today = DateTime.now().weekday;

  final weeks = <String>[
    S.current.connect_rest_note_mon,
    S.current.connect_rest_note_tue,
    S.current.connect_rest_note_wed,
    S.current.connect_rest_note_thu,
    S.current.connect_rest_note_fri,
    S.current.connect_rest_note_sat,
    S.current.connect_rest_note_sun,
  ];

  Widget buildTimeline() {
    return Container(
      height: barHeight,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ...times.map((e) => Text(
                e,
                style: TextStyle(fontSize: 10),
              ))
        ],
      ),
    );
  }

  Widget buildChart() {
    var date = DateTime.now();
    var currentMinute = date.hour * 60 + date.minute;
    var position = (barHeight - 5) * (currentMinute / minOfDay);
    return Expanded(
        flex: 1,
        child: Stack(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ...[1, 2, 3, 4, 5, 6, 7].map((e) => buildChartItem(e))
              ],
            ),
            Container(
              height: 5,
              margin: EdgeInsets.only(left: 16, right: 16, top: position),
              child: Stack(
                alignment: Alignment.centerLeft,
                children: [buildPoint(), Divider(color: Colors.red)],
              ),
            )
          ],
        ));
  }

  List<RestHours?> findHours(int day) {
    var sort = hours
            ?.where((element) => element != null && element.dayOfWeek == day)
            .toList() ??
        List.empty();
    sort.sort((a, b) => a!.start!.compareTo(b!.start!));
    return sort;
  }

  Widget buildChartItem(int day) {
    return Column(
      children: [
        Container(
          width: 16,
          height: barHeight,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                width: 12,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6.0),
                    color: Colors.grey[200]),
              ),
              ...findHours(day).map((e) => segmentation(e))
            ],
          ),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              today = day;
            });
          },
          child: Container(
            margin: EdgeInsets.only(top: 8.0),
            child: today == day
                ? Container(
                    decoration: BoxDecoration(
                        color: RColors.mr,
                        borderRadius: BorderRadius.all(Radius.circular(5))),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 3.0, vertical: 2.0),
                      child: Text(
                        weeks[day - 1],
                        style: TextStyle(color: Colors.white, fontSize: 10),
                      ),
                    ))
                : Text(
                    weeks[day - 1],
                    style: TextStyle(color: Colors.grey, fontSize: 10),
                  ),
          ),
        )
      ],
    );
  }

  Widget segmentation(RestHours? hour) {
    if (hour == null || hour.start == null || hour.end == null) {
      return SizedBox(height: 0);
    }
    return Positioned(
      top: barHeight * (hour.start! / minOfDay),
      child: AnimatedContainer(
        width: hour.dayOfWeek! == today ? 16 : 12,
        height: barHeight * ((hour.end! - hour.start!) / minOfDay),
        decoration: BoxDecoration(
            borderRadius:
                BorderRadius.circular(hour.dayOfWeek! == today ? 13 : 6),
            color: hour.dayOfWeek! == today ? RColors.mr : Colors.grey[300]),
        duration: Duration(milliseconds: 300),
      ),
    );
  }

  double wight = 0;

  // tags
  Widget buildTags() {
    return Container(
      width: double.infinity,
      child: Wrap(
        alignment: WrapAlignment.start,
        spacing: 12,
        children: [...findHours(today).map((e) => buildTag(e))],
      ),
    );
  }

  Widget buildTag(RestHours? hours) {
    if (hours == null) {
      return Container();
    }
    return Chip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildPoint(),
          Text(
            getDuration(hours),
            style: TextStyle(fontSize: 12),
          )
        ],
      ),
      backgroundColor: Colors.grey[100],
    );
  }

  Widget buildPoint() {
    return Container(
        width: 5,
        height: 5,
        margin: EdgeInsets.only(right: 3),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.all(Radius.circular(3.0)),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          buildTittle(S.of(context).time),
          Card(
            color: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(20))),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, top: 16.0, bottom: 16.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildTimeline(),
                      buildChart(),
                    ],
                  ),
                  buildTags(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
