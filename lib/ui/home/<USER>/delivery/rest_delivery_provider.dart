import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/widget.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';

class RestDeliveryProvider extends StatelessWidget {
  RestDeliveryProvider(this.provider, {Key? key}) : super(key: key);

  final String? provider;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          buildTittle(S.of(context).delivery),
          Card(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(20))),
            elevation: 0,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(child: Text(S.of(context).connect_rest_provider)),
                  provider?.contains("rice") ?? false
                      ? Image(
                          image: AssetImage(
                              'assets/images/rest/rice_delivery.png'),
                          width: 42,
                          height: 16,
                        )
                      : Text(
                          provider ?? "",
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.black87),
                        ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
