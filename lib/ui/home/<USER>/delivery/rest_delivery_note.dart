import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class RestDeliveryNote extends StatelessWidget {
  RestDeliveryNote(this.note, {Key? key}) : super(key: key);

  final MultiNameEntity? note;

  @override
  Widget build(BuildContext context) {
    var zhCn;
    var enUs;
    if (note != null) {
      zhCn = note?.zhCn ?? "";
      enUs = note?.enUs ?? "";
    }
    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        color: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(20))
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                children: [
                  Text(S.of(context).connect_rest_note_ch),
                  SizedBox(
                    width: Dimens.dp_20,
                  ),
                  Expanded(
                    child: Text(
                      zhCn == null ? "" : zhCn,
                      textAlign: TextAlign.right,
                      style: TextStyle(color: Colors.black87),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: Dimens.dp_30,
              ),
              Row(
                children: [
                  Text(S.of(context).connect_rest_note_en),
                  SizedBox(
                    width: Dimens.dp_20,
                  ),
                  Expanded(
                    child: Text(
                      enUs == null ? "" : enUs,
                      textAlign: TextAlign.right,
                      style: TextStyle(color: Colors.black87),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
