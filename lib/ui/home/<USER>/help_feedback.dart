import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

import 'help_feedback_route.dart';

class HelpFeedback extends StatefulWidget {
  HelpFeedback({Key? key}) : super(key: key);

  @override
  _HelpFeedbackState createState() => _HelpFeedbackState();
}

class _HelpFeedbackState extends State<HelpFeedback> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pushNamed(HelpFeedbackRoute.tag);
      },
      child: Container(
          height: Dimens.dp_45,
          color: Colors.white,
          padding:
              const EdgeInsets.only(left: Dimens.dp_20, right: Dimens.dp_20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                S.of(context).driver_feedback,
                style: TextStyle(color: Colors.black87),
              ),
              Icon(
                Icons.keyboard_arrow_right,
                color: Colors.black54,
                size: Dimens.dp_20,
              ),
            ],
          )),
    );
  }
}
