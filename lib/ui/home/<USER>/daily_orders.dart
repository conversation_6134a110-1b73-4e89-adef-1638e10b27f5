import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:timezone/timezone.dart' as tz;

import '../../../common/MyStyles.dart';
import '../../../data/orders_entity.dart';
import '../../../data/repository/restaurant_details_entity.dart';
import '../../../provider/report_model.dart';
import '../../../res/colors.dart';
import '../../../utils/date_util.dart';
import '../../../utils/server_multi_lan.dart';
import '../../view/gif_header.dart';
import '../orders/orders_details_route.dart';
import '../report_route_v2.dart';

class DailyOrders extends StatefulWidget {
  const DailyOrders({Key? key}) : super(key: key);

  @override
  _DailyOrdersState createState() => _DailyOrdersState();
}

class _DailyOrdersState extends State<DailyOrders>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;
  TextEditingController _searchController = TextEditingController();
  int page = 1;

  @override
  void initState() {
    super.initState();

    _refreshController = RefreshController(initialRefresh: true);

    _searchController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var reportModel = context.watch<ReportModel>();
    var restaurant = reportModel.restaurantEntity;
    final orders = _searchController.text.isEmpty
        ? reportModel.orderList
        : reportModel.searchList;
    return Container(
      color: MyColors.bg,
      child: SmartRefresher(
        child: CustomScrollView(
          slivers: <Widget>[
            SliverPersistentHeader(
              pinned: true,
              delegate: SliverHeaderDelegate(
                //有最大和最小高度
                maxHeight: 76,
                minHeight: 76,
                child: _buildSearchRow(context),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) =>
                    _buildRow(context, orders[index], restaurant),
                childCount: orders.length,
              ),
            ),
          ],
        ),
        controller: _refreshController,
        enablePullUp: true,
        physics: AlwaysScrollableScrollPhysics(),
        header: GifHeader(),
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        footer: ClassicFooter(
          loadStyle: LoadStyle.ShowWhenLoading,
        ),
      ),
    );
  }

  Widget _buildRow(BuildContext context, OrdersEntity order,
      RestaurantDetailsEntity? restaurant) {
    var formatDate = "";
    if (restaurant != null) {
      var detroit = tz.getLocation("${restaurant.timezone}");
      tz.setLocalLocation(detroit);
      var dateTime =
          tz.TZDateTime.from(DateUtil.getDateTime(order.createdAt!)!, detroit);
      formatDate = DateUtil.formatDate(dateTime, format: "MM/dd/yy HH:mm");
    }

    return GestureDetector(
      onTap: () {
        Navigator.of(context)
            .pushNamed(OrdersDetailsRoute.tag, arguments: order);
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(20, 8, 20, 8),
        padding: EdgeInsets.fromLTRB(20, 16, 20, 16),
        decoration: BoxDecoration(
          color: MyColors.W,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(children: [
          Expanded(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(text: "${order.passcode}"),
                  TextSpan(
                    text:
                        "  ${ServerMultiLan.multiAdapt(order.restaurant?.name)}  ",
                    style: MyStyles.r13.copyWith(color: MyColors.color333),
                  ),
                ],
              ),
              style: MyStyles.r13.copyWith(color: MyColors.color999),
              overflow: TextOverflow.fade,
              softWrap: false,
            ),
          ),
          SizedBox(
            width: 4,
          ),
          Text(
            formatDate,
            style: TextStyle(
              color: RColors.gray_99,
              fontSize: 12,
            ),
          ),
        ]),
      ),
    );
  }

  void _onRefresh() async {
    try {
      var reportModel = context.read<ReportModel>();

      var value = reportModel.restaurantEntity;
      if (value == null) value = await reportModel.restaurantDetails();
      if (value == null) {
        _refreshController.refreshCompleted();
        return;
      }

      final args =
          ModalRoute.of(context)!.settings.arguments as ReportRouteV2Arguments;
      final from = args.from;
      final to = args.to;
      if (_searchController.text.isEmpty) {
        await reportModel.refreshOrders(from, to, loading: false);
      } else {
        await reportModel.searchOrders(_searchController.text, from, to,
            loading: false);
      }
      _refreshController.refreshCompleted();
      page = 1;
    } catch (e) {
      _refreshController.refreshCompleted();
    }
  }

  void _onLoading() async {
    page++;
    final args =
        ModalRoute.of(context)!.settings.arguments as ReportRouteV2Arguments;
    final from = args.from;
    final to = args.to;
    if (_searchController.text.isEmpty) {
      context
          .read<ReportModel>()
          .loadOrders(page, from, to)
          .whenComplete(() => _refreshController.loadComplete());
    } else {
      context
          .read<ReportModel>()
          .loadSearchOrders(_searchController.text, page, from, to)
          .whenComplete(() => _refreshController.loadComplete());
    }
  }

  Widget _buildSearchRow(BuildContext context) {
    return Container(
      color: MyColors.bg,
      padding: EdgeInsets.fromLTRB(20, 20, 20, 20),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                contentPadding: EdgeInsets.zero,
                prefixIcon: Icon(
                  Icons.search,
                  color: MyColors.color999,
                ),
                suffixIcon: IconButton(
                  onPressed: clear,
                  icon: Icon(
                    Icons.clear,
                    color: MyColors.color999,
                  ),
                ),
                // prefixIconColor: MyColors.color999,
                hintText: 'Hint Text',
                hintStyle: MyStyles.r15.copyWith(color: MyColors.color999),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: MyColors.ccc),
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: MyColors.ccc),
                  borderRadius: BorderRadius.circular(8),
                ),
                isDense: true,
              ),
              onSubmitted: (text) {
                search(text);
              },
              autofocus: false,
              style: MyStyles.r15.copyWith(color: MyColors.color333),
              controller: _searchController,
              textInputAction: TextInputAction.search,
            ),
          ),
          if (_searchController.text.isNotEmpty)
            Row(
              children: [
                SizedBox(width: 16),
                InkWell(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      alignment: Alignment.center,
                      color: MyColors.portal,
                      width: 72,
                      height: 36,
                      child: Icon(
                        Icons.search,
                        color: MyColors.W,
                      ),
                    ),
                  ),
                  onTap: () {
                    search(_searchController.text);
                  },
                ),
              ],
            ),
        ],
      ),
    );
  }

  void clear() {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
    setState(() {
      _searchController.clear();
    });
  }

  void search(String keyword) {
    if (keyword.isEmpty) {
      return;
    }

    var reportModel = context.read<ReportModel>();
    final args =
        ModalRoute.of(context)!.settings.arguments as ReportRouteV2Arguments;
    final from = args.from;
    final to = args.to;
    reportModel.searchOrders(keyword, from, to, loading: true);
  }
}

typedef SliverHeaderBuilder = Widget Function(
    BuildContext context, double shrinkOffset, bool overlapsContent);

class SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  // child 为 header
  SliverHeaderDelegate({
    required this.maxHeight,
    this.minHeight = 0,
    required Widget child,
  })  : builder = ((a, b, c) => child),
        assert(minHeight <= maxHeight && minHeight >= 0);

  //最大和最小高度相同
  SliverHeaderDelegate.fixedHeight({
    required double height,
    required Widget child,
  })  : builder = ((a, b, c) => child),
        maxHeight = height,
        minHeight = height;

  //需要自定义builder时使用
  SliverHeaderDelegate.builder({
    required this.maxHeight,
    this.minHeight = 0,
    required this.builder,
  });

  final double maxHeight;
  final double minHeight;
  final SliverHeaderBuilder builder;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    Widget child = builder(context, shrinkOffset, overlapsContent);
    //测试代码：如果在调试模式，且子组件设置了key，则打印日志
    assert(() {
      if (child.key != null) {
        print('${child.key}: shrink: $shrinkOffset，overlaps:$overlapsContent');
      }
      return true;
    }());
    // 让 header 尽可能充满限制的空间；宽度为 Viewport 宽度，
    // 高度随着用户滑动在[minHeight,maxHeight]之间变化。
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(SliverHeaderDelegate old) {
    return true;
  }
}
