import 'package:connect/data/rest_entity.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/gif_header.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:provider/provider.dart';
import 'delivery/rest_delivery_hours.dart';
import 'delivery/rest_delivery_note.dart';
import 'delivery/rest_delivery_provider.dart';

class DeliveryView extends StatefulWidget {
  DeliveryView({Key? key}) : super(key: key);

  @override
  _DeliveryViewState createState() => _DeliveryViewState();
}

class _DeliveryViewState extends State<DeliveryView>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;

  void _onRefresh() {
    if (context != null) {
      context.read<RestModel>().restInfo().whenComplete(() {
        _refreshController.refreshCompleted();
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: false);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    LogUtil.v("ContactView build");
    var restEntity = context.watch<RestModel>().restEntity;
    String? provider;
    List<RestHours?>? hours;
    var note;
    if (restEntity != null && restEntity.delivery != null) {
      provider = restEntity.delivery?.provider;
      note = restEntity.delivery?.note;
    }
    if (restEntity != null && restEntity.hours != null) {
      hours = restEntity.hours;
    }

    return SmartRefresher(
      controller: _refreshController,
      physics: AlwaysScrollableScrollPhysics(),
      header: GifHeader(),
      onRefresh: _onRefresh,
      child: (restEntity == null)
          ? EmptyView(S.of(context).no_data)
          : ListView(
              children: [
                RestDeliveryProvider(provider),
                RestDeliveryHours(hours),
                RestDeliveryNote(note),
              ],
            ),
    );
  }
}
