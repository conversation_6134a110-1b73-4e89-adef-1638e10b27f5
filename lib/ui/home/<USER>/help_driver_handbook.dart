import 'package:connect/common/t_constants.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpDriverHandbook extends StatefulWidget {
  HelpDriverHandbook({Key? key}) : super(key: key);

  @override
  _HelpDriverHandbookState createState() => _HelpDriverHandbookState();
}

class _HelpDriverHandbookState extends State<HelpDriverHandbook> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () async {
            TrackingUtils.instance!.tracking(TConstants.D_MORE_HANDBOOK_CLICK);
            await launch("https://www.rice.rocks/driverhandbook");
          },
          child: Container(
              height: Dimens.dp_45,
              color: Colors.white,
              padding: const EdgeInsets.only(
                  left: Dimens.dp_20, right: Dimens.dp_20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    S.of(context).driver_driver_handbook,
                    style: TextStyle(color: Colors.black87),
                  ),
                  Icon(
                    Icons.keyboard_arrow_right,
                    color: Colors.black54,
                    size: Dimens.dp_20,
                  ),
                ],
              )),
        ),
      ],
    );
  }
}
