import 'package:connect/common/t_constants.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'center/help_feedback_image.dart';
import 'center/help_feedback_info.dart';
import 'center/help_feedback_submit.dart';
import 'center/help_feedback_type.dart';

class HelpFeedbackRoute extends StatefulWidget {
  static String tag = "HelpFeedbackRoute";

  HelpFeedbackRoute({Key? key}) : super(key: key);

  @override
  _HelpFeedbackRouteState createState() => _HelpFeedbackRouteState();
}

class _HelpFeedbackRouteState extends State<HelpFeedbackRoute> {
  @override
  void initState() {
    super.initState();
    TrackingUtils.instance!.tracking(TConstants.D_MORE_FEEDBACK_ENTER);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          systemOverlayStyle: SystemUiOverlayStyle.light,
          backgroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios_rounded,
              color: Colors.black87,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          elevation: 0,
          title: Text(S.of(context).driver_feedback,
              style: TextStyle(
                  color: Colors.black87,
                  fontSize: Dimens.sp_17,
                  fontWeight: FontWeight.bold)),
          centerTitle: true,
        ),
        body: GestureDetector(
          onTap: () {
            //hide keyboard when tap region is blank space
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Column(
            children: [
              Expanded(
                child: ListView(
                  children: [
                    HelpFeedbackType(),
                    HelpFeedbackInfo(),
                    HelpFeedbackImage(),
                  ],
                ),
              ),
              HelpFeedbackSubmit(),
              SizedBox(
                height: Dimens.dp_20,
              ),
            ],
          ),
        ));
  }
}
