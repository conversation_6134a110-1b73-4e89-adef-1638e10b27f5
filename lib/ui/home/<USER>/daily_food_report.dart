import 'package:connect/common/MyStyles.dart';
import 'package:connect/generated/l10n.dart' as L10n;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../data/repository/restaurant_details_entity.dart';
import '../../../provider/report_model.dart';
import '../../../utils/server_multi_lan.dart';
import '../../view/gif_header.dart';
import '../report_route_v2.dart';
import 'daily_header.dart';
import 'food/report_food_entity.dart';

class DailyFoodReport extends StatefulWidget {
  static String tag = "daily_food_report";
  const DailyFoodReport({Key? key}) : super(key: key);

  @override
  _DailyFoodReportState createState() => _DailyFoodReportState();
}

class _DailyFoodReportState extends State<DailyFoodReport>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;

  @override
  void initState() {
    super.initState();

    _refreshController = RefreshController(initialRefresh: true);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var restaurant = context.watch<ReportModel>().restaurantEntity;
    var foodItems = context.watch<ReportModel>().foodItems;
    return Scaffold(
      appBar: AppBar(
        leading: null,
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: true,
        title: DailyHeader(
          shouldShowReport: false,
        ),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(40),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Container(
              height: 40,
              child: Row(children: [
                Text(
                  L10n.S.of(context).dailyReport_food_foodAndQuantity,
                  style: MyStyles.m12.copyWith(
                    color: MyColors.color333,
                  ),
                ),
                Spacer(),
                Text(
                  L10n.S.of(context).dailyReport_food_sales,
                  style: MyStyles.m12.copyWith(
                    color: MyColors.color333,
                  ),
                ),
              ]),
            ),
          ),
        ),
      ),
      backgroundColor: Colors.grey[200],
      body: Container(
        color: MyColors.W,
        child: SmartRefresher(
          child: ListView.separated(
            itemCount: foodItems?.length ?? 0,
            //列表项构造器
            itemBuilder: (BuildContext context, int index) {
              final widget = _buildRow(context, foodItems?[index], restaurant);
              return widget;
            },
            //分割器构造器
            separatorBuilder: (BuildContext context, int index) {
              return Padding(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Divider(
                  color: MyColors.line,
                ),
              );
            },
          ),
          controller: _refreshController,
          enablePullUp: false,
          physics: AlwaysScrollableScrollPhysics(),
          header: GifHeader(),
          onRefresh: _onRefresh,
          footer: ClassicFooter(
            loadStyle: LoadStyle.ShowWhenLoading,
          ),
        ),
      ),
    );
  }

  Widget _buildRow(BuildContext context, ReportFoodEntity? food,
      RestaurantDetailsEntity? restaurant) {
    var country = "";
    if (restaurant != null) {
      country = restaurant.address!.country!;
    }
    return Container(
      // margin: EdgeInsets.symmetric(horizontal: 20),
      padding: EdgeInsets.fromLTRB(20, 12, 20, 12),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Expanded(
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${food?.rId?.food?.name?.zhCn}',
                    style: MyStyles.r13.copyWith(
                      color: MyColors.color333,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '${food?.rId?.food?.name?.enUs}',
                    style: MyStyles.r13.copyWith(
                      color: MyColors.color333,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'x ${food?.number}',
                    style: MyStyles.r15.copyWith(
                      color: MyColors.color999,
                    ),
                  ),
                ],
              ),
              Spacer(),
              Text(
                '${ServerMultiLan.coinSymbolCountry(country, (food?.price ?? 0) / 100)}',
                style: MyStyles.r15.copyWith(
                  color: MyColors.color333,
                ),
              ),
            ],
          ),
        ),
      ]),
    );
  }

  void _onRefresh() async {
    try {
      var reportModel = context.read<ReportModel>();

      var value = reportModel.restaurantEntity;
      if (value == null) value = await reportModel.restaurantDetails();
      if (value == null) {
        _refreshController.refreshCompleted();
        return;
      }

      final args =
          ModalRoute.of(context)!.settings.arguments as ReportRouteV2Arguments;
      final from = args.from;
      final to = args.to;
      await reportModel.reportFood(from, to, loading: false);
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshCompleted();
    }
  }
}
