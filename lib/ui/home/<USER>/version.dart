import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:sprintf/sprintf.dart';

class Version extends StatefulWidget {
  Version({Key? key}) : super(key: key);

  @override
  _VersionState createState() => _VersionState();
}

class _VersionState extends State<Version> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: () async {
        SmartDialog.showLoading(
            msg: S.of(context).connect_please_wait, maskColor: Colors.black54);

        await LogManager.instance!.uploadLog(true, error: () {
          SmartDialog.dismiss();
        });

        SmartDialog.dismiss();
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_40,
        child: Text(
          sprintf(S.of(context).current_version, [ConnectCache.getVersion()]),
          style: TextStyle(color: Colors.black38),
        ),
      ),
    );
  }
}
