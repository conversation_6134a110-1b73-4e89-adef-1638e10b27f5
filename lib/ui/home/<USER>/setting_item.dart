import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';

class SettingItem extends StatelessWidget {
  final String tittle;
  final Function? onTap;
  final String imagePath;
  final String? subTitle;

  SettingItem(
      {required this.tittle,
      this.subTitle,
      required this.imagePath,
      required this.onTap,
      Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () {
            onTap?.call();
          },
          child: Container(
              height: Dimens.dp_45,
              color: Colors.white,
              padding: const EdgeInsets.only(
                  left: Dimens.dp_20, right: Dimens.dp_20),
              child: Row(
                children: [
                  Image(width: 16, height: 16, image: AssetImage(imagePath)),
                  Expanded(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text(
                        tittle,
                        style: TextStyle(color: Colors.black87),
                      ),
                    ),
                  ),
                  if (subTitle != null)
                    Text(
                      subTitle!,
                      style: TextStyle(color: Colors.black38, fontSize: 12),
                    )
                  else
                    EmptyContainer(),
                  Icon(
                    Icons.keyboard_arrow_right,
                    color: Colors.black26,
                    size: Dimens.dp_20,
                  ),
                ],
              )),
        ),
      ],
    );
  }
}
