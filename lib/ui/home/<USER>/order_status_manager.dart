import 'package:connect/common/constants.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/date_util.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:sprintf/sprintf.dart';

class OrderStatusManager {
  static bool isNewOrder(OrdersEntity entity) {
    return entity.status == Constants.CREATED ||
        entity.status == Constants.SENT;
  }

  static bool isNewOrderWithoutPreOrder(OrdersEntity entity) {
    return (entity.status == Constants.CREATED ||
            entity.status == Constants.SENT) &&
        entity.restaurant!.delivery!.window == null;
  }

  static bool isNewPreOrder(OrdersEntity entity) {
    return (entity.status == Constants.CREATED ||
            entity.status == Constants.SENT) &&
        entity.restaurant!.delivery!.window != null;
  }

  static bool restaurantConfirmedButNotChucan(OrdersEntity entity) {
    return (entity.restaurant!.delivery!.prepareTime == null &&
            entity.status == Constants.CONFIRMED) &&
        !isCourierDeliveryCompleted(entity);
  }

  static bool matchCourierToRestaurant(OrdersEntity entity) {
    // LogUtil.v(
    //     "readyEntity_prepareTime_ma::${entity.restaurant.delivery.prepareTime},${entity.orderId},${entity.delivery.status}");
    return entity.status == Constants.CONFIRMED &&
        entity.restaurant!.delivery!.prepareTime != null &&
        entity.delivery != null &&
        (entity.delivery!.status == Constants.FAILED ||
            entity.delivery!.status == Constants.PROCESSING ||
            entity.delivery!.status == Constants.SCHEDULED ||
            entity.delivery!.status == Constants.EN_ROUTE_TO_PICKUP);
  }

  static bool courierAtPickup(OrdersEntity entity) {
    return entity.delivery != null &&
        entity.delivery!.status == Constants.AT_PICKUP;
  }

  static bool courierPickupCompleted(OrdersEntity entity) {
    return entity.delivery != null &&
        entity.delivery!.status == Constants.PICKUP_COMPLETED;
  }

  static bool isCourierAtDropOff(OrdersEntity entity) {
    return entity.delivery != null &&
        entity.delivery!.courier != null &&
        (entity.delivery!.status == Constants.EN_ROUTE_TO_DROPOFF ||
            entity.delivery!.status == Constants.AT_DROPOFF);
  }

  static bool isCourierDeliveryCompleted(OrdersEntity entity) {
    return entity.delivery != null && entity.delivery!.time != null;
  }

  static bool preOrder(OrdersEntity entity) {
    var isCancelled = cancelOrder(entity);
    if (isCancelled) {
      return false;
    }
    return entity.restaurant!.delivery!.window != null;
  }

  static bool cancelOrder(OrdersEntity entity) {
    return entity.status == Constants.DECLINED ||
        entity.status == Constants.CANCELLED;
  }

  static bool restaurantDeliverySelf(OrdersEntity entity) {
    return entity.delivery != null && entity.delivery!.provider == null;
  }

  static bool customerPickupSelf(OrdersEntity entity) {
    return entity.delivery == null;
  }

  static bool hasProvider(OrdersEntity entity) {
    // LogUtil.v("status::${entity.status},${entity.delivery.provider}");
    return entity.status == Constants.CONFIRMED &&
        (entity.delivery != null && entity.delivery!.provider != null);
  }

  // 如果订单是自取或者预订单需要在小票上打印出来
  // pickup(自取): order.delivery为空是是pickup订单
  // pre order(预定单): order.restaurant.delivery.window 不为空 and 当前时间和订单创建时间的距离超过2小时(iso time)
  static String specialType(OrdersEntity entity) {
    if (customerPickupSelf(entity)) {
      return S.current.pickup_by_myself;
    } else if (preOrder(entity)) {
      String deliveryTime = formatDate(
          DateUtil.getDateTime(entity.restaurant!.delivery!.window!.start!)!
              .toLocal(),
          [HH, ':', nn, ':', ss]);
      String deliveryStr =
          sprintf(S.current.pre_order_time, [deliveryTime.split(".")[0]])
              .toString();
      return deliveryStr;
    }
    return "";
  }
}
