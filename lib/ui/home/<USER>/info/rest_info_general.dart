import 'dart:convert';

import 'package:connect/data/rest_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart' show rootBundle;

import '../widget.dart';

class RestInfoGeneral extends StatefulWidget {
  RestInfoGeneral({Key? key}) : super(key: key);

  @override
  _RestInfoGeneralState createState() => _RestInfoGeneralState();
}

class _RestInfoGeneralState extends State<RestInfoGeneral> {
  List<Widget> widgets = [];

  Future<List<Widget>> _fetchTags(RestEntity? restEntity) async {
    var languageTag = ConnectCache.getLocale().toLanguageTag();
    LogUtil.v("languageTag::$languageTag");
    var tagsStr;
    if ("zh-CN" == languageTag) {
      tagsStr = await rootBundle.loadString('assets/config/tags_zh.json');
    } else if ("zh-HK" == languageTag) {
      tagsStr = await rootBundle.loadString('assets/config/tags_hk.json');
    } else {
      tagsStr = await rootBundle.loadString('assets/config/tags_en.json');
    }
    var tagsJson = jsonDecode(tagsStr);
    // LogUtil.v("tagsJson::${tagsJson.toString()}");
    //filter tags
    if (restEntity != null &&
        restEntity.tags != null &&
        restEntity.tags!.length > 0) {
      widgets.clear();
      restEntity.tags!.forEach((element) {
        var containsKey = tagsJson.containsKey(element);
        if (containsKey) {
          widgets.add(Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
            margin: const EdgeInsets.only(left: 5),
            decoration: BoxDecoration(
                color: RColors.mr,
                borderRadius: BorderRadius.all(Radius.circular(5))),
            child: Text(
              tagsJson[element],
              style: TextStyle(color: Colors.white),
            ),
          ));
        }
      });
    }
    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    var restEntity = context.read<RestModel>().restEntity;
    var name;
    var motd;
    var promotion;
    if (restEntity != null) {
      name = restEntity.name;
      motd = restEntity.motd;
      promotion = restEntity.promotion;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          buildTittle(S.of(context).connect_rest_general),
          Card(
            color: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_name_zh),
                      SizedBox(
                        width: Dimens.dp_10,
                      ),
                      Expanded(
                        child: Text(
                          name == null ? "" : name.zhCn,
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                  buildDivider(),
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_name_en),
                      SizedBox(
                        width: Dimens.dp_10,
                      ),
                      Expanded(
                        child: Text(
                          name == null ? "" : name.enUs,
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                  buildDivider(),
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_motd_zh),
                      SizedBox(
                        width: Dimens.dp_10,
                      ),
                      Expanded(
                        child: Text(
                          motd == null ? "" : motd.zhCn,
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                  buildDivider(),
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_motd_en),
                      SizedBox(
                        width: Dimens.dp_10,
                      ),
                      Expanded(
                        child: Text(
                          motd == null ? "" : motd.enUs,
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                  buildDivider(),
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_promotion_zh),
                      SizedBox(
                        width: Dimens.dp_60,
                      ),
                      Expanded(
                        child: Text(
                          promotion == null ? "" : promotion.zhCn,
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                 buildDivider(),
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_promotion_en),
                      SizedBox(
                        width: Dimens.dp_60,
                      ),
                      Expanded(
                        child: Text(
                          promotion == null ? "" : promotion.enUs,
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                  buildDivider(),
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_tags),
                      SizedBox(
                        width: Dimens.dp_10,
                      ),
                      Expanded(
                        child: FutureBuilder<List<Widget>>(
                          future: _fetchTags(restEntity),
                          builder: (BuildContext context,
                              AsyncSnapshot<List<Widget>> snapshot) {
                            if (snapshot.connectionState == ConnectionState.done) {
                              return SingleChildScrollView(
                                reverse: true,
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: snapshot.data!,
                                ),
                              );
                            } else {
                              return EmptyContainer();
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}


