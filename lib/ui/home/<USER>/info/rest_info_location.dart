import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/info/rest_info_general.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../widget.dart';

class RestInfoLocation extends StatefulWidget {
  RestInfoLocation({Key? key}) : super(key: key);

  @override
  _RestInfoLocationState createState() => _RestInfoLocationState();
}

class _RestInfoLocationState extends State<RestInfoLocation> {
  @override
  Widget build(BuildContext context) {
    var restEntity = context.read<RestModel>().restEntity;
    var formatted;
    var timezone;
    if (restEntity != null && restEntity.address != null) {
      formatted = restEntity.address!.formatted;
    }
    if (restEntity != null) {
      timezone = restEntity.timezone;
    }
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          buildTittle(S.of(context).connect_rest_location),
          Card(
            color: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(20))
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_address),
                      Expanded(
                        child: Text(
                          formatted == null ? "" : formatted,
                          textAlign: TextAlign.right,
                          style: TextStyle(color: RColors.mr),
                        ),
                      ),
                    ],
                  ),
                  buildDivider(),
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_timezone),
                      Expanded(
                        child: Text(
                          timezone == null ? "" : timezone,
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
