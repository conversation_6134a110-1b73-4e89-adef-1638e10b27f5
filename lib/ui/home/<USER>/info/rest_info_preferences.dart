import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/info/rest_info_general.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../widget.dart';

class RestInfoPreferences extends StatefulWidget {
  RestInfoPreferences({Key? key}) : super(key: key);

  @override
  _RestInfoPreferencesState createState() => _RestInfoPreferencesState();
}

class _RestInfoPreferencesState extends State<RestInfoPreferences> {
  @override
  Widget build(BuildContext context) {
    var restEntity = context.read<RestModel>().restEntity;
    var language;
    var dailyReport;
    var autoConfirm;
    if (restEntity != null && restEntity.preferences != null) {
      dailyReport = restEntity.preferences!.dailyReport;
      autoConfirm = restEntity.preferences!.autoConfirm;
    }
    if (restEntity != null) {
      language = restEntity.language;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          buildTittle(S.of(context).connect_rest_preferences),
          Card(
            color: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20)
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(S.of(context).connect_rest_language),
                      Expanded(
                        child: Text(
                          language == null ? "" : language,
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                  buildDivider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(S.of(context).connect_rest_daily_report),
                      toggle(dailyReport),
                    ],
                  ),
                  buildDivider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(S.of(context).connect_rest_auto_confirm),
                      toggle(autoConfirm),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget toggle(bool toggle) {
    return Switch(
        value: toggle == null ? false : toggle,
        activeColor: RColors.mr,
        onChanged: (bool) {});
  }
}
