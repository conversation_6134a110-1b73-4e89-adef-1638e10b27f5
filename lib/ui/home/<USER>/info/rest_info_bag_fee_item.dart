import 'package:connect/data/rest_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';

class RestInfoBagFeeItem extends StatefulWidget {
  RestInfoBagFeeItem(this.fee, {Key? key}) : super(key: key);

  final RestExtraFees? fee;

  @override
  _RestInfoBagFeeItemState createState() => _RestInfoBagFeeItemState();
}

class _RestInfoBagFeeItemState extends State<RestInfoBagFeeItem> {
  @override
  Widget build(BuildContext context) {
    var fee = widget.fee;
    var name = fee?.name;
    var price = fee?.price;

    return Container(
      padding: const EdgeInsets.all(Dimens.dp_20),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                ServerMultiLan.multiAdapt(name),
                style: TextStyle(
                    color: Colors.black,
                    fontSize: Dimens.sp_18,
                    fontWeight: FontWeight.bold),
              ),
            ],
          ),
          SizedBox(
            height: Dimens.dp_20,
          ),
          Row(
            children: [
              Text(S.of(context).connect_rest_name_zh),
              Expanded(
                child: Text(
                  name?.zhCn ?? "",
                  textAlign: TextAlign.right,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ],
          ),
          SizedBox(
            height: Dimens.dp_20,
          ),
          Row(
            children: [
              Text(S.of(context).connect_rest_name_en),
              Expanded(
                child: Text(
                  price == null ? "" : "$price",
                  textAlign: TextAlign.right,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ],
          ),
          SizedBox(
            height: Dimens.dp_20,
          ),
          Row(
            children: [
              Text(S.of(context).connect_rest_price),
              Expanded(
                child: Text(
                  name == null ? "" : "$price",
                  textAlign: TextAlign.right,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
