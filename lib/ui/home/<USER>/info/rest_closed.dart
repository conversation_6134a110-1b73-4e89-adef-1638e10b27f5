import 'package:connect/common/t_constants.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/ui/home/<USER>/widget.dart';
import 'package:connect/utils/date_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:connect/data/rest_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/info/rest_closed_reason_dialog.dart';
import 'package:connect/ui/home/<USER>/info/te_entity.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class RestClosed extends StatefulWidget {
  RestClosed({Key? key}) : super(key: key);

  @override
  _RestClosedState createState() => _RestClosedState();
}

class _RestClosedState extends State<RestClosed> {
  bool isOpened = false;
  RestClosePeriod? closePeriod;
  var formatDateStr;
  var toDateStr;

  @override
  void initState() {
    super.initState();
    //init timezone
    tz.initializeTimeZones();
  }

  @override
  Widget build(BuildContext context) {
    var restEntity = context.read<RestModel>().restEntity;
    var reason;
    if (restEntity != null && restEntity.closed == null) {
      //open
      isOpened = true;
    } else {
      isOpened = false;
    }
    if (restEntity != null && restEntity.closed != null) {
      reason = restEntity.closed!.reason;
    }

    if (restEntity != null &&
        restEntity.closePeriod != null &&
        restEntity.timezone != null) {
      closePeriod = restEntity.closePeriod;
      //calculate time according to timezone
      var location = tz.getLocation(restEntity.timezone!);
      var fromDate = DateUtil.getDateTime(closePeriod!.from!)!.toLocal();
      var tzFromDate = tz.TZDateTime.from(fromDate, location);
      formatDateStr = DateUtil.formatDate(tzFromDate, format: "MM/dd HH:mm");

      var toDate = DateUtil.getDateTime(closePeriod!.to!)!.toLocal();
      var tzToDate = tz.TZDateTime.from(toDate, location);
      toDateStr = DateUtil.formatDate(tzToDate, format: "MM/dd HH:mm");
    }

    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          buildTittle(S.of(context).open),
          Card(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20)),
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: isOpened
                    ? open()
                    : close(reason),
              ))
        ],
      ),
    );
  }

  Widget close(String? reason) {
    return Container(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(S.of(context).connect_rest_close),
              toggle(),
            ],
          ),
          SizedBox(
            height: Dimens.dp_8,
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  reason == null
                      ? ""
                      : "${S.of(context).connect_rest_reason} $reason",
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ],
          ),
          SizedBox(
            height: Dimens.dp_15,
          ),
          closedTime(),
        ],
      ),
    );
  }

  Widget open() {
    return Container(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(S.of(context).connect_rest_open),
              toggle(),
            ],
          ),
          buildDivider(),
          closedTime(),
        ],
      ),
    );
  }

  Widget toggle() {
    return Switch(
        value: isOpened,
        activeColor: RColors.mr,
        onChanged: (bool) {
          if (isOpened) {
            //close restaurant
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return RestClosedReasonDialog((reason) {
                    var teEntity = TeEntity();
                    teEntity.enUs = "Closed";
                    teEntity.zhCn = "暂不接单";
                    teEntity.zhHk = "暫不接單";
                    teEntity.reason = reason;
                    teEntity.user = ConnectCache.getUser()!.email!;
                    context.read<RestModel>().closed(teEntity.toJson());

                    var params = Map<String, dynamic>();
                    params["reason"] = "$reason";
                    TrackingUtils.instance!.tracking(
                        TConstants.R_REST_INFO_CLOSE_CONFIRM_CLICK,
                        value: params);
                  });
                });
          } else {
            //open restaurant
            context.read<RestModel>().open();
          }
        });
  }

  Widget closedTime() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(S.of(context).connect_rest_close_time),
        SizedBox(
          width: Dimens.dp_20,
        ),
        Expanded(
          child: Text(
            closePeriod == null
                ? ""
                : "$formatDateStr ~ $toDateStr (${closePeriod!.note})",
            textAlign: TextAlign.end,
            style: TextStyle(color: Colors.red, fontSize: 12),
          ),
        ),
      ],
    );
  }
}
