import 'package:connect/data/rest_entity.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/ui/home/<USER>/info/rest_info_bag_fee_item.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class RestInfoBagFee extends StatefulWidget {
  RestInfoBagFee({Key? key}) : super(key: key);

  @override
  _RestInfoBagFeeState createState() => _RestInfoBagFeeState();
}

class _RestInfoBagFeeState extends State<RestInfoBagFee> {
  @override
  Widget build(BuildContext context) {
    bool hasFees = false;
    List<RestExtraFees?>? fees;
    var restEntity = context.read<RestModel>().restEntity;
    if (restEntity != null &&
        restEntity.extraFees != null &&
        restEntity.extraFees!.length > 0) {
      hasFees = true;
      fees = restEntity.extraFees;
    }
    return hasFees
        ? ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return RestInfoBagFeeItem(fees![index]);
            },
            itemCount: fees!.length,
          )
        : EmptyContainer();
  }
}
