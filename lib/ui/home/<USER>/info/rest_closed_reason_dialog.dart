import 'package:connect/generated/l10n.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class RestClosedReasonDialog extends Dialog {
  RestClosedReasonDialog(this.callback, {Key? key}) : super(key: key);

  final ValueChanged callback;

  @override
  Widget build(BuildContext context) {
    String reason = "";
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: SizedBox(
          width: Dimens.dp_300,
          height: Dimens.dp_200,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(Dimens.dp_10),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Container(
                  alignment: Alignment.topCenter,
                  margin: EdgeInsets.only(top: Dimens.dp_25),
                  child: Text(
                    S.of(context).connect_rest_enter_reason,
                    style: TextStyle(
                        fontSize: Dimens.sp_16,
                        color: Colors.black,
                        fontWeight: FontWeight.bold),
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  child: TextField(
                    autofocus: true,
                    onChanged: (v) {
                      reason = v;
                    },
                    style: TextStyle(
                      fontSize: Dimens.sp_16,
                      color: Colors.black,
                    ),
                    decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.grey[200],
                        border: InputBorder.none,
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5),
                            borderSide: BorderSide.none),
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 12.0, horizontal: 15)),
                  ),
                  constraints: BoxConstraints(maxWidth: Dimens.dp_280),
                ),
                Container(
                  margin: EdgeInsets.only(bottom: Dimens.dp_15),
                  padding:
                      EdgeInsets.only(left: Dimens.dp_25, right: Dimens.dp_25),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                          child: TextButton(
                        style: ButtonStyle(
                            backgroundColor:
                                MaterialStateProperty.all(Color(0xFFFFFF))),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text(
                          S.of(context).cancel,
                          style: TextStyle(
                            color: Colors.black87,
                            fontSize: Dimens.sp_16,
                          ),
                        ),
                      )),
                      Expanded(
                          child: TextButton(
                        style: ButtonStyle(
                            backgroundColor:
                                MaterialStateProperty.all(Color(0xFF2196F3))),
                        onPressed: () async {
                          Navigator.pop(context);
                          callback(reason);
                        },
                        child: Text(
                          S.of(context).confirm,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: Dimens.sp_16,
                          ),
                        ),
                      )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
