import 'package:connect/generated/json/te_entity.g.dart';

import 'package:connect/generated/json/base/json_field.dart';

///restaurant closed request params
@JsonSerializable()
class TeEntity {

	TeEntity();

	factory TeEntity.fromJson(Map<String, dynamic> json) => $TeEntityFromJson(json);

	Map<String, dynamic> toJson() => $TeEntityToJson(this);

  @JSONField(name: "zh-CN")
  String? zhCn;
  @JSONField(name: "zh-HK")
  String? zhHk;
  @JSONField(name: "en-US")
  String? enUs;
  String? user;
  String? reason;
}
