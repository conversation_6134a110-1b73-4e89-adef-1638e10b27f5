import 'package:connect/common/t_constants.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_picker_plus/flutter_picker_plus.dart';

typedef _StringClickCallBack = void Function(
    int selectIndex, Object? selectStr);

class SettingPrepareTime extends StatefulWidget {
  SettingPrepareTime({Key? key}) : super(key: key);

  @override
  _SettingPrepareTimeState createState() => _SettingPrepareTimeState();
}

class _SettingPrepareTimeState extends State<SettingPrepareTime> {
  List<String> prepareTimeList = [];

  @override
  void initState() {
    super.initState();
    for (int i = 10; i <= 80; i++) {
      prepareTimeList.add(i.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        padding: const EdgeInsets.only(
            left: Dimens.dp_15, right: Dimens.dp_15, top: Dimens.dp_10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(S.of(context).prepare_time,
                style: TextStyle(
                    color: Colors.black87,
                    fontSize: Dimens.sp_18,
                    fontWeight: FontWeight.bold)),
            SizedBox(
              height: Dimens.dp_8,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(S.of(context).promise_prepare_time,
                      style: TextStyle(
                        color: Colors.grey[800],
                        fontSize: Dimens.sp_15,
                      )),
                  flex: 1,
                ),
                Container(
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(ConnectCache.getPrepareTime()!),
                      SizedBox(
                        width: Dimens.dp_3,
                      ),
                      Text(S.of(context).minutes,
                          style: TextStyle(
                            color: Colors.grey[800],
                            fontSize: Dimens.sp_14,
                          )),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.grey[800],
                        size: Dimens.dp_15,
                      ),
                    ],
                  ),
                ),
              ],
            )
          ],
        ),
      ),
      onTap: () {
        showStringPicker<String>(context, data: prepareTimeList,
            clickCallBack: (selectIndex, selectStr) {
          setState(() {
            LogUtil.v("selectStr:$selectStr");
            ConnectCache.savePrepareTime(selectStr.toString());
          });

          var params = Map<String, dynamic>();
          params["prepare"] = "${selectStr.toString()}";
          TrackingUtils.instance!.tracking(
              TConstants.R_ORDERS_SETTING_PREPARE_TIME_CONFIRM_CLICK,
              value: params);
        });
      },
    );
  }

  void showStringPicker<T>(
    BuildContext context, {
    required List<T> data,
    String? title,
    int? normalIndex,
    PickerDataAdapter? adapter,
    required _StringClickCallBack clickCallBack,
  }) {
    openModalPicker(context,
        adapter: adapter ?? PickerDataAdapter(pickerData: data, isArray: false),
        clickCallBack: (Picker picker, List<int> selected) {
      clickCallBack(selected[0], data[selected[0]]);
    }, selected: [normalIndex ?? 0], title: title);
  }

  void openModalPicker(
    BuildContext context, {
    required PickerAdapter adapter,
    String? title,
    List<int>? selected,
    required PickerConfirmCallback clickCallBack,
  }) {
    const double _kPickerHeight = Dimens.dp_216;
    const double _kItemHeight = Dimens.dp_40;
    const Color _kBtnColor = Color(0xFF323232);
    const Color _kTitleColor = Color(0xFF787878);
    const double _kTextFontSize = Dimens.sp_16;
    new Picker(
            adapter: adapter,
            title: new Text(
              title ?? S.of(context).prepare_time_const,
              style: TextStyle(color: _kTitleColor, fontSize: _kTextFontSize),
            ),
            selecteds: selected,
            cancelText: S.of(context).cancel,
            confirmText: S.of(context).confirm,
            cancelTextStyle:
                TextStyle(color: _kBtnColor, fontSize: _kTextFontSize),
            confirmTextStyle:
                TextStyle(color: _kBtnColor, fontSize: _kTextFontSize),
            textAlign: TextAlign.right,
            itemExtent: _kItemHeight,
            height: _kPickerHeight,
            selectedTextStyle: TextStyle(color: Colors.black),
            onConfirm: clickCallBack)
        .showModal(context);
  }
}
