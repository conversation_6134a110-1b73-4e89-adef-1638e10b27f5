import 'dart:io';
import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/setting_widget/setting_prepare_time.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/switch_button.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class OrdersSetting extends StatefulWidget {
  static String tag = "setting_route";

  OrdersSetting({Key? key}) : super(key: key);

  @override
  _OrdersSettingState createState() => _OrdersSettingState();
}

class _OrdersSettingState extends State<OrdersSetting> {
  static final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  bool isSunmi = false;

  @override
  void initState() {
    super.initState();
    LogUtil.v("initState::");
    if (Platform.isAndroid) {
      deviceInfoPlugin.androidInfo.then((value) {
        if (value.brand != null &&
            value.brand.toLowerCase().contains(Constants.SUNMI)) {
          setState(() {
            isSunmi = true;
          });
        }
      });
    }
    TrackingUtils.instance!.tracking(TConstants.R_ORDERS_SETTING_ENTER);
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("build::");
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: Colors.black87,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
        elevation: 2,
        backgroundColor: Colors.white,
        title: Text(
          S.of(context).setting,
          style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_18),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          _buildAutoReceive(),
          SettingPrepareTime(),
        ],
      ),
    );
  }

  Widget _buildAutoReceive() {
    return isSunmi ? _buildAuto() : EmptyContainer();
  }

  Widget _buildAuto() {
    return Container(
      padding: const EdgeInsets.only(
        left: Dimens.dp_15,
        right: Dimens.dp_15,
        top: Dimens.dp_15,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(S.of(context).auto_confirm,
              style: TextStyle(
                  color: Colors.black87,
                  fontSize: Dimens.sp_18,
                  fontWeight: FontWeight.bold)),
          SizedBox(
            height: Dimens.dp_8,
          ),
          Text(S.of(context).suggest_one_devices_auto_confirm,
              style: TextStyle(
                color: Colors.grey[800],
                fontSize: Dimens.sp_15,
              )),
          SizedBox(
            height: Dimens.dp_5,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(S.of(context).auto_confirm_devices,
                    style: TextStyle(
                        color: Colors.black87,
                        fontSize: Dimens.sp_15,
                        fontWeight: FontWeight.bold)),
              ),
              SwitchButton((b) {
                ConnectCache.saveAutoReceiveStatus(b);
                GlobalConfig.eventBus.fire(AutoConfirmed());

                var params = Map<String, dynamic>();
                params["auto"] = b;
                TrackingUtils.instance!.tracking(
                    TConstants.R_ORDERS_SETTING_AUTO_CLICK,
                    value: params);
              }, ConnectCache.getAutoReceiveStatus()),
            ],
          ),
          Divider(),
        ],
      ),
    );
  }
}
