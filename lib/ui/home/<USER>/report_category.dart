import 'package:connect/data/summary_report_entity.dart';
import 'package:connect/provider/report_model.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../generated/l10n.dart';
import '../../../res/dimens.dart';
import '../../../utils/server_multi_lan.dart';

class ReportCategory extends StatefulWidget {
  ReportCategory({Key? key}) : super(key: key);

  @override
  _ReportCategoryState createState() => _ReportCategoryState();
}

class _ReportCategoryState extends State<ReportCategory> {
  List<Widget> _expandedWidget = [];
  var mProvider;

  //customer payment
  num? orderCount;
  num? subtotal;
  num? averageOrderSubtotal;
  num? reachMinimum;
  num? tax;
  num? deliveryFee;
  num? tip;
  num? serviceFee;
  num? creditCardFee;
  late num subtotalTax;
  num? total;

  //restaurant payment
  num? commission;
  num? commissionTotal;
  num? commissionService;
  num? adjustmentsRestaurant;

  //total benefit
  num? totalBenefit;

  String? country = "";

  _summaryMapMethod(bool delivery, bool? provider) {
    LogUtil.v("orderCount::$orderCount");
    var stringAsFixed = ((subtotal! / orderCount!) / 100).toStringAsFixed(3);
    averageOrderSubtotal = num.parse(
        stringAsFixed.substring(0, stringAsFixed.lastIndexOf(".") + 3));
    var _summaryMap = {};
    if (mProvider == null) {
      _summaryMap = {
        S.of(context).summary_order_count: orderCount!.toInt(),
        S.of(context).summary_subtotal: subtotal! / 100,
        S.of(context).summary_average_order_subtotal: averageOrderSubtotal,
        S.of(context).summary_reach_minimum: reachMinimum! / 100,
        S.of(context).summary_tax: tax! / 100,
        S.of(context).summary_delivery_fee: deliveryFee! / 100,
        S.of(context).summary_tip: tip! / 100,
        S.of(context).summary_service_fee: serviceFee! / 100,
        S.of(context).summary_credit_card_fee: creditCardFee! / 100,
        S.of(context).summary_subtotal_tax: subtotalTax / 100,
      };
    } else {
      _summaryMap = {
        S.of(context).summary_order_count: orderCount!.toInt(),
        S.of(context).summary_subtotal: subtotal! / 100,
        S.of(context).summary_average_order_subtotal: averageOrderSubtotal,
        S.of(context).summary_tax: tax! / 100,
        S.of(context).summary_service_fee: serviceFee! / 100,
        S.of(context).summary_credit_card_fee: creditCardFee! / 100,
        S.of(context).summary_subtotal_tax: subtotalTax / 100,
      };
    }

    List<Widget> summaryWidget = [];
    _summaryMap.forEach((key, value) {
      // LogUtil.v("forEach::$key,$value");
      summaryWidget.add(_buildItem(key, value));
    });

    var _commissionMap = {
      S.of(context).summary_commission: commission! / 100,
      S.of(context).summary_total_based_commission: commissionTotal! / 100,
      S.of(context).summary_service_fee: commissionService! / 100,
      S.of(context).summary_adjustments: adjustmentsRestaurant! / 100
    };

    List<Widget> commissionWidget = [];
    _commissionMap.forEach((key, value) {
      commissionWidget.add(_buildCommissionItem(key, value));
    });

    _expandedWidget.add(_buildExpandedChild(
        delivery, provider, summaryWidget, commissionWidget));
  }

  Widget _buildCommissionItem(String name, num value) {
    return Column(
      children: [
        _buildRow(name, value),
        SizedBox(
          height: Dimens.dp_4,
        ),
      ],
    );
  }

  _buildExpandedChild(bool delivery, bool? provider, List<Widget> summaryWidget,
      List<Widget> commissionWidget) {
    String coinSymbolValue =
        ServerMultiLan.coinSymbolCountry(country, totalBenefit);
    String providerText = "";
    if (!delivery) {
      providerText = S.of(context).pickup;
    } else if (delivery && !provider!) {
      providerText = S.of(context).self_delivery;
    } else {
      // ricepo和三方provider都是true,都显示为ricepo
      providerText = S.of(context).ricepo_delivery;
    }
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: Dimens.dp_15),
      child: ExpansionTile(
        backgroundColor: Colors.white,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              providerText,
              style: TextStyle(
                  fontSize: Dimens.sp_16, fontWeight: FontWeight.bold),
            ),
            Row(
              children: [
                Text(
                  "${orderCount!.toInt()}",
                  style: TextStyle(fontSize: Dimens.sp_16),
                ),
                SizedBox(
                  width: Dimens.dp_15,
                ),
                Text(
                  totalBenefit! >= 0 ? coinSymbolValue : "($coinSymbolValue)",
                  style: TextStyle(fontSize: Dimens.sp_16),
                ),
              ],
            ),
          ],
        ),
        tilePadding: EdgeInsets.symmetric(horizontal: 0.0),
        initiallyExpanded: false,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: summaryWidget,
              ),
              _buildTotal(),
              Column(
                children: commissionWidget,
              ),
              _buildRevenue(),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildTotal() {
    String coinSymbolValue =
        ServerMultiLan.coinSymbolCountry(country, total! / 100);
    return Container(
      color: Colors.white,
      alignment: Alignment.centerLeft,
      height: Dimens.dp_50,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                S.of(context).summary_total,
                style:
                    TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
              ),
              Text(total! >= 0 ? coinSymbolValue : "($coinSymbolValue)"),
            ],
          ),
          SizedBox(
            height: Dimens.dp_10,
          ),
          Divider(
            color: Colors.grey[200],
            thickness: Dimens.dp_1,
            height: Dimens.dp_1,
          )
        ],
      ),
    );
  }

  Widget _buildRevenue() {
    String coinSymbolValue =
        ServerMultiLan.coinSymbolCountry(country, totalBenefit);
    return Container(
      color: Colors.white,
      alignment: Alignment.centerLeft,
      height: Dimens.dp_50,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            S.of(context).summary_revenue,
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          Text(totalBenefit! >= 0 ? coinSymbolValue : "($coinSymbolValue)"),
        ],
      ),
    );
  }

  _summaryValueReset() {
    orderCount = 0;
    subtotal = 0;
    averageOrderSubtotal = 0;
    reachMinimum = 0;
    tax = 0;
    deliveryFee = 0;
    tip = 0;
    serviceFee = 0;
    creditCardFee = 0;
    subtotalTax = 0;
    total = 0;

    commission = 0;
    commissionTotal = 0;
    commissionService = 0;
    adjustmentsRestaurant = 0;

    totalBenefit = 0;
  }

  _summaryValueMethod(List<SummaryReportEntity> summaryList) {
    _expandedWidget.clear();
    summaryList.forEach((element) {
      _summaryValueReset();
      if (element.number != null) orderCount = element.number;
      if (element.subtotal != null) subtotal = element.subtotal;
      if (element.number != null && element.subtotal != null)
        averageOrderSubtotal = element.subtotal! / element.number!;
      if (element.fees != null) {
        if (element.fees!.delta != null) reachMinimum = element.fees!.delta;
        if (element.fees!.tax != null) tax = element.fees!.tax;
        if (element.fees!.delivery != null)
          deliveryFee = element.fees!.delivery;
        if (element.fees!.tip != null && element.fees!.tip!.amount != null)
          tip = element.fees!.tip!.amount;
        if (element.fees!.service != null) serviceFee = element.fees!.service;
        if (element.fees!.credit != null) creditCardFee = element.fees!.credit;

        if (element.subtotal != null && element.fees!.tax != null) {
          subtotalTax = element.subtotal! + element.fees!.tax!;
        }
      }
      if (element.total != null) total = element.total;

      if (element.commission != null) {
        SummaryReportCommission commissionBean = element.commission!;
        commission = commissionBean.subtotal;
        commissionTotal = commissionBean.total;
        commissionService = commissionBean.service;
      }

      if (element.adjustments != null) {
        adjustmentsRestaurant = element.adjustments!.restaurant;
      }

      if (element.distribution != null &&
          element.distribution!.restaurant != null) {
        totalBenefit = element.distribution!.restaurant! / 100;
      }
      _summaryMapMethod(element.sId!.delivery!, element.sId!.provider);
      // LogUtil.v("provider::${element.sId.provider}");
      LogUtil.v("delivery::${element.sId!.delivery}");
    });
  }

  @override
  Widget build(BuildContext context) {
    var watch = context.watch<ReportModel>();
    if (watch.restaurantEntity != null &&
        watch.restaurantEntity!.address != null &&
        watch.restaurantEntity!.address!.country != null) {
      country = watch.restaurantEntity!.address!.country;
      mProvider = watch.restaurantEntity!.delivery!.provider;
      LogUtil.v("provider::$mProvider");
    }
    _summaryValueMethod(watch.summaryList);
    return Column(
      children: _expandedWidget,
    );
  }

  Widget _buildItem(String name, num value) {
    return Container(
        color: Colors.white,
        child: Column(
          children: [
            _buildRow(name, value),
            SizedBox(
              height: Dimens.dp_4,
            ),
          ],
        ));
  }

  Widget _buildRow(String name, num value) {
    String coinSymbolValue = ServerMultiLan.coinSymbolCountry(country, value);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(name),
        S.of(context).summary_order_count == name
            ? Text("${value.toInt()}")
            : Text(value >= 0 ? coinSymbolValue : "($coinSymbolValue)"),
      ],
    );
  }
}
