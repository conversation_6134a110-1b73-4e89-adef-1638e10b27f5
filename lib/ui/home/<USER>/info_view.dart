import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/rest_model.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/gif_header.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'info/rest_closed.dart';
import 'info/rest_info_bag_fee.dart';
import 'info/rest_info_location.dart';
import 'info/rest_info_general.dart';
import 'info/rest_info_preferences.dart';
import 'package:provider/provider.dart';

class InfoView extends StatefulWidget {
  InfoView({Key? key}) : super(key: key);

  @override
  _InfoViewState createState() => _InfoViewState();
}

class _InfoViewState extends State<InfoView>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;

  void _onRefresh({bool showLoading = false}) async {
    if (context != null) {
      if (showLoading) {
        LoadingUtils.show();
      }
      await context.read<RestModel>().restInfo().whenComplete(() {
        _refreshController.refreshCompleted();
      });

      if (showLoading) {
        LoadingUtils.dismiss();
      }
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<RestModel>().restEntity = null;
    _refreshController = RefreshController(initialRefresh: false);

    WidgetsBinding.instance!.addPostFrameCallback((timeStamp) {
      //request rest info data
      _onRefresh(showLoading: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    //listener request callback
    var restEntity = context.watch<RestModel>().restEntity;
    return SmartRefresher(
      controller: _refreshController,
      physics: AlwaysScrollableScrollPhysics(),
      header: GifHeader(),
      onRefresh: _onRefresh,
      child: restEntity == null
          ? EmptyView(S.of(context).no_data)
          : ListView(
              children: [
                RestClosed(),
                RestInfoGeneral(),
                RestInfoLocation(),
                // feat: https://ricerocks.atlassian.net/browse/NEW-577
                // RestInfoPreferences(),
                RestInfoBagFee(),
              ],
            ),
    );
  }
}
