import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/flutter_device_type.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class CallSupportSheet {
  static show(BuildContext context) {
    showModalBottomSheet(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(10.0)),
        ),
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context);
        },
        context: context);
  }

  static tracking(String phone) {
    var params = Map<String, dynamic>();
    params["phone"] = "$phone";
    TrackingUtils.instance!
        .tracking(TConstants.D_MORE_CALL_SUPPORT_SHEET_CLICK, value: params);
  }

  static Widget buildBottomSheetWidget(BuildContext context) {
    return Container(
      height: Dimens.dp_280,
      child: Column(
        children: [
          Container(
              width: double.infinity,
              child: Padding(
                padding: const EdgeInsets.only(left: 24, top: 32),
                child: Text(S.of(context).call_customer,
                    style: TextStyle(fontSize: 20)),
              )),
          buildItem(context, S.of(context).united_kingdoms, onTap: () async {
            if (GlobalConfig.isSunmi || Device.get().isTablet!) {
              ToastUtils.show(S
                  .of(GlobalConfig.navigatorKey.currentContext!)
                  .sunmi_not_support_calls);
            } else {
              tracking(Constants.UNITED_KINGDOMS_PHONE);
              launch("tel:${Constants.UNITED_KINGDOMS_PHONE}");
            }
          }),
          buildItem(context, S.of(context).spain, onTap: () async {
            if (GlobalConfig.isSunmi || Device.get().isTablet!) {
              ToastUtils.show(S
                  .of(GlobalConfig.navigatorKey.currentContext!)
                  .sunmi_not_support_calls);
            } else {
              tracking(Constants.SPAIN_PHONE);
              launch("tel:${Constants.SPAIN_PHONE}");
            }
          }),
          buildItem(context, S.of(context).france, onTap: () async {
            if (GlobalConfig.isSunmi || Device.get().isTablet!) {
              ToastUtils.show(S
                  .of(GlobalConfig.navigatorKey.currentContext!)
                  .sunmi_not_support_calls);
            } else {
              tracking(Constants.FRANCE_PHONE);
              launch("tel:${Constants.FRANCE_PHONE}");
            }
          }),
        ],
      ),
    );
  }


  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkResponse(
      borderRadius: new BorderRadius.all(new Radius.circular(30.0)),
      highlightColor: RColors.ink,
      highlightShape: BoxShape.rectangle,
      radius: 0.0,
      containedInkWell: true,
      onTap: () async {
        Navigator.of(context).pop();
        await Future.delayed(Duration(milliseconds: 300));
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_50,
        child: Text(
          title,
          style: TextStyle(
              color: S.of(context).call_customer == title
                  ? Colors.black87
                  : Colors.primaries.first,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
