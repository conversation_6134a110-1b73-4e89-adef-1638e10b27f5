import 'package:connect/provider/report_model.dart';
import 'package:connect/ui/home/<USER>/report_adjustments_item.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../generated/l10n.dart';
import '../../../res/dimens.dart';

class ReportAdjustments extends StatefulWidget {
  ReportAdjustments({Key? key}) : super(key: key);

  @override
  _ReportAdjustmentsState createState() => _ReportAdjustmentsState();
}

class _ReportAdjustmentsState extends State<ReportAdjustments> {
  @override
  Widget build(BuildContext context) {
    var items = context.watch<ReportModel>().adjustList;
    var restaurant = context.watch<ReportModel>().restaurantEntity;
    var country = "";
    if (restaurant != null) {
      country = restaurant.address!.country!;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: Dimens.dp_15),
          alignment: Alignment.centerLeft,
          height: Dimens.dp_40,
          child: Text(
            S.of(context).adjustments,
            style:
                TextStyle(fontSize: Dimens.sp_16, fontWeight: FontWeight.bold),
          ),
        ),
        ListView.builder(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) =>
              ReportAdjustmentsItem(items[index], country),
          itemCount: items.length,
        ),
      ],
    );
  }
}
