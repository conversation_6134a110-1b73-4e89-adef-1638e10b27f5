import 'package:connect/common/t_constants.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/login/login_route.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

class LogOutDialog {
  static show(BuildContext context) async {
    await showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            content: Text(S.of(context).log_out_confirmed),
            actions: [
              TextButton(
                onPressed: () {
                  TrackingUtils.instance!
                      .tracking(TConstants.log_out_cancel_click);
                  Navigator.of(context).pop();
                },
                child: Text(
                  S.of(context).cancel,
                  style: TextStyle(color: RColors.r_orange),
                ),
              ),
              TextButton(
                  onPressed: () async {
                    try {
                      await FirebaseMessaging.instance.deleteToken();
                    } catch (e) {}
                    TrackingUtils.instance!
                        .tracking(TConstants.log_out_ok_click);
                    ConnectCache.saveFirstHistoryTap(true);
                    ConnectCache.saveFirstScheduledTap(true);
                    Future.delayed(Duration(milliseconds: 200)).then((value) {
                      Navigator.of(context).pop();
                      ConnectCache.clearCurrentRoleId();
                      ConnectCache.clearToken();
                      Navigator.pushReplacementNamed(context, LoginRoute.tag);
                    });
                  },
                  child: Text(
                    S.of(context).ok,
                    style: TextStyle(color: Colors.black87),
                  ))
            ],
          );
        });
  }
}
