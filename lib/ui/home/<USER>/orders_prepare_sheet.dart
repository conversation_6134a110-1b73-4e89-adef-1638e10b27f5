import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';

class OrdersPrepareSheet {
  static show(BuildContext context, {ValueChanged<int>? callback}) {
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context, callback);
        },
        context: context);
  }

  static Widget buildBottomSheetWidget(BuildContext context, callback) {
    return Container(
      height: Dimens.dp_370,
      child: Column(
        children: [
          buildItem(context, S.of(context).prepare_time_const),
          Divider(
            height: Dimens.dp_1,
          ),
          Expanded(
            child: ListView(
              children: [
                buildItem(context,
                    sprintf(S.of(context).connect_prepare_time, ["10"]),
                    onTap: () async {
                  if (callback != null) callback(10);
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context,
                    sprintf(S.of(context).connect_prepare_time, ["20"]),
                    onTap: () async {
                  if (callback != null) callback(20);
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context,
                    sprintf(S.of(context).connect_prepare_time, ["30"]),
                    onTap: () async {
                  if (callback != null) callback(30);
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context,
                    sprintf(S.of(context).connect_prepare_time, ["40"]),
                    onTap: () async {
                  if (callback != null) callback(40);
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context,
                    sprintf(S.of(context).connect_prepare_time, ["50"]),
                    onTap: () async {
                  if (callback != null) callback(50);
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context,
                    sprintf(S.of(context).connect_prepare_time, ["60"]),
                    onTap: () async {
                  if (callback != null) callback(60);
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context,
                    sprintf(S.of(context).connect_prepare_time, ["70"]),
                    onTap: () async {
                  if (callback != null) callback(70);
                }),
                Divider(
                  height: Dimens.dp_1,
                ),
                buildItem(context, S.of(context).cancel),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildItem(BuildContext context, String title,
      {Function? onTap}) {
    return InkWell(
      onTap: () async {
        await Future.delayed(Duration(milliseconds: 300));
        Navigator.of(context).pop();
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: Dimens.dp_40,
        child: Text(
          title,
          style: TextStyle(
              color: S.of(context).prepare_time_const == title
                  ? Colors.black87
                  : Colors.blue,
              fontSize: Dimens.sp_16),
        ),
      ),
    );
  }
}
