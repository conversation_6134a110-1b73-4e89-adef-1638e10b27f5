import 'package:connect/data/rest_entity.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class RestContactItem extends StatefulWidget {
  RestContactItem(this.contact, {Key? key}) : super(key: key);

  final RestContacts? contact;

  @override
  _RestContactItemState createState() => _RestContactItemState();
}

class _RestContactItemState extends State<RestContactItem> {
  @override
  Widget build(BuildContext context) {
    var contact = widget.contact!;
    var type = contact.type;
    var content = contact.content;
    bool? report = contact.report;
    bool? order = contact.order;
    return Container(
      padding: const EdgeInsets.all(Dimens.dp_20),
      child: Column(
        children: [
          buildTittle(type??""),
          Card(
            elevation: 0,
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  SizedBox(
                    height: 40,
                    child: Row(
                      children: [
                        Text(type??""),
                        Expanded(
                          child: Text(
                            content??"",
                            textAlign: TextAlign.right,
                            style: TextStyle(color: Colors.grey),
                          ),
                        ),
                      ],
                    ),
                  ),
              buildDivider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(S.of(context).connect_rest_report),
                      toggle(report),
                    ],
                  ),
                  buildDivider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(S.of(context).connect_rest_order),
                      toggle(order),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget toggle(bool? toggle) {
    return Switch(
        value: toggle == null ? false : toggle,
        activeColor: RColors.mr,
        onChanged: (bool) {});
  }
}
