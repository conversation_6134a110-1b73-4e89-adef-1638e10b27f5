import 'package:connect/data/user_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class SwitchRoleItem extends StatefulWidget {
  SwitchRoleItem(this.role, this.selectedId, this.onChange, {Key? key})
      : super(key: key);

  final UserRole? role;

  final String? selectedId;

  final Function? onChange;

  @override
  State<StatefulWidget> createState() => _SwitchRoleItemState();
}

class _SwitchRoleItemState extends State<SwitchRoleItem> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onChange?.call();
      },
      child: Container(
        color: Colors.white,
        height: Dimens.dp_45,
        padding: const EdgeInsets.only(left: Dimens.dp_20, right: Dimens.dp_20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                widget.role?.nameAndDesc() ?? "",
                style: TextStyle(color: Colors.black87, fontSize: Dimens.sp_15),
              ),
            ),
            SizedBox(
              width: Dimens.dp_10,
            ),
            Radio(
                value: widget.role?.sId ?? "",
                groupValue: widget.selectedId,
                onChanged: (onChanged) {
                  widget.onChange?.call();
                }),
          ],
        ),
      ),
    );
  }
}
