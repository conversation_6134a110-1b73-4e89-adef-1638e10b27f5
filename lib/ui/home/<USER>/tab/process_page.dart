import 'dart:async';
import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/data/repository/login_repository.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/http/http_error.dart';
import 'package:connect/provider/orders_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/widget/label.dart';
import 'package:connect/ui/home/<USER>/widget/order_card.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/gif_header.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/audio_manager.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/update_utils.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:provider/provider.dart';

import '../widget/order_card_details.dart';

class ProcessPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => ProcessPageState();
}

class ProcessPageState extends State<ProcessPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;
  Timer? _timer;
  String currentLabel = "";

  void _onRefresh({bool showMsg = true, bool mOption = false}) {
    context.read<OrdersViewModel>().requestOrder(context,
        showMsg: showMsg, option: mOption, error: (code, msg) {
      if (code == HttpError.UNKNOWN) {
        ToastUtils.show(msg);
        _refreshController.refreshCompleted();
      } else if (code == HttpError.UNAUTHORIZED) {
        _timer?.cancel();
      }
    }).whenComplete(() {
      _refreshController.refreshCompleted();
    });
  }

  @override
  void initState() {
    LogUtil.v("ProcessPage::initState");
    super.initState();
    //upgrade app
    if (!GlobalConfig.isSunmi) {
      context.read<DriverModel>().checkAppUpgrade().then((value) {
        UpdateUtils.checkUpdate(context, value);
      });
    }
    //reset index when switch screen orientation
    GlobalConfig.currentTabIndex = 0;
    GlobalConfig.currentLabelIndex = 0;
    //reset items value when fist into orders page
    context.read<OrdersViewModel>().reset();
    //reset auto print
    GlobalConfig.autoConfirmedMapId = [];
    //update token
    LoginRepository.renewToken(context);
    _refreshController = RefreshController(initialRefresh: true);
    //time to refresh
    startTime();

    GlobalConfig.eventBus.on<ResRefreshOrders>().listen((event) async {
      LoadingUtils.show();
      await context.read<OrdersViewModel>().requestOrder(context, option: true);
      LoadingUtils.dismiss();
    });
  }

  void startTime() {
    _timer = Timer.periodic(Duration(seconds: 30), (Timer t) {
      _onRefresh(showMsg: false, mOption: true);
    });
  }

  void cancelTimer() {
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }
  }

  @override
  void dispose() {
    super.dispose();
    LogUtil.v("ProcessPage::dispose");
    cancelTimer();
    _refreshController.dispose();
    AudioManager.instance!.dispose();
  }

  int _itemCount(OrdersViewModel watch) {
    if (currentLabel == S.of(context).wait_confirm_receiving) {
      LogUtil.v(
          "sync itemsWaitConfirm size_2 :: ${watch.itemsWaitConfirm.length}");
      return watch.itemsWaitConfirm.length;
    } else if (currentLabel == S.of(context).wait_chucan_confirmed) {
      return watch.itemsWaitReady.length;
    } else if (currentLabel == S.of(context).wait_courier_come) {
      return watch.itemsWaitCourierCome.length;
    } else {
      return watch.items!.length;
    }
  }

  bool _itemIsEmpty(OrdersViewModel watch) {
    if (currentLabel == S.of(context).wait_confirm_receiving) {
      return watch.itemsWaitConfirm.length == 0;
    } else if (currentLabel == S.of(context).wait_chucan_confirmed) {
      return watch.itemsWaitReady.length == 0;
    } else if (currentLabel == S.of(context).wait_courier_come) {
      return watch.itemsWaitCourierCome.length == 0;
    } else {
      return watch.items!.length == 0;
    }
  }

  syncLabStatus(
      List<OrdersEntity>? allList, List<OrdersEntity?> labList) async {
    if (labList.length > 0) {
      allList!.forEach((element) {
        element.isSelected = false;
      });
      labList[0]!.isSelected = true;
      GlobalConfig.eventBus.fire(ResSyncOrderStatus(labList[0]));
    } else {
      GlobalConfig.eventBus.fire(ResSyncOrderStatus(null));
    }
    // GlobalConfig.eventBus.fire(ResSyncOrderStatus(null));
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("ProcessPage::build");
    var watch = context.watch<OrdersViewModel>();
    if (watch.items == null) {
      watch.items = [];
    }
    super.build(context);
    return Scaffold(
      backgroundColor: MyColors.bg,
      body: Column(
        children: [
          Label((label) {
            setState(() {
              currentLabel = label;
              Orientation orientation = MediaQuery.of(context).orientation;
              if (orientation == Orientation.landscape) {
                if (currentLabel == S.of(context).wait_confirm_receiving) {
                  GlobalConfig.currentLabelIndex = 1;
                  syncLabStatus(watch.items, watch.itemsWaitConfirm);
                } else if (currentLabel ==
                    S.of(context).wait_chucan_confirmed) {
                  GlobalConfig.currentLabelIndex = 2;
                  LogUtil.v(
                      "itemsWaitReady size::${watch.itemsWaitReady.length}");
                  syncLabStatus(watch.items, watch.itemsWaitReady);
                } else if (currentLabel == S.of(context).wait_courier_come) {
                  GlobalConfig.currentLabelIndex = 3;
                  syncLabStatus(watch.items, watch.itemsWaitCourierCome);
                } else {
                  GlobalConfig.currentLabelIndex = 0;
                  syncLabStatus(watch.items, watch.items!);
                }
              }
            });
          }),
          Expanded(
            child: SmartRefresher(
              controller: _refreshController,
              physics: AlwaysScrollableScrollPhysics(),
              header: GifHeader(),
              onRefresh: _onRefresh,
              child: _itemIsEmpty(watch)
                  ? EmptyView(S.of(context).no_pending_orders)
                  : ListView.builder(
                      itemBuilder: (context, index) {
                        if (currentLabel ==
                            S.of(context).wait_confirm_receiving) {
                          return OrderCard(watch.itemsWaitConfirm[index],
                              OrderCardFrom.progress);
                        } else if (currentLabel ==
                            S.of(context).wait_chucan_confirmed) {
                          return OrderCard(watch.itemsWaitReady[index],
                              OrderCardFrom.progress);
                        } else if (currentLabel ==
                            S.of(context).wait_courier_come) {
                          return OrderCard(watch.itemsWaitCourierCome[index],
                              OrderCardFrom.progress);
                        } else {
                          return OrderCard(
                              watch.items![index], OrderCardFrom.progress);
                        }
                      },
                      itemCount: _itemCount(watch),
                    ),
            ),
            flex: 1,
          ),
        ],
      ),
    );
  }
}
