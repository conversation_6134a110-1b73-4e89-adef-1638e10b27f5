import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/http/http_error.dart';
import 'package:connect/provider/history_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/widget/order_card.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/gif_header.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/format_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:provider/provider.dart';
import '../widget/history_date.dart';
import '../widget/order_card_details.dart';

class HistoryPage extends StatefulWidget {
  @override
  _HistoryPageState createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  int count = 1;
  String from = "";
  String to = "";

  late RefreshController _refreshController;

  void _onRefresh() {
    count = 1;
    smartRefresh(count);
  }

  void _onLoading() {
    count++;
    smartRefresh(count);
  }

  void smartRefresh(int page, {bool isShowLoading = false}) {
    LogUtil.v("smartRefresh_from::$from");
    LogUtil.v("smartRefresh_to::$to");
    context.read<HistoryViewModel>().requestHistory(context, from, to, page,
        isShowLoading: isShowLoading, error: (code, msg) {
      if (code == HttpError.UNKNOWN) {
        ToastUtils.show(msg);
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
        if (_refreshController.isLoading) {
          _refreshController.loadComplete();
        }
      }
    }).whenComplete(() {
      LogUtil.v(
          "smartRefresh::${_refreshController.isRefresh},${_refreshController.isLoading}");
      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
      if (_refreshController.isLoading) {
        _refreshController.loadComplete();
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    ConnectCache.saveFirstHistoryTap(true);
  }

  @override
  void initState() {
    LogUtil.v("HistoryPage::initState");
    super.initState();
    context.read<HistoryViewModel>().setItems([]);
    from = formatDate(
            DateTime(
              DateTime.now().year,
              DateTime.now().month,
              DateTime.now().day,
              00,
              00,
              00,
            ),
            [yyyy, '-', mm, '-', dd, 'T', HH, ':', nn, ':', ss]) +
        FormatUtils.format(DateTime.now());
    to = formatDate(DateTime.now(),
            [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
        FormatUtils.format(DateTime.now());
    _refreshController = RefreshController(initialRefresh: true);

    WidgetsBinding.instance!.addPostFrameCallback((_) {
      GlobalConfig.eventBus.on<ResRefreshHistoryOrders>().listen((event) async {
        if (mounted) {
          LogUtil.v("HistoryPage ResRefreshOrders mounted");
          if (!ConnectCache.getFirstHistoryTap()!) {
            LoadingUtils.show();
            await context
                .read<HistoryViewModel>()
                .requestHistory(context, from, to, 1, option: true);
            LoadingUtils.dismiss();
          } else {
            ConnectCache.saveFirstHistoryTap(false);
          }
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("HistoryPage::build");
    var items = context.watch<HistoryViewModel>().items;
    super.build(context);
    return Scaffold(
      backgroundColor: MyColors.bg,
      body: Column(
        children: [
          HistoryDate((selectedFrom, selectedTo) {
            count = 1;
            from = selectedFrom;
            to = selectedTo;
            smartRefresh(count, isShowLoading: true);

            var params = Map<String, dynamic>();
            params["from"] = "$selectedFrom";
            params["to"] = "$selectedTo";
            TrackingUtils.instance!
                .tracking(TConstants.R_HISTORY_DATE_OK_CLICK, value: params);
          }),
          Expanded(
            flex: 1,
            child: SmartRefresher(
              controller: _refreshController,
              enablePullUp: true,
              physics: AlwaysScrollableScrollPhysics(),
              header: GifHeader(),
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              footer: ClassicFooter(
                loadStyle: LoadStyle.ShowWhenLoading,
              ),
              child: items.isEmpty
                  ? EmptyView(S.of(context).no_history_orders)
                  : ListView.builder(
                      itemBuilder: (context, index) =>
                          OrderCard(items[index], OrderCardFrom.history),
                      itemCount: items.length,
                    ),
            ),
          )
        ],
      ),
    );
  }
}
