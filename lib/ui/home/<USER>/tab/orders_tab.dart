import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/provider/history_model.dart';
import 'package:connect/provider/orders_model.dart';
import 'package:connect/provider/pre_order_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/tab/process_page.dart';
import 'package:connect/ui/home/<USER>/tab/scheduled_page.dart';
import 'package:connect/ui/view/device_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'history_page.dart';
import 'package:provider/provider.dart';

class OrdersTab extends StatefulWidget {
  OrdersTab({Key? key}) : super(key: key);

  @override
  _OrdersTabState createState() => _OrdersTabState();
}

class _OrdersTabState extends State<OrdersTab>
    with SingleTickerProviderStateMixin {
  List tabs = [];
  late TabController _controller;

  buildSecondaryMenu() {
    tabs = [
      S.of(context).process_order,
      S.of(context).history_order,
      S.of(context).pre_order
    ];
  }

  @override
  void initState() {
    super.initState();
    _controller = TabController(
      length: 3,
      vsync: this,
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    LogUtil.v("orders_tab didChangeDependencies");
    Orientation orientation = MediaQuery.of(context).orientation;
    if (orientation == Orientation.landscape && DeviceUtils.isTablet()) {
      //tablet
      _controller.addListener(() {
        if (_controller.index == _controller.animation!.value) {
          GlobalConfig.currentTabIndex = _controller.index;
          LogUtil.v(
              "orders_tab currentTabIndex:${GlobalConfig.currentTabIndex}");
          switch (_controller.index) {
            case 0:
              OrdersViewModel model = context.read<OrdersViewModel>();
              model.items = [];
              GlobalConfig.eventBus.fire(ResSyncOrderStatus(null));
              GlobalConfig.eventBus.fire(ResRefreshOrders());
              break;
            case 1:
              HistoryViewModel model = context.read<HistoryViewModel>();
              model.items = [];
              GlobalConfig.eventBus.fire(ResSyncOrderStatus(null));
              GlobalConfig.eventBus.fire(ResRefreshHistoryOrders());
              break;
            case 2:
              PreOrderViewModel model = context.read<PreOrderViewModel>();
              model.items = [];
              GlobalConfig.eventBus.fire(ResSyncOrderStatus(null));
              GlobalConfig.eventBus.fire(ResRefreshScheduledOrders());
              break;
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    buildSecondaryMenu();
    return Scaffold(
      appBar: AppBar(
        elevation: 0.0,
        backgroundColor: MyColors.W,
        flexibleSpace: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TabBar(
              controller: _controller,
              labelColor: MyColors.color333,
              labelStyle: MyStyles.m12,
              unselectedLabelColor: MyColors.color666,
              indicatorColor: MyColors.portal,
              tabs: tabs
                  .map((e) => Tab(
                        child: Text(e),
                      ))
                  .toList(),
            )
          ],
        ),
      ),
      body: TabBarView(
        controller: _controller,
        children: [
          ProcessPage(),
          HistoryPage(),
          ScheduledPage(),
        ],
      ),
    );
  }
}
