import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/http/http_error.dart';
import 'package:connect/provider/pre_order_model.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/home/<USER>/widget/order_card.dart';
import 'package:connect/ui/home/<USER>/widget/pre_order_date.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/gif_header.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/format_utils.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:date_format/date_format.dart' hide S;
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:provider/provider.dart';

import '../widget/order_card_details.dart';

class ScheduledPage extends StatefulWidget {
  @override
  _ScheduledPageState createState() => _ScheduledPageState();
}

class _ScheduledPageState extends State<ScheduledPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  late RefreshController _refreshController;
  bool isExpanded = false;
  String from = "";
  String to = "";

  @override
  void dispose() {
    super.dispose();
    ConnectCache.saveFirstScheduledTap(true);
  }

  @override
  void initState() {
    LogUtil.v("ScheduledPage::initState");
    super.initState();
    context.read<PreOrderViewModel>().setItems([]);
    from = formatDate(DateTime.now(),
            [yyyy, '-', mm, '-', dd, 'T', HH, ':', nn, ':', ss]) +
        FormatUtils.format(DateTime.now());
    to = formatDate(DateTime.now(),
            [yyyy, '-', mm, '-', dd, 'T', '23', ':', '59', ':', '59']) +
        FormatUtils.format(DateTime.now());
    _refreshController = RefreshController(initialRefresh: true);

    WidgetsBinding.instance!.addPostFrameCallback((_) {
      GlobalConfig.eventBus
          .on<ResRefreshScheduledOrders>()
          .listen((event) async {
        if (mounted) {
          if (!ConnectCache.getFirstScheduledTap()!) {
            LoadingUtils.show();
            await smartRefresh(isShowLoading: false, option: true);
            LoadingUtils.dismiss();
          } else {
            ConnectCache.saveFirstScheduledTap(false);
          }
        }
      });
    });
  }

  void _onRefresh() {
    smartRefresh(isShowLoading: false);
  }

  Future smartRefresh({bool isShowLoading = true, bool option = false}) async {
    await context.read<PreOrderViewModel>().requestPreOrder(
        context, isExpanded, from, to,
        isShowLoading: isShowLoading, option: option, error: (code, msg) {
      if (code == HttpError.UNKNOWN) {
        ToastUtils.show(msg);
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
      }
    }).whenComplete(() {
      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var items = context.watch<PreOrderViewModel>().items;
    super.build(context);
    return Scaffold(
      backgroundColor: MyColors.bg,
      body: Column(
        children: [
          PreOrderDate((selectedFrom, selectedTo) {
            from = selectedFrom;
            to = selectedTo;
            _refreshController.refreshCompleted();
            smartRefresh(isShowLoading: true);

            var params = Map<String, dynamic>();
            params["from"] = "$selectedFrom";
            params["to"] = "$selectedTo";
            TrackingUtils.instance!
                .tracking(TConstants.R_PREORDER_DATE_CLICK, value: params);
          }),
          Expanded(
            flex: 1,
            child: SmartRefresher(
              controller: _refreshController,
              physics: AlwaysScrollableScrollPhysics(),
              header: GifHeader(),
              enablePullUp: false,
              onRefresh: _onRefresh,
              footer: ClassicFooter(
                loadStyle: LoadStyle.ShowWhenLoading,
              ),
              child: items.isEmpty
                  ? EmptyView(S.of(context).no_pending_orders)
                  : ListView.builder(
                      itemBuilder: (context, index) =>
                          OrderCard(items[index], OrderCardFrom.scheduled),
                      itemCount: items.length,
                    ),
            ),
          )
        ],
      ),
    );
  }
}
