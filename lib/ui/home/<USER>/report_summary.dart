import 'package:connect/data/summary_report_entity.dart';
import 'package:connect/provider/report_model.dart';
import 'package:connect/utils/log_util.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import 'package:sprintf/sprintf.dart';
import '../../../generated/l10n.dart';
import '../../../res/dimens.dart';
import '../../../utils/server_multi_lan.dart';

class ReportSummary extends StatefulWidget {
  ReportSummary(this.title, {Key? key}) : super(key: key);

  final String title;

  @override
  _ReportSummaryState createState() => _ReportSummaryState();
}

class _ReportSummaryState extends State<ReportSummary> {
  List<Widget> _summaryWidget = [];
  List<Widget> _commissionWidget = [];
  Map<String, num> _commissionMap = {};
  Map<String, num?> _summaryMap = {};

  //customer payment
  late num orderCount;
  late num subtotal;
  num? averageOrderSubtotal;
  late num reachMinimum;
  late num tax;
  late num ricepoTax;
  late num deliveryFee;
  late num tip;
  late num serviceFee;
  late num creditCardFee;
  late num subtotalTax;
  late num total;

  //restaurant payment
  late num commission;
  late num commissionTotal;
  late num commissionService;
  late num adjustmentsRestaurant;

  //total benefit
  late num totalBenefit;

  String country = "";

  _summaryMapMethod() {
    _summaryMap.clear();
    _summaryWidget.clear();
    LogUtil.v("subtotal::$subtotal,orderCount::$orderCount");
    var stringAsFixed = ((subtotal / orderCount) / 100).toStringAsFixed(3);
    if (stringAsFixed != "NaN") {
      averageOrderSubtotal = num.parse(
          stringAsFixed.substring(0, stringAsFixed.lastIndexOf(".") + 3));
    }
    if (provider == null) {
      _summaryMap = {
        S.of(context).summary_order_count: orderCount,
        S.of(context).summary_subtotal: subtotal / 100,
        S.of(context).summary_average_order_subtotal: averageOrderSubtotal,
        S.of(context).summary_reach_minimum: reachMinimum / 100,
        S.of(context).summary_tax: tax / 100,
        S.of(context).summary_delivery_fee: deliveryFee / 100,
        S.of(context).summary_tip: tip / 100,
        S.of(context).summary_service_fee: serviceFee / 100,
        S.of(context).summary_credit_card_fee: creditCardFee / 100,
        S.of(context).summary_subtotal_tax: subtotalTax / 100
      };
    } else {
      _summaryMap = {
        S.of(context).summary_order_count: orderCount,
        S.of(context).summary_subtotal: subtotal / 100,
        S.of(context).summary_average_order_subtotal: averageOrderSubtotal,
        S.of(context).summary_tax: tax / 100,
        S.of(context).summary_service_fee: serviceFee / 100,
        S.of(context).summary_credit_card_fee: creditCardFee / 100,
        S.of(context).summary_subtotal_tax: subtotalTax / 100
      };
    }
    _summaryMap.forEach((key, value) {
      _summaryWidget.add(_buildItem(key, value));
    });

    _commissionMap.clear();
    _commissionWidget.clear();
    _commissionMap = {
      S.of(context).summary_commission: commission / 100,
      S.of(context).summary_total_based_commission: commissionTotal / 100,
      S.of(context).summary_service_fee: commissionService / 100,
      S.of(context).summary_adjustments: adjustmentsRestaurant / 100
    };

    _commissionMap.forEach((key, value) {
      _commissionWidget.add(_buildCommissionItem(key, value));
    });
  }

  _summaryValueReset() {
    orderCount = 0;
    subtotal = 0;
    averageOrderSubtotal = 0;
    reachMinimum = 0;
    tax = 0;
    ricepoTax = 0;
    deliveryFee = 0;
    tip = 0;
    serviceFee = 0;
    creditCardFee = 0;
    subtotalTax = 0;
    total = 0;

    commission = 0;
    commissionTotal = 0;
    commissionService = 0;
    adjustmentsRestaurant = 0;

    totalBenefit = 0;
  }

  _summaryValueMethod(List<SummaryReportEntity> summaryList) {
    _summaryValueReset();
    summaryList.forEach((element) {
      if (element.number != null) orderCount += element.number!;
      if (element.subtotal != null) subtotal += element.subtotal!;
      if (element.fees != null) {
        if (element.fees!.delta != null) reachMinimum += element.fees!.delta!;
        if (element.fees!.tax != null) tax += element.fees!.tax!;
        if (element.fees!.delivery != null)
          deliveryFee += element.fees!.delivery!;
        if (element.fees!.tip != null && element.fees!.tip!.amount != null)
          tip += element.fees!.tip!.amount!;
        if (element.fees!.service != null) serviceFee += element.fees!.service!;
        if (element.fees!.credit != null)
          creditCardFee += element.fees!.credit!;

        if (element.subtotal != null && element.fees!.tax != null) {
          subtotalTax += (element.subtotal! + element.fees!.tax!);
        }
      }

      if (element.total != null) total += element.total!;

      if (element.commission != null) {
        SummaryReportCommission commissionBean = element.commission!;
        commission += commissionBean.subtotal!;
        commissionTotal += commissionBean.total!;
        commissionService += commissionBean.service!;
      }

      if (element.adjustments != null) {
        adjustmentsRestaurant += element.adjustments!.restaurant!;
      }

      if (element.distribution != null &&
          element.distribution!.restaurant != null) {
        totalBenefit += element.distribution!.restaurant!;

        if (element.distribution!.ricepo != null &&
            element.distribution!.ricepo!.tax != null) {
          ricepoTax += element.distribution!.ricepo!.tax!;
        }
      }
    });
  }

  var provider;

  @override
  Widget build(BuildContext context) {
    var watch = context.watch<ReportModel>();
    if (watch.restaurantEntity != null &&
        watch.restaurantEntity!.address != null &&
        watch.restaurantEntity!.address!.country != null) {
      country = watch.restaurantEntity!.address!.country!;
      provider = watch.restaurantEntity!.delivery!.provider;
    }
    _summaryValueMethod(watch.summaryList);
    _summaryMapMethod();
    return Container(
      padding: const EdgeInsets.fromLTRB(
          Dimens.dp_15, Dimens.dp_5, Dimens.dp_15, Dimens.dp_0),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            height: Dimens.dp_40,
            child: Text(
              widget.title,
              style: TextStyle(
                  fontSize: Dimens.sp_16, fontWeight: FontWeight.bold),
            ),
          ),
          Column(
            children: _summaryWidget,
          ),
          _buildTotal(),
          Column(
            children: _commissionWidget,
          ),
          _buildRevenue(),
        ],
      ),
    );
  }

  Widget _buildItem(String name, num? value) {
    return Column(
      children: [
        _buildRow(name, value),
        SizedBox(
          height: Dimens.dp_4,
        ),
      ],
    );
  }

  Widget _buildTotal() {
    String coinSymbolValue =
        ServerMultiLan.coinSymbolCountry(country, total / 100);
    return Container(
      alignment: Alignment.centerLeft,
      height: Dimens.dp_50,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                S.of(context).summary_total,
                style:
                    TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
              ),
              Text(total >= 0 ? coinSymbolValue : "($coinSymbolValue)"),
            ],
          ),
          SizedBox(
            height: Dimens.dp_10,
          ),
          Divider(
            color: Colors.grey[200],
            thickness: Dimens.dp_1,
            height: Dimens.dp_1,
          )
        ],
      ),
    );
  }

  Widget _buildCommissionItem(String name, num value) {
    return Column(
      children: [
        _buildRow(name, value),
        SizedBox(
          height: Dimens.dp_4,
        ),
      ],
    );
  }

  Widget _buildRow(String name, num? value) {
    String coinSymbolValue = ServerMultiLan.coinSymbolCountry(country, value);
    return S.of(context).summary_tax != name || ricepoTax == 0
        ? Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(name),
              S.of(context).summary_order_count == name
                  ? Text("${value!.toInt()}")
                  : Text(value! >= 0 ? coinSymbolValue : "($coinSymbolValue)"),
            ],
          )
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(name),
                  S.of(context).summary_order_count == name
                      ? Text("${value!.toInt()}")
                      : Text(
                          value! >= 0 ? coinSymbolValue : "($coinSymbolValue)"),
                ],
              ),
              Text(
                sprintf(S.of(context).summary_ricepo_tax, [
                  ServerMultiLan.coinSymbolCountry(country, ricepoTax / 100)
                ]),
                style: TextStyle(fontSize: 12, color: Colors.red),
              )
            ],
          );
  }

  Widget _buildRevenue() {
    String coinSymbolValue =
        ServerMultiLan.coinSymbolCountry(country, totalBenefit / 100);
    return Container(
      alignment: Alignment.centerLeft,
      height: Dimens.dp_50,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            S.of(context).summary_revenue,
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          Text(totalBenefit >= 0 ? coinSymbolValue : "($coinSymbolValue)"),
        ],
      ),
    );
  }
}
