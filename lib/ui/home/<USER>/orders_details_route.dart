import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/common/t_constants.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/provider/orders_details_model.dart';
import 'package:connect/ui/home/<USER>/widget/order_card_details.dart';
import 'package:connect/ui/view/empty_view.dart';
import 'package:connect/ui/view/gif_header.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:provider/provider.dart';

class OrdersDetailsRoute extends StatefulWidget {
  static String tag = "orders_details_route";

  OrdersDetailsRoute({Key? key}) : super(key: key);

  @override
  _OrdersDetailsRouteState createState() => _OrdersDetailsRouteState();
}

class _OrdersDetailsRouteState extends State<OrdersDetailsRoute> {
  late RefreshController _refreshController;
  OrdersEntity? entity;
  OrdersEntity? data;

  void _onRefresh() {
    if (entity != null) {
      LogUtil.v("_onRefresh::${entity!.orderId}");
      // SmartDialog.showLoading(msg: "loading", background: Colors.black54);
      OrdersDetailsModel.orderDetails(entity!.orderId)
          .then((value) {
            if (!mounted) return;
            if (value != null) {
              setState(() {
                data = JsonConvert.fromJsonAsT<OrdersEntity>(value.data);
                //keep the same object
                entity!.orderId = data!.orderId;
                entity!.comments = data!.comments;
                entity!.count = data!.count;
                entity!.passcode = data!.passcode;
                entity!.passcodeExt = data!.passcodeExt;
                entity!.items = data!.items;
                entity!.fees = data!.fees;
                entity!.region = data!.region;
                entity!.customer = data!.customer;
                entity!.restaurant = data!.restaurant;
                entity!.delivery = data!.delivery;
                entity!.distribution = data!.distribution;
                entity!.adj = data!.adj;
                entity!.adjustments = data!.adjustments;
                entity!.status = data!.status;
                entity!.createdAt = data!.createdAt;
                entity!.updatedAt = data!.updatedAt;
                entity!.confirmedAt = data!.confirmedAt;
                entity!.subtotal = data!.subtotal;
                entity!.doubt = data!.doubt;
              });
            }
          })
          .catchError((e) {})
          .whenComplete(() {
            if (_refreshController.isRefresh)
              _refreshController.refreshCompleted();
          });
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<OrdersDetailsModel>().ordersEntity = null;
    _refreshController = RefreshController(initialRefresh: true);

    TrackingUtils.instance!.tracking(TConstants.R_DETAILS_ORDER_ENTER);
  }

  @override
  void dispose() {
    super.dispose();
    GlobalConfig.eventBus.fire(SearchRefreshOrders());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    entity = ModalRoute.of(context)!.settings.arguments as OrdersEntity;
  }

  @override
  Widget build(BuildContext context) {
    //cancel order will trigger here
    var ordersEntity = context.watch<OrdersDetailsModel>().ordersEntity;
    if (ordersEntity != null) {
      entity = ordersEntity;
    }
    return Scaffold(
      backgroundColor: MyColors.bg,
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: Colors.black87,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
        elevation: 0,
        title: Text(ServerMultiLan.multiAdapt2(entity!.restaurant!.name),
            style: TextStyle(color: Colors.black87)),
        backgroundColor: Colors.white,
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SmartRefresher(
              controller: _refreshController,
              physics: AlwaysScrollableScrollPhysics(),
              header: GifHeader(),
              onRefresh: _onRefresh,
              child: data == null
                  ? EmptyView(S.of(context).no_data)
                  : ListView.builder(
                      // itemScrollController: itemScrollController,
                      itemBuilder: (context, index) {
                        return OrderCardDetails(entity, OrderCardFrom.portrait);
                      },
                      itemCount: 1,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
