import 'package:connect/provider/report_model.dart';
import 'package:connect/utils/date_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../data/orders_entity.dart';
import '../../../res/dimens.dart';
import '../../../utils/server_multi_lan.dart';
import 'package:timezone/timezone.dart' as tz;

class ReportAdjustmentsItem extends StatefulWidget {
  ReportAdjustmentsItem(this.entity, this.country, {Key? key})
      : super(key: key);

  final OrdersEntity entity;
  final String country;

  @override
  _ReportAdjustmentsItemState createState() => _ReportAdjustmentsItemState();
}

class _ReportAdjustmentsItemState extends State<ReportAdjustmentsItem> {
  var formatDate;

  @override
  void initState() {
    super.initState();
    formatDate = "";
  }

  @override
  Widget build(BuildContext context) {
    var restaurant = context.watch<ReportModel>().restaurantEntity;
    if (restaurant != null) {
      var detroit = tz.getLocation("${restaurant.timezone}");
      tz.setLocalLocation(detroit);
      var dateTime = tz.TZDateTime.from(
          DateUtil.getDateTime(widget.entity.createdAt!)!, detroit);
      formatDate = DateUtil.formatDate(dateTime, format: "MM/dd/yy HH:mm");
    }
    return Container(
      padding: const EdgeInsets.fromLTRB(
          Dimens.dp_15, Dimens.dp_10, Dimens.dp_15, Dimens.dp_10),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Container(
              child: Text.rich(TextSpan(children: [
                TextSpan(
                  text: "${widget.entity.passcode}  ",
                  style: TextStyle(
                    color: Colors.grey,
                  ),
                ),
                TextSpan(
                  text: ServerMultiLan.multiAdapt2(
                      widget.entity.restaurant!.name),
                  style: TextStyle(color: Colors.black),
                ),
                TextSpan(
                    text: "   ${widget.entity.adjustments!.reason}   ",
                    style: TextStyle(color: Colors.grey)),
                TextSpan(
                    text: "$formatDate", style: TextStyle(color: Colors.grey)),
              ])),
            ),
          ),
          Row(
            children: [
              SizedBox(
                width: Dimens.dp_15,
              ),
              Text(
                  widget.entity.adjustments!.restaurant! > 0
                      ? ServerMultiLan.coinSymbolCountry(widget.country,
                          widget.entity.adjustments!.restaurant! / 100)
                      : "(${ServerMultiLan.coinSymbolCountry(widget.country, widget.entity.adjustments!.restaurant! / 100)})",
                  style: TextStyle(color: Colors.grey)),
            ],
          )
        ],
      ),
    );
  }
}
