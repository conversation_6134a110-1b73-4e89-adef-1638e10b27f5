import 'package:connect/data/rest_menu_entity.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/home/<USER>/menu/rest_menu_sub_item.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/utils/server_multi_lan.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';

const _headerHeight = 80.0;
const _itemHeight = 108.0;
const _leftItemHeight = 57.0;
const _leftItemWidth = 80.0;

class DoubleScrollView extends StatefulWidget {
  final List<RestMenuCategories?> list;

  const DoubleScrollView(this.list, {Key? key}) : super(key: key);

  @override
  State<DoubleScrollView> createState() => _DoubleScrollViewState();
}

class _DoubleScrollViewState extends State<DoubleScrollView> {
  late int pageCount;

  final ScrollController scrollController = ScrollController();
  final ScrollController tabController = ScrollController();

  final ValueNotifier<int> pageIndex = ValueNotifier(0);

  final List<int> groupStartPositions = [];

  @override
  void initState() {
    pageCount = widget.list.length;
    var curHeight = 0;
    widget.list.forEach((element) {
      groupStartPositions.add(curHeight);
      curHeight =
          (curHeight + _headerHeight + _itemHeight * element!.food!.length)
              .toInt();
    });
    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          Container(
            width: _leftItemWidth,
            child: ValueListenableBuilder(
              valueListenable: pageIndex,
              builder: (BuildContext context, int value, Widget? child) {
                return buildLeftItem(value);
              },
            ),
          ),
          Expanded(
            child: CustomScrollView(
              controller: scrollController,
              // cacheExtent: 0,
              slivers: [
                for (int pIndex = 0; pIndex < widget.list.length; pIndex++) ...[
                  buildGroup(pIndex)
                ]
              ],
            ),
          ),
        ],
      ),
    );
  }

  void tabJumpToTop() {
    if (tabController.hasClients) {
      final position = tabController.position;
      final center = (pageIndex.value * _leftItemHeight)
          .clamp(position.minScrollExtent, position.maxScrollExtent);
      tabController.animateTo(center.toDouble(),
          duration: Duration(milliseconds: 500), curve: Curves.ease);
    }
  }

  Widget buildLeftItem(int value) {
    return ListView.builder(
      itemCount: pageCount,
      itemExtent: _leftItemHeight,
      controller: tabController,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            if (pageIndex.value == index) {
              return;
            }
            scrollController.jumpTo(groupStartPositions[index].toDouble());
            pageIndex.value = index;
          },
          child: Container(
            color: value == index ? Colors.white : Colors.grey[200],
            padding: EdgeInsets.all(7),
            alignment: Alignment.center,
            child: Text(
              ServerMultiLan.multiAdapt2(widget.list[index]!.name!),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
      },
    );
  }

  Widget buildGroup(int pIndex) {
    return SliverStickyHeader(
      key: ValueKey(pIndex),
      header: Container(child: buildHeader(widget.list[pIndex]!)),
      sliver: SliverFixedExtentList(
        itemExtent: _itemHeight,
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (scrollController.position.hasContentDimensions &&
                scrollController.position.isScrollingNotifier.value) {
              WidgetsBinding.instance?.addPostFrameCallback((timeStamp) {
                tabJumpToTop();
                pageIndex.value = pIndex;
              });
            }
            return Container(
              height: _itemHeight,
              alignment: Alignment.center,
              child: RestMenuSubItem(widget.list[pIndex]!.food![index]!),
            );
          },
          childCount: widget.list[pIndex]!.food!.length,
        ),
      ),
    );
  }

  Widget buildHeader(RestMenuCategories categories) {
    var availableFoods = categories.food!
        .where((element) => element!.available == true)
        .toList();
    return Container(
      height: _headerHeight,
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.symmetric(horizontal: 16),
      color: Colors.white,
      alignment: Alignment.centerLeft,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${ServerMultiLan.multiAdapt2(categories.name)} (${availableFoods.length} / ${categories.food!.length})",
            style: TextStyle(fontSize: Dimens.sp_16),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(
            height: Dimens.dp_3,
          ),
          Text(
            ServerMultiLan.multiAdapt2(categories.name!),
            style: TextStyle(color: Colors.grey),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(
            height: Dimens.dp_3,
          ),
          categories.description == null
              ? EmptyContainer()
              : Text(
                  ServerMultiLan.multiAdapt2(categories.description),
                  style: TextStyle(color: Colors.grey),
                ),
        ],
      ),
    );
  }
}
