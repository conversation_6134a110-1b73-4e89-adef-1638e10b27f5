import 'dart:async';
import 'package:connect/common/global.dart';
import 'package:connect/data/repository/login_repository.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/provider/login_provider.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/button_style_manager.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/toast_utils.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sprintf/sprintf.dart';

class TimerCountDown extends StatefulWidget {
  final Function? onTimerFinish;
  final Function? onSuccess;

  TimerCountDown({this.onTimerFinish, this.onSuccess}) : super();

  @override
  State<StatefulWidget> createState() => TimerCountDownWidgetState();
}

class TimerCountDownWidgetState extends State<TimerCountDown> {
  Timer? _timer;
  int _countdownTime = 0;

  @override
  void initState() {
    super.initState();
    GlobalConfig.eventBus.on<ResetSmsCode>().listen((event) {
      setState(() {
        _timerDismiss();
        _countdownTime = 0;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity:
          (Provider.of<LoginProvider>(context, listen: false).isValidPhone &&
                  _countdownTime == 0)
              ? 1
              : 0.5,
      child: Container(
        child: ElevatedButton(
          style: ButtonStyleManager.buttonStyle(),
          child: Text(
            _countdownTime > 0
                ? sprintf(S.of(context).vc_try_again, ["$_countdownTime"])
                : S.of(context).send_code,
            style: TextStyle(color: Colors.white, fontSize: Dimens.sp_16),
            textAlign: TextAlign.center,
          ),
          onPressed: _codeTap,
        ),
      ),
    );
  }

  _codeTap() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult == ConnectivityResult.none) {
      ToastUtils.show(S.of(context).network_check);
      return;
    }
    if (Provider.of<LoginProvider>(context, listen: false).isValidPhone &&
        _countdownTime == 0) {
      //start countdown
      String realPhone =
          Provider.of<LoginProvider>(context, listen: false).regionCode +
              Provider.of<LoginProvider>(context, listen: false).phone;
      LoginRepository.requestSmsCode(realPhone).then((value) {
        LogUtil.v("sms::$value");
        widget.onSuccess!();
        setState(() {
          _countdownTime = 30;
        });
        startCountdownTimer();
      });
    } else {
      return null;
    }
  }

  void startCountdownTimer() {
    _timer = Timer.periodic(
        Duration(seconds: 1),
        (Timer timer) => {
              setState(() {
                if (_countdownTime == 0) {
                  widget.onTimerFinish!();
                  _timer!.cancel();
                } else {
                  _countdownTime = _countdownTime - 1;
                }
              })
            });
  }

  @override
  void dispose() {
    super.dispose();
    _timerDismiss();
  }

  _timerDismiss() {
    if (_timer != null) {
      _timer!.cancel();
    }
  }
}
