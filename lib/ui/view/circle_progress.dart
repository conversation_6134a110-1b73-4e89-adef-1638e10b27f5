import 'package:connect/res/dimens.dart';
import 'package:flutter/material.dart';

class CircleProgress extends StatelessWidget {
  CircleProgress({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(
        left: Dimens.dp_10,
      ),
      width: Dimens.dp_15,
      height: Dimens.dp_15,
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation(Colors.white),
        strokeWidth: 2.0,
      ),
    );
  }
}
