import 'package:connect/common/global.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class MessageDialog {
  static messageAlert(String? message, {VoidCallback? ok}) {
    androidWidget(message, ok);
  }

  static androidWidget(String? message, ok) {
    showDialog(
        context: GlobalConfig.navigatorKey.currentContext!,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Text(message!),
            actions: [
              TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    Future.delayed(Duration(milliseconds: 300)).then((value) {
                      if (ok != null) ok();
                    });
                  },
                  child: Text(
                    S.of(context).ok,
                    style: TextStyle(color: RColors.r_orange),
                  ))
            ],
          );
        });
  }

  static iosWidget(BuildContext context, String message) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            content: Text(message),
            actions: [
              CupertinoDialogAction(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    S.of(context).ok,
                    style: TextStyle(color: RColors.r_orange),
                  ))
            ],
          );
        });
  }
}
