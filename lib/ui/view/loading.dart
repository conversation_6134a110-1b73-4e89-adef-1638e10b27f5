import 'package:connect/common/assets_path.dart';
import 'package:connect/common/global.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/ui/view/arg_progress_dialog.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';

class LoadingUtils {
  static ArsProgressDialog? _customProgressDialog;

  static createDialog() {
    _customProgressDialog =
        ArsProgressDialog(GlobalConfig.navigatorKey.currentContext!,
            backgroundColor: Color(0x33000000),
            dismissable: false,
            loadingWidget: Container(
              width: Dimens.dp_60,
              height: Dimens.dp_60,
              child: Image.asset(
                AssetsPath.LOADING_FASTER,
              ),
            ));
  }

  static show() {
    if (_customProgressDialog == null) {
      createDialog();
    }
    if (_customProgressDialog != null) {
      LogUtil.v("show");
      _customProgressDialog?.show();
    }
  }

  static dismiss() {
    if (_customProgressDialog != null) {
      LogUtil.v("dismiss");
      _customProgressDialog?.dismiss();
    }
  }

  static bool isShowing() {
    if (_customProgressDialog != null) {
      return _customProgressDialog!.isShowing;
    }
    return false;
  }
}
