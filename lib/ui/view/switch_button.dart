import 'dart:io';

import 'package:connect/res/colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class SwitchButton extends StatefulWidget {
  SwitchButton(this.switchCallback, this._switchValue, {Key? key})
      : super(key: key);

  final ValueChanged switchCallback;
  final bool? _switchValue;

  @override
  _SwitchButtonState createState() => _SwitchButtonState();
}

class _SwitchButtonState extends State<SwitchButton> {
  bool? value = true;

  @override
  void initState() {
    super.initState();
    value = widget._switchValue;
  }

  @override
  Widget build(BuildContext context) {
    return _buildSwitch();
  }

  Widget _buildSwitch() {
    if (Platform.isIOS) {
      return CupertinoSwitch(
          value: value!,
          activeColor: RColors.r_orange,
          onChanged: (bool) {
            change(bool);
          });
    } else {
      return Switch(
          value: value!,
          activeColor: RColors.r_orange,
          onChanged: (bool) {
            change(bool);
          });
    }
  }

  change(bool b) {
    setState(() {
      value = b;
      widget.switchCallback(b);
    });
  }
}
