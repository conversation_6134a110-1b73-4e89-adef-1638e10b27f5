import 'package:connect/common/constants.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class LoginHelp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return _buildLoginHelp(context);
  }

  Widget _buildLoginHelp(BuildContext context) {
    return Container(
        margin: const EdgeInsets.only(
            top: Dimens.dp_50, left: Dimens.dp_40, right: Dimens.dp_40),
        child: Text.rich(
          TextSpan(children: [
            TextSpan(
              text: S.of(context).connect_help,
              style: TextStyle(color: Colors.grey, fontSize: Dimens.sp_13),
            ),
            TextSpan(
                text: S.of(context).connect_help_email,
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: Dimens.sp_13,
                  decoration: TextDecoration.underline,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    try {
                      launch("mailto:" + Constants.EMAIL);
                    } catch (e) {}
                  }),
          ]),
          textAlign: TextAlign.center,
        ));
  }
}
