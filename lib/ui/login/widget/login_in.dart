import 'package:connect/common/t_constants.dart';
import 'package:connect/data/repository/login_repository.dart';
import 'package:connect/provider/login_provider.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/tracking_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class LoginIn extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => LoginInState();
}

class LoginInState extends State<LoginIn> {
  @override
  Widget build(BuildContext context) {
    // LogUtil.v("LoginIn::build");
    return _buildLoginButton(context);
  }

  Widget _buildLoginButton(BuildContext context) {
    return Opacity(
      opacity: context.watch<LoginProvider>().tabIndex == 0
          ? (context.watch<LoginProvider>().loginHighlightByPassword())
              ? 1
              : 0.5
          : (context.watch<LoginProvider>().loginHighlightByPhone())
              ? 1
              : 0.5,
      child: Container(
          margin: const EdgeInsets.fromLTRB(
              Dimens.dp_20, Dimens.dp_0, Dimens.dp_20, Dimens.dp_0),
          width: double.infinity,
          alignment: Alignment.center,
          child: SizedBox(
            width: double.infinity,
            height: Dimens.dp_40,
            child: ElevatedButton(
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(RColors.r_orange),
                shape: MaterialStateProperty.all(RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(Dimens.dp_20))),
              ),
              child: Text(S.of(context).log_in,
                  style:
                      TextStyle(color: Colors.white, fontSize: Dimens.sp_16)),
              onPressed: _loginClickable,
            ),
          )),
    );
  }

  _loginClickable() {
    var read = context.read<LoginProvider>();
    if (read.tabIndex == 0) {
      return read.loginHighlightByPassword() ? _showLoading(read) : null;
    } else {
      return read.loginHighlightByPhone() ? _showLoading(read) : null;
    }
  }

  _showLoading(LoginProvider read) {
    FocusScope.of(context).requestFocus(FocusNode());
    _login(read);
  }

  _login(LoginProvider read) {
    var params = Map<String, dynamic>();
    if (read.tabIndex == 0) {
      params["loginType"] = "password";
      params["userName"] = "${read.email}";
      LoginRepository.loginWithPassword(context, read.email, read.password);
    } else {
      params["loginType"] = "phone";
      params["userName"] = "${read.regionCode + read.phone}";
      LoginRepository.loginWithPhone(
          context, read.regionCode + read.phone, read.smsCode);
    }
    TrackingUtils.instance!
        .tracking(TConstants.R_MENU_OPTION_UPDATE_ALL_CLICK, value: params);
  }
}
