import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/provider/login_provider.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/view/timer_count_down.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';

class LoginWithPhone extends StatefulWidget {
  LoginWithPhone(this.notifier, {Key? key}) : super(key: key);

  final LoginProvider notifier;

  @override
  _LoginWithPhoneState createState() => _LoginWithPhoneState();
}

class _LoginWithPhoneState extends State<LoginWithPhone>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  VoidCallback? codeEvent;
  // late FlutterLibphonenumber libPhone;
  final TextEditingController _smsController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  FocusNode _phoneFocus = FocusNode();
  FocusNode _smsFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    // libPhone = FlutterLibphonenumber();
    // libPhone.init();

    WidgetsBinding.instance!.addPostFrameCallback((_) {
      widget.notifier.setPhoneNodeFocus(_phoneFocus);

      //init phone value
      var user = ConnectCache.getUser();
      if (user != null &&
          Constants.PHONE == user.loginType &&
          user.phone != null &&
          user.phone!.isNotEmpty) {
        List regionCodes = ["+1", "+86", "+33", "+34", "+44"];
        regionCodes.forEach((element) {
          if (user.phone!.contains(element)) {
            widget.notifier.setRegionCode(element);
            var cachePhone = user.phone!.split(element).last;
            widget.notifier.setPhone(cachePhone);
            _phoneController.text = cachePhone;
          }
        });
      }
    });

    GlobalConfig.eventBus.on<LoginPhoneEvent>().listen((event) {
      widget.notifier.setSmsCode("", notify: event.notify);
      _smsController.text = "";
    });
  }

  @override
  void dispose() {
    _phoneFocus.dispose();
    _phoneController.dispose();
    _smsFocus.dispose();
    _smsController.dispose();
    super.dispose();
  }

  Widget _buildPhoneWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(
          left: Dimens.dp_20, right: Dimens.dp_20, top: Dimens.dp_10),
      height: Dimens.dp_70,
      child: Row(
        children: [
          Container(
            width: Dimens.dp_125,
            child: Row(
              children: [
                Text(
                  S.of(context).phone,
                  style: TextStyle(fontSize: Dimens.sp_16),
                ),
                SizedBox(
                  width: Dimens.dp_20,
                ),
                InkWell(
                  onTap: () {
                    showBottomSheet();
                  },
                  child: Row(
                    children: [
                      Text(
                        widget.notifier.regionCode,
                        style: TextStyle(fontSize: Dimens.sp_16),
                      ),
                      Icon(Icons.arrow_drop_down_sharp),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
              child: TextField(
            controller: _phoneController,
            focusNode: _phoneFocus,
            style: TextStyle(fontSize: Dimens.sp_16),
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
                border: InputBorder.none,
                hintText: Constants.phone_num_hint,
                hintStyle: TextStyle(color: Colors.grey)),
            onChanged: (v) {
              LogUtil.v("phone::$v");
              widget.notifier.setPhone(v);
            },
          )),
        ],
      ),
    );
  }

  void showBottomSheet() {
    FocusScope.of(context).requestFocus(FocusNode());
    showModalBottomSheet(
        builder: (BuildContext context) {
          return buildBottomSheetWidget(context);
        },
        context: context);
  }

  Widget buildBottomSheetWidget(BuildContext context) {
    return Container(
      height: Dimens.dp_254,
      child: Column(
        children: [
          buildItem(S.of(context).region_china, onTap: () {
            _regionTap(S.of(context).region_china);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(S.of(context).region_usa, onTap: () {
            _regionTap(S.of(context).region_usa);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(S.of(context).region_france, onTap: () {
            _regionTap(S.of(context).region_france);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(S.of(context).region_spain, onTap: () {
            _regionTap(S.of(context).region_spain);
          }),
          Divider(
            height: Dimens.dp_1,
          ),
          buildItem(S.of(context).region_uk, onTap: () {
            _regionTap(S.of(context).region_uk);
          }),
        ],
      ),
    );
  }

  _regionTap(String regionName) {
    widget.notifier.setPhone("", notify: false);
    GlobalConfig.eventBus.fire(LoginPhoneEvent(false));
    GlobalConfig.eventBus.fire(ResetSmsCode());
    widget.notifier.setRegionCode(regionName.split(" ").last);
  }

  Widget buildItem(String title, {Function? onTap}) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        if (onTap != null) {
          _phoneController.text = "";
          onTap();
        }
      },
      child: Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: Dimens.dp_20),
        height: Dimens.dp_50,
        child: Text(
          title,
        ),
      ),
    );
  }

  Widget _buildCodeWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: Dimens.dp_20, right: Dimens.dp_20),
      height: Dimens.dp_70,
      child: Row(
        children: [
          Container(
            width: Dimens.dp_125,
            child: Text(
              S.of(context).sms_code,
              style: TextStyle(fontSize: Dimens.sp_16),
            ),
          ),
          Expanded(
              child: TextField(
            controller: _smsController,
            focusNode: _smsFocus,
            style: TextStyle(fontSize: Dimens.sp_16),
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
                border: InputBorder.none,
                hintText: S.of(context).enter_code,
                hintStyle: TextStyle(color: Colors.grey)),
            onChanged: (v) {
              widget.notifier.setSmsCode(v);
            },
          )),
          TimerCountDown(
            onTimerFinish: () {},
            onSuccess: () {
              FocusScope.of(context).requestFocus(_smsFocus);
            },
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        _buildPhoneWidget(context),
        _buildCodeWidget(context),
      ],
    );
  }
}
