import 'package:connect/common/constants.dart';
import 'package:connect/common/global.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/provider/login_provider.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:flutter/material.dart';

class LoginWithPassword extends StatefulWidget {
  LoginWithPassword(this.notifier, {Key? key}) : super(key: key);

  final LoginProvider notifier;

  @override
  _LoginWithPasswordState createState() => _LoginWithPasswordState();
}

class _LoginWithPasswordState extends State<LoginWithPassword>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final FocusNode _passwordFocus = FocusNode();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance!.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(_passwordFocus);
      widget.notifier.setPasswordNodeFocus(_passwordFocus);

      //init email value
      var user = ConnectCache.getUser();
      if (user != null &&
          Constants.PASSWORD == user.loginType &&
          user.email != null &&
          user.email!.isNotEmpty) {
        widget.notifier.setEmail(user.email!);
        _emailController.text = user.email!;
      }
    });

    GlobalConfig.eventBus.on<LoginPasswordEvent>().listen((event) {
      if (!mounted) return;
      widget.notifier.setPassword("");
      if (_passwordController != null) _passwordController.text = "";
    });
  }

  @override
  void dispose() {
    _passwordFocus.dispose();
    _passwordController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  Container _buildEmailWidget() {
    // print("password::"+widget.focus.toString());
    return Container(
      padding: const EdgeInsets.only(
          left: Dimens.dp_20, right: Dimens.dp_20, top: Dimens.dp_10),
      height: Dimens.dp_70,
      child: Row(
        children: [
          Container(
            width: Dimens.dp_90,
            child: Text(
              S.of(context).email,
              style: TextStyle(
                fontSize: Dimens.sp_16,
              ),
            ),
          ),
          Expanded(
              child: TextField(
            controller: _emailController,
            style: TextStyle(
                fontSize: Dimens.sp_16,
                color:
                    widget.notifier.isValidEmail ? Colors.black : Colors.red),
            keyboardType: TextInputType.emailAddress,
            focusNode: _passwordFocus,
            decoration: InputDecoration(border: InputBorder.none),
            onChanged: (v) {
              widget.notifier.setEmail(v);
            },
          ))
        ],
      ),
    );
  }

  Container _buildPasswordWidget() {
    return Container(
      padding: const EdgeInsets.only(left: Dimens.dp_20, right: Dimens.dp_20),
      height: Dimens.dp_70,
      child: Row(
        children: [
          Container(
            width: Dimens.dp_90,
            child: Text(
              S.of(context).password,
              style: TextStyle(fontSize: Dimens.sp_16),
            ),
          ),
          Expanded(
              flex: 1,
              child: TextField(
                controller: _passwordController,
                style: TextStyle(fontSize: Dimens.sp_16),
                obscureText: true,
                decoration: InputDecoration(border: InputBorder.none),
                onChanged: (v) {
                  widget.notifier.setPassword(v);
                },
              )),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        _buildEmailWidget(),
        _buildPasswordWidget(),
      ],
    );
  }
}
