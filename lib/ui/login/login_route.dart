import 'package:connect/provider/login_provider.dart';
import 'package:connect/res/colors.dart';
import 'package:connect/res/dimens.dart';
import 'package:connect/generated/l10n.dart';
import 'package:connect/ui/login/login_with_password.dart';
import 'package:connect/ui/login/widget/login_help.dart';
import 'package:connect/ui/login/widget/login_in.dart';
import 'package:connect/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'login_with_phone.dart';

class LoginRoute extends StatefulWidget {
  static String tag = "login_route";

  @override
  _LoginRouteState createState() => _LoginRouteState();
}

class _LoginRouteState extends State<LoginRoute>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List tabs = [];
  final LoginProvider notifier = LoginProvider();

  var tabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      // print(_tabController.index.toString() + "::mark");
      if (_tabController.index.toDouble() == _tabController.animation!.value) {
        switch (_tabController.index) {
          case 0:
            tabIndex = 0;
            FocusScope.of(context).requestFocus(notifier.passwordNodeFocus);
            break;
          case 1:
            tabIndex = 1;
            FocusScope.of(context).requestFocus(notifier.phoneNodeFocus);
            break;
        }
        notifier.setTabIndex(tabIndex);
        notifier.notify();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.v("LoginRoute::build");
    tabs = [S.of(context).login_password, S.of(context).login_phone];
    return WillPopScope(
        child: ChangeNotifierProvider.value(
            value: notifier,
            child: Scaffold(
              resizeToAvoidBottomInset: false,
              appBar: AppBar(
                title: Text(
                  S.of(context).ricepo_connect,
                  style: TextStyle(color: Colors.white, fontSize: Dimens.dp_20),
                ),
                backgroundColor: RColors.r_orange,
                centerTitle: true,
                bottom: TabBar(
                  controller: _tabController,
                  labelStyle: TextStyle(fontSize: Dimens.sp_16),
                  indicatorColor: Colors.white,
                  tabs: tabs.map((e) => Tab(text: e)).toList(),
                ),
              ),
              backgroundColor: Colors.white,
              body: Consumer<LoginProvider>(
                  builder: (_, localNotifier, __) => Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Container(
                              margin: const EdgeInsets.only(top: Dimens.dp_5),
                              width: double.infinity,
                              height: Dimens.dp_140,
                              child: TabBarView(
                                controller: _tabController,
                                children: [
                                  LoginWithPassword(notifier),
                                  LoginWithPhone(notifier),
                                ],
                                physics: NeverScrollableScrollPhysics(),
                              )),
                          SizedBox(
                            height: Dimens.dp_15,
                            width: double.infinity,
                          ),
                          LoginIn(),
                          LoginHelp(),
                        ],
                      )),
            )),
        onWillPop: () async => false);
  }
}
