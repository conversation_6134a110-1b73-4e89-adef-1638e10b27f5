import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/multi_name_entity.dart';

MultiNameEntity $MultiNameEntityFromJson(Map<String, dynamic> json) {
	final MultiNameEntity multiNameEntity = MultiNameEntity();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		multiNameEntity.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		multiNameEntity.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		multiNameEntity.zhHk = zhHk;
	}
	return multiNameEntity;
}

Map<String, dynamic> $MultiNameEntityToJson(MultiNameEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}