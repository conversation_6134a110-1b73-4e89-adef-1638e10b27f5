import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/rest_menu_toggle_entity.dart';

RestMenuToggleEntity $RestMenuToggleEntityFromJson(Map<String, dynamic> json) {
	final RestMenuToggleEntity restMenuToggleEntity = RestMenuToggleEntity();
	final String? foodId = jsonConvert.convert<String>(json['_id']);
	if (foodId != null) {
		restMenuToggleEntity.foodId = foodId;
	}
	final bool? available = jsonConvert.convert<bool>(json['available']);
	if (available != null) {
		restMenuToggleEntity.available = available;
	}
	return restMenuToggleEntity;
}

Map<String, dynamic> $RestMenuToggleEntityToJson(RestMenuToggleEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.foodId;
	data['available'] = entity.available;
	return data;
}