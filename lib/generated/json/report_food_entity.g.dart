import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/ui/home/<USER>/food/report_food_entity.dart';
import 'package:connect/data/multi_name_entity.dart';


ReportFoodEntity $ReportFoodEntityFromJson(Map<String, dynamic> json) {
	final ReportFoodEntity reportFoodEntity = ReportFoodEntity();
	final ReportFoodId? rId = jsonConvert.convert<ReportFoodId>(json['_id']);
	if (rId != null) {
		reportFoodEntity.rId = rId;
	}
	final num? number = jsonConvert.convert<num>(json['number']);
	if (number != null) {
		reportFoodEntity.number = number;
	}
	final num? price = jsonConvert.convert<num>(json['price']);
	if (price != null) {
		reportFoodEntity.price = price;
	}
	final double? subtotal = jsonConvert.convert<double>(json['subtotal']);
	if (subtotal != null) {
		reportFoodEntity.subtotal = subtotal;
	}
	final ReportFoodCost? cost = jsonConvert.convert<ReportFoodCost>(json['cost']);
	if (cost != null) {
		reportFoodEntity.cost = cost;
	}
	final ReportFoodFees? fees = jsonConvert.convert<ReportFoodFees>(json['fees']);
	if (fees != null) {
		reportFoodEntity.fees = fees;
	}
	final double? total = jsonConvert.convert<double>(json['total']);
	if (total != null) {
		reportFoodEntity.total = total;
	}
	final ReportFoodCommission? commission = jsonConvert.convert<ReportFoodCommission>(json['commission']);
	if (commission != null) {
		reportFoodEntity.commission = commission;
	}
	final ReportFoodDistribution? distribution = jsonConvert.convert<ReportFoodDistribution>(json['distribution']);
	if (distribution != null) {
		reportFoodEntity.distribution = distribution;
	}
	final ReportFoodAdjustments? adjustments = jsonConvert.convert<ReportFoodAdjustments>(json['adjustments']);
	if (adjustments != null) {
		reportFoodEntity.adjustments = adjustments;
	}
	return reportFoodEntity;
}

Map<String, dynamic> $ReportFoodEntityToJson(ReportFoodEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.rId?.toJson();
	data['number'] = entity.number;
	data['price'] = entity.price;
	data['subtotal'] = entity.subtotal;
	data['cost'] = entity.cost?.toJson();
	data['fees'] = entity.fees?.toJson();
	data['total'] = entity.total;
	data['commission'] = entity.commission?.toJson();
	data['distribution'] = entity.distribution?.toJson();
	data['adjustments'] = entity.adjustments?.toJson();
	return data;
}

ReportFoodId $ReportFoodIdFromJson(Map<String, dynamic> json) {
	final ReportFoodId reportFoodId = ReportFoodId();
	final ReportFoodIdFood? food = jsonConvert.convert<ReportFoodIdFood>(json['food']);
	if (food != null) {
		reportFoodId.food = food;
	}
	return reportFoodId;
}

Map<String, dynamic> $ReportFoodIdToJson(ReportFoodId entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['food'] = entity.food?.toJson();
	return data;
}

ReportFoodIdFood $ReportFoodIdFoodFromJson(Map<String, dynamic> json) {
	final ReportFoodIdFood reportFoodIdFood = ReportFoodIdFood();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		reportFoodIdFood.sId = sId;
	}
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		reportFoodIdFood.name = name;
	}
	final dynamic? code = jsonConvert.convert<dynamic>(json['code']);
	if (code != null) {
		reportFoodIdFood.code = code;
	}
	final List<MultiNameEntity?>? options = jsonConvert.convertList<MultiNameEntity>(json['options']);
	if (options != null) {
		reportFoodIdFood.options = options;
	}
	return reportFoodIdFood;
}

Map<String, dynamic> $ReportFoodIdFoodToJson(ReportFoodIdFood entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name?.toJson();
	data['code'] = entity.code;
	data['options'] =  entity.options?.map((v) => v?.toJson()).toList();
	return data;
}

ReportFoodCost $ReportFoodCostFromJson(Map<String, dynamic> json) {
	final ReportFoodCost reportFoodCost = ReportFoodCost();
	final double? subtotal = jsonConvert.convert<double>(json['subtotal']);
	if (subtotal != null) {
		reportFoodCost.subtotal = subtotal;
	}
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		reportFoodCost.tax = tax;
	}
	return reportFoodCost;
}

Map<String, dynamic> $ReportFoodCostToJson(ReportFoodCost entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['subtotal'] = entity.subtotal;
	data['tax'] = entity.tax;
	return data;
}

ReportFoodFees $ReportFoodFeesFromJson(Map<String, dynamic> json) {
	final ReportFoodFees reportFoodFees = ReportFoodFees();
	final double? delivery = jsonConvert.convert<double>(json['delivery']);
	if (delivery != null) {
		reportFoodFees.delivery = delivery;
	}
	final ReportFoodFeesTip? tip = jsonConvert.convert<ReportFoodFeesTip>(json['tip']);
	if (tip != null) {
		reportFoodFees.tip = tip;
	}
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		reportFoodFees.tax = tax;
	}
	final double? service = jsonConvert.convert<double>(json['service']);
	if (service != null) {
		reportFoodFees.service = service;
	}
	final double? credit = jsonConvert.convert<double>(json['credit']);
	if (credit != null) {
		reportFoodFees.credit = credit;
	}
	final double? delta = jsonConvert.convert<double>(json['delta']);
	if (delta != null) {
		reportFoodFees.delta = delta;
	}
	return reportFoodFees;
}

Map<String, dynamic> $ReportFoodFeesToJson(ReportFoodFees entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['delivery'] = entity.delivery;
	data['tip'] = entity.tip?.toJson();
	data['tax'] = entity.tax;
	data['service'] = entity.service;
	data['credit'] = entity.credit;
	data['delta'] = entity.delta;
	return data;
}

ReportFoodFeesTip $ReportFoodFeesTipFromJson(Map<String, dynamic> json) {
	final ReportFoodFeesTip reportFoodFeesTip = ReportFoodFeesTip();
	final double? amount = jsonConvert.convert<double>(json['amount']);
	if (amount != null) {
		reportFoodFeesTip.amount = amount;
	}
	return reportFoodFeesTip;
}

Map<String, dynamic> $ReportFoodFeesTipToJson(ReportFoodFeesTip entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['amount'] = entity.amount;
	return data;
}

ReportFoodCommission $ReportFoodCommissionFromJson(Map<String, dynamic> json) {
	final ReportFoodCommission reportFoodCommission = ReportFoodCommission();
	final double? subtotal = jsonConvert.convert<double>(json['subtotal']);
	if (subtotal != null) {
		reportFoodCommission.subtotal = subtotal;
	}
	final double? total = jsonConvert.convert<double>(json['total']);
	if (total != null) {
		reportFoodCommission.total = total;
	}
	final double? service = jsonConvert.convert<double>(json['service']);
	if (service != null) {
		reportFoodCommission.service = service;
	}
	return reportFoodCommission;
}

Map<String, dynamic> $ReportFoodCommissionToJson(ReportFoodCommission entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['subtotal'] = entity.subtotal;
	data['total'] = entity.total;
	data['service'] = entity.service;
	return data;
}

ReportFoodDistribution $ReportFoodDistributionFromJson(Map<String, dynamic> json) {
	final ReportFoodDistribution reportFoodDistribution = ReportFoodDistribution();
	final double? restaurant = jsonConvert.convert<double>(json['restaurant']);
	if (restaurant != null) {
		reportFoodDistribution.restaurant = restaurant;
	}
	final ReportFoodDistributionRicepo? ricepo = jsonConvert.convert<ReportFoodDistributionRicepo>(json['ricepo']);
	if (ricepo != null) {
		reportFoodDistribution.ricepo = ricepo;
	}
	return reportFoodDistribution;
}

Map<String, dynamic> $ReportFoodDistributionToJson(ReportFoodDistribution entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['restaurant'] = entity.restaurant;
	data['ricepo'] = entity.ricepo?.toJson();
	return data;
}

ReportFoodDistributionRicepo $ReportFoodDistributionRicepoFromJson(Map<String, dynamic> json) {
	final ReportFoodDistributionRicepo reportFoodDistributionRicepo = ReportFoodDistributionRicepo();
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		reportFoodDistributionRicepo.tax = tax;
	}
	return reportFoodDistributionRicepo;
}

Map<String, dynamic> $ReportFoodDistributionRicepoToJson(ReportFoodDistributionRicepo entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['tax'] = entity.tax;
	return data;
}

ReportFoodAdjustments $ReportFoodAdjustmentsFromJson(Map<String, dynamic> json) {
	final ReportFoodAdjustments reportFoodAdjustments = ReportFoodAdjustments();
	final double? restaurant = jsonConvert.convert<double>(json['restaurant']);
	if (restaurant != null) {
		reportFoodAdjustments.restaurant = restaurant;
	}
	return reportFoodAdjustments;
}

Map<String, dynamic> $ReportFoodAdjustmentsToJson(ReportFoodAdjustments entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['restaurant'] = entity.restaurant;
	return data;
}