import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/driver/bean/driver_orders_entity.dart';
import 'package:connect/data/multi_name_entity.dart';


DriverOrdersEntity $DriverOrdersEntityFromJson(Map<String, dynamic> json) {
	final DriverOrdersEntity driverOrdersEntity = DriverOrdersEntity();
	final int? alert = jsonConvert.convert<int>(json['alert']);
	if (alert != null) {
		driverOrdersEntity.alert = alert;
	}
	final List<DriverOrdersRoute>? route = jsonConvert.convertListNotNull<DriverOrdersRoute>(json['route']);
	if (route != null) {
		driverOrdersEntity.route = route;
	}
	final List<DriverOrdersNext>? more = jsonConvert.convertListNotNull<DriverOrdersNext>(json['more']);
	if (more != null) {
		driverOrdersEntity.more = more;
	}
	final String? startAt = jsonConvert.convert<String>(json['startAt']);
	if (startAt != null) {
		driverOrdersEntity.startAt = startAt;
	}
	final DriverOrdersNext? next = jsonConvert.convert<DriverOrdersNext>(json['next']);
	if (next != null) {
		driverOrdersEntity.next = next;
	}
	return driverOrdersEntity;
}

Map<String, dynamic> $DriverOrdersEntityToJson(DriverOrdersEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['alert'] = entity.alert;
	data['route'] =  entity.route?.map((v) => v.toJson()).toList();
	data['more'] =  entity.more?.map((v) => v.toJson()).toList();
	data['startAt'] = entity.startAt;
	data['next'] = entity.next?.toJson();
	return data;
}

DriverOrdersMore $DriverOrdersMoreFromJson(Map<String, dynamic> json) {
	final DriverOrdersMore driverOrdersMore = DriverOrdersMore();
	final String? locationId = jsonConvert.convert<String>(json['location_id']);
	if (locationId != null) {
		driverOrdersMore.locationId = locationId;
	}
	final String? locationName = jsonConvert.convert<String>(json['location_name']);
	if (locationName != null) {
		driverOrdersMore.locationName = locationName;
	}
	final int? arrivalTime = jsonConvert.convert<int>(json['arrival_time']);
	if (arrivalTime != null) {
		driverOrdersMore.arrivalTime = arrivalTime;
	}
	final int? finishTime = jsonConvert.convert<int>(json['finish_time']);
	if (finishTime != null) {
		driverOrdersMore.finishTime = finishTime;
	}
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		driverOrdersMore.type = type;
	}
	final double? distance = jsonConvert.convert<double>(json['distance']);
	if (distance != null) {
		driverOrdersMore.distance = distance;
	}
	final String? arriveAt = jsonConvert.convert<String>(json['arriveAt']);
	if (arriveAt != null) {
		driverOrdersMore.arriveAt = arriveAt;
	}
	final String? finishAt = jsonConvert.convert<String>(json['finishAt']);
	if (finishAt != null) {
		driverOrdersMore.finishAt = finishAt;
	}
	final String? estimateAt = jsonConvert.convert<String>(json['estimateAt']);
	if (estimateAt != null) {
		driverOrdersMore.estimateAt = estimateAt;
	}
	final String? phone = jsonConvert.convert<String>(json['phone']);
	if (phone != null) {
		driverOrdersMore.phone = phone;
	}
	final DriverOrdersMoreOrder? order = jsonConvert.convert<DriverOrdersMoreOrder>(json['order']);
	if (order != null) {
		driverOrdersMore.order = order;
	}
	return driverOrdersMore;
}

Map<String, dynamic> $DriverOrdersMoreToJson(DriverOrdersMore entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['location_id'] = entity.locationId;
	data['location_name'] = entity.locationName;
	data['arrival_time'] = entity.arrivalTime;
	data['finish_time'] = entity.finishTime;
	data['type'] = entity.type;
	data['distance'] = entity.distance;
	data['arriveAt'] = entity.arriveAt;
	data['finishAt'] = entity.finishAt;
	data['estimateAt'] = entity.estimateAt;
	data['phone'] = entity.phone;
	data['order'] = entity.order?.toJson();
	return data;
}

DriverOrdersRoute $DriverOrdersRouteFromJson(Map<String, dynamic> json) {
	final DriverOrdersRoute driverOrdersRoute = DriverOrdersRoute();
	final String? locationId = jsonConvert.convert<String>(json['location_id']);
	if (locationId != null) {
		driverOrdersRoute.locationId = locationId;
	}
	final String? locationName = jsonConvert.convert<String>(json['location_name']);
	if (locationName != null) {
		driverOrdersRoute.locationName = locationName;
	}
	final int? arrivalTime = jsonConvert.convert<int>(json['arrival_time']);
	if (arrivalTime != null) {
		driverOrdersRoute.arrivalTime = arrivalTime;
	}
	final int? finishTime = jsonConvert.convert<int>(json['finish_time']);
	if (finishTime != null) {
		driverOrdersRoute.finishTime = finishTime;
	}
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		driverOrdersRoute.type = type;
	}
	final double? distance = jsonConvert.convert<double>(json['distance']);
	if (distance != null) {
		driverOrdersRoute.distance = distance;
	}
	final bool? tooLate = jsonConvert.convert<bool>(json['too_late']);
	if (tooLate != null) {
		driverOrdersRoute.tooLate = tooLate;
	}
	final DriverOrdersRouteLateBy? lateBy = jsonConvert.convert<DriverOrdersRouteLateBy>(json['late_by']);
	if (lateBy != null) {
		driverOrdersRoute.lateBy = lateBy;
	}
	final String? arriveAt = jsonConvert.convert<String>(json['arriveAt']);
	if (arriveAt != null) {
		driverOrdersRoute.arriveAt = arriveAt;
	}
	final String? arrivedAt = jsonConvert.convert<String>(json['arrivedAt']);
	if (arrivedAt != null) {
		driverOrdersRoute.arrivedAt = arrivedAt;
	}
	final String? finishAt = jsonConvert.convert<String>(json['finishAt']);
	if (finishAt != null) {
		driverOrdersRoute.finishAt = finishAt;
	}
	final String? estimateAt = jsonConvert.convert<String>(json['estimateAt']);
	if (estimateAt != null) {
		driverOrdersRoute.estimateAt = estimateAt;
	}
	final DriverOrdersRouteAddress? address = jsonConvert.convert<DriverOrdersRouteAddress>(json['address']);
	if (address != null) {
		driverOrdersRoute.address = address;
	}
	final String? phone = jsonConvert.convert<String>(json['phone']);
	if (phone != null) {
		driverOrdersRoute.phone = phone;
	}
	final DriverOrdersRouteOrder? order = jsonConvert.convert<DriverOrdersRouteOrder>(json['order']);
	if (order != null) {
		driverOrdersRoute.order = order;
	}
	return driverOrdersRoute;
}

Map<String, dynamic> $DriverOrdersRouteToJson(DriverOrdersRoute entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['location_id'] = entity.locationId;
	data['location_name'] = entity.locationName;
	data['arrival_time'] = entity.arrivalTime;
	data['finish_time'] = entity.finishTime;
	data['type'] = entity.type;
	data['distance'] = entity.distance;
	data['too_late'] = entity.tooLate;
	data['late_by'] = entity.lateBy?.toJson();
	data['arriveAt'] = entity.arriveAt;
	data['arrivedAt'] = entity.arrivedAt;
	data['finishAt'] = entity.finishAt;
	data['estimateAt'] = entity.estimateAt;
	data['address'] = entity.address?.toJson();
	data['phone'] = entity.phone;
	data['order'] = entity.order?.toJson();
	return data;
}

DriverOrdersRouteLateBy $DriverOrdersRouteLateByFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteLateBy driverOrdersRouteLateBy = DriverOrdersRouteLateBy();
	final int? minutes = jsonConvert.convert<int>(json['minutes']);
	if (minutes != null) {
		driverOrdersRouteLateBy.minutes = minutes;
	}
	final int? seconds = jsonConvert.convert<int>(json['seconds']);
	if (seconds != null) {
		driverOrdersRouteLateBy.seconds = seconds;
	}
	return driverOrdersRouteLateBy;
}

Map<String, dynamic> $DriverOrdersRouteLateByToJson(DriverOrdersRouteLateBy entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['minutes'] = entity.minutes;
	data['seconds'] = entity.seconds;
	return data;
}

DriverOrdersRouteAddress $DriverOrdersRouteAddressFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteAddress driverOrdersRouteAddress = DriverOrdersRouteAddress();
	final String? unit = jsonConvert.convert<String>(json['unit']);
	if (unit != null) {
		driverOrdersRouteAddress.unit = unit;
	}
	final String? city = jsonConvert.convert<String>(json['city']);
	if (city != null) {
		driverOrdersRouteAddress.city = city;
	}
	final String? note = jsonConvert.convert<String>(json['note']);
	if (note != null) {
		driverOrdersRouteAddress.note = note;
	}
	final DriverOrdersRouteAddressLocation? location = jsonConvert.convert<DriverOrdersRouteAddressLocation>(json['location']);
	if (location != null) {
		driverOrdersRouteAddress.location = location;
	}
	final String? zipcode = jsonConvert.convert<String>(json['zipcode']);
	if (zipcode != null) {
		driverOrdersRouteAddress.zipcode = zipcode;
	}
	final String? country = jsonConvert.convert<String>(json['country']);
	if (country != null) {
		driverOrdersRouteAddress.country = country;
	}
	final String? formatted = jsonConvert.convert<String>(json['formatted']);
	if (formatted != null) {
		driverOrdersRouteAddress.formatted = formatted;
	}
	final String? state = jsonConvert.convert<String>(json['state']);
	if (state != null) {
		driverOrdersRouteAddress.state = state;
	}
	return driverOrdersRouteAddress;
}

Map<String, dynamic> $DriverOrdersRouteAddressToJson(DriverOrdersRouteAddress entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['unit'] = entity.unit;
	data['city'] = entity.city;
	data['note'] = entity.note;
	data['location'] = entity.location?.toJson();
	data['zipcode'] = entity.zipcode;
	data['country'] = entity.country;
	data['formatted'] = entity.formatted;
	data['state'] = entity.state;
	return data;
}

DriverOrdersRouteAddressLocation $DriverOrdersRouteAddressLocationFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteAddressLocation driverOrdersRouteAddressLocation = DriverOrdersRouteAddressLocation();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		driverOrdersRouteAddressLocation.type = type;
	}
	final List<double>? coordinates = jsonConvert.convertListNotNull<double>(json['coordinates']);
	if (coordinates != null) {
		driverOrdersRouteAddressLocation.coordinates = coordinates;
	}
	return driverOrdersRouteAddressLocation;
}

Map<String, dynamic> $DriverOrdersRouteAddressLocationToJson(DriverOrdersRouteAddressLocation entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['coordinates'] =  entity.coordinates;
	return data;
}

DriverOrdersRouteOrder $DriverOrdersRouteOrderFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteOrder driverOrdersRouteOrder = DriverOrdersRouteOrder();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOrdersRouteOrder.sId = sId;
	}
	final String? passcode = jsonConvert.convert<String>(json['passcode']);
	if (passcode != null) {
		driverOrdersRouteOrder.passcode = passcode;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		driverOrdersRouteOrder.createdAt = createdAt;
	}
	final DriverOrdersRouteOrderRegion? region = jsonConvert.convert<DriverOrdersRouteOrderRegion>(json['region']);
	if (region != null) {
		driverOrdersRouteOrder.region = region;
	}
	final String? confirmedAt = jsonConvert.convert<String>(json['confirmedAt']);
	if (confirmedAt != null) {
		driverOrdersRouteOrder.confirmedAt = confirmedAt;
	}
	final DriverOrdersRouteOrderDelivery? delivery = jsonConvert.convert<DriverOrdersRouteOrderDelivery>(json['delivery']);
	if (delivery != null) {
		driverOrdersRouteOrder.delivery = delivery;
	}
	final DriverOrdersRouteOrderRestaurant? restaurant = jsonConvert.convert<DriverOrdersRouteOrderRestaurant>(json['restaurant']);
	if (restaurant != null) {
		driverOrdersRouteOrder.restaurant = restaurant;
	}
	final DriverOrdersRouteOrderCustomer? customer = jsonConvert.convert<DriverOrdersRouteOrderCustomer>(json['customer']);
	if (customer != null) {
		driverOrdersRouteOrder.customer = customer;
	}
	final List<DriverOrdersRouteOrderItem>? items = jsonConvert.convertListNotNull<DriverOrdersRouteOrderItem>(json['items']);
	if (items != null) {
		driverOrdersRouteOrder.items = items;
	}
	return driverOrdersRouteOrder;
}

Map<String, dynamic> $DriverOrdersRouteOrderToJson(DriverOrdersRouteOrder entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['passcode'] = entity.passcode;
	data['createdAt'] = entity.createdAt;
	data['region'] = entity.region?.toJson();
	data['confirmedAt'] = entity.confirmedAt;
	data['delivery'] = entity.delivery?.toJson();
	data['restaurant'] = entity.restaurant?.toJson();
	data['customer'] = entity.customer?.toJson();
	data['items'] =  entity.items?.map((v) => v.toJson()).toList();
	return data;
}

DriverOrdersMoreOrder $DriverOrdersMoreOrderFromJson(Map<String, dynamic> json) {
	final DriverOrdersMoreOrder driverOrdersMoreOrder = DriverOrdersMoreOrder();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOrdersMoreOrder.sId = sId;
	}
	final String? passcode = jsonConvert.convert<String>(json['passcode']);
	if (passcode != null) {
		driverOrdersMoreOrder.passcode = passcode;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		driverOrdersMoreOrder.createdAt = createdAt;
	}
	final DriverOrdersMoreOrderRegion? region = jsonConvert.convert<DriverOrdersMoreOrderRegion>(json['region']);
	if (region != null) {
		driverOrdersMoreOrder.region = region;
	}
	final String? confirmedAt = jsonConvert.convert<String>(json['confirmedAt']);
	if (confirmedAt != null) {
		driverOrdersMoreOrder.confirmedAt = confirmedAt;
	}
	final DriverOrdersMoreOrderRestaurant? restaurant = jsonConvert.convert<DriverOrdersMoreOrderRestaurant>(json['restaurant']);
	if (restaurant != null) {
		driverOrdersMoreOrder.restaurant = restaurant;
	}
	return driverOrdersMoreOrder;
}

Map<String, dynamic> $DriverOrdersMoreOrderToJson(DriverOrdersMoreOrder entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['passcode'] = entity.passcode;
	data['createdAt'] = entity.createdAt;
	data['region'] = entity.region?.toJson();
	data['confirmedAt'] = entity.confirmedAt;
	data['restaurant'] = entity.restaurant?.toJson();
	return data;
}

DriverOrdersMoreOrderRegion $DriverOrdersMoreOrderRegionFromJson(Map<String, dynamic> json) {
	final DriverOrdersMoreOrderRegion driverOrdersMoreOrderRegion = DriverOrdersMoreOrderRegion();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOrdersMoreOrderRegion.sId = sId;
	}
	final String? name = jsonConvert.convert<String>(json['name']);
	if (name != null) {
		driverOrdersMoreOrderRegion.name = name;
	}
	return driverOrdersMoreOrderRegion;
}

Map<String, dynamic> $DriverOrdersMoreOrderRegionToJson(DriverOrdersMoreOrderRegion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name;
	return data;
}

DriverOrdersRouteOrderRegion $DriverOrdersRouteOrderRegionFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteOrderRegion driverOrdersRouteOrderRegion = DriverOrdersRouteOrderRegion();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOrdersRouteOrderRegion.sId = sId;
	}
	final String? name = jsonConvert.convert<String>(json['name']);
	if (name != null) {
		driverOrdersRouteOrderRegion.name = name;
	}
	return driverOrdersRouteOrderRegion;
}

Map<String, dynamic> $DriverOrdersRouteOrderRegionToJson(DriverOrdersRouteOrderRegion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name;
	return data;
}

DriverOrdersRouteOrderDelivery $DriverOrdersRouteOrderDeliveryFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteOrderDelivery driverOrdersRouteOrderDelivery = DriverOrdersRouteOrderDelivery();
	final String? provider = jsonConvert.convert<String>(json['provider']);
	if (provider != null) {
		driverOrdersRouteOrderDelivery.provider = provider;
	}
	final DriverOrdersRouteOrderDeliveryAddress? address = jsonConvert.convert<DriverOrdersRouteOrderDeliveryAddress>(json['address']);
	if (address != null) {
		driverOrdersRouteOrderDelivery.address = address;
	}
	final num? driverPay = jsonConvert.convert<num>(json['driverPay']);
	if (driverPay != null) {
		driverOrdersRouteOrderDelivery.driverPay = driverPay;
	}
	return driverOrdersRouteOrderDelivery;
}

Map<String, dynamic> $DriverOrdersRouteOrderDeliveryToJson(DriverOrdersRouteOrderDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['provider'] = entity.provider;
	data['address'] = entity.address?.toJson();
	data['driverPay'] = entity.driverPay;
	return data;
}

DriverOrdersRouteOrderDeliveryAddress $DriverOrdersRouteOrderDeliveryAddressFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteOrderDeliveryAddress driverOrdersRouteOrderDeliveryAddress = DriverOrdersRouteOrderDeliveryAddress();
	final String? formatted = jsonConvert.convert<String>(json['formatted']);
	if (formatted != null) {
		driverOrdersRouteOrderDeliveryAddress.formatted = formatted;
	}
	final String? unit = jsonConvert.convert<String>(json['unit']);
	if (unit != null) {
		driverOrdersRouteOrderDeliveryAddress.unit = unit;
	}
	final String? city = jsonConvert.convert<String>(json['city']);
	if (city != null) {
		driverOrdersRouteOrderDeliveryAddress.city = city;
	}
	final String? state = jsonConvert.convert<String>(json['state']);
	if (state != null) {
		driverOrdersRouteOrderDeliveryAddress.state = state;
	}
	final String? country = jsonConvert.convert<String>(json['country']);
	if (country != null) {
		driverOrdersRouteOrderDeliveryAddress.country = country;
	}
	final String? zipcode = jsonConvert.convert<String>(json['zipcode']);
	if (zipcode != null) {
		driverOrdersRouteOrderDeliveryAddress.zipcode = zipcode;
	}
	return driverOrdersRouteOrderDeliveryAddress;
}

Map<String, dynamic> $DriverOrdersRouteOrderDeliveryAddressToJson(DriverOrdersRouteOrderDeliveryAddress entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['formatted'] = entity.formatted;
	data['unit'] = entity.unit;
	data['city'] = entity.city;
	data['state'] = entity.state;
	data['country'] = entity.country;
	data['zipcode'] = entity.zipcode;
	return data;
}

DriverOrdersRouteOrderRestaurant $DriverOrdersRouteOrderRestaurantFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteOrderRestaurant driverOrdersRouteOrderRestaurant = DriverOrdersRouteOrderRestaurant();
	final DriverOrdersRouteOrderRestaurantName? name = jsonConvert.convert<DriverOrdersRouteOrderRestaurantName>(json['name']);
	if (name != null) {
		driverOrdersRouteOrderRestaurant.name = name;
	}
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOrdersRouteOrderRestaurant.sId = sId;
	}
	final DriverOrdersRouteOrderRestaurantDelivery? delivery = jsonConvert.convert<DriverOrdersRouteOrderRestaurantDelivery>(json['delivery']);
	if (delivery != null) {
		driverOrdersRouteOrderRestaurant.delivery = delivery;
	}
	return driverOrdersRouteOrderRestaurant;
}

Map<String, dynamic> $DriverOrdersRouteOrderRestaurantToJson(DriverOrdersRouteOrderRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['name'] = entity.name?.toJson();
	data['_id'] = entity.sId;
	data['delivery'] = entity.delivery?.toJson();
	return data;
}

DriverOrdersMoreOrderRestaurant $DriverOrdersMoreOrderRestaurantFromJson(Map<String, dynamic> json) {
	final DriverOrdersMoreOrderRestaurant driverOrdersMoreOrderRestaurant = DriverOrdersMoreOrderRestaurant();
	final DriverOrdersMoreOrderRestaurantName? name = jsonConvert.convert<DriverOrdersMoreOrderRestaurantName>(json['name']);
	if (name != null) {
		driverOrdersMoreOrderRestaurant.name = name;
	}
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOrdersMoreOrderRestaurant.sId = sId;
	}
	return driverOrdersMoreOrderRestaurant;
}

Map<String, dynamic> $DriverOrdersMoreOrderRestaurantToJson(DriverOrdersMoreOrderRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['name'] = entity.name?.toJson();
	data['_id'] = entity.sId;
	return data;
}

DriverOrdersRouteOrderRestaurantName $DriverOrdersRouteOrderRestaurantNameFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteOrderRestaurantName driverOrdersRouteOrderRestaurantName = DriverOrdersRouteOrderRestaurantName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		driverOrdersRouteOrderRestaurantName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		driverOrdersRouteOrderRestaurantName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		driverOrdersRouteOrderRestaurantName.zhHk = zhHk;
	}
	return driverOrdersRouteOrderRestaurantName;
}

Map<String, dynamic> $DriverOrdersRouteOrderRestaurantNameToJson(DriverOrdersRouteOrderRestaurantName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

DriverOrdersMoreOrderRestaurantName $DriverOrdersMoreOrderRestaurantNameFromJson(Map<String, dynamic> json) {
	final DriverOrdersMoreOrderRestaurantName driverOrdersMoreOrderRestaurantName = DriverOrdersMoreOrderRestaurantName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		driverOrdersMoreOrderRestaurantName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		driverOrdersMoreOrderRestaurantName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		driverOrdersMoreOrderRestaurantName.zhHk = zhHk;
	}
	return driverOrdersMoreOrderRestaurantName;
}

Map<String, dynamic> $DriverOrdersMoreOrderRestaurantNameToJson(DriverOrdersMoreOrderRestaurantName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

DriverOrdersRouteOrderRestaurantDelivery $DriverOrdersRouteOrderRestaurantDeliveryFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteOrderRestaurantDelivery driverOrdersRouteOrderRestaurantDelivery = DriverOrdersRouteOrderRestaurantDelivery();
	final int? prepare = jsonConvert.convert<int>(json['prepare']);
	if (prepare != null) {
		driverOrdersRouteOrderRestaurantDelivery.prepare = prepare;
	}
	final dynamic? note = jsonConvert.convert<dynamic>(json['note']);
	if (note != null) {
		driverOrdersRouteOrderRestaurantDelivery.note = note;
	}
	final dynamic? batch = jsonConvert.convert<dynamic>(json['batch']);
	if (batch != null) {
		driverOrdersRouteOrderRestaurantDelivery.batch = batch;
	}
	final dynamic? scheduled = jsonConvert.convert<dynamic>(json['scheduled']);
	if (scheduled != null) {
		driverOrdersRouteOrderRestaurantDelivery.scheduled = scheduled;
	}
	return driverOrdersRouteOrderRestaurantDelivery;
}

Map<String, dynamic> $DriverOrdersRouteOrderRestaurantDeliveryToJson(DriverOrdersRouteOrderRestaurantDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['prepare'] = entity.prepare;
	data['note'] = entity.note;
	data['batch'] = entity.batch;
	data['scheduled'] = entity.scheduled;
	return data;
}

DriverOrdersRouteOrderCustomer $DriverOrdersRouteOrderCustomerFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteOrderCustomer driverOrdersRouteOrderCustomer = DriverOrdersRouteOrderCustomer();
	final String? language = jsonConvert.convert<String>(json['language']);
	if (language != null) {
		driverOrdersRouteOrderCustomer.language = language;
	}
	return driverOrdersRouteOrderCustomer;
}

Map<String, dynamic> $DriverOrdersRouteOrderCustomerToJson(DriverOrdersRouteOrderCustomer entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['language'] = entity.language;
	return data;
}

DriverOrdersRouteOrderItem $DriverOrdersRouteOrderItemFromJson(Map<String, dynamic> json) {
	final DriverOrdersRouteOrderItem driverOrdersRouteOrderItem = DriverOrdersRouteOrderItem();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		driverOrdersRouteOrderItem.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		driverOrdersRouteOrderItem.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		driverOrdersRouteOrderItem.zhHk = zhHk;
	}
	return driverOrdersRouteOrderItem;
}

Map<String, dynamic> $DriverOrdersRouteOrderItemToJson(DriverOrdersRouteOrderItem entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

DriverOrdersNext $DriverOrdersNextFromJson(Map<String, dynamic> json) {
	final DriverOrdersNext driverOrdersNext = DriverOrdersNext();
	final String? locationId = jsonConvert.convert<String>(json['location_id']);
	if (locationId != null) {
		driverOrdersNext.locationId = locationId;
	}
	final String? locationName = jsonConvert.convert<String>(json['location_name']);
	if (locationName != null) {
		driverOrdersNext.locationName = locationName;
	}
	final int? arrivalTime = jsonConvert.convert<int>(json['arrival_time']);
	if (arrivalTime != null) {
		driverOrdersNext.arrivalTime = arrivalTime;
	}
	final int? finishTime = jsonConvert.convert<int>(json['finish_time']);
	if (finishTime != null) {
		driverOrdersNext.finishTime = finishTime;
	}
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		driverOrdersNext.type = type;
	}
	final double? distance = jsonConvert.convert<double>(json['distance']);
	if (distance != null) {
		driverOrdersNext.distance = distance;
	}
	final String? arriveAt = jsonConvert.convert<String>(json['arriveAt']);
	if (arriveAt != null) {
		driverOrdersNext.arriveAt = arriveAt;
	}
	final String? arrivedAt = jsonConvert.convert<String>(json['arrivedAt']);
	if (arrivedAt != null) {
		driverOrdersNext.arrivedAt = arrivedAt;
	}
	final String? failAt = jsonConvert.convert<String>(json['failAt']);
	if (failAt != null) {
		driverOrdersNext.failAt = failAt;
	}
	final DriverOrdersNextAddress? address = jsonConvert.convert<DriverOrdersNextAddress>(json['address']);
	if (address != null) {
		driverOrdersNext.address = address;
	}
	final String? phone = jsonConvert.convert<String>(json['phone']);
	if (phone != null) {
		driverOrdersNext.phone = phone;
	}
	final DriverOrdersNextOrder? order = jsonConvert.convert<DriverOrdersNextOrder>(json['order']);
	if (order != null) {
		driverOrdersNext.order = order;
	}
	final bool? isNext = jsonConvert.convert<bool>(json['isNext']);
	if (isNext != null) {
		driverOrdersNext.isNext = isNext;
	}
	return driverOrdersNext;
}

Map<String, dynamic> $DriverOrdersNextToJson(DriverOrdersNext entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['location_id'] = entity.locationId;
	data['location_name'] = entity.locationName;
	data['arrival_time'] = entity.arrivalTime;
	data['finish_time'] = entity.finishTime;
	data['type'] = entity.type;
	data['distance'] = entity.distance;
	data['arriveAt'] = entity.arriveAt;
	data['arrivedAt'] = entity.arrivedAt;
	data['failAt'] = entity.failAt;
	data['address'] = entity.address?.toJson();
	data['phone'] = entity.phone;
	data['order'] = entity.order?.toJson();
	data['isNext'] = entity.isNext;
	return data;
}

DriverOrdersNextAddress $DriverOrdersNextAddressFromJson(Map<String, dynamic> json) {
	final DriverOrdersNextAddress driverOrdersNextAddress = DriverOrdersNextAddress();
	final String? number = jsonConvert.convert<String>(json['number']);
	if (number != null) {
		driverOrdersNextAddress.number = number;
	}
	final String? street = jsonConvert.convert<String>(json['street']);
	if (street != null) {
		driverOrdersNextAddress.street = street;
	}
	final String? unit = jsonConvert.convert<String>(json['unit']);
	if (unit != null) {
		driverOrdersNextAddress.unit = unit;
	}
	final String? city = jsonConvert.convert<String>(json['city']);
	if (city != null) {
		driverOrdersNextAddress.city = city;
	}
	final String? state = jsonConvert.convert<String>(json['state']);
	if (state != null) {
		driverOrdersNextAddress.state = state;
	}
	final String? country = jsonConvert.convert<String>(json['country']);
	if (country != null) {
		driverOrdersNextAddress.country = country;
	}
	final String? zipcode = jsonConvert.convert<String>(json['zipcode']);
	if (zipcode != null) {
		driverOrdersNextAddress.zipcode = zipcode;
	}
	final String? formatted = jsonConvert.convert<String>(json['formatted']);
	if (formatted != null) {
		driverOrdersNextAddress.formatted = formatted;
	}
	final DriverOrdersNextAddressLocation? location = jsonConvert.convert<DriverOrdersNextAddressLocation>(json['location']);
	if (location != null) {
		driverOrdersNextAddress.location = location;
	}
	final String? note = jsonConvert.convert<String>(json['note']);
	if (note != null) {
		driverOrdersNextAddress.note = note;
	}
	return driverOrdersNextAddress;
}

Map<String, dynamic> $DriverOrdersNextAddressToJson(DriverOrdersNextAddress entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['number'] = entity.number;
	data['street'] = entity.street;
	data['unit'] = entity.unit;
	data['city'] = entity.city;
	data['state'] = entity.state;
	data['country'] = entity.country;
	data['zipcode'] = entity.zipcode;
	data['formatted'] = entity.formatted;
	data['location'] = entity.location?.toJson();
	data['note'] = entity.note;
	return data;
}

DriverOrdersNextAddressLocation $DriverOrdersNextAddressLocationFromJson(Map<String, dynamic> json) {
	final DriverOrdersNextAddressLocation driverOrdersNextAddressLocation = DriverOrdersNextAddressLocation();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		driverOrdersNextAddressLocation.type = type;
	}
	final List<double>? coordinates = jsonConvert.convertListNotNull<double>(json['coordinates']);
	if (coordinates != null) {
		driverOrdersNextAddressLocation.coordinates = coordinates;
	}
	return driverOrdersNextAddressLocation;
}

Map<String, dynamic> $DriverOrdersNextAddressLocationToJson(DriverOrdersNextAddressLocation entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['coordinates'] =  entity.coordinates;
	return data;
}

DriverOrdersNextOrder $DriverOrdersNextOrderFromJson(Map<String, dynamic> json) {
	final DriverOrdersNextOrder driverOrdersNextOrder = DriverOrdersNextOrder();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOrdersNextOrder.sId = sId;
	}
	final String? passcode = jsonConvert.convert<String>(json['passcode']);
	if (passcode != null) {
		driverOrdersNextOrder.passcode = passcode;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		driverOrdersNextOrder.createdAt = createdAt;
	}
	final DriverOrdersNextOrderRegion? region = jsonConvert.convert<DriverOrdersNextOrderRegion>(json['region']);
	if (region != null) {
		driverOrdersNextOrder.region = region;
	}
	final String? confirmedAt = jsonConvert.convert<String>(json['confirmedAt']);
	if (confirmedAt != null) {
		driverOrdersNextOrder.confirmedAt = confirmedAt;
	}
	final DriverOrdersNextOrderDelivery? delivery = jsonConvert.convert<DriverOrdersNextOrderDelivery>(json['delivery']);
	if (delivery != null) {
		driverOrdersNextOrder.delivery = delivery;
	}
	final DriverOrdersNextOrderRestaurant? restaurant = jsonConvert.convert<DriverOrdersNextOrderRestaurant>(json['restaurant']);
	if (restaurant != null) {
		driverOrdersNextOrder.restaurant = restaurant;
	}
	final DriverOrdersNextOrderCustomer? customer = jsonConvert.convert<DriverOrdersNextOrderCustomer>(json['customer']);
	if (customer != null) {
		driverOrdersNextOrder.customer = customer;
	}
	final List<DriverOrdersNextOrderItem>? items = jsonConvert.convertListNotNull<DriverOrdersNextOrderItem>(json['items']);
	if (items != null) {
		driverOrdersNextOrder.items = items;
	}
	return driverOrdersNextOrder;
}

Map<String, dynamic> $DriverOrdersNextOrderToJson(DriverOrdersNextOrder entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['passcode'] = entity.passcode;
	data['createdAt'] = entity.createdAt;
	data['region'] = entity.region?.toJson();
	data['confirmedAt'] = entity.confirmedAt;
	data['delivery'] = entity.delivery?.toJson();
	data['restaurant'] = entity.restaurant?.toJson();
	data['customer'] = entity.customer?.toJson();
	data['items'] =  entity.items?.map((v) => v.toJson()).toList();
	return data;
}

DriverOrdersNextOrderRegion $DriverOrdersNextOrderRegionFromJson(Map<String, dynamic> json) {
	final DriverOrdersNextOrderRegion driverOrdersNextOrderRegion = DriverOrdersNextOrderRegion();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOrdersNextOrderRegion.sId = sId;
	}
	final String? name = jsonConvert.convert<String>(json['name']);
	if (name != null) {
		driverOrdersNextOrderRegion.name = name;
	}
	return driverOrdersNextOrderRegion;
}

Map<String, dynamic> $DriverOrdersNextOrderRegionToJson(DriverOrdersNextOrderRegion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name;
	return data;
}

DriverOrdersNextOrderDelivery $DriverOrdersNextOrderDeliveryFromJson(Map<String, dynamic> json) {
	final DriverOrdersNextOrderDelivery driverOrdersNextOrderDelivery = DriverOrdersNextOrderDelivery();
	final String? provider = jsonConvert.convert<String>(json['provider']);
	if (provider != null) {
		driverOrdersNextOrderDelivery.provider = provider;
	}
	final num? bags = jsonConvert.convert<num>(json['bags']);
	if (bags != null) {
		driverOrdersNextOrderDelivery.bags = bags;
	}
	return driverOrdersNextOrderDelivery;
}

Map<String, dynamic> $DriverOrdersNextOrderDeliveryToJson(DriverOrdersNextOrderDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['provider'] = entity.provider;
	data['bags'] = entity.bags;
	return data;
}

DriverOrdersNextOrderRestaurant $DriverOrdersNextOrderRestaurantFromJson(Map<String, dynamic> json) {
	final DriverOrdersNextOrderRestaurant driverOrdersNextOrderRestaurant = DriverOrdersNextOrderRestaurant();
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		driverOrdersNextOrderRestaurant.name = name;
	}
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOrdersNextOrderRestaurant.sId = sId;
	}
	final DriverOrdersNextOrderRestaurantDelivery? delivery = jsonConvert.convert<DriverOrdersNextOrderRestaurantDelivery>(json['delivery']);
	if (delivery != null) {
		driverOrdersNextOrderRestaurant.delivery = delivery;
	}
	return driverOrdersNextOrderRestaurant;
}

Map<String, dynamic> $DriverOrdersNextOrderRestaurantToJson(DriverOrdersNextOrderRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['name'] = entity.name?.toJson();
	data['_id'] = entity.sId;
	data['delivery'] = entity.delivery?.toJson();
	return data;
}

DriverOrdersNextOrderRestaurantDelivery $DriverOrdersNextOrderRestaurantDeliveryFromJson(Map<String, dynamic> json) {
	final DriverOrdersNextOrderRestaurantDelivery driverOrdersNextOrderRestaurantDelivery = DriverOrdersNextOrderRestaurantDelivery();
	final MultiNameEntity? note = jsonConvert.convert<MultiNameEntity>(json['note']);
	if (note != null) {
		driverOrdersNextOrderRestaurantDelivery.note = note;
	}
	final String? batch = jsonConvert.convert<String>(json['batch']);
	if (batch != null) {
		driverOrdersNextOrderRestaurantDelivery.batch = batch;
	}
	final num? prepare = jsonConvert.convert<num>(json['prepare']);
	if (prepare != null) {
		driverOrdersNextOrderRestaurantDelivery.prepare = prepare;
	}
	return driverOrdersNextOrderRestaurantDelivery;
}

Map<String, dynamic> $DriverOrdersNextOrderRestaurantDeliveryToJson(DriverOrdersNextOrderRestaurantDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['note'] = entity.note?.toJson();
	data['batch'] = entity.batch;
	data['prepare'] = entity.prepare;
	return data;
}

DriverOrdersNextOrderCustomer $DriverOrdersNextOrderCustomerFromJson(Map<String, dynamic> json) {
	final DriverOrdersNextOrderCustomer driverOrdersNextOrderCustomer = DriverOrdersNextOrderCustomer();
	final String? language = jsonConvert.convert<String>(json['language']);
	if (language != null) {
		driverOrdersNextOrderCustomer.language = language;
	}
	return driverOrdersNextOrderCustomer;
}

Map<String, dynamic> $DriverOrdersNextOrderCustomerToJson(DriverOrdersNextOrderCustomer entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['language'] = entity.language;
	return data;
}

DriverOrdersNextOrderItem $DriverOrdersNextOrderItemFromJson(Map<String, dynamic> json) {
	final DriverOrdersNextOrderItem driverOrdersNextOrderItem = DriverOrdersNextOrderItem();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		driverOrdersNextOrderItem.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		driverOrdersNextOrderItem.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		driverOrdersNextOrderItem.zhHk = zhHk;
	}
	return driverOrdersNextOrderItem;
}

Map<String, dynamic> $DriverOrdersNextOrderItemToJson(DriverOrdersNextOrderItem entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}