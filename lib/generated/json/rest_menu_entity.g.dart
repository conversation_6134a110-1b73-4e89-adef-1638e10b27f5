import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/rest_menu_entity.dart';
import 'package:connect/data/multi_name_entity.dart';


RestMenuEntity $RestMenuEntityFromJson(Map<String, dynamic> json) {
	final RestMenuEntity restMenuEntity = RestMenuEntity();
	final List<RestMenuCategories?>? categories = jsonConvert.convertList<RestMenuCategories>(json['categories']);
	if (categories != null) {
		restMenuEntity.categories = categories;
	}
	final List<RestMenuFood?>? food = jsonConvert.convertList<RestMenuFood>(json['food']);
	if (food != null) {
		restMenuEntity.food = food;
	}
	final List<RestMenuReward?>? reward = jsonConvert.convertList<RestMenuReward>(json['reward']);
	if (reward != null) {
		restMenuEntity.reward = reward;
	}
	return restMenuEntity;
}

Map<String, dynamic> $RestMenuEntityToJson(RestMenuEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['categories'] =  entity.categories?.map((v) => v?.toJson()).toList();
	data['food'] =  entity.food?.map((v) => v?.toJson()).toList();
	data['reward'] =  entity.reward?.map((v) => v?.toJson()).toList();
	return data;
}

RestMenuCategories $RestMenuCategoriesFromJson(Map<String, dynamic> json) {
	final RestMenuCategories restMenuCategories = RestMenuCategories();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restMenuCategories.sId = sId;
	}
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		restMenuCategories.name = name;
	}
	final int? index = jsonConvert.convert<int>(json['index']);
	if (index != null) {
		restMenuCategories.index = index;
	}
	final MultiNameEntity? description = jsonConvert.convert<MultiNameEntity>(json['description']);
	if (description != null) {
		restMenuCategories.description = description;
	}
	final RestMenuCategoriesRestaurant? restaurant = jsonConvert.convert<RestMenuCategoriesRestaurant>(json['restaurant']);
	if (restaurant != null) {
		restMenuCategories.restaurant = restaurant;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restMenuCategories.updatedAt = updatedAt;
	}
	final int? iV = jsonConvert.convert<int>(json['__v']);
	if (iV != null) {
		restMenuCategories.iV = iV;
	}
	final bool? promotion = jsonConvert.convert<bool>(json['promotion']);
	if (promotion != null) {
		restMenuCategories.promotion = promotion;
	}
	final int? maxItem = jsonConvert.convert<int>(json['maxItem']);
	if (maxItem != null) {
		restMenuCategories.maxItem = maxItem;
	}
	final int? limit = jsonConvert.convert<int>(json['limit']);
	if (limit != null) {
		restMenuCategories.limit = limit;
	}
	final List<RestMenuFood?>? food = jsonConvert.convertList<RestMenuFood>(json['food']);
	if (food != null) {
		restMenuCategories.food = food;
	}
	final bool? isExpanded = jsonConvert.convert<bool>(json['isExpanded']);
	if (isExpanded != null) {
		restMenuCategories.isExpanded = isExpanded;
	}
	return restMenuCategories;
}

Map<String, dynamic> $RestMenuCategoriesToJson(RestMenuCategories entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name?.toJson();
	data['index'] = entity.index;
	data['description'] = entity.description?.toJson();
	data['restaurant'] = entity.restaurant?.toJson();
	data['updatedAt'] = entity.updatedAt;
	data['__v'] = entity.iV;
	data['promotion'] = entity.promotion;
	data['maxItem'] = entity.maxItem;
	data['limit'] = entity.limit;
	data['food'] =  entity.food?.map((v) => v?.toJson()).toList();
	data['isExpanded'] = entity.isExpanded;
	return data;
}

RestMenuCategoriesRestaurant $RestMenuCategoriesRestaurantFromJson(Map<String, dynamic> json) {
	final RestMenuCategoriesRestaurant restMenuCategoriesRestaurant = RestMenuCategoriesRestaurant();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restMenuCategoriesRestaurant.sId = sId;
	}
	return restMenuCategoriesRestaurant;
}

Map<String, dynamic> $RestMenuCategoriesRestaurantToJson(RestMenuCategoriesRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

RestMenuFood $RestMenuFoodFromJson(Map<String, dynamic> json) {
	final RestMenuFood restMenuFood = RestMenuFood();
	final String? foodId = jsonConvert.convert<String>(json['_id']);
	if (foodId != null) {
		restMenuFood.foodId = foodId;
	}
	final MultiNameEntity? description = jsonConvert.convert<MultiNameEntity>(json['description']);
	if (description != null) {
		restMenuFood.description = description;
	}
	final bool? available = jsonConvert.convert<bool>(json['available']);
	if (available != null) {
		restMenuFood.available = available;
	}
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		restMenuFood.name = name;
	}
	final String? code = jsonConvert.convert<String>(json['code']);
	if (code != null) {
		restMenuFood.code = code;
	}
	final int? price = jsonConvert.convert<int>(json['price']);
	if (price != null) {
		restMenuFood.price = price;
	}
	final int? cost = jsonConvert.convert<int>(json['cost']);
	if (cost != null) {
		restMenuFood.cost = cost;
	}
	final List<dynamic>? date = jsonConvert.convertListNotNull<dynamic>(json['date']);
	if (date != null) {
		restMenuFood.date = date;
	}
	final int? index = jsonConvert.convert<int>(json['index']);
	if (index != null) {
		restMenuFood.index = index;
	}
	final String? label = jsonConvert.convert<String>(json['label']);
	if (label != null) {
		restMenuFood.label = label;
	}
	final dynamic? limit = jsonConvert.convert<dynamic>(json['limit']);
	if (limit != null) {
		restMenuFood.limit = limit;
	}
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		restMenuFood.minimum = minimum;
	}
	final dynamic? stock = jsonConvert.convert<dynamic>(json['stock']);
	if (stock != null) {
		restMenuFood.stock = stock;
	}
	final RestMenuFoodCategory? category = jsonConvert.convert<RestMenuFoodCategory>(json['category']);
	if (category != null) {
		restMenuFood.category = category;
	}
	final List<RestMenuFoodOptions?>? options = jsonConvert.convertList<RestMenuFoodOptions>(json['options']);
	if (options != null) {
		restMenuFood.options = options;
	}
	final int? iV = jsonConvert.convert<int>(json['__v']);
	if (iV != null) {
		restMenuFood.iV = iV;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restMenuFood.updatedAt = updatedAt;
	}
	final List<RestMenuFoodHours?>? hours = jsonConvert.convertList<RestMenuFoodHours>(json['hours']);
	if (hours != null) {
		restMenuFood.hours = hours;
	}
	final RestMenuFoodImage? image = jsonConvert.convert<RestMenuFoodImage>(json['image']);
	if (image != null) {
		restMenuFood.image = image;
	}
	final RestMenuFoodCondition? condition = jsonConvert.convert<RestMenuFoodCondition>(json['condition']);
	if (condition != null) {
		restMenuFood.condition = condition;
	}
	final String? fullChar = jsonConvert.convert<String>(json['fullChar']);
	if (fullChar != null) {
		restMenuFood.fullChar = fullChar;
	}
	final int? popularity = jsonConvert.convert<int>(json['popularity']);
	if (popularity != null) {
		restMenuFood.popularity = popularity;
	}
	final bool? featured = jsonConvert.convert<bool>(json['featured']);
	if (featured != null) {
		restMenuFood.featured = featured;
	}
	final int? originalPrice = jsonConvert.convert<int>(json['originalPrice']);
	if (originalPrice != null) {
		restMenuFood.originalPrice = originalPrice;
	}
	final bool? cover = jsonConvert.convert<bool>(json['cover']);
	if (cover != null) {
		restMenuFood.cover = cover;
	}
	return restMenuFood;
}

Map<String, dynamic> $RestMenuFoodToJson(RestMenuFood entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.foodId;
	data['description'] = entity.description?.toJson();
	data['available'] = entity.available;
	data['name'] = entity.name?.toJson();
	data['code'] = entity.code;
	data['price'] = entity.price;
	data['cost'] = entity.cost;
	data['date'] =  entity.date;
	data['index'] = entity.index;
	data['label'] = entity.label;
	data['limit'] = entity.limit;
	data['minimum'] = entity.minimum;
	data['stock'] = entity.stock;
	data['category'] = entity.category?.toJson();
	data['options'] =  entity.options?.map((v) => v?.toJson()).toList();
	data['__v'] = entity.iV;
	data['updatedAt'] = entity.updatedAt;
	data['hours'] =  entity.hours?.map((v) => v?.toJson()).toList();
	data['image'] = entity.image?.toJson();
	data['condition'] = entity.condition?.toJson();
	data['fullChar'] = entity.fullChar;
	data['popularity'] = entity.popularity;
	data['featured'] = entity.featured;
	data['originalPrice'] = entity.originalPrice;
	data['cover'] = entity.cover;
	return data;
}

RestMenuFoodOptions $RestMenuFoodOptionsFromJson(Map<String, dynamic> json) {
	final RestMenuFoodOptions restMenuFoodOptions = RestMenuFoodOptions();
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		restMenuFoodOptions.name = name;
	}
	final int? min = jsonConvert.convert<int>(json['min']);
	if (min != null) {
		restMenuFoodOptions.min = min;
	}
	final int? max = jsonConvert.convert<int>(json['max']);
	if (max != null) {
		restMenuFoodOptions.max = max;
	}
	final List<RestMenuFoodOptionsItems?>? items = jsonConvert.convertList<RestMenuFoodOptionsItems>(json['items']);
	if (items != null) {
		restMenuFoodOptions.items = items;
	}
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restMenuFoodOptions.sId = sId;
	}
	return restMenuFoodOptions;
}

Map<String, dynamic> $RestMenuFoodOptionsToJson(RestMenuFoodOptions entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['name'] = entity.name?.toJson();
	data['min'] = entity.min;
	data['max'] = entity.max;
	data['items'] =  entity.items?.map((v) => v?.toJson()).toList();
	data['_id'] = entity.sId;
	return data;
}

RestMenuFoodOptionsItems $RestMenuFoodOptionsItemsFromJson(Map<String, dynamic> json) {
	final RestMenuFoodOptionsItems restMenuFoodOptionsItems = RestMenuFoodOptionsItems();
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		restMenuFoodOptionsItems.name = name;
	}
	final int? price = jsonConvert.convert<int>(json['price']);
	if (price != null) {
		restMenuFoodOptionsItems.price = price;
	}
	final RestMenuFoodOptionsItemsAnalytics? analytics = jsonConvert.convert<RestMenuFoodOptionsItemsAnalytics>(json['analytics']);
	if (analytics != null) {
		restMenuFoodOptionsItems.analytics = analytics;
	}
	final bool? available = jsonConvert.convert<bool>(json['available']);
	if (available != null) {
		restMenuFoodOptionsItems.available = available;
	}
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restMenuFoodOptionsItems.sId = sId;
	}
	return restMenuFoodOptionsItems;
}

Map<String, dynamic> $RestMenuFoodOptionsItemsToJson(RestMenuFoodOptionsItems entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['name'] = entity.name?.toJson();
	data['price'] = entity.price;
	data['analytics'] = entity.analytics?.toJson();
	data['available'] = entity.available;
	data['_id'] = entity.sId;
	return data;
}

RestMenuFoodOptionsItemsAnalytics $RestMenuFoodOptionsItemsAnalyticsFromJson(Map<String, dynamic> json) {
	final RestMenuFoodOptionsItemsAnalytics restMenuFoodOptionsItemsAnalytics = RestMenuFoodOptionsItemsAnalytics();
	final int? popularity = jsonConvert.convert<int>(json['popularity']);
	if (popularity != null) {
		restMenuFoodOptionsItemsAnalytics.popularity = popularity;
	}
	return restMenuFoodOptionsItemsAnalytics;
}

Map<String, dynamic> $RestMenuFoodOptionsItemsAnalyticsToJson(RestMenuFoodOptionsItemsAnalytics entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['popularity'] = entity.popularity;
	return data;
}

RestMenuFoodAnalytics $RestMenuFoodAnalyticsFromJson(Map<String, dynamic> json) {
	final RestMenuFoodAnalytics restMenuFoodAnalytics = RestMenuFoodAnalytics();
	final List<int?>? history = jsonConvert.convertList<int>(json['history']);
	if (history != null) {
		restMenuFoodAnalytics.history = history;
	}
	return restMenuFoodAnalytics;
}

Map<String, dynamic> $RestMenuFoodAnalyticsToJson(RestMenuFoodAnalytics entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['history'] =  entity.history;
	return data;
}

RestMenuFoodName $RestMenuFoodNameFromJson(Map<String, dynamic> json) {
	final RestMenuFoodName restMenuFoodName = RestMenuFoodName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restMenuFoodName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restMenuFoodName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restMenuFoodName.zhHk = zhHk;
	}
	return restMenuFoodName;
}

Map<String, dynamic> $RestMenuFoodNameToJson(RestMenuFoodName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestMenuFoodCategory $RestMenuFoodCategoryFromJson(Map<String, dynamic> json) {
	final RestMenuFoodCategory restMenuFoodCategory = RestMenuFoodCategory();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restMenuFoodCategory.sId = sId;
	}
	return restMenuFoodCategory;
}

Map<String, dynamic> $RestMenuFoodCategoryToJson(RestMenuFoodCategory entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

RestMenuFoodHours $RestMenuFoodHoursFromJson(Map<String, dynamic> json) {
	final RestMenuFoodHours restMenuFoodHours = RestMenuFoodHours();
	final int? dayOfWeek = jsonConvert.convert<int>(json['dayOfWeek']);
	if (dayOfWeek != null) {
		restMenuFoodHours.dayOfWeek = dayOfWeek;
	}
	final int? start = jsonConvert.convert<int>(json['start']);
	if (start != null) {
		restMenuFoodHours.start = start;
	}
	final int? end = jsonConvert.convert<int>(json['end']);
	if (end != null) {
		restMenuFoodHours.end = end;
	}
	return restMenuFoodHours;
}

Map<String, dynamic> $RestMenuFoodHoursToJson(RestMenuFoodHours entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['dayOfWeek'] = entity.dayOfWeek;
	data['start'] = entity.start;
	data['end'] = entity.end;
	return data;
}

RestMenuFoodImage $RestMenuFoodImageFromJson(Map<String, dynamic> json) {
	final RestMenuFoodImage restMenuFoodImage = RestMenuFoodImage();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restMenuFoodImage.sId = sId;
	}
	final String? url = jsonConvert.convert<String>(json['url']);
	if (url != null) {
		restMenuFoodImage.url = url;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		restMenuFoodImage.createdAt = createdAt;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restMenuFoodImage.updatedAt = updatedAt;
	}
	return restMenuFoodImage;
}

Map<String, dynamic> $RestMenuFoodImageToJson(RestMenuFoodImage entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['url'] = entity.url;
	data['createdAt'] = entity.createdAt;
	data['updatedAt'] = entity.updatedAt;
	return data;
}

RestMenuFoodCondition $RestMenuFoodConditionFromJson(Map<String, dynamic> json) {
	final RestMenuFoodCondition restMenuFoodCondition = RestMenuFoodCondition();
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		restMenuFoodCondition.minimum = minimum;
	}
	return restMenuFoodCondition;
}

Map<String, dynamic> $RestMenuFoodConditionToJson(RestMenuFoodCondition entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['minimum'] = entity.minimum;
	return data;
}

RestMenuReward $RestMenuRewardFromJson(Map<String, dynamic> json) {
	final RestMenuReward restMenuReward = RestMenuReward();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restMenuReward.sId = sId;
	}
	final RestMenuRewardCategory? category = jsonConvert.convert<RestMenuRewardCategory>(json['category']);
	if (category != null) {
		restMenuReward.category = category;
	}
	final int? index = jsonConvert.convert<int>(json['index']);
	if (index != null) {
		restMenuReward.index = index;
	}
	final RestMenuRewardName? name = jsonConvert.convert<RestMenuRewardName>(json['name']);
	if (name != null) {
		restMenuReward.name = name;
	}
	final String? color = jsonConvert.convert<String>(json['color']);
	if (color != null) {
		restMenuReward.color = color;
	}
	final int? price = jsonConvert.convert<int>(json['price']);
	if (price != null) {
		restMenuReward.price = price;
	}
	final dynamic? image = jsonConvert.convert<dynamic>(json['image']);
	if (image != null) {
		restMenuReward.image = image;
	}
	final dynamic? description = jsonConvert.convert<dynamic>(json['description']);
	if (description != null) {
		restMenuReward.description = description;
	}
	final dynamic? label = jsonConvert.convert<dynamic>(json['label']);
	if (label != null) {
		restMenuReward.label = label;
	}
	final bool? available = jsonConvert.convert<bool>(json['available']);
	if (available != null) {
		restMenuReward.available = available;
	}
	final int? popularity = jsonConvert.convert<int>(json['popularity']);
	if (popularity != null) {
		restMenuReward.popularity = popularity;
	}
	final List<dynamic>? options = jsonConvert.convertListNotNull<dynamic>(json['options']);
	if (options != null) {
		restMenuReward.options = options;
	}
	final RestMenuRewardAnalytics? analytics = jsonConvert.convert<RestMenuRewardAnalytics>(json['analytics']);
	if (analytics != null) {
		restMenuReward.analytics = analytics;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restMenuReward.updatedAt = updatedAt;
	}
	final int? zscore = jsonConvert.convert<int>(json['zscore']);
	if (zscore != null) {
		restMenuReward.zscore = zscore;
	}
	final int? cost = jsonConvert.convert<int>(json['cost']);
	if (cost != null) {
		restMenuReward.cost = cost;
	}
	final int? iV = jsonConvert.convert<int>(json['__v']);
	if (iV != null) {
		restMenuReward.iV = iV;
	}
	final List<dynamic>? tag = jsonConvert.convertListNotNull<dynamic>(json['tag']);
	if (tag != null) {
		restMenuReward.tag = tag;
	}
	final RestMenuRewardTags? tags = jsonConvert.convert<RestMenuRewardTags>(json['tags']);
	if (tags != null) {
		restMenuReward.tags = tags;
	}
	final RestMenuRewardScore? score = jsonConvert.convert<RestMenuRewardScore>(json['score']);
	if (score != null) {
		restMenuReward.score = score;
	}
	final bool? reward = jsonConvert.convert<bool>(json['reward']);
	if (reward != null) {
		restMenuReward.reward = reward;
	}
	final int? point = jsonConvert.convert<int>(json['point']);
	if (point != null) {
		restMenuReward.point = point;
	}
	final List<dynamic>? date = jsonConvert.convertListNotNull<dynamic>(json['date']);
	if (date != null) {
		restMenuReward.date = date;
	}
	final dynamic? hours = jsonConvert.convert<dynamic>(json['hours']);
	if (hours != null) {
		restMenuReward.hours = hours;
	}
	final int? last2WeekCount = jsonConvert.convert<int>(json['last2WeekCount']);
	if (last2WeekCount != null) {
		restMenuReward.last2WeekCount = last2WeekCount;
	}
	final String? fullChar = jsonConvert.convert<String>(json['fullChar']);
	if (fullChar != null) {
		restMenuReward.fullChar = fullChar;
	}
	final dynamic? code = jsonConvert.convert<dynamic>(json['code']);
	if (code != null) {
		restMenuReward.code = code;
	}
	final int? regionZscore = jsonConvert.convert<int>(json['regionZscore']);
	if (regionZscore != null) {
		restMenuReward.regionZscore = regionZscore;
	}
	final dynamic? condition = jsonConvert.convert<dynamic>(json['condition']);
	if (condition != null) {
		restMenuReward.condition = condition;
	}
	final dynamic? stock = jsonConvert.convert<dynamic>(json['stock']);
	if (stock != null) {
		restMenuReward.stock = stock;
	}
	final dynamic? limit = jsonConvert.convert<dynamic>(json['limit']);
	if (limit != null) {
		restMenuReward.limit = limit;
	}
	final int? originalPrice = jsonConvert.convert<int>(json['originalPrice']);
	if (originalPrice != null) {
		restMenuReward.originalPrice = originalPrice;
	}
	return restMenuReward;
}

Map<String, dynamic> $RestMenuRewardToJson(RestMenuReward entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['category'] = entity.category?.toJson();
	data['index'] = entity.index;
	data['name'] = entity.name?.toJson();
	data['color'] = entity.color;
	data['price'] = entity.price;
	data['image'] = entity.image;
	data['description'] = entity.description;
	data['label'] = entity.label;
	data['available'] = entity.available;
	data['popularity'] = entity.popularity;
	data['options'] =  entity.options;
	data['analytics'] = entity.analytics?.toJson();
	data['updatedAt'] = entity.updatedAt;
	data['zscore'] = entity.zscore;
	data['cost'] = entity.cost;
	data['__v'] = entity.iV;
	data['tag'] =  entity.tag;
	data['tags'] = entity.tags?.toJson();
	data['score'] = entity.score?.toJson();
	data['reward'] = entity.reward;
	data['point'] = entity.point;
	data['date'] =  entity.date;
	data['hours'] = entity.hours;
	data['last2WeekCount'] = entity.last2WeekCount;
	data['fullChar'] = entity.fullChar;
	data['code'] = entity.code;
	data['regionZscore'] = entity.regionZscore;
	data['condition'] = entity.condition;
	data['stock'] = entity.stock;
	data['limit'] = entity.limit;
	data['originalPrice'] = entity.originalPrice;
	return data;
}

RestMenuRewardCategory $RestMenuRewardCategoryFromJson(Map<String, dynamic> json) {
	final RestMenuRewardCategory restMenuRewardCategory = RestMenuRewardCategory();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restMenuRewardCategory.sId = sId;
	}
	return restMenuRewardCategory;
}

Map<String, dynamic> $RestMenuRewardCategoryToJson(RestMenuRewardCategory entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

RestMenuRewardName $RestMenuRewardNameFromJson(Map<String, dynamic> json) {
	final RestMenuRewardName restMenuRewardName = RestMenuRewardName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restMenuRewardName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restMenuRewardName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restMenuRewardName.zhHk = zhHk;
	}
	return restMenuRewardName;
}

Map<String, dynamic> $RestMenuRewardNameToJson(RestMenuRewardName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestMenuRewardAnalytics $RestMenuRewardAnalyticsFromJson(Map<String, dynamic> json) {
	final RestMenuRewardAnalytics restMenuRewardAnalytics = RestMenuRewardAnalytics();
	final List<int?>? history = jsonConvert.convertList<int>(json['history']);
	if (history != null) {
		restMenuRewardAnalytics.history = history;
	}
	return restMenuRewardAnalytics;
}

Map<String, dynamic> $RestMenuRewardAnalyticsToJson(RestMenuRewardAnalytics entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['history'] =  entity.history;
	return data;
}

RestMenuRewardTags $RestMenuRewardTagsFromJson(Map<String, dynamic> json) {
	final RestMenuRewardTags restMenuRewardTags = RestMenuRewardTags();
	return restMenuRewardTags;
}

Map<String, dynamic> $RestMenuRewardTagsToJson(RestMenuRewardTags entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	return data;
}

RestMenuRewardScore $RestMenuRewardScoreFromJson(Map<String, dynamic> json) {
	final RestMenuRewardScore restMenuRewardScore = RestMenuRewardScore();
	final int? restaurant = jsonConvert.convert<int>(json['restaurant']);
	if (restaurant != null) {
		restMenuRewardScore.restaurant = restaurant;
	}
	return restMenuRewardScore;
}

Map<String, dynamic> $RestMenuRewardScoreToJson(RestMenuRewardScore entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['restaurant'] = entity.restaurant;
	return data;
}