import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/driver/bean/driver_online_switch_entity.dart';

DriverOnlineSwitchEntity $DriverOnlineSwitchEntityFromJson(Map<String, dynamic> json) {
	final DriverOnlineSwitchEntity driverOnlineSwitchEntity = DriverOnlineSwitchEntity();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverOnlineSwitchEntity.sId = sId;
	}
	final bool? onCall = jsonConvert.convert<bool>(json['onCall']);
	if (onCall != null) {
		driverOnlineSwitchEntity.onCall = onCall;
	}
	return driverOnlineSwitchEntity;
}

Map<String, dynamic> $DriverOnlineSwitchEntityToJson(DriverOnlineSwitchEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['onCall'] = entity.onCall;
	return data;
}