import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/repository/restaurant_details_entity.dart';

RestaurantDetailsEntity $RestaurantDetailsEntityFromJson(Map<String, dynamic> json) {
	final RestaurantDetailsEntity restaurantDetailsEntity = RestaurantDetailsEntity();
	final RestaurantDetailsAddress? address = jsonConvert.convert<RestaurantDetailsAddress>(json['address']);
	if (address != null) {
		restaurantDetailsEntity.address = address;
	}
	final String? timezone = jsonConvert.convert<String>(json['timezone']);
	if (timezone != null) {
		restaurantDetailsEntity.timezone = timezone;
	}
	final RestaurantDetailsDelivery? delivery = jsonConvert.convert<RestaurantDetailsDelivery>(json['delivery']);
	if (delivery != null) {
		restaurantDetailsEntity.delivery = delivery;
	}
	return restaurantDetailsEntity;
}

Map<String, dynamic> $RestaurantDetailsEntityToJson(RestaurantDetailsEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['address'] = entity.address?.toJson();
	data['timezone'] = entity.timezone;
	data['delivery'] = entity.delivery?.toJson();
	return data;
}

RestaurantDetailsAddress $RestaurantDetailsAddressFromJson(Map<String, dynamic> json) {
	final RestaurantDetailsAddress restaurantDetailsAddress = RestaurantDetailsAddress();
	final String? country = jsonConvert.convert<String>(json['country']);
	if (country != null) {
		restaurantDetailsAddress.country = country;
	}
	return restaurantDetailsAddress;
}

Map<String, dynamic> $RestaurantDetailsAddressToJson(RestaurantDetailsAddress entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['country'] = entity.country;
	return data;
}

RestaurantDetailsDelivery $RestaurantDetailsDeliveryFromJson(Map<String, dynamic> json) {
	final RestaurantDetailsDelivery restaurantDetailsDelivery = RestaurantDetailsDelivery();
	final String? provider = jsonConvert.convert<String>(json['provider']);
	if (provider != null) {
		restaurantDetailsDelivery.provider = provider;
	}
	return restaurantDetailsDelivery;
}

Map<String, dynamic> $RestaurantDetailsDeliveryToJson(RestaurantDetailsDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['provider'] = entity.provider;
	return data;
}