import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/response_error_entity.dart';
import 'dart:io';


ResponseErrorEntity $ResponseErrorEntityFromJson(Map<String, dynamic> json) {
	final ResponseErrorEntity responseErrorEntity = ResponseErrorEntity();
	final String? code = jsonConvert.convert<String>(json['code']);
	if (code != null) {
		responseErrorEntity.code = code;
	}
	final String? message = jsonConvert.convert<String>(json['message']);
	if (message != null) {
		responseErrorEntity.message = message;
	}
	final ResponseErrorDetails? details = jsonConvert.convert<ResponseErrorDetails>(json['details']);
	if (details != null) {
		responseErrorEntity.details = details;
	}
	final String? error = jsonConvert.convert<String>(json['error']);
	if (error != null) {
		responseErrorEntity.error = error;
	}
	return responseErrorEntity;
}

Map<String, dynamic> $ResponseErrorEntityToJson(ResponseErrorEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['code'] = entity.code;
	data['message'] = entity.message;
	data['details'] = entity.details?.toJson();
	data['error'] = entity.error;
	return data;
}

ResponseErrorDetails $ResponseErrorDetailsFromJson(Map<String, dynamic> json) {
	final ResponseErrorDetails responseErrorDetails = ResponseErrorDetails();
	final String? skipSentry = jsonConvert.convert<String>(json['skipSentry']);
	if (skipSentry != null) {
		responseErrorDetails.skipSentry = skipSentry;
	}
	return responseErrorDetails;
}

Map<String, dynamic> $ResponseErrorDetailsToJson(ResponseErrorDetails entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['skipSentry'] = entity.skipSentry;
	return data;
}