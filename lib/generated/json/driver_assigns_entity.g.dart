import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/driver/bean/driver_assigns_entity.dart';
import 'package:connect/data/multi_name_entity.dart';


DriverAssignsEntity $DriverAssignsEntityFromJson(Map<String, dynamic> json) {
	final DriverAssignsEntity driverAssignsEntity = DriverAssignsEntity();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverAssignsEntity.sId = sId;
	}
	final String? passcode = jsonConvert.convert<String>(json['passcode']);
	if (passcode != null) {
		driverAssignsEntity.passcode = passcode;
	}
	final String? passcodeExt = jsonConvert.convert<String>(json['passcodeExt']);
	if (passcodeExt != null) {
		driverAssignsEntity.passcodeExt = passcodeExt;
	}
	final String? confirmedAt = jsonConvert.convert<String>(json['confirmedAt']);
	if (confirmedAt != null) {
		driverAssignsEntity.confirmedAt = confirmedAt;
	}
	final DriverAssignsRestaurant? restaurant = jsonConvert.convert<DriverAssignsRestaurant>(json['restaurant']);
	if (restaurant != null) {
		driverAssignsEntity.restaurant = restaurant;
	}
	final DriverAssignsCustomer? customer = jsonConvert.convert<DriverAssignsCustomer>(json['customer']);
	if (customer != null) {
		driverAssignsEntity.customer = customer;
	}
	final DriverAssignsDelivery? delivery = jsonConvert.convert<DriverAssignsDelivery>(json['delivery']);
	if (delivery != null) {
		driverAssignsEntity.delivery = delivery;
	}
	final DriverAssignsBundle? bundle = jsonConvert.convert<DriverAssignsBundle>(json['bundle']);
	if (bundle != null) {
		driverAssignsEntity.bundle = bundle;
	}
	final String? status = jsonConvert.convert<String>(json['status']);
	if (status != null) {
		driverAssignsEntity.status = status;
	}
	return driverAssignsEntity;
}

Map<String, dynamic> $DriverAssignsEntityToJson(DriverAssignsEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['passcode'] = entity.passcode;
	data['passcodeExt'] = entity.passcodeExt;
	data['confirmedAt'] = entity.confirmedAt;
	data['restaurant'] = entity.restaurant?.toJson();
	data['customer'] = entity.customer?.toJson();
	data['delivery'] = entity.delivery?.toJson();
	data['bundle'] = entity.bundle?.toJson();
	data['status'] = entity.status;
	return data;
}

DriverAssignsBundle $DriverAssignsBundleFromJson(Map<String, dynamic> json) {
	final DriverAssignsBundle driverAssignsBundle = DriverAssignsBundle();
	final String? combineId = jsonConvert.convert<String>(json['combineId']);
	if (combineId != null) {
		driverAssignsBundle.combineId = combineId;
	}
	return driverAssignsBundle;
}

Map<String, dynamic> $DriverAssignsBundleToJson(DriverAssignsBundle entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['combineId'] = entity.combineId;
	return data;
}

DriverAssignsCustomer $DriverAssignsCustomerFromJson(Map<String, dynamic> json) {
	final DriverAssignsCustomer driverAssignsCustomer = DriverAssignsCustomer();
	final String? phone = jsonConvert.convert<String>(json['phone']);
	if (phone != null) {
		driverAssignsCustomer.phone = phone;
	}
	return driverAssignsCustomer;
}

Map<String, dynamic> $DriverAssignsCustomerToJson(DriverAssignsCustomer entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['phone'] = entity.phone;
	return data;
}

DriverAssignsRestaurant $DriverAssignsRestaurantFromJson(Map<String, dynamic> json) {
	final DriverAssignsRestaurant driverAssignsRestaurant = DriverAssignsRestaurant();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverAssignsRestaurant.sId = sId;
	}
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		driverAssignsRestaurant.name = name;
	}
	final String? phone = jsonConvert.convert<String>(json['phone']);
	if (phone != null) {
		driverAssignsRestaurant.phone = phone;
	}
	return driverAssignsRestaurant;
}

Map<String, dynamic> $DriverAssignsRestaurantToJson(DriverAssignsRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name?.toJson();
	data['phone'] = entity.phone;
	return data;
}

DriverAssignsDelivery $DriverAssignsDeliveryFromJson(Map<String, dynamic> json) {
	final DriverAssignsDelivery driverAssignsDelivery = DriverAssignsDelivery();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverAssignsDelivery.sId = sId;
	}
	final String? provider = jsonConvert.convert<String>(json['provider']);
	if (provider != null) {
		driverAssignsDelivery.provider = provider;
	}
	final String? status = jsonConvert.convert<String>(json['status']);
	if (status != null) {
		driverAssignsDelivery.status = status;
	}
	final String? completedAt = jsonConvert.convert<String>(json['completedAt']);
	if (completedAt != null) {
		driverAssignsDelivery.completedAt = completedAt;
	}
	final DriverAssignsDeliveryStats? stats = jsonConvert.convert<DriverAssignsDeliveryStats>(json['stats']);
	if (stats != null) {
		driverAssignsDelivery.stats = stats;
	}
	final num? time = jsonConvert.convert<num>(json['time']);
	if (time != null) {
		driverAssignsDelivery.time = time;
	}
	return driverAssignsDelivery;
}

Map<String, dynamic> $DriverAssignsDeliveryToJson(DriverAssignsDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['provider'] = entity.provider;
	data['status'] = entity.status;
	data['completedAt'] = entity.completedAt;
	data['stats'] = entity.stats?.toJson();
	data['time'] = entity.time;
	return data;
}

DriverAssignsDeliveryStats $DriverAssignsDeliveryStatsFromJson(Map<String, dynamic> json) {
	final DriverAssignsDeliveryStats driverAssignsDeliveryStats = DriverAssignsDeliveryStats();
	final num? enRouteToPickup = jsonConvert.convert<num>(json['en-route-to-pickup']);
	if (enRouteToPickup != null) {
		driverAssignsDeliveryStats.enRouteToPickup = enRouteToPickup;
	}
	final num? atPickup = jsonConvert.convert<num>(json['at-pickup']);
	if (atPickup != null) {
		driverAssignsDeliveryStats.atPickup = atPickup;
	}
	final num? minPickupCompleted = jsonConvert.convert<num>(json['min-pickup-completed']);
	if (minPickupCompleted != null) {
		driverAssignsDeliveryStats.minPickupCompleted = minPickupCompleted;
	}
	final num? pickupCompleted = jsonConvert.convert<num>(json['pickup-completed']);
	if (pickupCompleted != null) {
		driverAssignsDeliveryStats.pickupCompleted = pickupCompleted;
	}
	final num? enRouteToDropoff = jsonConvert.convert<num>(json['en-route-to-dropoff']);
	if (enRouteToDropoff != null) {
		driverAssignsDeliveryStats.enRouteToDropoff = enRouteToDropoff;
	}
	return driverAssignsDeliveryStats;
}

Map<String, dynamic> $DriverAssignsDeliveryStatsToJson(DriverAssignsDeliveryStats entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['en-route-to-pickup'] = entity.enRouteToPickup;
	data['at-pickup'] = entity.atPickup;
	data['min-pickup-completed'] = entity.minPickupCompleted;
	data['pickup-completed'] = entity.pickupCompleted;
	data['en-route-to-dropoff'] = entity.enRouteToDropoff;
	return data;
}