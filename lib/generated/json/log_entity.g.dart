import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/log_entity.dart';

LogEntity $LogEntityFromJson(Map<String, dynamic> json) {
	final LogEntity logEntity = LogEntity();
	final String? method = jsonConvert.convert<String>(json['method']);
	if (method != null) {
		logEntity.method = method;
	}
	final String? bodyParams = jsonConvert.convert<String>(json['bodyParams']);
	if (bodyParams != null) {
		logEntity.bodyParams = bodyParams;
	}
	final String? url = jsonConvert.convert<String>(json['url']);
	if (url != null) {
		logEntity.url = url;
	}
	final String? header = jsonConvert.convert<String>(json['header']);
	if (header != null) {
		logEntity.header = header;
	}
	final String? response = jsonConvert.convert<String>(json['response']);
	if (response != null) {
		logEntity.response = response;
	}
	final int? responseLength = jsonConvert.convert<int>(json['responseLength']);
	if (responseLength != null) {
		logEntity.responseLength = responseLength;
	}
	final int? statusCode = jsonConvert.convert<int>(json['statusCode']);
	if (statusCode != null) {
		logEntity.statusCode = statusCode;
	}
	return logEntity;
}

Map<String, dynamic> $LogEntityToJson(LogEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['method'] = entity.method;
	data['bodyParams'] = entity.bodyParams;
	data['url'] = entity.url;
	data['header'] = entity.header;
	data['response'] = entity.response;
	data['responseLength'] = entity.responseLength;
	data['statusCode'] = entity.statusCode;
	return data;
}