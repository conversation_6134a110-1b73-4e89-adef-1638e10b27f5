import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/PhoneMasking.dart';
import 'package:json_annotation/json_annotation.dart';


PhoneMasking $PhoneMaskingFromJson(Map<String, dynamic> json) {
	final PhoneMasking phoneMasking = PhoneMasking();
	final String? phone = jsonConvert.convert<String>(json['phone']);
	if (phone != null) {
		phoneMasking.phone = phone;
	}
	return phoneMasking;
}

Map<String, dynamic> $PhoneMaskingToJson(PhoneMasking entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['phone'] = entity.phone;
	return data;
}