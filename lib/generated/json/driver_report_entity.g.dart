import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/driver/bean/driver_report_entity.dart';

DriverReportEntity $DriverReportEntityFromJson(Map<String, dynamic> json) {
	final DriverReportEntity driverReportEntity = DriverReportEntity();
	final DriverReportId? dId = jsonConvert.convert<DriverReportId>(json['_id']);
	if (dId != null) {
		driverReportEntity.dId = dId;
	}
	final double? number = jsonConvert.convert<double>(json['number']);
	if (number != null) {
		driverReportEntity.number = number;
	}
	final double? uniqueNumber = jsonConvert.convert<double>(json['uniqueNumber']);
	if (uniqueNumber != null) {
		driverReportEntity.uniqueNumber = uniqueNumber;
	}
	final double? singleNumber = jsonConvert.convert<double>(json['singleNumber']);
	if (singleNumber != null) {
		driverReportEntity.singleNumber = singleNumber;
	}
	final double? bundleNumber = jsonConvert.convert<double>(json['bundleNumber']);
	if (bundleNumber != null) {
		driverReportEntity.bundleNumber = bundleNumber;
	}
	final String? deliveryTime = jsonConvert.convert<String>(json['deliveryTime']);
	if (deliveryTime != null) {
		driverReportEntity.deliveryTime = deliveryTime;
	}
	final DriverReportFees? fees = jsonConvert.convert<DriverReportFees>(json['fees']);
	if (fees != null) {
		driverReportEntity.fees = fees;
	}
	final DriverReportCommission? commission = jsonConvert.convert<DriverReportCommission>(json['commission']);
	if (commission != null) {
		driverReportEntity.commission = commission;
	}
	final DriverReportAdjustments? adjustments = jsonConvert.convert<DriverReportAdjustments>(json['adjustments']);
	if (adjustments != null) {
		driverReportEntity.adjustments = adjustments;
	}
	final DriverReportDistribution? distribution = jsonConvert.convert<DriverReportDistribution>(json['distribution']);
	if (distribution != null) {
		driverReportEntity.distribution = distribution;
	}
	final DriverReportRating? rating = jsonConvert.convert<DriverReportRating>(json['rating']);
	if (rating != null) {
		driverReportEntity.rating = rating;
	}
	final DriverReportDriving? driving = jsonConvert.convert<DriverReportDriving>(json['driving']);
	if (driving != null) {
		driverReportEntity.driving = driving;
	}
	return driverReportEntity;
}

Map<String, dynamic> $DriverReportEntityToJson(DriverReportEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.dId?.toJson();
	data['number'] = entity.number;
	data['uniqueNumber'] = entity.uniqueNumber;
	data['singleNumber'] = entity.singleNumber;
	data['bundleNumber'] = entity.bundleNumber;
	data['deliveryTime'] = entity.deliveryTime;
	data['fees'] = entity.fees?.toJson();
	data['commission'] = entity.commission?.toJson();
	data['adjustments'] = entity.adjustments?.toJson();
	data['distribution'] = entity.distribution?.toJson();
	data['rating'] = entity.rating?.toJson();
	data['driving'] = entity.driving?.toJson();
	return data;
}

DriverReportRating $DriverReportRatingFromJson(Map<String, dynamic> json) {
	final DriverReportRating driverReportRating = DriverReportRating();
	final num? good = jsonConvert.convert<num>(json['good']);
	if (good != null) {
		driverReportRating.good = good;
	}
	final num? bad = jsonConvert.convert<num>(json['bad']);
	if (bad != null) {
		driverReportRating.bad = bad;
	}
	return driverReportRating;
}

Map<String, dynamic> $DriverReportRatingToJson(DriverReportRating entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['good'] = entity.good;
	data['bad'] = entity.bad;
	return data;
}

DriverReportDriving $DriverReportDrivingFromJson(Map<String, dynamic> json) {
	final DriverReportDriving driverReportDriving = DriverReportDriving();
	final DriverReportDrivingTime? time = jsonConvert.convert<DriverReportDrivingTime>(json['time']);
	if (time != null) {
		driverReportDriving.time = time;
	}
	final DriverReportDrivingDistance? distance = jsonConvert.convert<DriverReportDrivingDistance>(json['distance']);
	if (distance != null) {
		driverReportDriving.distance = distance;
	}
	return driverReportDriving;
}

Map<String, dynamic> $DriverReportDrivingToJson(DriverReportDriving entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['time'] = entity.time?.toJson();
	data['distance'] = entity.distance?.toJson();
	return data;
}

DriverReportDrivingTime $DriverReportDrivingTimeFromJson(Map<String, dynamic> json) {
	final DriverReportDrivingTime driverReportDrivingTime = DriverReportDrivingTime();
	final num? pickup = jsonConvert.convert<num>(json['pickup']);
	if (pickup != null) {
		driverReportDrivingTime.pickup = pickup;
	}
	final num? dropoff = jsonConvert.convert<num>(json['dropoff']);
	if (dropoff != null) {
		driverReportDrivingTime.dropoff = dropoff;
	}
	final num? total = jsonConvert.convert<num>(json['total']);
	if (total != null) {
		driverReportDrivingTime.total = total;
	}
	return driverReportDrivingTime;
}

Map<String, dynamic> $DriverReportDrivingTimeToJson(DriverReportDrivingTime entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['pickup'] = entity.pickup;
	data['dropoff'] = entity.dropoff;
	data['total'] = entity.total;
	return data;
}

DriverReportDrivingDistance $DriverReportDrivingDistanceFromJson(Map<String, dynamic> json) {
	final DriverReportDrivingDistance driverReportDrivingDistance = DriverReportDrivingDistance();
	final num? pickup = jsonConvert.convert<num>(json['pickup']);
	if (pickup != null) {
		driverReportDrivingDistance.pickup = pickup;
	}
	final num? dropoff = jsonConvert.convert<num>(json['dropoff']);
	if (dropoff != null) {
		driverReportDrivingDistance.dropoff = dropoff;
	}
	final num? total = jsonConvert.convert<num>(json['total']);
	if (total != null) {
		driverReportDrivingDistance.total = total;
	}
	return driverReportDrivingDistance;
}

Map<String, dynamic> $DriverReportDrivingDistanceToJson(DriverReportDrivingDistance entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['pickup'] = entity.pickup;
	data['dropoff'] = entity.dropoff;
	data['total'] = entity.total;
	return data;
}

DriverReportId $DriverReportIdFromJson(Map<String, dynamic> json) {
	final DriverReportId driverReportId = DriverReportId();
	final String? batch = jsonConvert.convert<String>(json['batch']);
	if (batch != null) {
		driverReportId.batch = batch;
	}
	return driverReportId;
}

Map<String, dynamic> $DriverReportIdToJson(DriverReportId entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['batch'] = entity.batch;
	return data;
}

DriverReportFees $DriverReportFeesFromJson(Map<String, dynamic> json) {
	final DriverReportFees driverReportFees = DriverReportFees();
	final DriverReportFeesTip? tip = jsonConvert.convert<DriverReportFeesTip>(json['tip']);
	if (tip != null) {
		driverReportFees.tip = tip;
	}
	return driverReportFees;
}

Map<String, dynamic> $DriverReportFeesToJson(DriverReportFees entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['tip'] = entity.tip?.toJson();
	return data;
}

DriverReportFeesTip $DriverReportFeesTipFromJson(Map<String, dynamic> json) {
	final DriverReportFeesTip driverReportFeesTip = DriverReportFeesTip();
	final double? amount = jsonConvert.convert<double>(json['amount']);
	if (amount != null) {
		driverReportFeesTip.amount = amount;
	}
	return driverReportFeesTip;
}

Map<String, dynamic> $DriverReportFeesTipToJson(DriverReportFeesTip entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['amount'] = entity.amount;
	return data;
}

DriverReportCommission $DriverReportCommissionFromJson(Map<String, dynamic> json) {
	final DriverReportCommission driverReportCommission = DriverReportCommission();
	final double? driver = jsonConvert.convert<double>(json['driver']);
	if (driver != null) {
		driverReportCommission.driver = driver;
	}
	return driverReportCommission;
}

Map<String, dynamic> $DriverReportCommissionToJson(DriverReportCommission entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['driver'] = entity.driver;
	return data;
}

DriverReportAdjustments $DriverReportAdjustmentsFromJson(Map<String, dynamic> json) {
	final DriverReportAdjustments driverReportAdjustments = DriverReportAdjustments();
	final double? driver = jsonConvert.convert<double>(json['driver']);
	if (driver != null) {
		driverReportAdjustments.driver = driver;
	}
	return driverReportAdjustments;
}

Map<String, dynamic> $DriverReportAdjustmentsToJson(DriverReportAdjustments entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['driver'] = entity.driver;
	return data;
}

DriverReportDistribution $DriverReportDistributionFromJson(Map<String, dynamic> json) {
	final DriverReportDistribution driverReportDistribution = DriverReportDistribution();
	final double? driver = jsonConvert.convert<double>(json['driver']);
	if (driver != null) {
		driverReportDistribution.driver = driver;
	}
	return driverReportDistribution;
}

Map<String, dynamic> $DriverReportDistributionToJson(DriverReportDistribution entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['driver'] = entity.driver;
	return data;
}