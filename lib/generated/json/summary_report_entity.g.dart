import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/summary_report_entity.dart';

SummaryReportEntity $SummaryReportEntityFromJson(Map<String, dynamic> json) {
	final SummaryReportEntity summaryReportEntity = SummaryReportEntity();
	final SummaryReportId? sId = jsonConvert.convert<SummaryReportId>(json['_id']);
	if (sId != null) {
		summaryReportEntity.sId = sId;
	}
	final double? subtotal = jsonConvert.convert<double>(json['subtotal']);
	if (subtotal != null) {
		summaryReportEntity.subtotal = subtotal;
	}
	final SummaryReportCost? cost = jsonConvert.convert<SummaryReportCost>(json['cost']);
	if (cost != null) {
		summaryReportEntity.cost = cost;
	}
	final double? total = jsonConvert.convert<double>(json['total']);
	if (total != null) {
		summaryReportEntity.total = total;
	}
	final SummaryReportFees? fees = jsonConvert.convert<SummaryReportFees>(json['fees']);
	if (fees != null) {
		summaryReportEntity.fees = fees;
	}
	final SummaryReportPostmates? postmates = jsonConvert.convert<SummaryReportPostmates>(json['postmates']);
	if (postmates != null) {
		summaryReportEntity.postmates = postmates;
	}
	final SummaryReportDelivery? delivery = jsonConvert.convert<SummaryReportDelivery>(json['delivery']);
	if (delivery != null) {
		summaryReportEntity.delivery = delivery;
	}
	final SummaryReportAdjustments? adjustments = jsonConvert.convert<SummaryReportAdjustments>(json['adjustments']);
	if (adjustments != null) {
		summaryReportEntity.adjustments = adjustments;
	}
	final SummaryReportCoupon? coupon = jsonConvert.convert<SummaryReportCoupon>(json['coupon']);
	if (coupon != null) {
		summaryReportEntity.coupon = coupon;
	}
	final SummaryReportCommission? commission = jsonConvert.convert<SummaryReportCommission>(json['commission']);
	if (commission != null) {
		summaryReportEntity.commission = commission;
	}
	final SummaryReportDistribution? distribution = jsonConvert.convert<SummaryReportDistribution>(json['distribution']);
	if (distribution != null) {
		summaryReportEntity.distribution = distribution;
	}
	final double? number = jsonConvert.convert<double>(json['number']);
	if (number != null) {
		summaryReportEntity.number = number;
	}
	final double? uniqueNumber = jsonConvert.convert<double>(json['uniqueNumber']);
	if (uniqueNumber != null) {
		summaryReportEntity.uniqueNumber = uniqueNumber;
	}
	final double? bundleNumber = jsonConvert.convert<double>(json['bundleNumber']);
	if (bundleNumber != null) {
		summaryReportEntity.bundleNumber = bundleNumber;
	}
	final double? subtotalAvg = jsonConvert.convert<double>(json['subtotalAvg']);
	if (subtotalAvg != null) {
		summaryReportEntity.subtotalAvg = subtotalAvg;
	}
	final double? totalAvg = jsonConvert.convert<double>(json['totalAvg']);
	if (totalAvg != null) {
		summaryReportEntity.totalAvg = totalAvg;
	}
	final SummaryReportPayment? payment = jsonConvert.convert<SummaryReportPayment>(json['payment']);
	if (payment != null) {
		summaryReportEntity.payment = payment;
	}
	return summaryReportEntity;
}

Map<String, dynamic> $SummaryReportEntityToJson(SummaryReportEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId?.toJson();
	data['subtotal'] = entity.subtotal;
	data['cost'] = entity.cost?.toJson();
	data['total'] = entity.total;
	data['fees'] = entity.fees?.toJson();
	data['postmates'] = entity.postmates?.toJson();
	data['delivery'] = entity.delivery?.toJson();
	data['adjustments'] = entity.adjustments?.toJson();
	data['coupon'] = entity.coupon?.toJson();
	data['commission'] = entity.commission?.toJson();
	data['distribution'] = entity.distribution?.toJson();
	data['number'] = entity.number;
	data['uniqueNumber'] = entity.uniqueNumber;
	data['bundleNumber'] = entity.bundleNumber;
	data['subtotalAvg'] = entity.subtotalAvg;
	data['totalAvg'] = entity.totalAvg;
	data['payment'] = entity.payment?.toJson();
	return data;
}

SummaryReportId $SummaryReportIdFromJson(Map<String, dynamic> json) {
	final SummaryReportId summaryReportId = SummaryReportId();
	final bool? delivery = jsonConvert.convert<bool>(json['delivery']);
	if (delivery != null) {
		summaryReportId.delivery = delivery;
	}
	final bool? provider = jsonConvert.convert<bool>(json['provider']);
	if (provider != null) {
		summaryReportId.provider = provider;
	}
	return summaryReportId;
}

Map<String, dynamic> $SummaryReportIdToJson(SummaryReportId entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['delivery'] = entity.delivery;
	data['provider'] = entity.provider;
	return data;
}

SummaryReportCost $SummaryReportCostFromJson(Map<String, dynamic> json) {
	final SummaryReportCost summaryReportCost = SummaryReportCost();
	final double? subtotal = jsonConvert.convert<double>(json['subtotal']);
	if (subtotal != null) {
		summaryReportCost.subtotal = subtotal;
	}
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		summaryReportCost.tax = tax;
	}
	return summaryReportCost;
}

Map<String, dynamic> $SummaryReportCostToJson(SummaryReportCost entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['subtotal'] = entity.subtotal;
	data['tax'] = entity.tax;
	return data;
}

SummaryReportFees $SummaryReportFeesFromJson(Map<String, dynamic> json) {
	final SummaryReportFees summaryReportFees = SummaryReportFees();
	final double? delivery = jsonConvert.convert<double>(json['delivery']);
	if (delivery != null) {
		summaryReportFees.delivery = delivery;
	}
	final SummaryReportFeesTip? tip = jsonConvert.convert<SummaryReportFeesTip>(json['tip']);
	if (tip != null) {
		summaryReportFees.tip = tip;
	}
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		summaryReportFees.tax = tax;
	}
	final double? service = jsonConvert.convert<double>(json['service']);
	if (service != null) {
		summaryReportFees.service = service;
	}
	final double? credit = jsonConvert.convert<double>(json['credit']);
	if (credit != null) {
		summaryReportFees.credit = credit;
	}
	final double? delta = jsonConvert.convert<double>(json['delta']);
	if (delta != null) {
		summaryReportFees.delta = delta;
	}
	return summaryReportFees;
}

Map<String, dynamic> $SummaryReportFeesToJson(SummaryReportFees entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['delivery'] = entity.delivery;
	data['tip'] = entity.tip?.toJson();
	data['tax'] = entity.tax;
	data['service'] = entity.service;
	data['credit'] = entity.credit;
	data['delta'] = entity.delta;
	return data;
}

SummaryReportFeesTip $SummaryReportFeesTipFromJson(Map<String, dynamic> json) {
	final SummaryReportFeesTip summaryReportFeesTip = SummaryReportFeesTip();
	final double? amount = jsonConvert.convert<double>(json['amount']);
	if (amount != null) {
		summaryReportFeesTip.amount = amount;
	}
	return summaryReportFeesTip;
}

Map<String, dynamic> $SummaryReportFeesTipToJson(SummaryReportFeesTip entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['amount'] = entity.amount;
	return data;
}

SummaryReportPostmates $SummaryReportPostmatesFromJson(Map<String, dynamic> json) {
	final SummaryReportPostmates summaryReportPostmates = SummaryReportPostmates();
	final SummaryReportPostmatesDelivery? delivery = jsonConvert.convert<SummaryReportPostmatesDelivery>(json['delivery']);
	if (delivery != null) {
		summaryReportPostmates.delivery = delivery;
	}
	return summaryReportPostmates;
}

Map<String, dynamic> $SummaryReportPostmatesToJson(SummaryReportPostmates entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['delivery'] = entity.delivery?.toJson();
	return data;
}

SummaryReportPostmatesDelivery $SummaryReportPostmatesDeliveryFromJson(Map<String, dynamic> json) {
	final SummaryReportPostmatesDelivery summaryReportPostmatesDelivery = SummaryReportPostmatesDelivery();
	final double? fee = jsonConvert.convert<double>(json['fee']);
	if (fee != null) {
		summaryReportPostmatesDelivery.fee = fee;
	}
	return summaryReportPostmatesDelivery;
}

Map<String, dynamic> $SummaryReportPostmatesDeliveryToJson(SummaryReportPostmatesDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['fee'] = entity.fee;
	return data;
}

SummaryReportDelivery $SummaryReportDeliveryFromJson(Map<String, dynamic> json) {
	final SummaryReportDelivery summaryReportDelivery = SummaryReportDelivery();
	final double? fee = jsonConvert.convert<double>(json['fee']);
	if (fee != null) {
		summaryReportDelivery.fee = fee;
	}
	return summaryReportDelivery;
}

Map<String, dynamic> $SummaryReportDeliveryToJson(SummaryReportDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['fee'] = entity.fee;
	return data;
}

SummaryReportAdjustments $SummaryReportAdjustmentsFromJson(Map<String, dynamic> json) {
	final SummaryReportAdjustments summaryReportAdjustments = SummaryReportAdjustments();
	final double? ricepo = jsonConvert.convert<double>(json['ricepo']);
	if (ricepo != null) {
		summaryReportAdjustments.ricepo = ricepo;
	}
	final double? restaurant = jsonConvert.convert<double>(json['restaurant']);
	if (restaurant != null) {
		summaryReportAdjustments.restaurant = restaurant;
	}
	final double? driver = jsonConvert.convert<double>(json['driver']);
	if (driver != null) {
		summaryReportAdjustments.driver = driver;
	}
	final double? customer = jsonConvert.convert<double>(json['customer']);
	if (customer != null) {
		summaryReportAdjustments.customer = customer;
	}
	final double? unionpay = jsonConvert.convert<double>(json['unionpay']);
	if (unionpay != null) {
		summaryReportAdjustments.unionpay = unionpay;
	}
	return summaryReportAdjustments;
}

Map<String, dynamic> $SummaryReportAdjustmentsToJson(SummaryReportAdjustments entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['ricepo'] = entity.ricepo;
	data['restaurant'] = entity.restaurant;
	data['driver'] = entity.driver;
	data['customer'] = entity.customer;
	data['unionpay'] = entity.unionpay;
	return data;
}

SummaryReportCoupon $SummaryReportCouponFromJson(Map<String, dynamic> json) {
	final SummaryReportCoupon summaryReportCoupon = SummaryReportCoupon();
	final double? amount = jsonConvert.convert<double>(json['amount']);
	if (amount != null) {
		summaryReportCoupon.amount = amount;
	}
	return summaryReportCoupon;
}

Map<String, dynamic> $SummaryReportCouponToJson(SummaryReportCoupon entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['amount'] = entity.amount;
	return data;
}

SummaryReportCommission $SummaryReportCommissionFromJson(Map<String, dynamic> json) {
	final SummaryReportCommission summaryReportCommission = SummaryReportCommission();
	final double? subtotal = jsonConvert.convert<double>(json['subtotal']);
	if (subtotal != null) {
		summaryReportCommission.subtotal = subtotal;
	}
	final double? total = jsonConvert.convert<double>(json['total']);
	if (total != null) {
		summaryReportCommission.total = total;
	}
	final double? service = jsonConvert.convert<double>(json['service']);
	if (service != null) {
		summaryReportCommission.service = service;
	}
	final double? driver = jsonConvert.convert<double>(json['driver']);
	if (driver != null) {
		summaryReportCommission.driver = driver;
	}
	return summaryReportCommission;
}

Map<String, dynamic> $SummaryReportCommissionToJson(SummaryReportCommission entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['subtotal'] = entity.subtotal;
	data['total'] = entity.total;
	data['service'] = entity.service;
	data['driver'] = entity.driver;
	return data;
}

SummaryReportDistribution $SummaryReportDistributionFromJson(Map<String, dynamic> json) {
	final SummaryReportDistribution summaryReportDistribution = SummaryReportDistribution();
	final SummaryReportDistributionRicepo? ricepo = jsonConvert.convert<SummaryReportDistributionRicepo>(json['ricepo']);
	if (ricepo != null) {
		summaryReportDistribution.ricepo = ricepo;
	}
	final double? restaurant = jsonConvert.convert<double>(json['restaurant']);
	if (restaurant != null) {
		summaryReportDistribution.restaurant = restaurant;
	}
	return summaryReportDistribution;
}

Map<String, dynamic> $SummaryReportDistributionToJson(SummaryReportDistribution entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['ricepo'] = entity.ricepo?.toJson();
	data['restaurant'] = entity.restaurant;
	return data;
}

SummaryReportDistributionRicepo $SummaryReportDistributionRicepoFromJson(Map<String, dynamic> json) {
	final SummaryReportDistributionRicepo summaryReportDistributionRicepo = SummaryReportDistributionRicepo();
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		summaryReportDistributionRicepo.tax = tax;
	}
	return summaryReportDistributionRicepo;
}

Map<String, dynamic> $SummaryReportDistributionRicepoToJson(SummaryReportDistributionRicepo entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['tax'] = entity.tax;
	return data;
}

SummaryReportPayment $SummaryReportPaymentFromJson(Map<String, dynamic> json) {
	final SummaryReportPayment summaryReportPayment = SummaryReportPayment();
	final SummaryReportPaymentSale? sale = jsonConvert.convert<SummaryReportPaymentSale>(json['sale']);
	if (sale != null) {
		summaryReportPayment.sale = sale;
	}
	final SummaryReportPaymentNumber? number = jsonConvert.convert<SummaryReportPaymentNumber>(json['number']);
	if (number != null) {
		summaryReportPayment.number = number;
	}
	return summaryReportPayment;
}

Map<String, dynamic> $SummaryReportPaymentToJson(SummaryReportPayment entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['sale'] = entity.sale?.toJson();
	data['number'] = entity.number?.toJson();
	return data;
}

SummaryReportPaymentSale $SummaryReportPaymentSaleFromJson(Map<String, dynamic> json) {
	final SummaryReportPaymentSale summaryReportPaymentSale = SummaryReportPaymentSale();
	final double? stripe = jsonConvert.convert<double>(json['stripe']);
	if (stripe != null) {
		summaryReportPaymentSale.stripe = stripe;
	}
	final double? alipay = jsonConvert.convert<double>(json['alipay']);
	if (alipay != null) {
		summaryReportPaymentSale.alipay = alipay;
	}
	final double? wechat = jsonConvert.convert<double>(json['wechat']);
	if (wechat != null) {
		summaryReportPaymentSale.wechat = wechat;
	}
	final double? applepay = jsonConvert.convert<double>(json['applepay']);
	if (applepay != null) {
		summaryReportPaymentSale.applepay = applepay;
	}
	return summaryReportPaymentSale;
}

Map<String, dynamic> $SummaryReportPaymentSaleToJson(SummaryReportPaymentSale entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['stripe'] = entity.stripe;
	data['alipay'] = entity.alipay;
	data['wechat'] = entity.wechat;
	data['applepay'] = entity.applepay;
	return data;
}

SummaryReportPaymentNumber $SummaryReportPaymentNumberFromJson(Map<String, dynamic> json) {
	final SummaryReportPaymentNumber summaryReportPaymentNumber = SummaryReportPaymentNumber();
	final double? stripe = jsonConvert.convert<double>(json['stripe']);
	if (stripe != null) {
		summaryReportPaymentNumber.stripe = stripe;
	}
	final double? alipay = jsonConvert.convert<double>(json['alipay']);
	if (alipay != null) {
		summaryReportPaymentNumber.alipay = alipay;
	}
	final double? wechat = jsonConvert.convert<double>(json['wechat']);
	if (wechat != null) {
		summaryReportPaymentNumber.wechat = wechat;
	}
	final double? applepay = jsonConvert.convert<double>(json['applepay']);
	if (applepay != null) {
		summaryReportPaymentNumber.applepay = applepay;
	}
	return summaryReportPaymentNumber;
}

Map<String, dynamic> $SummaryReportPaymentNumberToJson(SummaryReportPaymentNumber entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['stripe'] = entity.stripe;
	data['alipay'] = entity.alipay;
	data['wechat'] = entity.wechat;
	data['applepay'] = entity.applepay;
	return data;
}