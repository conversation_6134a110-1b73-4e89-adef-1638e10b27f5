// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:connect/data/PhoneMasking.dart';
import 'package:connect/data/history_total_report_entity.dart';
import 'package:connect/data/log_entity.dart';
import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/data/ready_entity.dart';
import 'package:connect/data/repository/restaurant_details_entity.dart';
import 'package:connect/data/response_error_entity.dart';
import 'package:connect/data/rest_entity.dart';
import 'package:connect/data/rest_menu_entity.dart';
import 'package:connect/data/rest_menu_toggle_entity.dart';
import 'package:connect/data/summary_report_entity.dart';
import 'package:connect/data/summary_report_v1_entity.dart';
import 'package:connect/data/user_entity.dart';
import 'package:connect/driver/bean/driver_assigns_entity.dart';
import 'package:connect/driver/bean/driver_heat_entity.dart';
import 'package:connect/driver/bean/driver_online_switch_entity.dart';
import 'package:connect/driver/bean/driver_orders_entity.dart';
import 'package:connect/driver/bean/driver_region_entity.dart';
import 'package:connect/driver/bean/driver_report_adjust_entity.dart';
import 'package:connect/driver/bean/driver_report_entity.dart';
import 'package:connect/driver/bean/driver_shifts_entity.dart';
import 'package:connect/ui/home/<USER>/food/report_food_entity.dart';
import 'package:connect/ui/home/<USER>/info/te_entity.dart';
import 'package:connect/ui/home/<USER>/menu/update_one_params_entity.dart';

JsonConvert jsonConvert = JsonConvert();
typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);

class JsonConvert {
	static final Map<String, JsonConvertFunction> _convertFuncMap = {
		(PhoneMasking).toString(): PhoneMasking.fromJson,
		(HistoryTotalReportEntity).toString(): HistoryTotalReportEntity.fromJson,
		(HistoryTotalReportId).toString(): HistoryTotalReportId.fromJson,
		(HistoryTotalReportCost).toString(): HistoryTotalReportCost.fromJson,
		(HistoryTotalReportFees).toString(): HistoryTotalReportFees.fromJson,
		(HistoryTotalReportFeesTip).toString(): HistoryTotalReportFeesTip.fromJson,
		(HistoryTotalReportCommission).toString(): HistoryTotalReportCommission.fromJson,
		(HistoryTotalReportDistribution).toString(): HistoryTotalReportDistribution.fromJson,
		(HistoryTotalReportAdjustments).toString(): HistoryTotalReportAdjustments.fromJson,
		(LogEntity).toString(): LogEntity.fromJson,
		(MultiNameEntity).toString(): MultiNameEntity.fromJson,
		(OrdersEntity).toString(): OrdersEntity.fromJson,
		(OrdersItem).toString(): OrdersItem.fromJson,
		(OrdersDistribution).toString(): OrdersDistribution.fromJson,
		(OrdersItemOptions).toString(): OrdersItemOptions.fromJson,
		(OrdersFees).toString(): OrdersFees.fromJson,
		(OrdersFeesTip).toString(): OrdersFeesTip.fromJson,
		(OrdersRegion).toString(): OrdersRegion.fromJson,
		(OrdersCustomer).toString(): OrdersCustomer.fromJson,
		(OrdersRestaurant).toString(): OrdersRestaurant.fromJson,
		(OrdersRestaurantAddress).toString(): OrdersRestaurantAddress.fromJson,
		(OrdersRestaurantAddressLocation).toString(): OrdersRestaurantAddressLocation.fromJson,
		(OrdersRestaurantDelivery).toString(): OrdersRestaurantDelivery.fromJson,
		(OrdersRestaurantDeliveryWindow).toString(): OrdersRestaurantDeliveryWindow.fromJson,
		(OrdersRestaurantDeliveryFee).toString(): OrdersRestaurantDeliveryFee.fromJson,
		(OrdersRestaurantDeliveryFeesAdjustments).toString(): OrdersRestaurantDeliveryFeesAdjustments.fromJson,
		(OrdersRestaurantDeliveryEstimate).toString(): OrdersRestaurantDeliveryEstimate.fromJson,
		(OrdersDelivery).toString(): OrdersDelivery.fromJson,
		(OrdersDeliveryStats).toString(): OrdersDeliveryStats.fromJson,
		(OrdersDeliveryAddress).toString(): OrdersDeliveryAddress.fromJson,
		(OrdersDeliveryAddressLocation).toString(): OrdersDeliveryAddressLocation.fromJson,
		(OrdersDeliveryCourier).toString(): OrdersDeliveryCourier.fromJson,
		(OrdersDeliveryEstimate).toString(): OrdersDeliveryEstimate.fromJson,
		(OrdersAdj).toString(): OrdersAdj.fromJson,
		(OrdersAdjustments).toString(): OrdersAdjustments.fromJson,
		(ReadyEntity).toString(): ReadyEntity.fromJson,
		(ReadyRestaurant).toString(): ReadyRestaurant.fromJson,
		(ReadyRestaurantName).toString(): ReadyRestaurantName.fromJson,
		(ReadyRestaurantAddress).toString(): ReadyRestaurantAddress.fromJson,
		(ReadyRestaurantAddressLocation).toString(): ReadyRestaurantAddressLocation.fromJson,
		(ReadyRestaurantDelivery).toString(): ReadyRestaurantDelivery.fromJson,
		(ReadyRestaurantDeliveryFee).toString(): ReadyRestaurantDeliveryFee.fromJson,
		(ReadyRestaurantDeliveryEstimate).toString(): ReadyRestaurantDeliveryEstimate.fromJson,
		(ReadyDestination).toString(): ReadyDestination.fromJson,
		(ReadyDelivery).toString(): ReadyDelivery.fromJson,
		(RestaurantDetailsEntity).toString(): RestaurantDetailsEntity.fromJson,
		(RestaurantDetailsAddress).toString(): RestaurantDetailsAddress.fromJson,
		(RestaurantDetailsDelivery).toString(): RestaurantDetailsDelivery.fromJson,
		(ResponseErrorEntity).toString(): ResponseErrorEntity.fromJson,
		(ResponseErrorDetails).toString(): ResponseErrorDetails.fromJson,
		(RestEntity).toString(): RestEntity.fromJson,
		(RestClosePeriod).toString(): RestClosePeriod.fromJson,
		(RestContacts).toString(): RestContacts.fromJson,
		(RestHours).toString(): RestHours.fromJson,
		(RestDelivery).toString(): RestDelivery.fromJson,
		(RestDeliveryZone).toString(): RestDeliveryZone.fromJson,
		(RestDeliveryZoneFeatures).toString(): RestDeliveryZoneFeatures.fromJson,
		(RestDeliveryZoneFeaturesGeometry).toString(): RestDeliveryZoneFeaturesGeometry.fromJson,
		(RestDeliveryZoneFeaturesProperties).toString(): RestDeliveryZoneFeaturesProperties.fromJson,
		(RestDeliveryZoneFeaturesPropertiesFee).toString(): RestDeliveryZoneFeaturesPropertiesFee.fromJson,
		(RestDeliveryZoneFeaturesPropertiesEstimate).toString(): RestDeliveryZoneFeaturesPropertiesEstimate.fromJson,
		(RestDeliveryZoneFeaturesPropertiesFees).toString(): RestDeliveryZoneFeaturesPropertiesFees.fromJson,
		(RestDeliveryZoneFeaturesPropertiesFeesAdjustments).toString(): RestDeliveryZoneFeaturesPropertiesFeesAdjustments.fromJson,
		(RestDeliveryZoneFeaturesPropertiesWindows).toString(): RestDeliveryZoneFeaturesPropertiesWindows.fromJson,
		(RestDeliveryPickupEst).toString(): RestDeliveryPickupEst.fromJson,
		(RestCredit).toString(): RestCredit.fromJson,
		(RestCreditFee).toString(): RestCreditFee.fromJson,
		(RestStripe).toString(): RestStripe.fromJson,
		(RestCommission).toString(): RestCommission.fromJson,
		(RestCommissionSubtotal).toString(): RestCommissionSubtotal.fromJson,
		(RestCommissionTotal).toString(): RestCommissionTotal.fromJson,
		(RestPreferences).toString(): RestPreferences.fromJson,
		(RestLocation).toString(): RestLocation.fromJson,
		(RestName).toString(): RestName.fromJson,
		(RestAddress).toString(): RestAddress.fromJson,
		(RestAddressLocation).toString(): RestAddressLocation.fromJson,
		(RestRegion).toString(): RestRegion.fromJson,
		(RestReward).toString(): RestReward.fromJson,
		(RestDiscount).toString(): RestDiscount.fromJson,
		(RestDiscountMenu).toString(): RestDiscountMenu.fromJson,
		(RestDiscountMenuDiscount).toString(): RestDiscountMenuDiscount.fromJson,
		(RestDiscountRicepo).toString(): RestDiscountRicepo.fromJson,
		(RestDiscountRicepoDiscount).toString(): RestDiscountRicepoDiscount.fromJson,
		(RestDiscountVip).toString(): RestDiscountVip.fromJson,
		(RestDiscountVipDiscount).toString(): RestDiscountVipDiscount.fromJson,
		(RestAnalytics).toString(): RestAnalytics.fromJson,
		(RestItems).toString(): RestItems.fromJson,
		(RestItemsName).toString(): RestItemsName.fromJson,
		(RestItemsImage).toString(): RestItemsImage.fromJson,
		(RestItemsSortMetrics).toString(): RestItemsSortMetrics.fromJson,
		(RestBundleOpts).toString(): RestBundleOpts.fromJson,
		(RestBundleOptsPopular).toString(): RestBundleOptsPopular.fromJson,
		(RestBundleOptsPopularName).toString(): RestBundleOptsPopularName.fromJson,
		(RestBundleOptsPopularBundleItems).toString(): RestBundleOptsPopularBundleItems.fromJson,
		(RestBundleOptsPopularBundleItemsName).toString(): RestBundleOptsPopularBundleItemsName.fromJson,
		(RestBundleOptsPopularBundleItemsImage).toString(): RestBundleOptsPopularBundleItemsImage.fromJson,
		(RestBundleOptsPopularBundleItemsRestaurant).toString(): RestBundleOptsPopularBundleItemsRestaurant.fromJson,
		(RestBundleOptsPopularBundleItemsRestaurantName).toString(): RestBundleOptsPopularBundleItemsRestaurantName.fromJson,
		(RestBundleOptsPopularBundleItemsRegion).toString(): RestBundleOptsPopularBundleItemsRegion.fromJson,
		(RestBundleOptsPopularBundleItemsCategory).toString(): RestBundleOptsPopularBundleItemsCategory.fromJson,
		(RestBundleOptsPopularBundleItemsTags).toString(): RestBundleOptsPopularBundleItemsTags.fromJson,
		(RestBundleOptsPopularBundleItemsScore).toString(): RestBundleOptsPopularBundleItemsScore.fromJson,
		(RestServiceFee).toString(): RestServiceFee.fromJson,
		(RestMotd).toString(): RestMotd.fromJson,
		(RestPromotion).toString(): RestPromotion.fromJson,
		(RestExpireProp).toString(): RestExpireProp.fromJson,
		(RestExtraFees).toString(): RestExtraFees.fromJson,
		(RestExtraFeesName).toString(): RestExtraFeesName.fromJson,
		(RestPrepare).toString(): RestPrepare.fromJson,
		(RestPrepareFood).toString(): RestPrepareFood.fromJson,
		(RestBundlePopular).toString(): RestBundlePopular.fromJson,
		(RestBundlePopularName).toString(): RestBundlePopularName.fromJson,
		(RestBundlePopularBundleItems).toString(): RestBundlePopularBundleItems.fromJson,
		(RestBundlePopularBundleItemsName).toString(): RestBundlePopularBundleItemsName.fromJson,
		(RestBundlePopularBundleItemsImage).toString(): RestBundlePopularBundleItemsImage.fromJson,
		(RestBundlePopularBundleItemsRestaurant).toString(): RestBundlePopularBundleItemsRestaurant.fromJson,
		(RestBundlePopularBundleItemsRegion).toString(): RestBundlePopularBundleItemsRegion.fromJson,
		(RestBundlePopularBundleItemsCategory).toString(): RestBundlePopularBundleItemsCategory.fromJson,
		(RestBundlePopularBundleItemsTags).toString(): RestBundlePopularBundleItemsTags.fromJson,
		(RestBundlePopularBundleItemsScore).toString(): RestBundlePopularBundleItemsScore.fromJson,
		(RestPromoItems).toString(): RestPromoItems.fromJson,
		(RestPromoItemsName).toString(): RestPromoItemsName.fromJson,
		(RestPromoItemsImage).toString(): RestPromoItemsImage.fromJson,
		(RestClosed).toString(): RestClosed.fromJson,
		(RestMenuEntity).toString(): RestMenuEntity.fromJson,
		(RestMenuCategories).toString(): RestMenuCategories.fromJson,
		(RestMenuCategoriesRestaurant).toString(): RestMenuCategoriesRestaurant.fromJson,
		(RestMenuFood).toString(): RestMenuFood.fromJson,
		(RestMenuFoodOptions).toString(): RestMenuFoodOptions.fromJson,
		(RestMenuFoodOptionsItems).toString(): RestMenuFoodOptionsItems.fromJson,
		(RestMenuFoodOptionsItemsAnalytics).toString(): RestMenuFoodOptionsItemsAnalytics.fromJson,
		(RestMenuFoodAnalytics).toString(): RestMenuFoodAnalytics.fromJson,
		(RestMenuFoodName).toString(): RestMenuFoodName.fromJson,
		(RestMenuFoodCategory).toString(): RestMenuFoodCategory.fromJson,
		(RestMenuFoodHours).toString(): RestMenuFoodHours.fromJson,
		(RestMenuFoodImage).toString(): RestMenuFoodImage.fromJson,
		(RestMenuFoodCondition).toString(): RestMenuFoodCondition.fromJson,
		(RestMenuReward).toString(): RestMenuReward.fromJson,
		(RestMenuRewardCategory).toString(): RestMenuRewardCategory.fromJson,
		(RestMenuRewardName).toString(): RestMenuRewardName.fromJson,
		(RestMenuRewardAnalytics).toString(): RestMenuRewardAnalytics.fromJson,
		(RestMenuRewardTags).toString(): RestMenuRewardTags.fromJson,
		(RestMenuRewardScore).toString(): RestMenuRewardScore.fromJson,
		(RestMenuToggleEntity).toString(): RestMenuToggleEntity.fromJson,
		(SummaryReportEntity).toString(): SummaryReportEntity.fromJson,
		(SummaryReportId).toString(): SummaryReportId.fromJson,
		(SummaryReportCost).toString(): SummaryReportCost.fromJson,
		(SummaryReportFees).toString(): SummaryReportFees.fromJson,
		(SummaryReportFeesTip).toString(): SummaryReportFeesTip.fromJson,
		(SummaryReportPostmates).toString(): SummaryReportPostmates.fromJson,
		(SummaryReportPostmatesDelivery).toString(): SummaryReportPostmatesDelivery.fromJson,
		(SummaryReportDelivery).toString(): SummaryReportDelivery.fromJson,
		(SummaryReportAdjustments).toString(): SummaryReportAdjustments.fromJson,
		(SummaryReportCoupon).toString(): SummaryReportCoupon.fromJson,
		(SummaryReportCommission).toString(): SummaryReportCommission.fromJson,
		(SummaryReportDistribution).toString(): SummaryReportDistribution.fromJson,
		(SummaryReportDistributionRicepo).toString(): SummaryReportDistributionRicepo.fromJson,
		(SummaryReportPayment).toString(): SummaryReportPayment.fromJson,
		(SummaryReportPaymentSale).toString(): SummaryReportPaymentSale.fromJson,
		(SummaryReportPaymentNumber).toString(): SummaryReportPaymentNumber.fromJson,
		(SummaryReportV1Entity).toString(): SummaryReportV1Entity.fromJson,
		(SummaryReportV1Id).toString(): SummaryReportV1Id.fromJson,
		(SummaryReportV1Metadata).toString(): SummaryReportV1Metadata.fromJson,
		(SummaryReportV1Cost).toString(): SummaryReportV1Cost.fromJson,
		(SummaryReportV1Fees).toString(): SummaryReportV1Fees.fromJson,
		(SummaryReportV1Tip).toString(): SummaryReportV1Tip.fromJson,
		(SummaryReportV1Commission).toString(): SummaryReportV1Commission.fromJson,
		(SummaryReportV1Distribution).toString(): SummaryReportV1Distribution.fromJson,
		(SummaryReportV1Ricepo).toString(): SummaryReportV1Ricepo.fromJson,
		(SummaryReportV1Adjustments).toString(): SummaryReportV1Adjustments.fromJson,
		(UserEntity).toString(): UserEntity.fromJson,
		(UserRole).toString(): UserRole.fromJson,
		(UserStripe).toString(): UserStripe.fromJson,
		(UserCommission).toString(): UserCommission.fromJson,
		(DriverAssignsEntity).toString(): DriverAssignsEntity.fromJson,
		(DriverAssignsBundle).toString(): DriverAssignsBundle.fromJson,
		(DriverAssignsCustomer).toString(): DriverAssignsCustomer.fromJson,
		(DriverAssignsRestaurant).toString(): DriverAssignsRestaurant.fromJson,
		(DriverAssignsDelivery).toString(): DriverAssignsDelivery.fromJson,
		(DriverAssignsDeliveryStats).toString(): DriverAssignsDeliveryStats.fromJson,
		(DriverHeatEntity).toString(): DriverHeatEntity.fromJson,
		(DriverOnlineSwitchEntity).toString(): DriverOnlineSwitchEntity.fromJson,
		(DriverOrdersEntity).toString(): DriverOrdersEntity.fromJson,
		(DriverOrdersMore).toString(): DriverOrdersMore.fromJson,
		(DriverOrdersRoute).toString(): DriverOrdersRoute.fromJson,
		(DriverOrdersRouteLateBy).toString(): DriverOrdersRouteLateBy.fromJson,
		(DriverOrdersRouteAddress).toString(): DriverOrdersRouteAddress.fromJson,
		(DriverOrdersRouteAddressLocation).toString(): DriverOrdersRouteAddressLocation.fromJson,
		(DriverOrdersRouteOrder).toString(): DriverOrdersRouteOrder.fromJson,
		(DriverOrdersMoreOrder).toString(): DriverOrdersMoreOrder.fromJson,
		(DriverOrdersMoreOrderRegion).toString(): DriverOrdersMoreOrderRegion.fromJson,
		(DriverOrdersRouteOrderRegion).toString(): DriverOrdersRouteOrderRegion.fromJson,
		(DriverOrdersRouteOrderDelivery).toString(): DriverOrdersRouteOrderDelivery.fromJson,
		(DriverOrdersRouteOrderDeliveryAddress).toString(): DriverOrdersRouteOrderDeliveryAddress.fromJson,
		(DriverOrdersRouteOrderRestaurant).toString(): DriverOrdersRouteOrderRestaurant.fromJson,
		(DriverOrdersMoreOrderRestaurant).toString(): DriverOrdersMoreOrderRestaurant.fromJson,
		(DriverOrdersRouteOrderRestaurantName).toString(): DriverOrdersRouteOrderRestaurantName.fromJson,
		(DriverOrdersMoreOrderRestaurantName).toString(): DriverOrdersMoreOrderRestaurantName.fromJson,
		(DriverOrdersRouteOrderRestaurantDelivery).toString(): DriverOrdersRouteOrderRestaurantDelivery.fromJson,
		(DriverOrdersRouteOrderCustomer).toString(): DriverOrdersRouteOrderCustomer.fromJson,
		(DriverOrdersRouteOrderItem).toString(): DriverOrdersRouteOrderItem.fromJson,
		(DriverOrdersNext).toString(): DriverOrdersNext.fromJson,
		(DriverOrdersNextAddress).toString(): DriverOrdersNextAddress.fromJson,
		(DriverOrdersNextAddressLocation).toString(): DriverOrdersNextAddressLocation.fromJson,
		(DriverOrdersNextOrder).toString(): DriverOrdersNextOrder.fromJson,
		(DriverOrdersNextOrderRegion).toString(): DriverOrdersNextOrderRegion.fromJson,
		(DriverOrdersNextOrderDelivery).toString(): DriverOrdersNextOrderDelivery.fromJson,
		(DriverOrdersNextOrderRestaurant).toString(): DriverOrdersNextOrderRestaurant.fromJson,
		(DriverOrdersNextOrderRestaurantDelivery).toString(): DriverOrdersNextOrderRestaurantDelivery.fromJson,
		(DriverOrdersNextOrderCustomer).toString(): DriverOrdersNextOrderCustomer.fromJson,
		(DriverOrdersNextOrderItem).toString(): DriverOrdersNextOrderItem.fromJson,
		(DriverRegionEntity).toString(): DriverRegionEntity.fromJson,
		(DriverRegionLocation).toString(): DriverRegionLocation.fromJson,
		(DriverRegionShift).toString(): DriverRegionShift.fromJson,
		(DriverRegionBundle).toString(): DriverRegionBundle.fromJson,
		(Tron).toString(): Tron.fromJson,
		(TronOptions).toString(): TronOptions.fromJson,
		(DriverReportAdjustEntity).toString(): DriverReportAdjustEntity.fromJson,
		(DriverReportAdjustDistribution).toString(): DriverReportAdjustDistribution.fromJson,
		(DriverReportAdjustRating).toString(): DriverReportAdjustRating.fromJson,
		(DriverReportAdjustRegion).toString(): DriverReportAdjustRegion.fromJson,
		(DriverReportAdjustRestaurant).toString(): DriverReportAdjustRestaurant.fromJson,
		(DriverReportAdjustAdj).toString(): DriverReportAdjustAdj.fromJson,
		(DriverReportAdjustAdjustments).toString(): DriverReportAdjustAdjustments.fromJson,
		(DriverReportAdjustBundle).toString(): DriverReportAdjustBundle.fromJson,
		(DriverReportEntity).toString(): DriverReportEntity.fromJson,
		(DriverReportRating).toString(): DriverReportRating.fromJson,
		(DriverReportDriving).toString(): DriverReportDriving.fromJson,
		(DriverReportDrivingTime).toString(): DriverReportDrivingTime.fromJson,
		(DriverReportDrivingDistance).toString(): DriverReportDrivingDistance.fromJson,
		(DriverReportId).toString(): DriverReportId.fromJson,
		(DriverReportFees).toString(): DriverReportFees.fromJson,
		(DriverReportFeesTip).toString(): DriverReportFeesTip.fromJson,
		(DriverReportCommission).toString(): DriverReportCommission.fromJson,
		(DriverReportAdjustments).toString(): DriverReportAdjustments.fromJson,
		(DriverReportDistribution).toString(): DriverReportDistribution.fromJson,
		(DriverShiftsEntity).toString(): DriverShiftsEntity.fromJson,
		(DriverShiftsRegion).toString(): DriverShiftsRegion.fromJson,
		(DriverShiftsDriver).toString(): DriverShiftsDriver.fromJson,
		(DriverShiftsDriverSwap).toString(): DriverShiftsDriverSwap.fromJson,
		(ReportFoodEntity).toString(): ReportFoodEntity.fromJson,
		(ReportFoodId).toString(): ReportFoodId.fromJson,
		(ReportFoodIdFood).toString(): ReportFoodIdFood.fromJson,
		(ReportFoodCost).toString(): ReportFoodCost.fromJson,
		(ReportFoodFees).toString(): ReportFoodFees.fromJson,
		(ReportFoodFeesTip).toString(): ReportFoodFeesTip.fromJson,
		(ReportFoodCommission).toString(): ReportFoodCommission.fromJson,
		(ReportFoodDistribution).toString(): ReportFoodDistribution.fromJson,
		(ReportFoodDistributionRicepo).toString(): ReportFoodDistributionRicepo.fromJson,
		(ReportFoodAdjustments).toString(): ReportFoodAdjustments.fromJson,
		(TeEntity).toString(): TeEntity.fromJson,
		(UpdateOneParamsEntity).toString(): UpdateOneParamsEntity.fromJson,
	};

  T? convert<T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return asT<T>(value);
  }

  List<T?>? convertList<T>(List<dynamic>? value) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => asT<T>(e)).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) => asT<T>(e)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      return <T>[];
    }
  }

  T? asT<T extends Object?>(dynamic value) {
    if(value == null){
      return null;
    }
    if (value is T) {
      return value;
    }
    final String type = T.toString();
    try {
      final String valueS = value.toString();
      if (type == "String") {
        return valueS as T;
      } else if (type == "int") {
        final int? intValue = int.tryParse(valueS);
        if (intValue == null) {
          return double.tryParse(valueS)?.toInt() as T?;
        } else {
          return intValue as T;
        }
      } else if (type == "double") {
        return double.parse(valueS) as T;
      } else if (type == "DateTime") {
        return DateTime.parse(valueS) as T;
      } else if (type == "bool") {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else if (type == "Map" || type.startsWith("Map<")) {
        return value as T;
      } else {
        if (_convertFuncMap.containsKey(type)) {
          return _convertFuncMap[type]!(value) as T;
        } else {
          throw UnimplementedError('$type unimplemented');
        }
      }
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      return null;
    }
  }

	//list is returned by type
	static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
		if(<PhoneMasking>[] is M){
			return data.map<PhoneMasking>((Map<String, dynamic> e) => PhoneMasking.fromJson(e)).toList() as M;
		}
		if(<HistoryTotalReportEntity>[] is M){
			return data.map<HistoryTotalReportEntity>((Map<String, dynamic> e) => HistoryTotalReportEntity.fromJson(e)).toList() as M;
		}
		if(<HistoryTotalReportId>[] is M){
			return data.map<HistoryTotalReportId>((Map<String, dynamic> e) => HistoryTotalReportId.fromJson(e)).toList() as M;
		}
		if(<HistoryTotalReportCost>[] is M){
			return data.map<HistoryTotalReportCost>((Map<String, dynamic> e) => HistoryTotalReportCost.fromJson(e)).toList() as M;
		}
		if(<HistoryTotalReportFees>[] is M){
			return data.map<HistoryTotalReportFees>((Map<String, dynamic> e) => HistoryTotalReportFees.fromJson(e)).toList() as M;
		}
		if(<HistoryTotalReportFeesTip>[] is M){
			return data.map<HistoryTotalReportFeesTip>((Map<String, dynamic> e) => HistoryTotalReportFeesTip.fromJson(e)).toList() as M;
		}
		if(<HistoryTotalReportCommission>[] is M){
			return data.map<HistoryTotalReportCommission>((Map<String, dynamic> e) => HistoryTotalReportCommission.fromJson(e)).toList() as M;
		}
		if(<HistoryTotalReportDistribution>[] is M){
			return data.map<HistoryTotalReportDistribution>((Map<String, dynamic> e) => HistoryTotalReportDistribution.fromJson(e)).toList() as M;
		}
		if(<HistoryTotalReportAdjustments>[] is M){
			return data.map<HistoryTotalReportAdjustments>((Map<String, dynamic> e) => HistoryTotalReportAdjustments.fromJson(e)).toList() as M;
		}
		if(<LogEntity>[] is M){
			return data.map<LogEntity>((Map<String, dynamic> e) => LogEntity.fromJson(e)).toList() as M;
		}
		if(<MultiNameEntity>[] is M){
			return data.map<MultiNameEntity>((Map<String, dynamic> e) => MultiNameEntity.fromJson(e)).toList() as M;
		}
		if(<OrdersEntity>[] is M){
			return data.map<OrdersEntity>((Map<String, dynamic> e) => OrdersEntity.fromJson(e)).toList() as M;
		}
		if(<OrdersItem>[] is M){
			return data.map<OrdersItem>((Map<String, dynamic> e) => OrdersItem.fromJson(e)).toList() as M;
		}
		if(<OrdersDistribution>[] is M){
			return data.map<OrdersDistribution>((Map<String, dynamic> e) => OrdersDistribution.fromJson(e)).toList() as M;
		}
		if(<OrdersItemOptions>[] is M){
			return data.map<OrdersItemOptions>((Map<String, dynamic> e) => OrdersItemOptions.fromJson(e)).toList() as M;
		}
		if(<OrdersFees>[] is M){
			return data.map<OrdersFees>((Map<String, dynamic> e) => OrdersFees.fromJson(e)).toList() as M;
		}
		if(<OrdersFeesTip>[] is M){
			return data.map<OrdersFeesTip>((Map<String, dynamic> e) => OrdersFeesTip.fromJson(e)).toList() as M;
		}
		if(<OrdersRegion>[] is M){
			return data.map<OrdersRegion>((Map<String, dynamic> e) => OrdersRegion.fromJson(e)).toList() as M;
		}
		if(<OrdersCustomer>[] is M){
			return data.map<OrdersCustomer>((Map<String, dynamic> e) => OrdersCustomer.fromJson(e)).toList() as M;
		}
		if(<OrdersRestaurant>[] is M){
			return data.map<OrdersRestaurant>((Map<String, dynamic> e) => OrdersRestaurant.fromJson(e)).toList() as M;
		}
		if(<OrdersRestaurantAddress>[] is M){
			return data.map<OrdersRestaurantAddress>((Map<String, dynamic> e) => OrdersRestaurantAddress.fromJson(e)).toList() as M;
		}
		if(<OrdersRestaurantAddressLocation>[] is M){
			return data.map<OrdersRestaurantAddressLocation>((Map<String, dynamic> e) => OrdersRestaurantAddressLocation.fromJson(e)).toList() as M;
		}
		if(<OrdersRestaurantDelivery>[] is M){
			return data.map<OrdersRestaurantDelivery>((Map<String, dynamic> e) => OrdersRestaurantDelivery.fromJson(e)).toList() as M;
		}
		if(<OrdersRestaurantDeliveryWindow>[] is M){
			return data.map<OrdersRestaurantDeliveryWindow>((Map<String, dynamic> e) => OrdersRestaurantDeliveryWindow.fromJson(e)).toList() as M;
		}
		if(<OrdersRestaurantDeliveryFee>[] is M){
			return data.map<OrdersRestaurantDeliveryFee>((Map<String, dynamic> e) => OrdersRestaurantDeliveryFee.fromJson(e)).toList() as M;
		}
		if(<OrdersRestaurantDeliveryFeesAdjustments>[] is M){
			return data.map<OrdersRestaurantDeliveryFeesAdjustments>((Map<String, dynamic> e) => OrdersRestaurantDeliveryFeesAdjustments.fromJson(e)).toList() as M;
		}
		if(<OrdersRestaurantDeliveryEstimate>[] is M){
			return data.map<OrdersRestaurantDeliveryEstimate>((Map<String, dynamic> e) => OrdersRestaurantDeliveryEstimate.fromJson(e)).toList() as M;
		}
		if(<OrdersDelivery>[] is M){
			return data.map<OrdersDelivery>((Map<String, dynamic> e) => OrdersDelivery.fromJson(e)).toList() as M;
		}
		if(<OrdersDeliveryStats>[] is M){
			return data.map<OrdersDeliveryStats>((Map<String, dynamic> e) => OrdersDeliveryStats.fromJson(e)).toList() as M;
		}
		if(<OrdersDeliveryAddress>[] is M){
			return data.map<OrdersDeliveryAddress>((Map<String, dynamic> e) => OrdersDeliveryAddress.fromJson(e)).toList() as M;
		}
		if(<OrdersDeliveryAddressLocation>[] is M){
			return data.map<OrdersDeliveryAddressLocation>((Map<String, dynamic> e) => OrdersDeliveryAddressLocation.fromJson(e)).toList() as M;
		}
		if(<OrdersDeliveryCourier>[] is M){
			return data.map<OrdersDeliveryCourier>((Map<String, dynamic> e) => OrdersDeliveryCourier.fromJson(e)).toList() as M;
		}
		if(<OrdersDeliveryEstimate>[] is M){
			return data.map<OrdersDeliveryEstimate>((Map<String, dynamic> e) => OrdersDeliveryEstimate.fromJson(e)).toList() as M;
		}
		if(<OrdersAdj>[] is M){
			return data.map<OrdersAdj>((Map<String, dynamic> e) => OrdersAdj.fromJson(e)).toList() as M;
		}
		if(<OrdersAdjustments>[] is M){
			return data.map<OrdersAdjustments>((Map<String, dynamic> e) => OrdersAdjustments.fromJson(e)).toList() as M;
		}
		if(<ReadyEntity>[] is M){
			return data.map<ReadyEntity>((Map<String, dynamic> e) => ReadyEntity.fromJson(e)).toList() as M;
		}
		if(<ReadyRestaurant>[] is M){
			return data.map<ReadyRestaurant>((Map<String, dynamic> e) => ReadyRestaurant.fromJson(e)).toList() as M;
		}
		if(<ReadyRestaurantName>[] is M){
			return data.map<ReadyRestaurantName>((Map<String, dynamic> e) => ReadyRestaurantName.fromJson(e)).toList() as M;
		}
		if(<ReadyRestaurantAddress>[] is M){
			return data.map<ReadyRestaurantAddress>((Map<String, dynamic> e) => ReadyRestaurantAddress.fromJson(e)).toList() as M;
		}
		if(<ReadyRestaurantAddressLocation>[] is M){
			return data.map<ReadyRestaurantAddressLocation>((Map<String, dynamic> e) => ReadyRestaurantAddressLocation.fromJson(e)).toList() as M;
		}
		if(<ReadyRestaurantDelivery>[] is M){
			return data.map<ReadyRestaurantDelivery>((Map<String, dynamic> e) => ReadyRestaurantDelivery.fromJson(e)).toList() as M;
		}
		if(<ReadyRestaurantDeliveryFee>[] is M){
			return data.map<ReadyRestaurantDeliveryFee>((Map<String, dynamic> e) => ReadyRestaurantDeliveryFee.fromJson(e)).toList() as M;
		}
		if(<ReadyRestaurantDeliveryEstimate>[] is M){
			return data.map<ReadyRestaurantDeliveryEstimate>((Map<String, dynamic> e) => ReadyRestaurantDeliveryEstimate.fromJson(e)).toList() as M;
		}
		if(<ReadyDestination>[] is M){
			return data.map<ReadyDestination>((Map<String, dynamic> e) => ReadyDestination.fromJson(e)).toList() as M;
		}
		if(<ReadyDelivery>[] is M){
			return data.map<ReadyDelivery>((Map<String, dynamic> e) => ReadyDelivery.fromJson(e)).toList() as M;
		}
		if(<RestaurantDetailsEntity>[] is M){
			return data.map<RestaurantDetailsEntity>((Map<String, dynamic> e) => RestaurantDetailsEntity.fromJson(e)).toList() as M;
		}
		if(<RestaurantDetailsAddress>[] is M){
			return data.map<RestaurantDetailsAddress>((Map<String, dynamic> e) => RestaurantDetailsAddress.fromJson(e)).toList() as M;
		}
		if(<RestaurantDetailsDelivery>[] is M){
			return data.map<RestaurantDetailsDelivery>((Map<String, dynamic> e) => RestaurantDetailsDelivery.fromJson(e)).toList() as M;
		}
		if(<ResponseErrorEntity>[] is M){
			return data.map<ResponseErrorEntity>((Map<String, dynamic> e) => ResponseErrorEntity.fromJson(e)).toList() as M;
		}
		if(<ResponseErrorDetails>[] is M){
			return data.map<ResponseErrorDetails>((Map<String, dynamic> e) => ResponseErrorDetails.fromJson(e)).toList() as M;
		}
		if(<RestEntity>[] is M){
			return data.map<RestEntity>((Map<String, dynamic> e) => RestEntity.fromJson(e)).toList() as M;
		}
		if(<RestClosePeriod>[] is M){
			return data.map<RestClosePeriod>((Map<String, dynamic> e) => RestClosePeriod.fromJson(e)).toList() as M;
		}
		if(<RestContacts>[] is M){
			return data.map<RestContacts>((Map<String, dynamic> e) => RestContacts.fromJson(e)).toList() as M;
		}
		if(<RestHours>[] is M){
			return data.map<RestHours>((Map<String, dynamic> e) => RestHours.fromJson(e)).toList() as M;
		}
		if(<RestDelivery>[] is M){
			return data.map<RestDelivery>((Map<String, dynamic> e) => RestDelivery.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryZone>[] is M){
			return data.map<RestDeliveryZone>((Map<String, dynamic> e) => RestDeliveryZone.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryZoneFeatures>[] is M){
			return data.map<RestDeliveryZoneFeatures>((Map<String, dynamic> e) => RestDeliveryZoneFeatures.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryZoneFeaturesGeometry>[] is M){
			return data.map<RestDeliveryZoneFeaturesGeometry>((Map<String, dynamic> e) => RestDeliveryZoneFeaturesGeometry.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryZoneFeaturesProperties>[] is M){
			return data.map<RestDeliveryZoneFeaturesProperties>((Map<String, dynamic> e) => RestDeliveryZoneFeaturesProperties.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryZoneFeaturesPropertiesFee>[] is M){
			return data.map<RestDeliveryZoneFeaturesPropertiesFee>((Map<String, dynamic> e) => RestDeliveryZoneFeaturesPropertiesFee.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryZoneFeaturesPropertiesEstimate>[] is M){
			return data.map<RestDeliveryZoneFeaturesPropertiesEstimate>((Map<String, dynamic> e) => RestDeliveryZoneFeaturesPropertiesEstimate.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryZoneFeaturesPropertiesFees>[] is M){
			return data.map<RestDeliveryZoneFeaturesPropertiesFees>((Map<String, dynamic> e) => RestDeliveryZoneFeaturesPropertiesFees.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryZoneFeaturesPropertiesFeesAdjustments>[] is M){
			return data.map<RestDeliveryZoneFeaturesPropertiesFeesAdjustments>((Map<String, dynamic> e) => RestDeliveryZoneFeaturesPropertiesFeesAdjustments.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryZoneFeaturesPropertiesWindows>[] is M){
			return data.map<RestDeliveryZoneFeaturesPropertiesWindows>((Map<String, dynamic> e) => RestDeliveryZoneFeaturesPropertiesWindows.fromJson(e)).toList() as M;
		}
		if(<RestDeliveryPickupEst>[] is M){
			return data.map<RestDeliveryPickupEst>((Map<String, dynamic> e) => RestDeliveryPickupEst.fromJson(e)).toList() as M;
		}
		if(<RestCredit>[] is M){
			return data.map<RestCredit>((Map<String, dynamic> e) => RestCredit.fromJson(e)).toList() as M;
		}
		if(<RestCreditFee>[] is M){
			return data.map<RestCreditFee>((Map<String, dynamic> e) => RestCreditFee.fromJson(e)).toList() as M;
		}
		if(<RestStripe>[] is M){
			return data.map<RestStripe>((Map<String, dynamic> e) => RestStripe.fromJson(e)).toList() as M;
		}
		if(<RestCommission>[] is M){
			return data.map<RestCommission>((Map<String, dynamic> e) => RestCommission.fromJson(e)).toList() as M;
		}
		if(<RestCommissionSubtotal>[] is M){
			return data.map<RestCommissionSubtotal>((Map<String, dynamic> e) => RestCommissionSubtotal.fromJson(e)).toList() as M;
		}
		if(<RestCommissionTotal>[] is M){
			return data.map<RestCommissionTotal>((Map<String, dynamic> e) => RestCommissionTotal.fromJson(e)).toList() as M;
		}
		if(<RestPreferences>[] is M){
			return data.map<RestPreferences>((Map<String, dynamic> e) => RestPreferences.fromJson(e)).toList() as M;
		}
		if(<RestLocation>[] is M){
			return data.map<RestLocation>((Map<String, dynamic> e) => RestLocation.fromJson(e)).toList() as M;
		}
		if(<RestName>[] is M){
			return data.map<RestName>((Map<String, dynamic> e) => RestName.fromJson(e)).toList() as M;
		}
		if(<RestAddress>[] is M){
			return data.map<RestAddress>((Map<String, dynamic> e) => RestAddress.fromJson(e)).toList() as M;
		}
		if(<RestAddressLocation>[] is M){
			return data.map<RestAddressLocation>((Map<String, dynamic> e) => RestAddressLocation.fromJson(e)).toList() as M;
		}
		if(<RestRegion>[] is M){
			return data.map<RestRegion>((Map<String, dynamic> e) => RestRegion.fromJson(e)).toList() as M;
		}
		if(<RestReward>[] is M){
			return data.map<RestReward>((Map<String, dynamic> e) => RestReward.fromJson(e)).toList() as M;
		}
		if(<RestDiscount>[] is M){
			return data.map<RestDiscount>((Map<String, dynamic> e) => RestDiscount.fromJson(e)).toList() as M;
		}
		if(<RestDiscountMenu>[] is M){
			return data.map<RestDiscountMenu>((Map<String, dynamic> e) => RestDiscountMenu.fromJson(e)).toList() as M;
		}
		if(<RestDiscountMenuDiscount>[] is M){
			return data.map<RestDiscountMenuDiscount>((Map<String, dynamic> e) => RestDiscountMenuDiscount.fromJson(e)).toList() as M;
		}
		if(<RestDiscountRicepo>[] is M){
			return data.map<RestDiscountRicepo>((Map<String, dynamic> e) => RestDiscountRicepo.fromJson(e)).toList() as M;
		}
		if(<RestDiscountRicepoDiscount>[] is M){
			return data.map<RestDiscountRicepoDiscount>((Map<String, dynamic> e) => RestDiscountRicepoDiscount.fromJson(e)).toList() as M;
		}
		if(<RestDiscountVip>[] is M){
			return data.map<RestDiscountVip>((Map<String, dynamic> e) => RestDiscountVip.fromJson(e)).toList() as M;
		}
		if(<RestDiscountVipDiscount>[] is M){
			return data.map<RestDiscountVipDiscount>((Map<String, dynamic> e) => RestDiscountVipDiscount.fromJson(e)).toList() as M;
		}
		if(<RestAnalytics>[] is M){
			return data.map<RestAnalytics>((Map<String, dynamic> e) => RestAnalytics.fromJson(e)).toList() as M;
		}
		if(<RestItems>[] is M){
			return data.map<RestItems>((Map<String, dynamic> e) => RestItems.fromJson(e)).toList() as M;
		}
		if(<RestItemsName>[] is M){
			return data.map<RestItemsName>((Map<String, dynamic> e) => RestItemsName.fromJson(e)).toList() as M;
		}
		if(<RestItemsImage>[] is M){
			return data.map<RestItemsImage>((Map<String, dynamic> e) => RestItemsImage.fromJson(e)).toList() as M;
		}
		if(<RestItemsSortMetrics>[] is M){
			return data.map<RestItemsSortMetrics>((Map<String, dynamic> e) => RestItemsSortMetrics.fromJson(e)).toList() as M;
		}
		if(<RestBundleOpts>[] is M){
			return data.map<RestBundleOpts>((Map<String, dynamic> e) => RestBundleOpts.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopular>[] is M){
			return data.map<RestBundleOptsPopular>((Map<String, dynamic> e) => RestBundleOptsPopular.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularName>[] is M){
			return data.map<RestBundleOptsPopularName>((Map<String, dynamic> e) => RestBundleOptsPopularName.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularBundleItems>[] is M){
			return data.map<RestBundleOptsPopularBundleItems>((Map<String, dynamic> e) => RestBundleOptsPopularBundleItems.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularBundleItemsName>[] is M){
			return data.map<RestBundleOptsPopularBundleItemsName>((Map<String, dynamic> e) => RestBundleOptsPopularBundleItemsName.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularBundleItemsImage>[] is M){
			return data.map<RestBundleOptsPopularBundleItemsImage>((Map<String, dynamic> e) => RestBundleOptsPopularBundleItemsImage.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularBundleItemsRestaurant>[] is M){
			return data.map<RestBundleOptsPopularBundleItemsRestaurant>((Map<String, dynamic> e) => RestBundleOptsPopularBundleItemsRestaurant.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularBundleItemsRestaurantName>[] is M){
			return data.map<RestBundleOptsPopularBundleItemsRestaurantName>((Map<String, dynamic> e) => RestBundleOptsPopularBundleItemsRestaurantName.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularBundleItemsRegion>[] is M){
			return data.map<RestBundleOptsPopularBundleItemsRegion>((Map<String, dynamic> e) => RestBundleOptsPopularBundleItemsRegion.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularBundleItemsCategory>[] is M){
			return data.map<RestBundleOptsPopularBundleItemsCategory>((Map<String, dynamic> e) => RestBundleOptsPopularBundleItemsCategory.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularBundleItemsTags>[] is M){
			return data.map<RestBundleOptsPopularBundleItemsTags>((Map<String, dynamic> e) => RestBundleOptsPopularBundleItemsTags.fromJson(e)).toList() as M;
		}
		if(<RestBundleOptsPopularBundleItemsScore>[] is M){
			return data.map<RestBundleOptsPopularBundleItemsScore>((Map<String, dynamic> e) => RestBundleOptsPopularBundleItemsScore.fromJson(e)).toList() as M;
		}
		if(<RestServiceFee>[] is M){
			return data.map<RestServiceFee>((Map<String, dynamic> e) => RestServiceFee.fromJson(e)).toList() as M;
		}
		if(<RestMotd>[] is M){
			return data.map<RestMotd>((Map<String, dynamic> e) => RestMotd.fromJson(e)).toList() as M;
		}
		if(<RestPromotion>[] is M){
			return data.map<RestPromotion>((Map<String, dynamic> e) => RestPromotion.fromJson(e)).toList() as M;
		}
		if(<RestExpireProp>[] is M){
			return data.map<RestExpireProp>((Map<String, dynamic> e) => RestExpireProp.fromJson(e)).toList() as M;
		}
		if(<RestExtraFees>[] is M){
			return data.map<RestExtraFees>((Map<String, dynamic> e) => RestExtraFees.fromJson(e)).toList() as M;
		}
		if(<RestExtraFeesName>[] is M){
			return data.map<RestExtraFeesName>((Map<String, dynamic> e) => RestExtraFeesName.fromJson(e)).toList() as M;
		}
		if(<RestPrepare>[] is M){
			return data.map<RestPrepare>((Map<String, dynamic> e) => RestPrepare.fromJson(e)).toList() as M;
		}
		if(<RestPrepareFood>[] is M){
			return data.map<RestPrepareFood>((Map<String, dynamic> e) => RestPrepareFood.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopular>[] is M){
			return data.map<RestBundlePopular>((Map<String, dynamic> e) => RestBundlePopular.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopularName>[] is M){
			return data.map<RestBundlePopularName>((Map<String, dynamic> e) => RestBundlePopularName.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopularBundleItems>[] is M){
			return data.map<RestBundlePopularBundleItems>((Map<String, dynamic> e) => RestBundlePopularBundleItems.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopularBundleItemsName>[] is M){
			return data.map<RestBundlePopularBundleItemsName>((Map<String, dynamic> e) => RestBundlePopularBundleItemsName.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopularBundleItemsImage>[] is M){
			return data.map<RestBundlePopularBundleItemsImage>((Map<String, dynamic> e) => RestBundlePopularBundleItemsImage.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopularBundleItemsRestaurant>[] is M){
			return data.map<RestBundlePopularBundleItemsRestaurant>((Map<String, dynamic> e) => RestBundlePopularBundleItemsRestaurant.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopularBundleItemsRegion>[] is M){
			return data.map<RestBundlePopularBundleItemsRegion>((Map<String, dynamic> e) => RestBundlePopularBundleItemsRegion.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopularBundleItemsCategory>[] is M){
			return data.map<RestBundlePopularBundleItemsCategory>((Map<String, dynamic> e) => RestBundlePopularBundleItemsCategory.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopularBundleItemsTags>[] is M){
			return data.map<RestBundlePopularBundleItemsTags>((Map<String, dynamic> e) => RestBundlePopularBundleItemsTags.fromJson(e)).toList() as M;
		}
		if(<RestBundlePopularBundleItemsScore>[] is M){
			return data.map<RestBundlePopularBundleItemsScore>((Map<String, dynamic> e) => RestBundlePopularBundleItemsScore.fromJson(e)).toList() as M;
		}
		if(<RestPromoItems>[] is M){
			return data.map<RestPromoItems>((Map<String, dynamic> e) => RestPromoItems.fromJson(e)).toList() as M;
		}
		if(<RestPromoItemsName>[] is M){
			return data.map<RestPromoItemsName>((Map<String, dynamic> e) => RestPromoItemsName.fromJson(e)).toList() as M;
		}
		if(<RestPromoItemsImage>[] is M){
			return data.map<RestPromoItemsImage>((Map<String, dynamic> e) => RestPromoItemsImage.fromJson(e)).toList() as M;
		}
		if(<RestClosed>[] is M){
			return data.map<RestClosed>((Map<String, dynamic> e) => RestClosed.fromJson(e)).toList() as M;
		}
		if(<RestMenuEntity>[] is M){
			return data.map<RestMenuEntity>((Map<String, dynamic> e) => RestMenuEntity.fromJson(e)).toList() as M;
		}
		if(<RestMenuCategories>[] is M){
			return data.map<RestMenuCategories>((Map<String, dynamic> e) => RestMenuCategories.fromJson(e)).toList() as M;
		}
		if(<RestMenuCategoriesRestaurant>[] is M){
			return data.map<RestMenuCategoriesRestaurant>((Map<String, dynamic> e) => RestMenuCategoriesRestaurant.fromJson(e)).toList() as M;
		}
		if(<RestMenuFood>[] is M){
			return data.map<RestMenuFood>((Map<String, dynamic> e) => RestMenuFood.fromJson(e)).toList() as M;
		}
		if(<RestMenuFoodOptions>[] is M){
			return data.map<RestMenuFoodOptions>((Map<String, dynamic> e) => RestMenuFoodOptions.fromJson(e)).toList() as M;
		}
		if(<RestMenuFoodOptionsItems>[] is M){
			return data.map<RestMenuFoodOptionsItems>((Map<String, dynamic> e) => RestMenuFoodOptionsItems.fromJson(e)).toList() as M;
		}
		if(<RestMenuFoodOptionsItemsAnalytics>[] is M){
			return data.map<RestMenuFoodOptionsItemsAnalytics>((Map<String, dynamic> e) => RestMenuFoodOptionsItemsAnalytics.fromJson(e)).toList() as M;
		}
		if(<RestMenuFoodAnalytics>[] is M){
			return data.map<RestMenuFoodAnalytics>((Map<String, dynamic> e) => RestMenuFoodAnalytics.fromJson(e)).toList() as M;
		}
		if(<RestMenuFoodName>[] is M){
			return data.map<RestMenuFoodName>((Map<String, dynamic> e) => RestMenuFoodName.fromJson(e)).toList() as M;
		}
		if(<RestMenuFoodCategory>[] is M){
			return data.map<RestMenuFoodCategory>((Map<String, dynamic> e) => RestMenuFoodCategory.fromJson(e)).toList() as M;
		}
		if(<RestMenuFoodHours>[] is M){
			return data.map<RestMenuFoodHours>((Map<String, dynamic> e) => RestMenuFoodHours.fromJson(e)).toList() as M;
		}
		if(<RestMenuFoodImage>[] is M){
			return data.map<RestMenuFoodImage>((Map<String, dynamic> e) => RestMenuFoodImage.fromJson(e)).toList() as M;
		}
		if(<RestMenuFoodCondition>[] is M){
			return data.map<RestMenuFoodCondition>((Map<String, dynamic> e) => RestMenuFoodCondition.fromJson(e)).toList() as M;
		}
		if(<RestMenuReward>[] is M){
			return data.map<RestMenuReward>((Map<String, dynamic> e) => RestMenuReward.fromJson(e)).toList() as M;
		}
		if(<RestMenuRewardCategory>[] is M){
			return data.map<RestMenuRewardCategory>((Map<String, dynamic> e) => RestMenuRewardCategory.fromJson(e)).toList() as M;
		}
		if(<RestMenuRewardName>[] is M){
			return data.map<RestMenuRewardName>((Map<String, dynamic> e) => RestMenuRewardName.fromJson(e)).toList() as M;
		}
		if(<RestMenuRewardAnalytics>[] is M){
			return data.map<RestMenuRewardAnalytics>((Map<String, dynamic> e) => RestMenuRewardAnalytics.fromJson(e)).toList() as M;
		}
		if(<RestMenuRewardTags>[] is M){
			return data.map<RestMenuRewardTags>((Map<String, dynamic> e) => RestMenuRewardTags.fromJson(e)).toList() as M;
		}
		if(<RestMenuRewardScore>[] is M){
			return data.map<RestMenuRewardScore>((Map<String, dynamic> e) => RestMenuRewardScore.fromJson(e)).toList() as M;
		}
		if(<RestMenuToggleEntity>[] is M){
			return data.map<RestMenuToggleEntity>((Map<String, dynamic> e) => RestMenuToggleEntity.fromJson(e)).toList() as M;
		}
		if(<SummaryReportEntity>[] is M){
			return data.map<SummaryReportEntity>((Map<String, dynamic> e) => SummaryReportEntity.fromJson(e)).toList() as M;
		}
		if(<SummaryReportId>[] is M){
			return data.map<SummaryReportId>((Map<String, dynamic> e) => SummaryReportId.fromJson(e)).toList() as M;
		}
		if(<SummaryReportCost>[] is M){
			return data.map<SummaryReportCost>((Map<String, dynamic> e) => SummaryReportCost.fromJson(e)).toList() as M;
		}
		if(<SummaryReportFees>[] is M){
			return data.map<SummaryReportFees>((Map<String, dynamic> e) => SummaryReportFees.fromJson(e)).toList() as M;
		}
		if(<SummaryReportFeesTip>[] is M){
			return data.map<SummaryReportFeesTip>((Map<String, dynamic> e) => SummaryReportFeesTip.fromJson(e)).toList() as M;
		}
		if(<SummaryReportPostmates>[] is M){
			return data.map<SummaryReportPostmates>((Map<String, dynamic> e) => SummaryReportPostmates.fromJson(e)).toList() as M;
		}
		if(<SummaryReportPostmatesDelivery>[] is M){
			return data.map<SummaryReportPostmatesDelivery>((Map<String, dynamic> e) => SummaryReportPostmatesDelivery.fromJson(e)).toList() as M;
		}
		if(<SummaryReportDelivery>[] is M){
			return data.map<SummaryReportDelivery>((Map<String, dynamic> e) => SummaryReportDelivery.fromJson(e)).toList() as M;
		}
		if(<SummaryReportAdjustments>[] is M){
			return data.map<SummaryReportAdjustments>((Map<String, dynamic> e) => SummaryReportAdjustments.fromJson(e)).toList() as M;
		}
		if(<SummaryReportCoupon>[] is M){
			return data.map<SummaryReportCoupon>((Map<String, dynamic> e) => SummaryReportCoupon.fromJson(e)).toList() as M;
		}
		if(<SummaryReportCommission>[] is M){
			return data.map<SummaryReportCommission>((Map<String, dynamic> e) => SummaryReportCommission.fromJson(e)).toList() as M;
		}
		if(<SummaryReportDistribution>[] is M){
			return data.map<SummaryReportDistribution>((Map<String, dynamic> e) => SummaryReportDistribution.fromJson(e)).toList() as M;
		}
		if(<SummaryReportDistributionRicepo>[] is M){
			return data.map<SummaryReportDistributionRicepo>((Map<String, dynamic> e) => SummaryReportDistributionRicepo.fromJson(e)).toList() as M;
		}
		if(<SummaryReportPayment>[] is M){
			return data.map<SummaryReportPayment>((Map<String, dynamic> e) => SummaryReportPayment.fromJson(e)).toList() as M;
		}
		if(<SummaryReportPaymentSale>[] is M){
			return data.map<SummaryReportPaymentSale>((Map<String, dynamic> e) => SummaryReportPaymentSale.fromJson(e)).toList() as M;
		}
		if(<SummaryReportPaymentNumber>[] is M){
			return data.map<SummaryReportPaymentNumber>((Map<String, dynamic> e) => SummaryReportPaymentNumber.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Entity>[] is M){
			return data.map<SummaryReportV1Entity>((Map<String, dynamic> e) => SummaryReportV1Entity.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Id>[] is M){
			return data.map<SummaryReportV1Id>((Map<String, dynamic> e) => SummaryReportV1Id.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Metadata>[] is M){
			return data.map<SummaryReportV1Metadata>((Map<String, dynamic> e) => SummaryReportV1Metadata.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Cost>[] is M){
			return data.map<SummaryReportV1Cost>((Map<String, dynamic> e) => SummaryReportV1Cost.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Fees>[] is M){
			return data.map<SummaryReportV1Fees>((Map<String, dynamic> e) => SummaryReportV1Fees.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Tip>[] is M){
			return data.map<SummaryReportV1Tip>((Map<String, dynamic> e) => SummaryReportV1Tip.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Commission>[] is M){
			return data.map<SummaryReportV1Commission>((Map<String, dynamic> e) => SummaryReportV1Commission.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Distribution>[] is M){
			return data.map<SummaryReportV1Distribution>((Map<String, dynamic> e) => SummaryReportV1Distribution.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Ricepo>[] is M){
			return data.map<SummaryReportV1Ricepo>((Map<String, dynamic> e) => SummaryReportV1Ricepo.fromJson(e)).toList() as M;
		}
		if(<SummaryReportV1Adjustments>[] is M){
			return data.map<SummaryReportV1Adjustments>((Map<String, dynamic> e) => SummaryReportV1Adjustments.fromJson(e)).toList() as M;
		}
		if(<UserEntity>[] is M){
			return data.map<UserEntity>((Map<String, dynamic> e) => UserEntity.fromJson(e)).toList() as M;
		}
		if(<UserRole>[] is M){
			return data.map<UserRole>((Map<String, dynamic> e) => UserRole.fromJson(e)).toList() as M;
		}
		if(<UserStripe>[] is M){
			return data.map<UserStripe>((Map<String, dynamic> e) => UserStripe.fromJson(e)).toList() as M;
		}
		if(<UserCommission>[] is M){
			return data.map<UserCommission>((Map<String, dynamic> e) => UserCommission.fromJson(e)).toList() as M;
		}
		if(<DriverAssignsEntity>[] is M){
			return data.map<DriverAssignsEntity>((Map<String, dynamic> e) => DriverAssignsEntity.fromJson(e)).toList() as M;
		}
		if(<DriverAssignsBundle>[] is M){
			return data.map<DriverAssignsBundle>((Map<String, dynamic> e) => DriverAssignsBundle.fromJson(e)).toList() as M;
		}
		if(<DriverAssignsCustomer>[] is M){
			return data.map<DriverAssignsCustomer>((Map<String, dynamic> e) => DriverAssignsCustomer.fromJson(e)).toList() as M;
		}
		if(<DriverAssignsRestaurant>[] is M){
			return data.map<DriverAssignsRestaurant>((Map<String, dynamic> e) => DriverAssignsRestaurant.fromJson(e)).toList() as M;
		}
		if(<DriverAssignsDelivery>[] is M){
			return data.map<DriverAssignsDelivery>((Map<String, dynamic> e) => DriverAssignsDelivery.fromJson(e)).toList() as M;
		}
		if(<DriverAssignsDeliveryStats>[] is M){
			return data.map<DriverAssignsDeliveryStats>((Map<String, dynamic> e) => DriverAssignsDeliveryStats.fromJson(e)).toList() as M;
		}
		if(<DriverHeatEntity>[] is M){
			return data.map<DriverHeatEntity>((Map<String, dynamic> e) => DriverHeatEntity.fromJson(e)).toList() as M;
		}
		if(<DriverOnlineSwitchEntity>[] is M){
			return data.map<DriverOnlineSwitchEntity>((Map<String, dynamic> e) => DriverOnlineSwitchEntity.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersEntity>[] is M){
			return data.map<DriverOrdersEntity>((Map<String, dynamic> e) => DriverOrdersEntity.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersMore>[] is M){
			return data.map<DriverOrdersMore>((Map<String, dynamic> e) => DriverOrdersMore.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRoute>[] is M){
			return data.map<DriverOrdersRoute>((Map<String, dynamic> e) => DriverOrdersRoute.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteLateBy>[] is M){
			return data.map<DriverOrdersRouteLateBy>((Map<String, dynamic> e) => DriverOrdersRouteLateBy.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteAddress>[] is M){
			return data.map<DriverOrdersRouteAddress>((Map<String, dynamic> e) => DriverOrdersRouteAddress.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteAddressLocation>[] is M){
			return data.map<DriverOrdersRouteAddressLocation>((Map<String, dynamic> e) => DriverOrdersRouteAddressLocation.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteOrder>[] is M){
			return data.map<DriverOrdersRouteOrder>((Map<String, dynamic> e) => DriverOrdersRouteOrder.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersMoreOrder>[] is M){
			return data.map<DriverOrdersMoreOrder>((Map<String, dynamic> e) => DriverOrdersMoreOrder.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersMoreOrderRegion>[] is M){
			return data.map<DriverOrdersMoreOrderRegion>((Map<String, dynamic> e) => DriverOrdersMoreOrderRegion.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteOrderRegion>[] is M){
			return data.map<DriverOrdersRouteOrderRegion>((Map<String, dynamic> e) => DriverOrdersRouteOrderRegion.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteOrderDelivery>[] is M){
			return data.map<DriverOrdersRouteOrderDelivery>((Map<String, dynamic> e) => DriverOrdersRouteOrderDelivery.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteOrderDeliveryAddress>[] is M){
			return data.map<DriverOrdersRouteOrderDeliveryAddress>((Map<String, dynamic> e) => DriverOrdersRouteOrderDeliveryAddress.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteOrderRestaurant>[] is M){
			return data.map<DriverOrdersRouteOrderRestaurant>((Map<String, dynamic> e) => DriverOrdersRouteOrderRestaurant.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersMoreOrderRestaurant>[] is M){
			return data.map<DriverOrdersMoreOrderRestaurant>((Map<String, dynamic> e) => DriverOrdersMoreOrderRestaurant.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteOrderRestaurantName>[] is M){
			return data.map<DriverOrdersRouteOrderRestaurantName>((Map<String, dynamic> e) => DriverOrdersRouteOrderRestaurantName.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersMoreOrderRestaurantName>[] is M){
			return data.map<DriverOrdersMoreOrderRestaurantName>((Map<String, dynamic> e) => DriverOrdersMoreOrderRestaurantName.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteOrderRestaurantDelivery>[] is M){
			return data.map<DriverOrdersRouteOrderRestaurantDelivery>((Map<String, dynamic> e) => DriverOrdersRouteOrderRestaurantDelivery.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteOrderCustomer>[] is M){
			return data.map<DriverOrdersRouteOrderCustomer>((Map<String, dynamic> e) => DriverOrdersRouteOrderCustomer.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersRouteOrderItem>[] is M){
			return data.map<DriverOrdersRouteOrderItem>((Map<String, dynamic> e) => DriverOrdersRouteOrderItem.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNext>[] is M){
			return data.map<DriverOrdersNext>((Map<String, dynamic> e) => DriverOrdersNext.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNextAddress>[] is M){
			return data.map<DriverOrdersNextAddress>((Map<String, dynamic> e) => DriverOrdersNextAddress.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNextAddressLocation>[] is M){
			return data.map<DriverOrdersNextAddressLocation>((Map<String, dynamic> e) => DriverOrdersNextAddressLocation.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNextOrder>[] is M){
			return data.map<DriverOrdersNextOrder>((Map<String, dynamic> e) => DriverOrdersNextOrder.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNextOrderRegion>[] is M){
			return data.map<DriverOrdersNextOrderRegion>((Map<String, dynamic> e) => DriverOrdersNextOrderRegion.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNextOrderDelivery>[] is M){
			return data.map<DriverOrdersNextOrderDelivery>((Map<String, dynamic> e) => DriverOrdersNextOrderDelivery.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNextOrderRestaurant>[] is M){
			return data.map<DriverOrdersNextOrderRestaurant>((Map<String, dynamic> e) => DriverOrdersNextOrderRestaurant.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNextOrderRestaurantDelivery>[] is M){
			return data.map<DriverOrdersNextOrderRestaurantDelivery>((Map<String, dynamic> e) => DriverOrdersNextOrderRestaurantDelivery.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNextOrderCustomer>[] is M){
			return data.map<DriverOrdersNextOrderCustomer>((Map<String, dynamic> e) => DriverOrdersNextOrderCustomer.fromJson(e)).toList() as M;
		}
		if(<DriverOrdersNextOrderItem>[] is M){
			return data.map<DriverOrdersNextOrderItem>((Map<String, dynamic> e) => DriverOrdersNextOrderItem.fromJson(e)).toList() as M;
		}
		if(<DriverRegionEntity>[] is M){
			return data.map<DriverRegionEntity>((Map<String, dynamic> e) => DriverRegionEntity.fromJson(e)).toList() as M;
		}
		if(<DriverRegionLocation>[] is M){
			return data.map<DriverRegionLocation>((Map<String, dynamic> e) => DriverRegionLocation.fromJson(e)).toList() as M;
		}
		if(<DriverRegionShift>[] is M){
			return data.map<DriverRegionShift>((Map<String, dynamic> e) => DriverRegionShift.fromJson(e)).toList() as M;
		}
		if(<DriverRegionBundle>[] is M){
			return data.map<DriverRegionBundle>((Map<String, dynamic> e) => DriverRegionBundle.fromJson(e)).toList() as M;
		}
		if(<Tron>[] is M){
			return data.map<Tron>((Map<String, dynamic> e) => Tron.fromJson(e)).toList() as M;
		}
		if(<TronOptions>[] is M){
			return data.map<TronOptions>((Map<String, dynamic> e) => TronOptions.fromJson(e)).toList() as M;
		}
		if(<DriverReportAdjustEntity>[] is M){
			return data.map<DriverReportAdjustEntity>((Map<String, dynamic> e) => DriverReportAdjustEntity.fromJson(e)).toList() as M;
		}
		if(<DriverReportAdjustDistribution>[] is M){
			return data.map<DriverReportAdjustDistribution>((Map<String, dynamic> e) => DriverReportAdjustDistribution.fromJson(e)).toList() as M;
		}
		if(<DriverReportAdjustRating>[] is M){
			return data.map<DriverReportAdjustRating>((Map<String, dynamic> e) => DriverReportAdjustRating.fromJson(e)).toList() as M;
		}
		if(<DriverReportAdjustRegion>[] is M){
			return data.map<DriverReportAdjustRegion>((Map<String, dynamic> e) => DriverReportAdjustRegion.fromJson(e)).toList() as M;
		}
		if(<DriverReportAdjustRestaurant>[] is M){
			return data.map<DriverReportAdjustRestaurant>((Map<String, dynamic> e) => DriverReportAdjustRestaurant.fromJson(e)).toList() as M;
		}
		if(<DriverReportAdjustAdj>[] is M){
			return data.map<DriverReportAdjustAdj>((Map<String, dynamic> e) => DriverReportAdjustAdj.fromJson(e)).toList() as M;
		}
		if(<DriverReportAdjustAdjustments>[] is M){
			return data.map<DriverReportAdjustAdjustments>((Map<String, dynamic> e) => DriverReportAdjustAdjustments.fromJson(e)).toList() as M;
		}
		if(<DriverReportAdjustBundle>[] is M){
			return data.map<DriverReportAdjustBundle>((Map<String, dynamic> e) => DriverReportAdjustBundle.fromJson(e)).toList() as M;
		}
		if(<DriverReportEntity>[] is M){
			return data.map<DriverReportEntity>((Map<String, dynamic> e) => DriverReportEntity.fromJson(e)).toList() as M;
		}
		if(<DriverReportRating>[] is M){
			return data.map<DriverReportRating>((Map<String, dynamic> e) => DriverReportRating.fromJson(e)).toList() as M;
		}
		if(<DriverReportDriving>[] is M){
			return data.map<DriverReportDriving>((Map<String, dynamic> e) => DriverReportDriving.fromJson(e)).toList() as M;
		}
		if(<DriverReportDrivingTime>[] is M){
			return data.map<DriverReportDrivingTime>((Map<String, dynamic> e) => DriverReportDrivingTime.fromJson(e)).toList() as M;
		}
		if(<DriverReportDrivingDistance>[] is M){
			return data.map<DriverReportDrivingDistance>((Map<String, dynamic> e) => DriverReportDrivingDistance.fromJson(e)).toList() as M;
		}
		if(<DriverReportId>[] is M){
			return data.map<DriverReportId>((Map<String, dynamic> e) => DriverReportId.fromJson(e)).toList() as M;
		}
		if(<DriverReportFees>[] is M){
			return data.map<DriverReportFees>((Map<String, dynamic> e) => DriverReportFees.fromJson(e)).toList() as M;
		}
		if(<DriverReportFeesTip>[] is M){
			return data.map<DriverReportFeesTip>((Map<String, dynamic> e) => DriverReportFeesTip.fromJson(e)).toList() as M;
		}
		if(<DriverReportCommission>[] is M){
			return data.map<DriverReportCommission>((Map<String, dynamic> e) => DriverReportCommission.fromJson(e)).toList() as M;
		}
		if(<DriverReportAdjustments>[] is M){
			return data.map<DriverReportAdjustments>((Map<String, dynamic> e) => DriverReportAdjustments.fromJson(e)).toList() as M;
		}
		if(<DriverReportDistribution>[] is M){
			return data.map<DriverReportDistribution>((Map<String, dynamic> e) => DriverReportDistribution.fromJson(e)).toList() as M;
		}
		if(<DriverShiftsEntity>[] is M){
			return data.map<DriverShiftsEntity>((Map<String, dynamic> e) => DriverShiftsEntity.fromJson(e)).toList() as M;
		}
		if(<DriverShiftsRegion>[] is M){
			return data.map<DriverShiftsRegion>((Map<String, dynamic> e) => DriverShiftsRegion.fromJson(e)).toList() as M;
		}
		if(<DriverShiftsDriver>[] is M){
			return data.map<DriverShiftsDriver>((Map<String, dynamic> e) => DriverShiftsDriver.fromJson(e)).toList() as M;
		}
		if(<DriverShiftsDriverSwap>[] is M){
			return data.map<DriverShiftsDriverSwap>((Map<String, dynamic> e) => DriverShiftsDriverSwap.fromJson(e)).toList() as M;
		}
		if(<ReportFoodEntity>[] is M){
			return data.map<ReportFoodEntity>((Map<String, dynamic> e) => ReportFoodEntity.fromJson(e)).toList() as M;
		}
		if(<ReportFoodId>[] is M){
			return data.map<ReportFoodId>((Map<String, dynamic> e) => ReportFoodId.fromJson(e)).toList() as M;
		}
		if(<ReportFoodIdFood>[] is M){
			return data.map<ReportFoodIdFood>((Map<String, dynamic> e) => ReportFoodIdFood.fromJson(e)).toList() as M;
		}
		if(<ReportFoodCost>[] is M){
			return data.map<ReportFoodCost>((Map<String, dynamic> e) => ReportFoodCost.fromJson(e)).toList() as M;
		}
		if(<ReportFoodFees>[] is M){
			return data.map<ReportFoodFees>((Map<String, dynamic> e) => ReportFoodFees.fromJson(e)).toList() as M;
		}
		if(<ReportFoodFeesTip>[] is M){
			return data.map<ReportFoodFeesTip>((Map<String, dynamic> e) => ReportFoodFeesTip.fromJson(e)).toList() as M;
		}
		if(<ReportFoodCommission>[] is M){
			return data.map<ReportFoodCommission>((Map<String, dynamic> e) => ReportFoodCommission.fromJson(e)).toList() as M;
		}
		if(<ReportFoodDistribution>[] is M){
			return data.map<ReportFoodDistribution>((Map<String, dynamic> e) => ReportFoodDistribution.fromJson(e)).toList() as M;
		}
		if(<ReportFoodDistributionRicepo>[] is M){
			return data.map<ReportFoodDistributionRicepo>((Map<String, dynamic> e) => ReportFoodDistributionRicepo.fromJson(e)).toList() as M;
		}
		if(<ReportFoodAdjustments>[] is M){
			return data.map<ReportFoodAdjustments>((Map<String, dynamic> e) => ReportFoodAdjustments.fromJson(e)).toList() as M;
		}
		if(<TeEntity>[] is M){
			return data.map<TeEntity>((Map<String, dynamic> e) => TeEntity.fromJson(e)).toList() as M;
		}
		if(<UpdateOneParamsEntity>[] is M){
			return data.map<UpdateOneParamsEntity>((Map<String, dynamic> e) => UpdateOneParamsEntity.fromJson(e)).toList() as M;
		}

		debugPrint("${M.toString()} not found");
	
		return null;
}

	static M? fromJsonAsT<M>(dynamic json) {
		if (json is List) {
			return _getListChildType<M>(json.map((e) => e as Map<String, dynamic>).toList());
		} else {
			return jsonConvert.asT<M>(json);
		}
	}
}