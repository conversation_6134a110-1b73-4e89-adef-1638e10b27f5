import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/ready_entity.dart';

ReadyEntity $ReadyEntityFromJson(Map<String, dynamic> json) {
	final ReadyEntity readyEntity = ReadyEntity();
	final String? orderId = jsonConvert.convert<String>(json['_id']);
	if (orderId != null) {
		readyEntity.orderId = orderId;
	}
	final String? comments = jsonConvert.convert<String>(json['comments']);
	if (comments != null) {
		readyEntity.comments = comments;
	}
	final String? passcode = jsonConvert.convert<String>(json['passcode']);
	if (passcode != null) {
		readyEntity.passcode = passcode;
	}
	final String? passcodeExt = jsonConvert.convert<String>(json['passcodeExt']);
	if (passcodeExt != null) {
		readyEntity.passcodeExt = passcodeExt;
	}
	final ReadyRestaurant? restaurant = jsonConvert.convert<ReadyRestaurant>(json['restaurant']);
	if (restaurant != null) {
		readyEntity.restaurant = restaurant;
	}
	final ReadyDestination? destination = jsonConvert.convert<ReadyDestination>(json['destination']);
	if (destination != null) {
		readyEntity.destination = destination;
	}
	final ReadyDelivery? delivery = jsonConvert.convert<ReadyDelivery>(json['delivery']);
	if (delivery != null) {
		readyEntity.delivery = delivery;
	}
	final String? status = jsonConvert.convert<String>(json['status']);
	if (status != null) {
		readyEntity.status = status;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		readyEntity.createdAt = createdAt;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		readyEntity.updatedAt = updatedAt;
	}
	final bool? doubt = jsonConvert.convert<bool>(json['doubt']);
	if (doubt != null) {
		readyEntity.doubt = doubt;
	}
	final String? confirmedAt = jsonConvert.convert<String>(json['confirmedAt']);
	if (confirmedAt != null) {
		readyEntity.confirmedAt = confirmedAt;
	}
	final String? message = jsonConvert.convert<String>(json['message']);
	if (message != null) {
		readyEntity.message = message;
	}
	return readyEntity;
}

Map<String, dynamic> $ReadyEntityToJson(ReadyEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.orderId;
	data['comments'] = entity.comments;
	data['passcode'] = entity.passcode;
	data['passcodeExt'] = entity.passcodeExt;
	data['restaurant'] = entity.restaurant?.toJson();
	data['destination'] = entity.destination?.toJson();
	data['delivery'] = entity.delivery?.toJson();
	data['status'] = entity.status;
	data['createdAt'] = entity.createdAt;
	data['updatedAt'] = entity.updatedAt;
	data['doubt'] = entity.doubt;
	data['confirmedAt'] = entity.confirmedAt;
	data['message'] = entity.message;
	return data;
}

ReadyRestaurant $ReadyRestaurantFromJson(Map<String, dynamic> json) {
	final ReadyRestaurant readyRestaurant = ReadyRestaurant();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		readyRestaurant.sId = sId;
	}
	final ReadyRestaurantName? name = jsonConvert.convert<ReadyRestaurantName>(json['name']);
	if (name != null) {
		readyRestaurant.name = name;
	}
	final bool? manual = jsonConvert.convert<bool>(json['manual']);
	if (manual != null) {
		readyRestaurant.manual = manual;
	}
	final bool? fake = jsonConvert.convert<bool>(json['fake']);
	if (fake != null) {
		readyRestaurant.fake = fake;
	}
	final String? color = jsonConvert.convert<String>(json['color']);
	if (color != null) {
		readyRestaurant.color = color;
	}
	final String? timezone = jsonConvert.convert<String>(json['timezone']);
	if (timezone != null) {
		readyRestaurant.timezone = timezone;
	}
	final ReadyRestaurantAddress? address = jsonConvert.convert<ReadyRestaurantAddress>(json['address']);
	if (address != null) {
		readyRestaurant.address = address;
	}
	final String? language = jsonConvert.convert<String>(json['language']);
	if (language != null) {
		readyRestaurant.language = language;
	}
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		readyRestaurant.tax = tax;
	}
	final ReadyRestaurantDelivery? delivery = jsonConvert.convert<ReadyRestaurantDelivery>(json['delivery']);
	if (delivery != null) {
		readyRestaurant.delivery = delivery;
	}
	return readyRestaurant;
}

Map<String, dynamic> $ReadyRestaurantToJson(ReadyRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name?.toJson();
	data['manual'] = entity.manual;
	data['fake'] = entity.fake;
	data['color'] = entity.color;
	data['timezone'] = entity.timezone;
	data['address'] = entity.address?.toJson();
	data['language'] = entity.language;
	data['tax'] = entity.tax;
	data['delivery'] = entity.delivery?.toJson();
	return data;
}

ReadyRestaurantName $ReadyRestaurantNameFromJson(Map<String, dynamic> json) {
	final ReadyRestaurantName readyRestaurantName = ReadyRestaurantName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		readyRestaurantName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		readyRestaurantName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		readyRestaurantName.zhHk = zhHk;
	}
	return readyRestaurantName;
}

Map<String, dynamic> $ReadyRestaurantNameToJson(ReadyRestaurantName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

ReadyRestaurantAddress $ReadyRestaurantAddressFromJson(Map<String, dynamic> json) {
	final ReadyRestaurantAddress readyRestaurantAddress = ReadyRestaurantAddress();
	final String? number = jsonConvert.convert<String>(json['number']);
	if (number != null) {
		readyRestaurantAddress.number = number;
	}
	final String? street = jsonConvert.convert<String>(json['street']);
	if (street != null) {
		readyRestaurantAddress.street = street;
	}
	final String? unit = jsonConvert.convert<String>(json['unit']);
	if (unit != null) {
		readyRestaurantAddress.unit = unit;
	}
	final String? city = jsonConvert.convert<String>(json['city']);
	if (city != null) {
		readyRestaurantAddress.city = city;
	}
	final String? state = jsonConvert.convert<String>(json['state']);
	if (state != null) {
		readyRestaurantAddress.state = state;
	}
	final String? country = jsonConvert.convert<String>(json['country']);
	if (country != null) {
		readyRestaurantAddress.country = country;
	}
	final String? zipcode = jsonConvert.convert<String>(json['zipcode']);
	if (zipcode != null) {
		readyRestaurantAddress.zipcode = zipcode;
	}
	final String? formatted = jsonConvert.convert<String>(json['formatted']);
	if (formatted != null) {
		readyRestaurantAddress.formatted = formatted;
	}
	final ReadyRestaurantAddressLocation? location = jsonConvert.convert<ReadyRestaurantAddressLocation>(json['location']);
	if (location != null) {
		readyRestaurantAddress.location = location;
	}
	final String? note = jsonConvert.convert<String>(json['note']);
	if (note != null) {
		readyRestaurantAddress.note = note;
	}
	return readyRestaurantAddress;
}

Map<String, dynamic> $ReadyRestaurantAddressToJson(ReadyRestaurantAddress entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['number'] = entity.number;
	data['street'] = entity.street;
	data['unit'] = entity.unit;
	data['city'] = entity.city;
	data['state'] = entity.state;
	data['country'] = entity.country;
	data['zipcode'] = entity.zipcode;
	data['formatted'] = entity.formatted;
	data['location'] = entity.location?.toJson();
	data['note'] = entity.note;
	return data;
}

ReadyRestaurantAddressLocation $ReadyRestaurantAddressLocationFromJson(Map<String, dynamic> json) {
	final ReadyRestaurantAddressLocation readyRestaurantAddressLocation = ReadyRestaurantAddressLocation();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		readyRestaurantAddressLocation.type = type;
	}
	final List<double?>? coordinates = jsonConvert.convertList<double>(json['coordinates']);
	if (coordinates != null) {
		readyRestaurantAddressLocation.coordinates = coordinates;
	}
	return readyRestaurantAddressLocation;
}

Map<String, dynamic> $ReadyRestaurantAddressLocationToJson(ReadyRestaurantAddressLocation entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['coordinates'] =  entity.coordinates;
	return data;
}

ReadyRestaurantDelivery $ReadyRestaurantDeliveryFromJson(Map<String, dynamic> json) {
	final ReadyRestaurantDelivery readyRestaurantDelivery = ReadyRestaurantDelivery();
	final String? provider = jsonConvert.convert<String>(json['provider']);
	if (provider != null) {
		readyRestaurantDelivery.provider = provider;
	}
	final num? prepare = jsonConvert.convert<num>(json['prepare']);
	if (prepare != null) {
		readyRestaurantDelivery.prepare = prepare;
	}
	final ReadyRestaurantDeliveryFee? fee = jsonConvert.convert<ReadyRestaurantDeliveryFee>(json['fee']);
	if (fee != null) {
		readyRestaurantDelivery.fee = fee;
	}
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		readyRestaurantDelivery.minimum = minimum;
	}
	final ReadyRestaurantDeliveryEstimate? estimate = jsonConvert.convert<ReadyRestaurantDeliveryEstimate>(json['estimate']);
	if (estimate != null) {
		readyRestaurantDelivery.estimate = estimate;
	}
	final int? prepareTime = jsonConvert.convert<int>(json['prepareTime']);
	if (prepareTime != null) {
		readyRestaurantDelivery.prepareTime = prepareTime;
	}
	return readyRestaurantDelivery;
}

Map<String, dynamic> $ReadyRestaurantDeliveryToJson(ReadyRestaurantDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['provider'] = entity.provider;
	data['prepare'] = entity.prepare;
	data['fee'] = entity.fee?.toJson();
	data['minimum'] = entity.minimum;
	data['estimate'] = entity.estimate?.toJson();
	data['prepareTime'] = entity.prepareTime;
	return data;
}

ReadyRestaurantDeliveryFee $ReadyRestaurantDeliveryFeeFromJson(Map<String, dynamic> json) {
	final ReadyRestaurantDeliveryFee readyRestaurantDeliveryFee = ReadyRestaurantDeliveryFee();
	final int? factor = jsonConvert.convert<int>(json['factor']);
	if (factor != null) {
		readyRestaurantDeliveryFee.factor = factor;
	}
	final int? flat = jsonConvert.convert<int>(json['flat']);
	if (flat != null) {
		readyRestaurantDeliveryFee.flat = flat;
	}
	return readyRestaurantDeliveryFee;
}

Map<String, dynamic> $ReadyRestaurantDeliveryFeeToJson(ReadyRestaurantDeliveryFee entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['factor'] = entity.factor;
	data['flat'] = entity.flat;
	return data;
}

ReadyRestaurantDeliveryEstimate $ReadyRestaurantDeliveryEstimateFromJson(Map<String, dynamic> json) {
	final ReadyRestaurantDeliveryEstimate readyRestaurantDeliveryEstimate = ReadyRestaurantDeliveryEstimate();
	final String? deadline = jsonConvert.convert<String>(json['deadline']);
	if (deadline != null) {
		readyRestaurantDeliveryEstimate.deadline = deadline;
	}
	final int? max = jsonConvert.convert<int>(json['max']);
	if (max != null) {
		readyRestaurantDeliveryEstimate.max = max;
	}
	final int? min = jsonConvert.convert<int>(json['min']);
	if (min != null) {
		readyRestaurantDeliveryEstimate.min = min;
	}
	return readyRestaurantDeliveryEstimate;
}

Map<String, dynamic> $ReadyRestaurantDeliveryEstimateToJson(ReadyRestaurantDeliveryEstimate entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['deadline'] = entity.deadline;
	data['max'] = entity.max;
	data['min'] = entity.min;
	return data;
}

ReadyDestination $ReadyDestinationFromJson(Map<String, dynamic> json) {
	final ReadyDestination readyDestination = ReadyDestination();
	final String? address = jsonConvert.convert<String>(json['address']);
	if (address != null) {
		readyDestination.address = address;
	}
	final String? formattedAddress = jsonConvert.convert<String>(json['formattedAddress']);
	if (formattedAddress != null) {
		readyDestination.formattedAddress = formattedAddress;
	}
	return readyDestination;
}

Map<String, dynamic> $ReadyDestinationToJson(ReadyDestination entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['address'] = entity.address;
	data['formattedAddress'] = entity.formattedAddress;
	return data;
}

ReadyDelivery $ReadyDeliveryFromJson(Map<String, dynamic> json) {
	final ReadyDelivery readyDelivery = ReadyDelivery();
	final String? status = jsonConvert.convert<String>(json['status']);
	if (status != null) {
		readyDelivery.status = status;
	}
	return readyDelivery;
}

Map<String, dynamic> $ReadyDeliveryToJson(ReadyDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['status'] = entity.status;
	return data;
}