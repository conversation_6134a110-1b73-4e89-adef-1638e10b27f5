import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/driver/bean/driver_shifts_entity.dart';

DriverShiftsEntity $DriverShiftsEntityFromJson(Map<String, dynamic> json) {
	final DriverShiftsEntity driverShiftsEntity = DriverShiftsEntity();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverShiftsEntity.sId = sId;
	}
	final String? start = jsonConvert.convert<String>(json['start']);
	if (start != null) {
		driverShiftsEntity.start = start;
	}
	final String? end = jsonConvert.convert<String>(json['end']);
	if (end != null) {
		driverShiftsEntity.end = end;
	}
	final String? batch = jsonConvert.convert<String>(json['batch']);
	if (batch != null) {
		driverShiftsEntity.batch = batch;
	}
	final DriverShiftsRegion? region = jsonConvert.convert<DriverShiftsRegion>(json['region']);
	if (region != null) {
		driverShiftsEntity.region = region;
	}
	final List<DriverShiftsDriver>? drivers = jsonConvert.convertListNotNull<DriverShiftsDriver>(json['drivers']);
	if (drivers != null) {
		driverShiftsEntity.drivers = drivers;
	}
	final double? instances = jsonConvert.convert<double>(json['instances']);
	if (instances != null) {
		driverShiftsEntity.instances = instances;
	}
	final double? available = jsonConvert.convert<double>(json['available']);
	if (available != null) {
		driverShiftsEntity.available = available;
	}
	final num? extraPay = jsonConvert.convert<num>(json['extraPay']);
	if (extraPay != null) {
		driverShiftsEntity.extraPay = extraPay;
	}
	final num? guarantee = jsonConvert.convert<num>(json['guarantee']);
	if (guarantee != null) {
		driverShiftsEntity.guarantee = guarantee;
	}
	final String? notes = jsonConvert.convert<String>(json['notes']);
	if (notes != null) {
		driverShiftsEntity.notes = notes;
	}
	final String? address = jsonConvert.convert<String>(json['address']);
	if (address != null) {
		driverShiftsEntity.address = address;
	}
	final bool? isMyShift = jsonConvert.convert<bool>(json['isMyShift']);
	if (isMyShift != null) {
		driverShiftsEntity.isMyShift = isMyShift;
	}
	final bool? isContained = jsonConvert.convert<bool>(json['isContained']);
	if (isContained != null) {
		driverShiftsEntity.isContained = isContained;
	}
	return driverShiftsEntity;
}

Map<String, dynamic> $DriverShiftsEntityToJson(DriverShiftsEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['start'] = entity.start;
	data['end'] = entity.end;
	data['batch'] = entity.batch;
	data['region'] = entity.region?.toJson();
	data['drivers'] =  entity.drivers?.map((v) => v.toJson()).toList();
	data['instances'] = entity.instances;
	data['available'] = entity.available;
	data['extraPay'] = entity.extraPay;
	data['guarantee'] = entity.guarantee;
	data['notes'] = entity.notes;
	data['address'] = entity.address;
	data['isMyShift'] = entity.isMyShift;
	data['isContained'] = entity.isContained;
	return data;
}

DriverShiftsRegion $DriverShiftsRegionFromJson(Map<String, dynamic> json) {
	final DriverShiftsRegion driverShiftsRegion = DriverShiftsRegion();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverShiftsRegion.sId = sId;
	}
	return driverShiftsRegion;
}

Map<String, dynamic> $DriverShiftsRegionToJson(DriverShiftsRegion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

DriverShiftsDriver $DriverShiftsDriverFromJson(Map<String, dynamic> json) {
	final DriverShiftsDriver driverShiftsDriver = DriverShiftsDriver();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverShiftsDriver.sId = sId;
	}
	final String? email = jsonConvert.convert<String>(json['email']);
	if (email != null) {
		driverShiftsDriver.email = email;
	}
	final String? assignedAt = jsonConvert.convert<String>(json['assignedAt']);
	if (assignedAt != null) {
		driverShiftsDriver.assignedAt = assignedAt;
	}
	final DriverShiftsDriverSwap? swap = jsonConvert.convert<DriverShiftsDriverSwap>(json['swap']);
	if (swap != null) {
		driverShiftsDriver.swap = swap;
	}
	return driverShiftsDriver;
}

Map<String, dynamic> $DriverShiftsDriverToJson(DriverShiftsDriver entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['email'] = entity.email;
	data['assignedAt'] = entity.assignedAt;
	data['swap'] = entity.swap?.toJson();
	return data;
}

DriverShiftsDriverSwap $DriverShiftsDriverSwapFromJson(Map<String, dynamic> json) {
	final DriverShiftsDriverSwap driverShiftsDriverSwap = DriverShiftsDriverSwap();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverShiftsDriverSwap.sId = sId;
	}
	final String? email = jsonConvert.convert<String>(json['email']);
	if (email != null) {
		driverShiftsDriverSwap.email = email;
	}
	final String? date = jsonConvert.convert<String>(json['date']);
	if (date != null) {
		driverShiftsDriverSwap.date = date;
	}
	return driverShiftsDriverSwap;
}

Map<String, dynamic> $DriverShiftsDriverSwapToJson(DriverShiftsDriverSwap entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['email'] = entity.email;
	data['date'] = entity.date;
	return data;
}