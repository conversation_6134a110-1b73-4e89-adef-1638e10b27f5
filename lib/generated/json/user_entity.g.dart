import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/user_entity.dart';
import "package:connect/ext/ext.dart";


UserEntity $UserEntityFromJson(Map<String, dynamic> json) {
	final UserEntity userEntity = UserEntity();
	final String? userId = jsonConvert.convert<String>(json['_id']);
	if (userId != null) {
		userEntity.userId = userId;
	}
	final String? email = jsonConvert.convert<String>(json['email']);
	if (email != null) {
		userEntity.email = email;
	}
	final List<UserRole?>? roles = jsonConvert.convertList<UserRole>(json['roles']);
	if (roles != null) {
		userEntity.roles = roles;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		userEntity.createdAt = createdAt;
	}
	final UserCommission? commission = jsonConvert.convert<UserCommission>(json['commission']);
	if (commission != null) {
		userEntity.commission = commission;
	}
	final UserStripe? stripe = jsonConvert.convert<UserStripe>(json['stripe']);
	if (stripe != null) {
		userEntity.stripe = stripe;
	}
	final bool? onCall = jsonConvert.convert<bool>(json['onCall']);
	if (onCall != null) {
		userEntity.onCall = onCall;
	}
	final String? phone = jsonConvert.convert<String>(json['phone']);
	if (phone != null) {
		userEntity.phone = phone;
	}
	final String? regionCode = jsonConvert.convert<String>(json['regionCode']);
	if (regionCode != null) {
		userEntity.regionCode = regionCode;
	}
	final String? language = jsonConvert.convert<String>(json['language']);
	if (language != null) {
		userEntity.language = language;
	}
	final String? token = jsonConvert.convert<String>(json['token']);
	if (token != null) {
		userEntity.token = token;
	}
	final String? loginType = jsonConvert.convert<String>(json['loginType']);
	if (loginType != null) {
		userEntity.loginType = loginType;
	}
	final bool? autoLog = jsonConvert.convert<bool>(json['autoLog']);
	if (autoLog != null) {
		userEntity.autoLog = autoLog;
	}
	return userEntity;
}

Map<String, dynamic> $UserEntityToJson(UserEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.userId;
	data['email'] = entity.email;
	data['roles'] =  entity.roles?.map((v) => v?.toJson()).toList();
	data['createdAt'] = entity.createdAt;
	data['commission'] = entity.commission?.toJson();
	data['stripe'] = entity.stripe?.toJson();
	data['onCall'] = entity.onCall;
	data['phone'] = entity.phone;
	data['regionCode'] = entity.regionCode;
	data['language'] = entity.language;
	data['token'] = entity.token;
	data['loginType'] = entity.loginType;
	data['autoLog'] = entity.autoLog;
	return data;
}

UserRole $UserRoleFromJson(Map<String, dynamic> json) {
	final UserRole userRole = UserRole();
	final String? scope = jsonConvert.convert<String>(json['scope']);
	if (scope != null) {
		userRole.scope = scope;
	}
	final String? name = jsonConvert.convert<String>(json['name']);
	if (name != null) {
		userRole.name = name;
	}
	final String? description = jsonConvert.convert<String>(json['description']);
	if (description != null) {
		userRole.description = description;
	}
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		userRole.sId = sId;
	}
	final String? timezone = jsonConvert.convert<String>(json['timezone']);
	if (timezone != null) {
		userRole.timezone = timezone;
	}
	final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
	if (isSelected != null) {
		userRole.isSelected = isSelected;
	}
	return userRole;
}

Map<String, dynamic> $UserRoleToJson(UserRole entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['scope'] = entity.scope;
	data['name'] = entity.name;
	data['description'] = entity.description;
	data['_id'] = entity.sId;
	data['timezone'] = entity.timezone;
	data['isSelected'] = entity.isSelected;
	return data;
}

UserStripe $UserStripeFromJson(Map<String, dynamic> json) {
	final UserStripe userStripe = UserStripe();
	final String? id = jsonConvert.convert<String>(json['id']);
	if (id != null) {
		userStripe.id = id;
	}
	final bool? onboarding = jsonConvert.convert<bool>(json['onboarding']);
	if (onboarding != null) {
		userStripe.onboarding = onboarding;
	}
	final String? status = jsonConvert.convert<String>(json['status']);
	if (status != null) {
		userStripe.status = status;
	}
	return userStripe;
}

Map<String, dynamic> $UserStripeToJson(UserStripe entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['id'] = entity.id;
	data['onboarding'] = entity.onboarding;
	data['status'] = entity.status;
	return data;
}

UserCommission $UserCommissionFromJson(Map<String, dynamic> json) {
	final UserCommission userCommission = UserCommission();
	final num? flat = jsonConvert.convert<num>(json['flat']);
	if (flat != null) {
		userCommission.flat = flat;
	}
	final num? factor = jsonConvert.convert<num>(json['factor']);
	if (factor != null) {
		userCommission.factor = factor;
	}
	return userCommission;
}

Map<String, dynamic> $UserCommissionToJson(UserCommission entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['flat'] = entity.flat;
	data['factor'] = entity.factor;
	return data;
}