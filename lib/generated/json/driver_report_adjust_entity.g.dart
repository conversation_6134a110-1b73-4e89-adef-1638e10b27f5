import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/driver/bean/driver_report_adjust_entity.dart';
import 'package:connect/data/multi_name_entity.dart';


DriverReportAdjustEntity $DriverReportAdjustEntityFromJson(Map<String, dynamic> json) {
	final DriverReportAdjustEntity driverReportAdjustEntity = DriverReportAdjustEntity();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverReportAdjustEntity.sId = sId;
	}
	final String? passcode = jsonConvert.convert<String>(json['passcode']);
	if (passcode != null) {
		driverReportAdjustEntity.passcode = passcode;
	}
	final String? passcodeExt = jsonConvert.convert<String>(json['passcodeExt']);
	if (passcodeExt != null) {
		driverReportAdjustEntity.passcodeExt = passcodeExt;
	}
	final DriverReportAdjustRegion? region = jsonConvert.convert<DriverReportAdjustRegion>(json['region']);
	if (region != null) {
		driverReportAdjustEntity.region = region;
	}
	final DriverReportAdjustRestaurant? restaurant = jsonConvert.convert<DriverReportAdjustRestaurant>(json['restaurant']);
	if (restaurant != null) {
		driverReportAdjustEntity.restaurant = restaurant;
	}
	final List<DriverReportAdjustAdj>? adj = jsonConvert.convertListNotNull<DriverReportAdjustAdj>(json['adj']);
	if (adj != null) {
		driverReportAdjustEntity.adj = adj;
	}
	final DriverReportAdjustAdjustments? adjustments = jsonConvert.convert<DriverReportAdjustAdjustments>(json['adjustments']);
	if (adjustments != null) {
		driverReportAdjustEntity.adjustments = adjustments;
	}
	final DriverReportAdjustBundle? bundle = jsonConvert.convert<DriverReportAdjustBundle>(json['bundle']);
	if (bundle != null) {
		driverReportAdjustEntity.bundle = bundle;
	}
	final DriverReportAdjustDistribution? distribution = jsonConvert.convert<DriverReportAdjustDistribution>(json['distribution']);
	if (distribution != null) {
		driverReportAdjustEntity.distribution = distribution;
	}
	final DriverReportAdjustRating? rating = jsonConvert.convert<DriverReportAdjustRating>(json['rating']);
	if (rating != null) {
		driverReportAdjustEntity.rating = rating;
	}
	final String? status = jsonConvert.convert<String>(json['status']);
	if (status != null) {
		driverReportAdjustEntity.status = status;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		driverReportAdjustEntity.createdAt = createdAt;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		driverReportAdjustEntity.updatedAt = updatedAt;
	}
	final bool? doubt = jsonConvert.convert<bool>(json['doubt']);
	if (doubt != null) {
		driverReportAdjustEntity.doubt = doubt;
	}
	final String? confirmedAt = jsonConvert.convert<String>(json['confirmedAt']);
	if (confirmedAt != null) {
		driverReportAdjustEntity.confirmedAt = confirmedAt;
	}
	return driverReportAdjustEntity;
}

Map<String, dynamic> $DriverReportAdjustEntityToJson(DriverReportAdjustEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['passcode'] = entity.passcode;
	data['passcodeExt'] = entity.passcodeExt;
	data['region'] = entity.region?.toJson();
	data['restaurant'] = entity.restaurant?.toJson();
	data['adj'] =  entity.adj?.map((v) => v.toJson()).toList();
	data['adjustments'] = entity.adjustments?.toJson();
	data['bundle'] = entity.bundle?.toJson();
	data['distribution'] = entity.distribution?.toJson();
	data['rating'] = entity.rating?.toJson();
	data['status'] = entity.status;
	data['createdAt'] = entity.createdAt;
	data['updatedAt'] = entity.updatedAt;
	data['doubt'] = entity.doubt;
	data['confirmedAt'] = entity.confirmedAt;
	return data;
}

DriverReportAdjustDistribution $DriverReportAdjustDistributionFromJson(Map<String, dynamic> json) {
	final DriverReportAdjustDistribution driverReportAdjustDistribution = DriverReportAdjustDistribution();
	final double? driver = jsonConvert.convert<double>(json['driver']);
	if (driver != null) {
		driverReportAdjustDistribution.driver = driver;
	}
	return driverReportAdjustDistribution;
}

Map<String, dynamic> $DriverReportAdjustDistributionToJson(DriverReportAdjustDistribution entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['driver'] = entity.driver;
	return data;
}

DriverReportAdjustRating $DriverReportAdjustRatingFromJson(Map<String, dynamic> json) {
	final DriverReportAdjustRating driverReportAdjustRating = DriverReportAdjustRating();
	final num? stars = jsonConvert.convert<num>(json['stars']);
	if (stars != null) {
		driverReportAdjustRating.stars = stars;
	}
	final String? content = jsonConvert.convert<String>(json['content']);
	if (content != null) {
		driverReportAdjustRating.content = content;
	}
	final List<String>? reasons = jsonConvert.convertListNotNull<String>(json['reasons']);
	if (reasons != null) {
		driverReportAdjustRating.reasons = reasons;
	}
	return driverReportAdjustRating;
}

Map<String, dynamic> $DriverReportAdjustRatingToJson(DriverReportAdjustRating entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['stars'] = entity.stars;
	data['content'] = entity.content;
	data['reasons'] =  entity.reasons;
	return data;
}

DriverReportAdjustRegion $DriverReportAdjustRegionFromJson(Map<String, dynamic> json) {
	final DriverReportAdjustRegion driverReportAdjustRegion = DriverReportAdjustRegion();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverReportAdjustRegion.sId = sId;
	}
	return driverReportAdjustRegion;
}

Map<String, dynamic> $DriverReportAdjustRegionToJson(DriverReportAdjustRegion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

DriverReportAdjustRestaurant $DriverReportAdjustRestaurantFromJson(Map<String, dynamic> json) {
	final DriverReportAdjustRestaurant driverReportAdjustRestaurant = DriverReportAdjustRestaurant();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		driverReportAdjustRestaurant.sId = sId;
	}
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		driverReportAdjustRestaurant.name = name;
	}
	return driverReportAdjustRestaurant;
}

Map<String, dynamic> $DriverReportAdjustRestaurantToJson(DriverReportAdjustRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name?.toJson();
	return data;
}

DriverReportAdjustAdj $DriverReportAdjustAdjFromJson(Map<String, dynamic> json) {
	final DriverReportAdjustAdj driverReportAdjustAdj = DriverReportAdjustAdj();
	final double? customer = jsonConvert.convert<double>(json['customer']);
	if (customer != null) {
		driverReportAdjustAdj.customer = customer;
	}
	final double? ricepo = jsonConvert.convert<double>(json['ricepo']);
	if (ricepo != null) {
		driverReportAdjustAdj.ricepo = ricepo;
	}
	final double? restaurant = jsonConvert.convert<double>(json['restaurant']);
	if (restaurant != null) {
		driverReportAdjustAdj.restaurant = restaurant;
	}
	final String? reason = jsonConvert.convert<String>(json['reason']);
	if (reason != null) {
		driverReportAdjustAdj.reason = reason;
	}
	return driverReportAdjustAdj;
}

Map<String, dynamic> $DriverReportAdjustAdjToJson(DriverReportAdjustAdj entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['customer'] = entity.customer;
	data['ricepo'] = entity.ricepo;
	data['restaurant'] = entity.restaurant;
	data['reason'] = entity.reason;
	return data;
}

DriverReportAdjustAdjustments $DriverReportAdjustAdjustmentsFromJson(Map<String, dynamic> json) {
	final DriverReportAdjustAdjustments driverReportAdjustAdjustments = DriverReportAdjustAdjustments();
	final double? customer = jsonConvert.convert<double>(json['customer']);
	if (customer != null) {
		driverReportAdjustAdjustments.customer = customer;
	}
	final double? ricepo = jsonConvert.convert<double>(json['ricepo']);
	if (ricepo != null) {
		driverReportAdjustAdjustments.ricepo = ricepo;
	}
	final double? restaurant = jsonConvert.convert<double>(json['restaurant']);
	if (restaurant != null) {
		driverReportAdjustAdjustments.restaurant = restaurant;
	}
	final String? reason = jsonConvert.convert<String>(json['reason']);
	if (reason != null) {
		driverReportAdjustAdjustments.reason = reason;
	}
	final double? driver = jsonConvert.convert<double>(json['driver']);
	if (driver != null) {
		driverReportAdjustAdjustments.driver = driver;
	}
	return driverReportAdjustAdjustments;
}

Map<String, dynamic> $DriverReportAdjustAdjustmentsToJson(DriverReportAdjustAdjustments entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['customer'] = entity.customer;
	data['ricepo'] = entity.ricepo;
	data['restaurant'] = entity.restaurant;
	data['reason'] = entity.reason;
	data['driver'] = entity.driver;
	return data;
}

DriverReportAdjustBundle $DriverReportAdjustBundleFromJson(Map<String, dynamic> json) {
	final DriverReportAdjustBundle driverReportAdjustBundle = DriverReportAdjustBundle();
	final String? combineId = jsonConvert.convert<String>(json['combineId']);
	if (combineId != null) {
		driverReportAdjustBundle.combineId = combineId;
	}
	return driverReportAdjustBundle;
}

Map<String, dynamic> $DriverReportAdjustBundleToJson(DriverReportAdjustBundle entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['combineId'] = entity.combineId;
	return data;
}