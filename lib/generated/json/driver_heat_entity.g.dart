import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/driver/bean/driver_heat_entity.dart';

DriverHeatEntity $DriverHeatEntityFromJson(Map<String, dynamic> json) {
	final DriverHeatEntity driverHeatEntity = DriverHeatEntity();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		driverHeatEntity.type = type;
	}
	final List<double>? coordinates = jsonConvert.convertListNotNull<double>(json['coordinates']);
	if (coordinates != null) {
		driverHeatEntity.coordinates = coordinates;
	}
	return driverHeatEntity;
}

Map<String, dynamic> $DriverHeatEntityToJson(DriverHeatEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['coordinates'] =  entity.coordinates;
	return data;
}