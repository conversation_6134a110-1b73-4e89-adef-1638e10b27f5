import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/summary_report_v1_entity.dart';
import 'package:json_annotation/json_annotation.dart';


SummaryReportV1Entity $SummaryReportV1EntityFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Entity summaryReportV1Entity = SummaryReportV1Entity();
	final SummaryReportV1Id? id = jsonConvert.convert<SummaryReportV1Id>(json['id']);
	if (id != null) {
		summaryReportV1Entity.id = id;
	}
	final int? number = jsonConvert.convert<int>(json['number']);
	if (number != null) {
		summaryReportV1Entity.number = number;
	}
	final SummaryReportV1Metadata? metadata = jsonConvert.convert<SummaryReportV1Metadata>(json['metadata']);
	if (metadata != null) {
		summaryReportV1Entity.metadata = metadata;
	}
	final int? subtotal = jsonConvert.convert<int>(json['subtotal']);
	if (subtotal != null) {
		summaryReportV1Entity.subtotal = subtotal;
	}
	final SummaryReportV1Cost? cost = jsonConvert.convert<SummaryReportV1Cost>(json['cost']);
	if (cost != null) {
		summaryReportV1Entity.cost = cost;
	}
	final SummaryReportV1Fees? fees = jsonConvert.convert<SummaryReportV1Fees>(json['fees']);
	if (fees != null) {
		summaryReportV1Entity.fees = fees;
	}
	final int? total = jsonConvert.convert<int>(json['total']);
	if (total != null) {
		summaryReportV1Entity.total = total;
	}
	final int? deliveryTime = jsonConvert.convert<int>(json['deliveryTime']);
	if (deliveryTime != null) {
		summaryReportV1Entity.deliveryTime = deliveryTime;
	}
	final int? deliveryTimeStd = jsonConvert.convert<int>(json['deliveryTimeStd']);
	if (deliveryTimeStd != null) {
		summaryReportV1Entity.deliveryTimeStd = deliveryTimeStd;
	}
	final SummaryReportV1Commission? commission = jsonConvert.convert<SummaryReportV1Commission>(json['commission']);
	if (commission != null) {
		summaryReportV1Entity.commission = commission;
	}
	final SummaryReportV1Distribution? distribution = jsonConvert.convert<SummaryReportV1Distribution>(json['distribution']);
	if (distribution != null) {
		summaryReportV1Entity.distribution = distribution;
	}
	final SummaryReportV1Adjustments? adjustments = jsonConvert.convert<SummaryReportV1Adjustments>(json['adjustments']);
	if (adjustments != null) {
		summaryReportV1Entity.adjustments = adjustments;
	}
	return summaryReportV1Entity;
}

Map<String, dynamic> $SummaryReportV1EntityToJson(SummaryReportV1Entity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['id'] = entity.id?.toJson();
	data['number'] = entity.number;
	data['metadata'] = entity.metadata?.toJson();
	data['subtotal'] = entity.subtotal;
	data['cost'] = entity.cost?.toJson();
	data['fees'] = entity.fees?.toJson();
	data['total'] = entity.total;
	data['deliveryTime'] = entity.deliveryTime;
	data['deliveryTimeStd'] = entity.deliveryTimeStd;
	data['commission'] = entity.commission?.toJson();
	data['distribution'] = entity.distribution?.toJson();
	data['adjustments'] = entity.adjustments?.toJson();
	return data;
}

SummaryReportV1Id $SummaryReportV1IdFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Id summaryReportV1Id = SummaryReportV1Id();
	final bool? delivery = jsonConvert.convert<bool>(json['delivery']);
	if (delivery != null) {
		summaryReportV1Id.delivery = delivery;
	}
	final bool? provider = jsonConvert.convert<bool>(json['provider']);
	if (provider != null) {
		summaryReportV1Id.provider = provider;
	}
	return summaryReportV1Id;
}

Map<String, dynamic> $SummaryReportV1IdToJson(SummaryReportV1Id entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['delivery'] = entity.delivery;
	data['provider'] = entity.provider;
	return data;
}

SummaryReportV1Metadata $SummaryReportV1MetadataFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Metadata summaryReportV1Metadata = SummaryReportV1Metadata();
	final int? mktFee = jsonConvert.convert<int>(json['mktFee']);
	if (mktFee != null) {
		summaryReportV1Metadata.mktFee = mktFee;
	}
	return summaryReportV1Metadata;
}

Map<String, dynamic> $SummaryReportV1MetadataToJson(SummaryReportV1Metadata entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['mktFee'] = entity.mktFee;
	return data;
}

SummaryReportV1Cost $SummaryReportV1CostFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Cost summaryReportV1Cost = SummaryReportV1Cost();
	final int? subtotal = jsonConvert.convert<int>(json['subtotal']);
	if (subtotal != null) {
		summaryReportV1Cost.subtotal = subtotal;
	}
	final int? tax = jsonConvert.convert<int>(json['tax']);
	if (tax != null) {
		summaryReportV1Cost.tax = tax;
	}
	return summaryReportV1Cost;
}

Map<String, dynamic> $SummaryReportV1CostToJson(SummaryReportV1Cost entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['subtotal'] = entity.subtotal;
	data['tax'] = entity.tax;
	return data;
}

SummaryReportV1Fees $SummaryReportV1FeesFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Fees summaryReportV1Fees = SummaryReportV1Fees();
	final int? delivery = jsonConvert.convert<int>(json['delivery']);
	if (delivery != null) {
		summaryReportV1Fees.delivery = delivery;
	}
	final SummaryReportV1Tip? tip = jsonConvert.convert<SummaryReportV1Tip>(json['tip']);
	if (tip != null) {
		summaryReportV1Fees.tip = tip;
	}
	final int? tax = jsonConvert.convert<int>(json['tax']);
	if (tax != null) {
		summaryReportV1Fees.tax = tax;
	}
	final int? service = jsonConvert.convert<int>(json['service']);
	if (service != null) {
		summaryReportV1Fees.service = service;
	}
	final int? credit = jsonConvert.convert<int>(json['credit']);
	if (credit != null) {
		summaryReportV1Fees.credit = credit;
	}
	final int? delta = jsonConvert.convert<int>(json['delta']);
	if (delta != null) {
		summaryReportV1Fees.delta = delta;
	}
	return summaryReportV1Fees;
}

Map<String, dynamic> $SummaryReportV1FeesToJson(SummaryReportV1Fees entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['delivery'] = entity.delivery;
	data['tip'] = entity.tip?.toJson();
	data['tax'] = entity.tax;
	data['service'] = entity.service;
	data['credit'] = entity.credit;
	data['delta'] = entity.delta;
	return data;
}

SummaryReportV1Tip $SummaryReportV1TipFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Tip summaryReportV1Tip = SummaryReportV1Tip();
	final int? amount = jsonConvert.convert<int>(json['amount']);
	if (amount != null) {
		summaryReportV1Tip.amount = amount;
	}
	return summaryReportV1Tip;
}

Map<String, dynamic> $SummaryReportV1TipToJson(SummaryReportV1Tip entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['amount'] = entity.amount;
	return data;
}

SummaryReportV1Commission $SummaryReportV1CommissionFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Commission summaryReportV1Commission = SummaryReportV1Commission();
	final int? subtotal = jsonConvert.convert<int>(json['subtotal']);
	if (subtotal != null) {
		summaryReportV1Commission.subtotal = subtotal;
	}
	final int? total = jsonConvert.convert<int>(json['total']);
	if (total != null) {
		summaryReportV1Commission.total = total;
	}
	final int? service = jsonConvert.convert<int>(json['service']);
	if (service != null) {
		summaryReportV1Commission.service = service;
	}
	final int? totTax = jsonConvert.convert<int>(json['totTax']);
	if (totTax != null) {
		summaryReportV1Commission.totTax = totTax;
	}
	final int? subTax = jsonConvert.convert<int>(json['subTax']);
	if (subTax != null) {
		summaryReportV1Commission.subTax = subTax;
	}
	final SummaryReportV1Adjustments? adjustments = jsonConvert.convert<SummaryReportV1Adjustments>(json['adjustments']);
	if (adjustments != null) {
		summaryReportV1Commission.adjustments = adjustments;
	}
	return summaryReportV1Commission;
}

Map<String, dynamic> $SummaryReportV1CommissionToJson(SummaryReportV1Commission entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['subtotal'] = entity.subtotal;
	data['total'] = entity.total;
	data['service'] = entity.service;
	data['totTax'] = entity.totTax;
	data['subTax'] = entity.subTax;
	data['adjustments'] = entity.adjustments?.toJson();
	return data;
}

SummaryReportV1Distribution $SummaryReportV1DistributionFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Distribution summaryReportV1Distribution = SummaryReportV1Distribution();
	final int? restaurant = jsonConvert.convert<int>(json['restaurant']);
	if (restaurant != null) {
		summaryReportV1Distribution.restaurant = restaurant;
	}
	final SummaryReportV1Ricepo? ricepo = jsonConvert.convert<SummaryReportV1Ricepo>(json['ricepo']);
	if (ricepo != null) {
		summaryReportV1Distribution.ricepo = ricepo;
	}
	return summaryReportV1Distribution;
}

Map<String, dynamic> $SummaryReportV1DistributionToJson(SummaryReportV1Distribution entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['restaurant'] = entity.restaurant;
	data['ricepo'] = entity.ricepo?.toJson();
	return data;
}

SummaryReportV1Ricepo $SummaryReportV1RicepoFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Ricepo summaryReportV1Ricepo = SummaryReportV1Ricepo();
	final int? tax = jsonConvert.convert<int>(json['tax']);
	if (tax != null) {
		summaryReportV1Ricepo.tax = tax;
	}
	return summaryReportV1Ricepo;
}

Map<String, dynamic> $SummaryReportV1RicepoToJson(SummaryReportV1Ricepo entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['tax'] = entity.tax;
	return data;
}

SummaryReportV1Adjustments $SummaryReportV1AdjustmentsFromJson(Map<String, dynamic> json) {
	final SummaryReportV1Adjustments summaryReportV1Adjustments = SummaryReportV1Adjustments();
	final int? ricepo = jsonConvert.convert<int>(json['ricepo']);
	if (ricepo != null) {
		summaryReportV1Adjustments.ricepo = ricepo;
	}
	final int? restaurant = jsonConvert.convert<int>(json['restaurant']);
	if (restaurant != null) {
		summaryReportV1Adjustments.restaurant = restaurant;
	}
	final int? driver = jsonConvert.convert<int>(json['driver']);
	if (driver != null) {
		summaryReportV1Adjustments.driver = driver;
	}
	final int? customer = jsonConvert.convert<int>(json['customer']);
	if (customer != null) {
		summaryReportV1Adjustments.customer = customer;
	}
	final int? unionpay = jsonConvert.convert<int>(json['unionpay']);
	if (unionpay != null) {
		summaryReportV1Adjustments.unionpay = unionpay;
	}
	return summaryReportV1Adjustments;
}

Map<String, dynamic> $SummaryReportV1AdjustmentsToJson(SummaryReportV1Adjustments entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['ricepo'] = entity.ricepo;
	data['restaurant'] = entity.restaurant;
	data['driver'] = entity.driver;
	data['customer'] = entity.customer;
	data['unionpay'] = entity.unionpay;
	return data;
}