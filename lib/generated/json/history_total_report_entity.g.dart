import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/history_total_report_entity.dart';

HistoryTotalReportEntity $HistoryTotalReportEntityFromJson(Map<String, dynamic> json) {
	final HistoryTotalReportEntity historyTotalReportEntity = HistoryTotalReportEntity();
	final HistoryTotalReportId? hId = jsonConvert.convert<HistoryTotalReportId>(json['_id']);
	if (hId != null) {
		historyTotalReportEntity.hId = hId;
	}
	final double? number = jsonConvert.convert<double>(json['number']);
	if (number != null) {
		historyTotalReportEntity.number = number;
	}
	final double? subtotal = jsonConvert.convert<double>(json['subtotal']);
	if (subtotal != null) {
		historyTotalReportEntity.subtotal = subtotal;
	}
	final HistoryTotalReportCost? cost = jsonConvert.convert<HistoryTotalReportCost>(json['cost']);
	if (cost != null) {
		historyTotalReportEntity.cost = cost;
	}
	final HistoryTotalReportFees? fees = jsonConvert.convert<HistoryTotalReportFees>(json['fees']);
	if (fees != null) {
		historyTotalReportEntity.fees = fees;
	}
	final double? total = jsonConvert.convert<double>(json['total']);
	if (total != null) {
		historyTotalReportEntity.total = total;
	}
	final HistoryTotalReportCommission? commission = jsonConvert.convert<HistoryTotalReportCommission>(json['commission']);
	if (commission != null) {
		historyTotalReportEntity.commission = commission;
	}
	final HistoryTotalReportDistribution? distribution = jsonConvert.convert<HistoryTotalReportDistribution>(json['distribution']);
	if (distribution != null) {
		historyTotalReportEntity.distribution = distribution;
	}
	final HistoryTotalReportAdjustments? adjustments = jsonConvert.convert<HistoryTotalReportAdjustments>(json['adjustments']);
	if (adjustments != null) {
		historyTotalReportEntity.adjustments = adjustments;
	}
	return historyTotalReportEntity;
}

Map<String, dynamic> $HistoryTotalReportEntityToJson(HistoryTotalReportEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.hId?.toJson();
	data['number'] = entity.number;
	data['subtotal'] = entity.subtotal;
	data['cost'] = entity.cost?.toJson();
	data['fees'] = entity.fees?.toJson();
	data['total'] = entity.total;
	data['commission'] = entity.commission?.toJson();
	data['distribution'] = entity.distribution?.toJson();
	data['adjustments'] = entity.adjustments?.toJson();
	return data;
}

HistoryTotalReportId $HistoryTotalReportIdFromJson(Map<String, dynamic> json) {
	final HistoryTotalReportId historyTotalReportId = HistoryTotalReportId();
	final bool? delivery = jsonConvert.convert<bool>(json['delivery']);
	if (delivery != null) {
		historyTotalReportId.delivery = delivery;
	}
	final bool? provider = jsonConvert.convert<bool>(json['provider']);
	if (provider != null) {
		historyTotalReportId.provider = provider;
	}
	return historyTotalReportId;
}

Map<String, dynamic> $HistoryTotalReportIdToJson(HistoryTotalReportId entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['delivery'] = entity.delivery;
	data['provider'] = entity.provider;
	return data;
}

HistoryTotalReportCost $HistoryTotalReportCostFromJson(Map<String, dynamic> json) {
	final HistoryTotalReportCost historyTotalReportCost = HistoryTotalReportCost();
	final double? subtotal = jsonConvert.convert<double>(json['subtotal']);
	if (subtotal != null) {
		historyTotalReportCost.subtotal = subtotal;
	}
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		historyTotalReportCost.tax = tax;
	}
	return historyTotalReportCost;
}

Map<String, dynamic> $HistoryTotalReportCostToJson(HistoryTotalReportCost entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['subtotal'] = entity.subtotal;
	data['tax'] = entity.tax;
	return data;
}

HistoryTotalReportFees $HistoryTotalReportFeesFromJson(Map<String, dynamic> json) {
	final HistoryTotalReportFees historyTotalReportFees = HistoryTotalReportFees();
	final double? delivery = jsonConvert.convert<double>(json['delivery']);
	if (delivery != null) {
		historyTotalReportFees.delivery = delivery;
	}
	final HistoryTotalReportFeesTip? tip = jsonConvert.convert<HistoryTotalReportFeesTip>(json['tip']);
	if (tip != null) {
		historyTotalReportFees.tip = tip;
	}
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		historyTotalReportFees.tax = tax;
	}
	final double? service = jsonConvert.convert<double>(json['service']);
	if (service != null) {
		historyTotalReportFees.service = service;
	}
	final double? credit = jsonConvert.convert<double>(json['credit']);
	if (credit != null) {
		historyTotalReportFees.credit = credit;
	}
	final double? delta = jsonConvert.convert<double>(json['delta']);
	if (delta != null) {
		historyTotalReportFees.delta = delta;
	}
	return historyTotalReportFees;
}

Map<String, dynamic> $HistoryTotalReportFeesToJson(HistoryTotalReportFees entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['delivery'] = entity.delivery;
	data['tip'] = entity.tip?.toJson();
	data['tax'] = entity.tax;
	data['service'] = entity.service;
	data['credit'] = entity.credit;
	data['delta'] = entity.delta;
	return data;
}

HistoryTotalReportFeesTip $HistoryTotalReportFeesTipFromJson(Map<String, dynamic> json) {
	final HistoryTotalReportFeesTip historyTotalReportFeesTip = HistoryTotalReportFeesTip();
	final double? amount = jsonConvert.convert<double>(json['amount']);
	if (amount != null) {
		historyTotalReportFeesTip.amount = amount;
	}
	return historyTotalReportFeesTip;
}

Map<String, dynamic> $HistoryTotalReportFeesTipToJson(HistoryTotalReportFeesTip entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['amount'] = entity.amount;
	return data;
}

HistoryTotalReportCommission $HistoryTotalReportCommissionFromJson(Map<String, dynamic> json) {
	final HistoryTotalReportCommission historyTotalReportCommission = HistoryTotalReportCommission();
	final double? subtotal = jsonConvert.convert<double>(json['subtotal']);
	if (subtotal != null) {
		historyTotalReportCommission.subtotal = subtotal;
	}
	final double? total = jsonConvert.convert<double>(json['total']);
	if (total != null) {
		historyTotalReportCommission.total = total;
	}
	final double? service = jsonConvert.convert<double>(json['service']);
	if (service != null) {
		historyTotalReportCommission.service = service;
	}
	return historyTotalReportCommission;
}

Map<String, dynamic> $HistoryTotalReportCommissionToJson(HistoryTotalReportCommission entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['subtotal'] = entity.subtotal;
	data['total'] = entity.total;
	data['service'] = entity.service;
	return data;
}

HistoryTotalReportDistribution $HistoryTotalReportDistributionFromJson(Map<String, dynamic> json) {
	final HistoryTotalReportDistribution historyTotalReportDistribution = HistoryTotalReportDistribution();
	final double? restaurant = jsonConvert.convert<double>(json['restaurant']);
	if (restaurant != null) {
		historyTotalReportDistribution.restaurant = restaurant;
	}
	return historyTotalReportDistribution;
}

Map<String, dynamic> $HistoryTotalReportDistributionToJson(HistoryTotalReportDistribution entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['restaurant'] = entity.restaurant;
	return data;
}

HistoryTotalReportAdjustments $HistoryTotalReportAdjustmentsFromJson(Map<String, dynamic> json) {
	final HistoryTotalReportAdjustments historyTotalReportAdjustments = HistoryTotalReportAdjustments();
	final double? restaurant = jsonConvert.convert<double>(json['restaurant']);
	if (restaurant != null) {
		historyTotalReportAdjustments.restaurant = restaurant;
	}
	return historyTotalReportAdjustments;
}

Map<String, dynamic> $HistoryTotalReportAdjustmentsToJson(HistoryTotalReportAdjustments entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['restaurant'] = entity.restaurant;
	return data;
}