import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/ui/home/<USER>/menu/update_one_params_entity.dart';
import 'package:connect/data/multi_name_entity.dart';

import 'package:connect/data/rest_menu_entity.dart';


UpdateOneParamsEntity $UpdateOneParamsEntityFromJson(Map<String, dynamic> json) {
	final UpdateOneParamsEntity updateOneParamsEntity = UpdateOneParamsEntity();
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		updateOneParamsEntity.name = name;
	}
	final dynamic? description = jsonConvert.convert<dynamic>(json['description']);
	if (description != null) {
		updateOneParamsEntity.description = description;
	}
	final dynamic? code = jsonConvert.convert<dynamic>(json['code']);
	if (code != null) {
		updateOneParamsEntity.code = code;
	}
	final int? price = jsonConvert.convert<int>(json['price']);
	if (price != null) {
		updateOneParamsEntity.price = price;
	}
	final int? cost = jsonConvert.convert<int>(json['cost']);
	if (cost != null) {
		updateOneParamsEntity.cost = cost;
	}
	final int? index = jsonConvert.convert<int>(json['index']);
	if (index != null) {
		updateOneParamsEntity.index = index;
	}
	final dynamic? label = jsonConvert.convert<dynamic>(json['label']);
	if (label != null) {
		updateOneParamsEntity.label = label;
	}
	final List<RestMenuFoodOptions?>? options = jsonConvert.convertList<RestMenuFoodOptions>(json['options']);
	if (options != null) {
		updateOneParamsEntity.options = options;
	}
	final dynamic? hours = jsonConvert.convert<dynamic>(json['hours']);
	if (hours != null) {
		updateOneParamsEntity.hours = hours;
	}
	final dynamic? image = jsonConvert.convert<dynamic>(json['image']);
	if (image != null) {
		updateOneParamsEntity.image = image;
	}
	final List<dynamic?>? date = jsonConvert.convertList<dynamic>(json['date']);
	if (date != null) {
		updateOneParamsEntity.date = date;
	}
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		updateOneParamsEntity.minimum = minimum;
	}
	final dynamic? stock = jsonConvert.convert<dynamic>(json['stock']);
	if (stock != null) {
		updateOneParamsEntity.stock = stock;
	}
	final dynamic? limit = jsonConvert.convert<dynamic>(json['limit']);
	if (limit != null) {
		updateOneParamsEntity.limit = limit;
	}
	return updateOneParamsEntity;
}

Map<String, dynamic> $UpdateOneParamsEntityToJson(UpdateOneParamsEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['name'] = entity.name?.toJson();
	data['description'] = entity.description;
	data['code'] = entity.code;
	data['price'] = entity.price;
	data['cost'] = entity.cost;
	data['index'] = entity.index;
	data['label'] = entity.label;
	data['options'] =  entity.options?.map((v) => v?.toJson()).toList();
	data['hours'] = entity.hours;
	data['image'] = entity.image;
	data['date'] =  entity.date;
	data['minimum'] = entity.minimum;
	data['stock'] = entity.stock;
	data['limit'] = entity.limit;
	return data;
}