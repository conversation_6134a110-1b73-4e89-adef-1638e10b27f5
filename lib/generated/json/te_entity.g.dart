import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/ui/home/<USER>/info/te_entity.dart';

TeEntity $TeEntityFromJson(Map<String, dynamic> json) {
	final TeEntity teEntity = TeEntity();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		teEntity.zhCn = zhCn;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		teEntity.zhHk = zhHk;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		teEntity.enUs = enUs;
	}
	final String? user = jsonConvert.convert<String>(json['user']);
	if (user != null) {
		teEntity.user = user;
	}
	final String? reason = jsonConvert.convert<String>(json['reason']);
	if (reason != null) {
		teEntity.reason = reason;
	}
	return teEntity;
}

Map<String, dynamic> $TeEntityToJson(TeEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['zh-HK'] = entity.zhHk;
	data['en-US'] = entity.enUs;
	data['user'] = entity.user;
	data['reason'] = entity.reason;
	return data;
}