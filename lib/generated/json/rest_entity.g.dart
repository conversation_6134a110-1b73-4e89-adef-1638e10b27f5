import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/rest_entity.dart';
import 'package:connect/data/multi_name_entity.dart';


RestEntity $RestEntityFromJson(Map<String, dynamic> json) {
	final RestEntity restEntity = RestEntity();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restEntity.sId = sId;
	}
	final String? color = jsonConvert.convert<String>(json['color']);
	if (color != null) {
		restEntity.color = color;
	}
	final String? timezone = jsonConvert.convert<String>(json['timezone']);
	if (timezone != null) {
		restEntity.timezone = timezone;
	}
	final double? zscore = jsonConvert.convert<double>(json['zscore']);
	if (zscore != null) {
		restEntity.zscore = zscore;
	}
	final bool? featured = jsonConvert.convert<bool>(json['featured']);
	if (featured != null) {
		restEntity.featured = featured;
	}
	final bool? fake = jsonConvert.convert<bool>(json['fake']);
	if (fake != null) {
		restEntity.fake = fake;
	}
	final bool? suspended = jsonConvert.convert<bool>(json['suspended']);
	if (suspended != null) {
		restEntity.suspended = suspended;
	}
	final bool? manual = jsonConvert.convert<bool>(json['manual']);
	if (manual != null) {
		restEntity.manual = manual;
	}
	final String? language = jsonConvert.convert<String>(json['language']);
	if (language != null) {
		restEntity.language = language;
	}
	final bool? canDeliver = jsonConvert.convert<bool>(json['canDeliver']);
	if (canDeliver != null) {
		restEntity.canDeliver = canDeliver;
	}
	final bool? canPickup = jsonConvert.convert<bool>(json['canPickup']);
	if (canPickup != null) {
		restEntity.canPickup = canPickup;
	}
	final String? acceptCredit = jsonConvert.convert<String>(json['acceptCredit']);
	if (acceptCredit != null) {
		restEntity.acceptCredit = acceptCredit;
	}
	final List<String?>? tags = jsonConvert.convertList<String>(json['tags']);
	if (tags != null) {
		restEntity.tags = tags;
	}
	final List<RestContacts?>? contacts = jsonConvert.convertList<RestContacts>(json['contacts']);
	if (contacts != null) {
		restEntity.contacts = contacts;
	}
	final List<RestHours?>? hours = jsonConvert.convertList<RestHours>(json['hours']);
	if (hours != null) {
		restEntity.hours = hours;
	}
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		restEntity.tax = tax;
	}
	final RestDelivery? delivery = jsonConvert.convert<RestDelivery>(json['delivery']);
	if (delivery != null) {
		restEntity.delivery = delivery;
	}
	final RestCredit? credit = jsonConvert.convert<RestCredit>(json['credit']);
	if (credit != null) {
		restEntity.credit = credit;
	}
	final RestStripe? stripe = jsonConvert.convert<RestStripe>(json['stripe']);
	if (stripe != null) {
		restEntity.stripe = stripe;
	}
	final RestCommission? commission = jsonConvert.convert<RestCommission>(json['commission']);
	if (commission != null) {
		restEntity.commission = commission;
	}
	final RestPreferences? preferences = jsonConvert.convert<RestPreferences>(json['preferences']);
	if (preferences != null) {
		restEntity.preferences = preferences;
	}
	final RestLocation? location = jsonConvert.convert<RestLocation>(json['location']);
	if (location != null) {
		restEntity.location = location;
	}
	final RestName? name = jsonConvert.convert<RestName>(json['name']);
	if (name != null) {
		restEntity.name = name;
	}
	final RestAddress? address = jsonConvert.convert<RestAddress>(json['address']);
	if (address != null) {
		restEntity.address = address;
	}
	final RestRegion? region = jsonConvert.convert<RestRegion>(json['region']);
	if (region != null) {
		restEntity.region = region;
	}
	final String? lastUpdate = jsonConvert.convert<String>(json['lastUpdate']);
	if (lastUpdate != null) {
		restEntity.lastUpdate = lastUpdate;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		restEntity.createdAt = createdAt;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restEntity.updatedAt = updatedAt;
	}
	final RestReward? reward = jsonConvert.convert<RestReward>(json['reward']);
	if (reward != null) {
		restEntity.reward = reward;
	}
	final RestDiscount? discount = jsonConvert.convert<RestDiscount>(json['discount']);
	if (discount != null) {
		restEntity.discount = discount;
	}
	final RestAnalytics? analytics = jsonConvert.convert<RestAnalytics>(json['analytics']);
	if (analytics != null) {
		restEntity.analytics = analytics;
	}
	final List<RestItems?>? items = jsonConvert.convertList<RestItems>(json['items']);
	if (items != null) {
		restEntity.items = items;
	}
	final String? transferedAt = jsonConvert.convert<String>(json['transferedAt']);
	if (transferedAt != null) {
		restEntity.transferedAt = transferedAt;
	}
	final RestBundleOpts? bundleOpts = jsonConvert.convert<RestBundleOpts>(json['bundleOpts']);
	if (bundleOpts != null) {
		restEntity.bundleOpts = bundleOpts;
	}
	final RestServiceFee? serviceFee = jsonConvert.convert<RestServiceFee>(json['serviceFee']);
	if (serviceFee != null) {
		restEntity.serviceFee = serviceFee;
	}
	final RestMotd? motd = jsonConvert.convert<RestMotd>(json['motd']);
	if (motd != null) {
		restEntity.motd = motd;
	}
	final RestPromotion? promotion = jsonConvert.convert<RestPromotion>(json['promotion']);
	if (promotion != null) {
		restEntity.promotion = promotion;
	}
	final RestExpireProp? expireProp = jsonConvert.convert<RestExpireProp>(json['expireProp']);
	if (expireProp != null) {
		restEntity.expireProp = expireProp;
	}
	final List<RestExtraFees?>? extraFees = jsonConvert.convertList<RestExtraFees>(json['extraFees']);
	if (extraFees != null) {
		restEntity.extraFees = extraFees;
	}
	final RestPrepare? prepare = jsonConvert.convert<RestPrepare>(json['prepare']);
	if (prepare != null) {
		restEntity.prepare = prepare;
	}
	final String? fullChar = jsonConvert.convert<String>(json['fullChar']);
	if (fullChar != null) {
		restEntity.fullChar = fullChar;
	}
	final RestClosed? closed = jsonConvert.convert<RestClosed>(json['closed']);
	if (closed != null) {
		restEntity.closed = closed;
	}
	final RestClosePeriod? closePeriod = jsonConvert.convert<RestClosePeriod>(json['closePeriod']);
	if (closePeriod != null) {
		restEntity.closePeriod = closePeriod;
	}
	return restEntity;
}

Map<String, dynamic> $RestEntityToJson(RestEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['color'] = entity.color;
	data['timezone'] = entity.timezone;
	data['zscore'] = entity.zscore;
	data['featured'] = entity.featured;
	data['fake'] = entity.fake;
	data['suspended'] = entity.suspended;
	data['manual'] = entity.manual;
	data['language'] = entity.language;
	data['canDeliver'] = entity.canDeliver;
	data['canPickup'] = entity.canPickup;
	data['acceptCredit'] = entity.acceptCredit;
	data['tags'] =  entity.tags;
	data['contacts'] =  entity.contacts?.map((v) => v?.toJson()).toList();
	data['hours'] =  entity.hours?.map((v) => v?.toJson()).toList();
	data['tax'] = entity.tax;
	data['delivery'] = entity.delivery?.toJson();
	data['credit'] = entity.credit?.toJson();
	data['stripe'] = entity.stripe?.toJson();
	data['commission'] = entity.commission?.toJson();
	data['preferences'] = entity.preferences?.toJson();
	data['location'] = entity.location?.toJson();
	data['name'] = entity.name?.toJson();
	data['address'] = entity.address?.toJson();
	data['region'] = entity.region?.toJson();
	data['lastUpdate'] = entity.lastUpdate;
	data['createdAt'] = entity.createdAt;
	data['updatedAt'] = entity.updatedAt;
	data['reward'] = entity.reward?.toJson();
	data['discount'] = entity.discount?.toJson();
	data['analytics'] = entity.analytics?.toJson();
	data['items'] =  entity.items?.map((v) => v?.toJson()).toList();
	data['transferedAt'] = entity.transferedAt;
	data['bundleOpts'] = entity.bundleOpts?.toJson();
	data['serviceFee'] = entity.serviceFee?.toJson();
	data['motd'] = entity.motd?.toJson();
	data['promotion'] = entity.promotion?.toJson();
	data['expireProp'] = entity.expireProp?.toJson();
	data['extraFees'] =  entity.extraFees?.map((v) => v?.toJson()).toList();
	data['prepare'] = entity.prepare?.toJson();
	data['fullChar'] = entity.fullChar;
	data['closed'] = entity.closed?.toJson();
	data['closePeriod'] = entity.closePeriod?.toJson();
	return data;
}

RestClosePeriod $RestClosePeriodFromJson(Map<String, dynamic> json) {
	final RestClosePeriod restClosePeriod = RestClosePeriod();
	final String? from = jsonConvert.convert<String>(json['from']);
	if (from != null) {
		restClosePeriod.from = from;
	}
	final String? to = jsonConvert.convert<String>(json['to']);
	if (to != null) {
		restClosePeriod.to = to;
	}
	final String? note = jsonConvert.convert<String>(json['note']);
	if (note != null) {
		restClosePeriod.note = note;
	}
	return restClosePeriod;
}

Map<String, dynamic> $RestClosePeriodToJson(RestClosePeriod entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['from'] = entity.from;
	data['to'] = entity.to;
	data['note'] = entity.note;
	return data;
}

RestContacts $RestContactsFromJson(Map<String, dynamic> json) {
	final RestContacts restContacts = RestContacts();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		restContacts.type = type;
	}
	final String? content = jsonConvert.convert<String>(json['content']);
	if (content != null) {
		restContacts.content = content;
	}
	final bool? report = jsonConvert.convert<bool>(json['report']);
	if (report != null) {
		restContacts.report = report;
	}
	final bool? order = jsonConvert.convert<bool>(json['order']);
	if (order != null) {
		restContacts.order = order;
	}
	return restContacts;
}

Map<String, dynamic> $RestContactsToJson(RestContacts entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['content'] = entity.content;
	data['report'] = entity.report;
	data['order'] = entity.order;
	return data;
}

RestHours $RestHoursFromJson(Map<String, dynamic> json) {
	final RestHours restHours = RestHours();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		restHours.type = type;
	}
	final int? dayOfWeek = jsonConvert.convert<int>(json['dayOfWeek']);
	if (dayOfWeek != null) {
		restHours.dayOfWeek = dayOfWeek;
	}
	final int? start = jsonConvert.convert<int>(json['start']);
	if (start != null) {
		restHours.start = start;
	}
	final int? end = jsonConvert.convert<int>(json['end']);
	if (end != null) {
		restHours.end = end;
	}
	return restHours;
}

Map<String, dynamic> $RestHoursToJson(RestHours entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['dayOfWeek'] = entity.dayOfWeek;
	data['start'] = entity.start;
	data['end'] = entity.end;
	return data;
}

RestDelivery $RestDeliveryFromJson(Map<String, dynamic> json) {
	final RestDelivery restDelivery = RestDelivery();
	final RestDeliveryZone? zone = jsonConvert.convert<RestDeliveryZone>(json['zone']);
	if (zone != null) {
		restDelivery.zone = zone;
	}
	final String? provider = jsonConvert.convert<String>(json['provider']);
	if (provider != null) {
		restDelivery.provider = provider;
	}
	final MultiNameEntity? note = jsonConvert.convert<MultiNameEntity>(json['note']);
	if (note != null) {
		restDelivery.note = note;
	}
	final RestDeliveryPickupEst? pickupEst = jsonConvert.convert<RestDeliveryPickupEst>(json['pickupEst']);
	if (pickupEst != null) {
		restDelivery.pickupEst = pickupEst;
	}
	return restDelivery;
}

Map<String, dynamic> $RestDeliveryToJson(RestDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zone'] = entity.zone?.toJson();
	data['provider'] = entity.provider;
	data['note'] = entity.note?.toJson();
	data['pickupEst'] = entity.pickupEst?.toJson();
	return data;
}

RestDeliveryZone $RestDeliveryZoneFromJson(Map<String, dynamic> json) {
	final RestDeliveryZone restDeliveryZone = RestDeliveryZone();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		restDeliveryZone.type = type;
	}
	final List<RestDeliveryZoneFeatures?>? features = jsonConvert.convertList<RestDeliveryZoneFeatures>(json['features']);
	if (features != null) {
		restDeliveryZone.features = features;
	}
	return restDeliveryZone;
}

Map<String, dynamic> $RestDeliveryZoneToJson(RestDeliveryZone entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['features'] =  entity.features?.map((v) => v?.toJson()).toList();
	return data;
}

RestDeliveryZoneFeatures $RestDeliveryZoneFeaturesFromJson(Map<String, dynamic> json) {
	final RestDeliveryZoneFeatures restDeliveryZoneFeatures = RestDeliveryZoneFeatures();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		restDeliveryZoneFeatures.type = type;
	}
	final RestDeliveryZoneFeaturesGeometry? geometry = jsonConvert.convert<RestDeliveryZoneFeaturesGeometry>(json['geometry']);
	if (geometry != null) {
		restDeliveryZoneFeatures.geometry = geometry;
	}
	final RestDeliveryZoneFeaturesProperties? properties = jsonConvert.convert<RestDeliveryZoneFeaturesProperties>(json['properties']);
	if (properties != null) {
		restDeliveryZoneFeatures.properties = properties;
	}
	return restDeliveryZoneFeatures;
}

Map<String, dynamic> $RestDeliveryZoneFeaturesToJson(RestDeliveryZoneFeatures entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['geometry'] = entity.geometry?.toJson();
	data['properties'] = entity.properties?.toJson();
	return data;
}

RestDeliveryZoneFeaturesGeometry $RestDeliveryZoneFeaturesGeometryFromJson(Map<String, dynamic> json) {
	final RestDeliveryZoneFeaturesGeometry restDeliveryZoneFeaturesGeometry = RestDeliveryZoneFeaturesGeometry();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		restDeliveryZoneFeaturesGeometry.type = type;
	}
	return restDeliveryZoneFeaturesGeometry;
}

Map<String, dynamic> $RestDeliveryZoneFeaturesGeometryToJson(RestDeliveryZoneFeaturesGeometry entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	return data;
}

RestDeliveryZoneFeaturesProperties $RestDeliveryZoneFeaturesPropertiesFromJson(Map<String, dynamic> json) {
	final RestDeliveryZoneFeaturesProperties restDeliveryZoneFeaturesProperties = RestDeliveryZoneFeaturesProperties();
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		restDeliveryZoneFeaturesProperties.minimum = minimum;
	}
	final RestDeliveryZoneFeaturesPropertiesFee? fee = jsonConvert.convert<RestDeliveryZoneFeaturesPropertiesFee>(json['fee']);
	if (fee != null) {
		restDeliveryZoneFeaturesProperties.fee = fee;
	}
	final RestDeliveryZoneFeaturesPropertiesEstimate? estimate = jsonConvert.convert<RestDeliveryZoneFeaturesPropertiesEstimate>(json['estimate']);
	if (estimate != null) {
		restDeliveryZoneFeaturesProperties.estimate = estimate;
	}
	final List<RestDeliveryZoneFeaturesPropertiesFees?>? fees = jsonConvert.convertList<RestDeliveryZoneFeaturesPropertiesFees>(json['fees']);
	if (fees != null) {
		restDeliveryZoneFeaturesProperties.fees = fees;
	}
	final dynamic? name = jsonConvert.convert<dynamic>(json['name']);
	if (name != null) {
		restDeliveryZoneFeaturesProperties.name = name;
	}
	final dynamic? motd = jsonConvert.convert<dynamic>(json['motd']);
	if (motd != null) {
		restDeliveryZoneFeaturesProperties.motd = motd;
	}
	final dynamic? promotion = jsonConvert.convert<dynamic>(json['promotion']);
	if (promotion != null) {
		restDeliveryZoneFeaturesProperties.promotion = promotion;
	}
	final bool? scheduled = jsonConvert.convert<bool>(json['scheduled']);
	if (scheduled != null) {
		restDeliveryZoneFeaturesProperties.scheduled = scheduled;
	}
	final dynamic? batch = jsonConvert.convert<dynamic>(json['batch']);
	if (batch != null) {
		restDeliveryZoneFeaturesProperties.batch = batch;
	}
	final int? driverBonus = jsonConvert.convert<int>(json['driverBonus']);
	if (driverBonus != null) {
		restDeliveryZoneFeaturesProperties.driverBonus = driverBonus;
	}
	final List<RestDeliveryZoneFeaturesPropertiesWindows?>? windows = jsonConvert.convertList<RestDeliveryZoneFeaturesPropertiesWindows>(json['windows']);
	if (windows != null) {
		restDeliveryZoneFeaturesProperties.windows = windows;
	}
	final String? provider = jsonConvert.convert<String>(json['provider']);
	if (provider != null) {
		restDeliveryZoneFeaturesProperties.provider = provider;
	}
	return restDeliveryZoneFeaturesProperties;
}

Map<String, dynamic> $RestDeliveryZoneFeaturesPropertiesToJson(RestDeliveryZoneFeaturesProperties entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['minimum'] = entity.minimum;
	data['fee'] = entity.fee?.toJson();
	data['estimate'] = entity.estimate?.toJson();
	data['fees'] =  entity.fees?.map((v) => v?.toJson()).toList();
	data['name'] = entity.name;
	data['motd'] = entity.motd;
	data['promotion'] = entity.promotion;
	data['scheduled'] = entity.scheduled;
	data['batch'] = entity.batch;
	data['driverBonus'] = entity.driverBonus;
	data['windows'] =  entity.windows?.map((v) => v?.toJson()).toList();
	data['provider'] = entity.provider;
	return data;
}

RestDeliveryZoneFeaturesPropertiesFee $RestDeliveryZoneFeaturesPropertiesFeeFromJson(Map<String, dynamic> json) {
	final RestDeliveryZoneFeaturesPropertiesFee restDeliveryZoneFeaturesPropertiesFee = RestDeliveryZoneFeaturesPropertiesFee();
	final int? flat = jsonConvert.convert<int>(json['flat']);
	if (flat != null) {
		restDeliveryZoneFeaturesPropertiesFee.flat = flat;
	}
	final int? factor = jsonConvert.convert<int>(json['factor']);
	if (factor != null) {
		restDeliveryZoneFeaturesPropertiesFee.factor = factor;
	}
	return restDeliveryZoneFeaturesPropertiesFee;
}

Map<String, dynamic> $RestDeliveryZoneFeaturesPropertiesFeeToJson(RestDeliveryZoneFeaturesPropertiesFee entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['flat'] = entity.flat;
	data['factor'] = entity.factor;
	return data;
}

RestDeliveryZoneFeaturesPropertiesEstimate $RestDeliveryZoneFeaturesPropertiesEstimateFromJson(Map<String, dynamic> json) {
	final RestDeliveryZoneFeaturesPropertiesEstimate restDeliveryZoneFeaturesPropertiesEstimate = RestDeliveryZoneFeaturesPropertiesEstimate();
	final int? min = jsonConvert.convert<int>(json['min']);
	if (min != null) {
		restDeliveryZoneFeaturesPropertiesEstimate.min = min;
	}
	final int? max = jsonConvert.convert<int>(json['max']);
	if (max != null) {
		restDeliveryZoneFeaturesPropertiesEstimate.max = max;
	}
	return restDeliveryZoneFeaturesPropertiesEstimate;
}

Map<String, dynamic> $RestDeliveryZoneFeaturesPropertiesEstimateToJson(RestDeliveryZoneFeaturesPropertiesEstimate entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['min'] = entity.min;
	data['max'] = entity.max;
	return data;
}

RestDeliveryZoneFeaturesPropertiesFees $RestDeliveryZoneFeaturesPropertiesFeesFromJson(Map<String, dynamic> json) {
	final RestDeliveryZoneFeaturesPropertiesFees restDeliveryZoneFeaturesPropertiesFees = RestDeliveryZoneFeaturesPropertiesFees();
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		restDeliveryZoneFeaturesPropertiesFees.minimum = minimum;
	}
	final int? delivery = jsonConvert.convert<int>(json['delivery']);
	if (delivery != null) {
		restDeliveryZoneFeaturesPropertiesFees.delivery = delivery;
	}
	final RestDeliveryZoneFeaturesPropertiesFeesAdjustments? adjustments = jsonConvert.convert<RestDeliveryZoneFeaturesPropertiesFeesAdjustments>(json['adjustments']);
	if (adjustments != null) {
		restDeliveryZoneFeaturesPropertiesFees.adjustments = adjustments;
	}
	return restDeliveryZoneFeaturesPropertiesFees;
}

Map<String, dynamic> $RestDeliveryZoneFeaturesPropertiesFeesToJson(RestDeliveryZoneFeaturesPropertiesFees entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['minimum'] = entity.minimum;
	data['delivery'] = entity.delivery;
	data['adjustments'] = entity.adjustments?.toJson();
	return data;
}

RestDeliveryZoneFeaturesPropertiesFeesAdjustments $RestDeliveryZoneFeaturesPropertiesFeesAdjustmentsFromJson(Map<String, dynamic> json) {
	final RestDeliveryZoneFeaturesPropertiesFeesAdjustments restDeliveryZoneFeaturesPropertiesFeesAdjustments = RestDeliveryZoneFeaturesPropertiesFeesAdjustments();
	final int? ricepo = jsonConvert.convert<int>(json['ricepo']);
	if (ricepo != null) {
		restDeliveryZoneFeaturesPropertiesFeesAdjustments.ricepo = ricepo;
	}
	final String? reason = jsonConvert.convert<String>(json['reason']);
	if (reason != null) {
		restDeliveryZoneFeaturesPropertiesFeesAdjustments.reason = reason;
	}
	final int? restaurant = jsonConvert.convert<int>(json['restaurant']);
	if (restaurant != null) {
		restDeliveryZoneFeaturesPropertiesFeesAdjustments.restaurant = restaurant;
	}
	final int? customer = jsonConvert.convert<int>(json['customer']);
	if (customer != null) {
		restDeliveryZoneFeaturesPropertiesFeesAdjustments.customer = customer;
	}
	return restDeliveryZoneFeaturesPropertiesFeesAdjustments;
}

Map<String, dynamic> $RestDeliveryZoneFeaturesPropertiesFeesAdjustmentsToJson(RestDeliveryZoneFeaturesPropertiesFeesAdjustments entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['ricepo'] = entity.ricepo;
	data['reason'] = entity.reason;
	data['restaurant'] = entity.restaurant;
	data['customer'] = entity.customer;
	return data;
}

RestDeliveryZoneFeaturesPropertiesWindows $RestDeliveryZoneFeaturesPropertiesWindowsFromJson(Map<String, dynamic> json) {
	final RestDeliveryZoneFeaturesPropertiesWindows restDeliveryZoneFeaturesPropertiesWindows = RestDeliveryZoneFeaturesPropertiesWindows();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		restDeliveryZoneFeaturesPropertiesWindows.type = type;
	}
	final int? dayOfWeek = jsonConvert.convert<int>(json['dayOfWeek']);
	if (dayOfWeek != null) {
		restDeliveryZoneFeaturesPropertiesWindows.dayOfWeek = dayOfWeek;
	}
	final int? start = jsonConvert.convert<int>(json['start']);
	if (start != null) {
		restDeliveryZoneFeaturesPropertiesWindows.start = start;
	}
	final int? end = jsonConvert.convert<int>(json['end']);
	if (end != null) {
		restDeliveryZoneFeaturesPropertiesWindows.end = end;
	}
	return restDeliveryZoneFeaturesPropertiesWindows;
}

Map<String, dynamic> $RestDeliveryZoneFeaturesPropertiesWindowsToJson(RestDeliveryZoneFeaturesPropertiesWindows entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['dayOfWeek'] = entity.dayOfWeek;
	data['start'] = entity.start;
	data['end'] = entity.end;
	return data;
}

RestDeliveryPickupEst $RestDeliveryPickupEstFromJson(Map<String, dynamic> json) {
	final RestDeliveryPickupEst restDeliveryPickupEst = RestDeliveryPickupEst();
	final int? duration = jsonConvert.convert<int>(json['duration']);
	if (duration != null) {
		restDeliveryPickupEst.duration = duration;
	}
	final int? count = jsonConvert.convert<int>(json['count']);
	if (count != null) {
		restDeliveryPickupEst.count = count;
	}
	return restDeliveryPickupEst;
}

Map<String, dynamic> $RestDeliveryPickupEstToJson(RestDeliveryPickupEst entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['duration'] = entity.duration;
	data['count'] = entity.count;
	return data;
}

RestCredit $RestCreditFromJson(Map<String, dynamic> json) {
	final RestCredit restCredit = RestCredit();
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		restCredit.minimum = minimum;
	}
	final RestCreditFee? fee = jsonConvert.convert<RestCreditFee>(json['fee']);
	if (fee != null) {
		restCredit.fee = fee;
	}
	return restCredit;
}

Map<String, dynamic> $RestCreditToJson(RestCredit entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['minimum'] = entity.minimum;
	data['fee'] = entity.fee?.toJson();
	return data;
}

RestCreditFee $RestCreditFeeFromJson(Map<String, dynamic> json) {
	final RestCreditFee restCreditFee = RestCreditFee();
	final int? flat = jsonConvert.convert<int>(json['flat']);
	if (flat != null) {
		restCreditFee.flat = flat;
	}
	final int? factor = jsonConvert.convert<int>(json['factor']);
	if (factor != null) {
		restCreditFee.factor = factor;
	}
	return restCreditFee;
}

Map<String, dynamic> $RestCreditFeeToJson(RestCreditFee entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['flat'] = entity.flat;
	data['factor'] = entity.factor;
	return data;
}

RestStripe $RestStripeFromJson(Map<String, dynamic> json) {
	final RestStripe restStripe = RestStripe();
	final String? id = jsonConvert.convert<String>(json['id']);
	if (id != null) {
		restStripe.id = id;
	}
	final bool? onboarding = jsonConvert.convert<bool>(json['onboarding']);
	if (onboarding != null) {
		restStripe.onboarding = onboarding;
	}
	return restStripe;
}

Map<String, dynamic> $RestStripeToJson(RestStripe entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['id'] = entity.id;
	data['onboarding'] = entity.onboarding;
	return data;
}

RestCommission $RestCommissionFromJson(Map<String, dynamic> json) {
	final RestCommission restCommission = RestCommission();
	final RestCommissionSubtotal? subtotal = jsonConvert.convert<RestCommissionSubtotal>(json['subtotal']);
	if (subtotal != null) {
		restCommission.subtotal = subtotal;
	}
	final RestCommissionTotal? total = jsonConvert.convert<RestCommissionTotal>(json['total']);
	if (total != null) {
		restCommission.total = total;
	}
	return restCommission;
}

Map<String, dynamic> $RestCommissionToJson(RestCommission entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['subtotal'] = entity.subtotal?.toJson();
	data['total'] = entity.total?.toJson();
	return data;
}

RestCommissionSubtotal $RestCommissionSubtotalFromJson(Map<String, dynamic> json) {
	final RestCommissionSubtotal restCommissionSubtotal = RestCommissionSubtotal();
	final int? flat = jsonConvert.convert<int>(json['flat']);
	if (flat != null) {
		restCommissionSubtotal.flat = flat;
	}
	final double? factor = jsonConvert.convert<double>(json['factor']);
	if (factor != null) {
		restCommissionSubtotal.factor = factor;
	}
	return restCommissionSubtotal;
}

Map<String, dynamic> $RestCommissionSubtotalToJson(RestCommissionSubtotal entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['flat'] = entity.flat;
	data['factor'] = entity.factor;
	return data;
}

RestCommissionTotal $RestCommissionTotalFromJson(Map<String, dynamic> json) {
	final RestCommissionTotal restCommissionTotal = RestCommissionTotal();
	final int? flat = jsonConvert.convert<int>(json['flat']);
	if (flat != null) {
		restCommissionTotal.flat = flat;
	}
	final int? factor = jsonConvert.convert<int>(json['factor']);
	if (factor != null) {
		restCommissionTotal.factor = factor;
	}
	return restCommissionTotal;
}

Map<String, dynamic> $RestCommissionTotalToJson(RestCommissionTotal entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['flat'] = entity.flat;
	data['factor'] = entity.factor;
	return data;
}

RestPreferences $RestPreferencesFromJson(Map<String, dynamic> json) {
	final RestPreferences restPreferences = RestPreferences();
	final bool? driverCopy = jsonConvert.convert<bool>(json['driverCopy']);
	if (driverCopy != null) {
		restPreferences.driverCopy = driverCopy;
	}
	final bool? dailyReport = jsonConvert.convert<bool>(json['dailyReport']);
	if (dailyReport != null) {
		restPreferences.dailyReport = dailyReport;
	}
	final bool? autoConfirm = jsonConvert.convert<bool>(json['autoConfirm']);
	if (autoConfirm != null) {
		restPreferences.autoConfirm = autoConfirm;
	}
	return restPreferences;
}

Map<String, dynamic> $RestPreferencesToJson(RestPreferences entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['driverCopy'] = entity.driverCopy;
	data['dailyReport'] = entity.dailyReport;
	data['autoConfirm'] = entity.autoConfirm;
	return data;
}

RestLocation $RestLocationFromJson(Map<String, dynamic> json) {
	final RestLocation restLocation = RestLocation();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		restLocation.type = type;
	}
	final List<double?>? coordinates = jsonConvert.convertList<double>(json['coordinates']);
	if (coordinates != null) {
		restLocation.coordinates = coordinates;
	}
	return restLocation;
}

Map<String, dynamic> $RestLocationToJson(RestLocation entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['coordinates'] =  entity.coordinates;
	return data;
}

RestName $RestNameFromJson(Map<String, dynamic> json) {
	final RestName restName = RestName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restName.zhHk = zhHk;
	}
	return restName;
}

Map<String, dynamic> $RestNameToJson(RestName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestAddress $RestAddressFromJson(Map<String, dynamic> json) {
	final RestAddress restAddress = RestAddress();
	final String? number = jsonConvert.convert<String>(json['number']);
	if (number != null) {
		restAddress.number = number;
	}
	final String? street = jsonConvert.convert<String>(json['street']);
	if (street != null) {
		restAddress.street = street;
	}
	final String? unit = jsonConvert.convert<String>(json['unit']);
	if (unit != null) {
		restAddress.unit = unit;
	}
	final String? city = jsonConvert.convert<String>(json['city']);
	if (city != null) {
		restAddress.city = city;
	}
	final String? state = jsonConvert.convert<String>(json['state']);
	if (state != null) {
		restAddress.state = state;
	}
	final String? country = jsonConvert.convert<String>(json['country']);
	if (country != null) {
		restAddress.country = country;
	}
	final String? zipcode = jsonConvert.convert<String>(json['zipcode']);
	if (zipcode != null) {
		restAddress.zipcode = zipcode;
	}
	final String? formatted = jsonConvert.convert<String>(json['formatted']);
	if (formatted != null) {
		restAddress.formatted = formatted;
	}
	final RestAddressLocation? location = jsonConvert.convert<RestAddressLocation>(json['location']);
	if (location != null) {
		restAddress.location = location;
	}
	final String? note = jsonConvert.convert<String>(json['note']);
	if (note != null) {
		restAddress.note = note;
	}
	return restAddress;
}

Map<String, dynamic> $RestAddressToJson(RestAddress entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['number'] = entity.number;
	data['street'] = entity.street;
	data['unit'] = entity.unit;
	data['city'] = entity.city;
	data['state'] = entity.state;
	data['country'] = entity.country;
	data['zipcode'] = entity.zipcode;
	data['formatted'] = entity.formatted;
	data['location'] = entity.location?.toJson();
	data['note'] = entity.note;
	return data;
}

RestAddressLocation $RestAddressLocationFromJson(Map<String, dynamic> json) {
	final RestAddressLocation restAddressLocation = RestAddressLocation();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		restAddressLocation.type = type;
	}
	final List<double?>? coordinates = jsonConvert.convertList<double>(json['coordinates']);
	if (coordinates != null) {
		restAddressLocation.coordinates = coordinates;
	}
	return restAddressLocation;
}

Map<String, dynamic> $RestAddressLocationToJson(RestAddressLocation entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['coordinates'] =  entity.coordinates;
	return data;
}

RestRegion $RestRegionFromJson(Map<String, dynamic> json) {
	final RestRegion restRegion = RestRegion();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restRegion.sId = sId;
	}
	return restRegion;
}

Map<String, dynamic> $RestRegionToJson(RestRegion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

RestReward $RestRewardFromJson(Map<String, dynamic> json) {
	final RestReward restReward = RestReward();
	final bool? enabled = jsonConvert.convert<bool>(json['enabled']);
	if (enabled != null) {
		restReward.enabled = enabled;
	}
	return restReward;
}

Map<String, dynamic> $RestRewardToJson(RestReward entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['enabled'] = entity.enabled;
	return data;
}

RestDiscount $RestDiscountFromJson(Map<String, dynamic> json) {
	final RestDiscount restDiscount = RestDiscount();
	final List<RestDiscountMenu?>? menu = jsonConvert.convertList<RestDiscountMenu>(json['menu']);
	if (menu != null) {
		restDiscount.menu = menu;
	}
	final List<RestDiscountRicepo?>? ricepo = jsonConvert.convertList<RestDiscountRicepo>(json['ricepo']);
	if (ricepo != null) {
		restDiscount.ricepo = ricepo;
	}
	final List<RestDiscountVip?>? vip = jsonConvert.convertList<RestDiscountVip>(json['vip']);
	if (vip != null) {
		restDiscount.vip = vip;
	}
	return restDiscount;
}

Map<String, dynamic> $RestDiscountToJson(RestDiscount entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['menu'] =  entity.menu?.map((v) => v?.toJson()).toList();
	data['ricepo'] =  entity.ricepo?.map((v) => v?.toJson()).toList();
	data['vip'] =  entity.vip?.map((v) => v?.toJson()).toList();
	return data;
}

RestDiscountMenu $RestDiscountMenuFromJson(Map<String, dynamic> json) {
	final RestDiscountMenu restDiscountMenu = RestDiscountMenu();
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		restDiscountMenu.minimum = minimum;
	}
	final RestDiscountMenuDiscount? discount = jsonConvert.convert<RestDiscountMenuDiscount>(json['discount']);
	if (discount != null) {
		restDiscountMenu.discount = discount;
	}
	return restDiscountMenu;
}

Map<String, dynamic> $RestDiscountMenuToJson(RestDiscountMenu entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['minimum'] = entity.minimum;
	data['discount'] = entity.discount?.toJson();
	return data;
}

RestDiscountMenuDiscount $RestDiscountMenuDiscountFromJson(Map<String, dynamic> json) {
	final RestDiscountMenuDiscount restDiscountMenuDiscount = RestDiscountMenuDiscount();
	final int? flat = jsonConvert.convert<int>(json['flat']);
	if (flat != null) {
		restDiscountMenuDiscount.flat = flat;
	}
	final int? factor = jsonConvert.convert<int>(json['factor']);
	if (factor != null) {
		restDiscountMenuDiscount.factor = factor;
	}
	return restDiscountMenuDiscount;
}

Map<String, dynamic> $RestDiscountMenuDiscountToJson(RestDiscountMenuDiscount entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['flat'] = entity.flat;
	data['factor'] = entity.factor;
	return data;
}

RestDiscountRicepo $RestDiscountRicepoFromJson(Map<String, dynamic> json) {
	final RestDiscountRicepo restDiscountRicepo = RestDiscountRicepo();
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		restDiscountRicepo.minimum = minimum;
	}
	final RestDiscountRicepoDiscount? discount = jsonConvert.convert<RestDiscountRicepoDiscount>(json['discount']);
	if (discount != null) {
		restDiscountRicepo.discount = discount;
	}
	return restDiscountRicepo;
}

Map<String, dynamic> $RestDiscountRicepoToJson(RestDiscountRicepo entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['minimum'] = entity.minimum;
	data['discount'] = entity.discount?.toJson();
	return data;
}

RestDiscountRicepoDiscount $RestDiscountRicepoDiscountFromJson(Map<String, dynamic> json) {
	final RestDiscountRicepoDiscount restDiscountRicepoDiscount = RestDiscountRicepoDiscount();
	final int? flat = jsonConvert.convert<int>(json['flat']);
	if (flat != null) {
		restDiscountRicepoDiscount.flat = flat;
	}
	final int? factor = jsonConvert.convert<int>(json['factor']);
	if (factor != null) {
		restDiscountRicepoDiscount.factor = factor;
	}
	return restDiscountRicepoDiscount;
}

Map<String, dynamic> $RestDiscountRicepoDiscountToJson(RestDiscountRicepoDiscount entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['flat'] = entity.flat;
	data['factor'] = entity.factor;
	return data;
}

RestDiscountVip $RestDiscountVipFromJson(Map<String, dynamic> json) {
	final RestDiscountVip restDiscountVip = RestDiscountVip();
	final int? minimum = jsonConvert.convert<int>(json['minimum']);
	if (minimum != null) {
		restDiscountVip.minimum = minimum;
	}
	final RestDiscountVipDiscount? discount = jsonConvert.convert<RestDiscountVipDiscount>(json['discount']);
	if (discount != null) {
		restDiscountVip.discount = discount;
	}
	return restDiscountVip;
}

Map<String, dynamic> $RestDiscountVipToJson(RestDiscountVip entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['minimum'] = entity.minimum;
	data['discount'] = entity.discount?.toJson();
	return data;
}

RestDiscountVipDiscount $RestDiscountVipDiscountFromJson(Map<String, dynamic> json) {
	final RestDiscountVipDiscount restDiscountVipDiscount = RestDiscountVipDiscount();
	final int? flat = jsonConvert.convert<int>(json['flat']);
	if (flat != null) {
		restDiscountVipDiscount.flat = flat;
	}
	final double? factor = jsonConvert.convert<double>(json['factor']);
	if (factor != null) {
		restDiscountVipDiscount.factor = factor;
	}
	return restDiscountVipDiscount;
}

Map<String, dynamic> $RestDiscountVipDiscountToJson(RestDiscountVipDiscount entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['flat'] = entity.flat;
	data['factor'] = entity.factor;
	return data;
}

RestAnalytics $RestAnalyticsFromJson(Map<String, dynamic> json) {
	final RestAnalytics restAnalytics = RestAnalytics();
	final int? history = jsonConvert.convert<int>(json['history']);
	if (history != null) {
		restAnalytics.history = history;
	}
	return restAnalytics;
}

Map<String, dynamic> $RestAnalyticsToJson(RestAnalytics entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['history'] = entity.history;
	return data;
}

RestItems $RestItemsFromJson(Map<String, dynamic> json) {
	final RestItems restItems = RestItems();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restItems.sId = sId;
	}
	final bool? available = jsonConvert.convert<bool>(json['available']);
	if (available != null) {
		restItems.available = available;
	}
	final List<dynamic>? options = jsonConvert.convertListNotNull<dynamic>(json['options']);
	if (options != null) {
		restItems.options = options;
	}
	final RestItemsName? name = jsonConvert.convert<RestItemsName>(json['name']);
	if (name != null) {
		restItems.name = name;
	}
	final int? price = jsonConvert.convert<int>(json['price']);
	if (price != null) {
		restItems.price = price;
	}
	final RestItemsImage? image = jsonConvert.convert<RestItemsImage>(json['image']);
	if (image != null) {
		restItems.image = image;
	}
	final bool? cover = jsonConvert.convert<bool>(json['cover']);
	if (cover != null) {
		restItems.cover = cover;
	}
	final double? zscore = jsonConvert.convert<double>(json['zscore']);
	if (zscore != null) {
		restItems.zscore = zscore;
	}
	final int? last2WeekCount = jsonConvert.convert<int>(json['last2WeekCount']);
	if (last2WeekCount != null) {
		restItems.last2WeekCount = last2WeekCount;
	}
	final RestItemsSortMetrics? sortMetrics = jsonConvert.convert<RestItemsSortMetrics>(json['sortMetrics']);
	if (sortMetrics != null) {
		restItems.sortMetrics = sortMetrics;
	}
	final int? originalPrice = jsonConvert.convert<int>(json['originalPrice']);
	if (originalPrice != null) {
		restItems.originalPrice = originalPrice;
	}
	return restItems;
}

Map<String, dynamic> $RestItemsToJson(RestItems entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['available'] = entity.available;
	data['options'] =  entity.options;
	data['name'] = entity.name?.toJson();
	data['price'] = entity.price;
	data['image'] = entity.image?.toJson();
	data['cover'] = entity.cover;
	data['zscore'] = entity.zscore;
	data['last2WeekCount'] = entity.last2WeekCount;
	data['sortMetrics'] = entity.sortMetrics?.toJson();
	data['originalPrice'] = entity.originalPrice;
	return data;
}

RestItemsName $RestItemsNameFromJson(Map<String, dynamic> json) {
	final RestItemsName restItemsName = RestItemsName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restItemsName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restItemsName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restItemsName.zhHk = zhHk;
	}
	return restItemsName;
}

Map<String, dynamic> $RestItemsNameToJson(RestItemsName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestItemsImage $RestItemsImageFromJson(Map<String, dynamic> json) {
	final RestItemsImage restItemsImage = RestItemsImage();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restItemsImage.sId = sId;
	}
	final String? url = jsonConvert.convert<String>(json['url']);
	if (url != null) {
		restItemsImage.url = url;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		restItemsImage.createdAt = createdAt;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restItemsImage.updatedAt = updatedAt;
	}
	final bool? exclusive = jsonConvert.convert<bool>(json['exclusive']);
	if (exclusive != null) {
		restItemsImage.exclusive = exclusive;
	}
	return restItemsImage;
}

Map<String, dynamic> $RestItemsImageToJson(RestItemsImage entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['url'] = entity.url;
	data['createdAt'] = entity.createdAt;
	data['updatedAt'] = entity.updatedAt;
	data['exclusive'] = entity.exclusive;
	return data;
}

RestItemsSortMetrics $RestItemsSortMetricsFromJson(Map<String, dynamic> json) {
	final RestItemsSortMetrics restItemsSortMetrics = RestItemsSortMetrics();
	final bool? cover = jsonConvert.convert<bool>(json['cover']);
	if (cover != null) {
		restItemsSortMetrics.cover = cover;
	}
	final bool? available = jsonConvert.convert<bool>(json['available']);
	if (available != null) {
		restItemsSortMetrics.available = available;
	}
	final bool? highPrice = jsonConvert.convert<bool>(json['highPrice']);
	if (highPrice != null) {
		restItemsSortMetrics.highPrice = highPrice;
	}
	final double? zscore = jsonConvert.convert<double>(json['zscore']);
	if (zscore != null) {
		restItemsSortMetrics.zscore = zscore;
	}
	return restItemsSortMetrics;
}

Map<String, dynamic> $RestItemsSortMetricsToJson(RestItemsSortMetrics entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['cover'] = entity.cover;
	data['available'] = entity.available;
	data['highPrice'] = entity.highPrice;
	data['zscore'] = entity.zscore;
	return data;
}

RestBundleOpts $RestBundleOptsFromJson(Map<String, dynamic> json) {
	final RestBundleOpts restBundleOpts = RestBundleOpts();
	final List<String?>? include = jsonConvert.convertList<String>(json['include']);
	if (include != null) {
		restBundleOpts.include = include;
	}
	final List<RestBundleOptsPopular?>? popular = jsonConvert.convertList<RestBundleOptsPopular>(json['popular']);
	if (popular != null) {
		restBundleOpts.popular = popular;
	}
	return restBundleOpts;
}

Map<String, dynamic> $RestBundleOptsToJson(RestBundleOpts entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['include'] =  entity.include;
	data['popular'] =  entity.popular?.map((v) => v?.toJson()).toList();
	return data;
}

RestBundleOptsPopular $RestBundleOptsPopularFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopular restBundleOptsPopular = RestBundleOptsPopular();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundleOptsPopular.sId = sId;
	}
	final RestBundleOptsPopularName? name = jsonConvert.convert<RestBundleOptsPopularName>(json['name']);
	if (name != null) {
		restBundleOptsPopular.name = name;
	}
	final int? index = jsonConvert.convert<int>(json['index']);
	if (index != null) {
		restBundleOptsPopular.index = index;
	}
	final List<RestBundleOptsPopularBundleItems?>? bundleItems = jsonConvert.convertList<RestBundleOptsPopularBundleItems>(json['bundleItems']);
	if (bundleItems != null) {
		restBundleOptsPopular.bundleItems = bundleItems;
	}
	return restBundleOptsPopular;
}

Map<String, dynamic> $RestBundleOptsPopularToJson(RestBundleOptsPopular entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name?.toJson();
	data['index'] = entity.index;
	data['bundleItems'] =  entity.bundleItems?.map((v) => v?.toJson()).toList();
	return data;
}

RestBundleOptsPopularName $RestBundleOptsPopularNameFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularName restBundleOptsPopularName = RestBundleOptsPopularName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restBundleOptsPopularName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restBundleOptsPopularName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restBundleOptsPopularName.zhHk = zhHk;
	}
	return restBundleOptsPopularName;
}

Map<String, dynamic> $RestBundleOptsPopularNameToJson(RestBundleOptsPopularName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestBundleOptsPopularBundleItems $RestBundleOptsPopularBundleItemsFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularBundleItems restBundleOptsPopularBundleItems = RestBundleOptsPopularBundleItems();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundleOptsPopularBundleItems.sId = sId;
	}
	final dynamic? code = jsonConvert.convert<dynamic>(json['code']);
	if (code != null) {
		restBundleOptsPopularBundleItems.code = code;
	}
	final dynamic? description = jsonConvert.convert<dynamic>(json['description']);
	if (description != null) {
		restBundleOptsPopularBundleItems.description = description;
	}
	final dynamic? label = jsonConvert.convert<dynamic>(json['label']);
	if (label != null) {
		restBundleOptsPopularBundleItems.label = label;
	}
	final bool? available = jsonConvert.convert<bool>(json['available']);
	if (available != null) {
		restBundleOptsPopularBundleItems.available = available;
	}
	final List<dynamic>? options = jsonConvert.convertListNotNull<dynamic>(json['options']);
	if (options != null) {
		restBundleOptsPopularBundleItems.options = options;
	}
	final RestBundleOptsPopularBundleItemsName? name = jsonConvert.convert<RestBundleOptsPopularBundleItemsName>(json['name']);
	if (name != null) {
		restBundleOptsPopularBundleItems.name = name;
	}
	final int? price = jsonConvert.convert<int>(json['price']);
	if (price != null) {
		restBundleOptsPopularBundleItems.price = price;
	}
	final int? cost = jsonConvert.convert<int>(json['cost']);
	if (cost != null) {
		restBundleOptsPopularBundleItems.cost = cost;
	}
	final int? index = jsonConvert.convert<int>(json['index']);
	if (index != null) {
		restBundleOptsPopularBundleItems.index = index;
	}
	final List<dynamic>? date = jsonConvert.convertListNotNull<dynamic>(json['date']);
	if (date != null) {
		restBundleOptsPopularBundleItems.date = date;
	}
	final bool? reward = jsonConvert.convert<bool>(json['reward']);
	if (reward != null) {
		restBundleOptsPopularBundleItems.reward = reward;
	}
	final int? point = jsonConvert.convert<int>(json['point']);
	if (point != null) {
		restBundleOptsPopularBundleItems.point = point;
	}
	final dynamic? hours = jsonConvert.convert<dynamic>(json['hours']);
	if (hours != null) {
		restBundleOptsPopularBundleItems.hours = hours;
	}
	final RestBundleOptsPopularBundleItemsImage? image = jsonConvert.convert<RestBundleOptsPopularBundleItemsImage>(json['image']);
	if (image != null) {
		restBundleOptsPopularBundleItems.image = image;
	}
	final dynamic? stock = jsonConvert.convert<dynamic>(json['stock']);
	if (stock != null) {
		restBundleOptsPopularBundleItems.stock = stock;
	}
	final dynamic? limit = jsonConvert.convert<dynamic>(json['limit']);
	if (limit != null) {
		restBundleOptsPopularBundleItems.limit = limit;
	}
	final RestBundleOptsPopularBundleItemsRestaurant? restaurant = jsonConvert.convert<RestBundleOptsPopularBundleItemsRestaurant>(json['restaurant']);
	if (restaurant != null) {
		restBundleOptsPopularBundleItems.restaurant = restaurant;
	}
	final RestBundleOptsPopularBundleItemsRegion? region = jsonConvert.convert<RestBundleOptsPopularBundleItemsRegion>(json['region']);
	if (region != null) {
		restBundleOptsPopularBundleItems.region = region;
	}
	final RestBundleOptsPopularBundleItemsCategory? category = jsonConvert.convert<RestBundleOptsPopularBundleItemsCategory>(json['category']);
	if (category != null) {
		restBundleOptsPopularBundleItems.category = category;
	}
	final RestBundleOptsPopularBundleItemsTags? tags = jsonConvert.convert<RestBundleOptsPopularBundleItemsTags>(json['tags']);
	if (tags != null) {
		restBundleOptsPopularBundleItems.tags = tags;
	}
	final RestBundleOptsPopularBundleItemsScore? score = jsonConvert.convert<RestBundleOptsPopularBundleItemsScore>(json['score']);
	if (score != null) {
		restBundleOptsPopularBundleItems.score = score;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restBundleOptsPopularBundleItems.updatedAt = updatedAt;
	}
	final int? zscore = jsonConvert.convert<int>(json['zscore']);
	if (zscore != null) {
		restBundleOptsPopularBundleItems.zscore = zscore;
	}
	final int? originalPrice = jsonConvert.convert<int>(json['originalPrice']);
	if (originalPrice != null) {
		restBundleOptsPopularBundleItems.originalPrice = originalPrice;
	}
	return restBundleOptsPopularBundleItems;
}

Map<String, dynamic> $RestBundleOptsPopularBundleItemsToJson(RestBundleOptsPopularBundleItems entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['code'] = entity.code;
	data['description'] = entity.description;
	data['label'] = entity.label;
	data['available'] = entity.available;
	data['options'] =  entity.options;
	data['name'] = entity.name?.toJson();
	data['price'] = entity.price;
	data['cost'] = entity.cost;
	data['index'] = entity.index;
	data['date'] =  entity.date;
	data['reward'] = entity.reward;
	data['point'] = entity.point;
	data['hours'] = entity.hours;
	data['image'] = entity.image?.toJson();
	data['stock'] = entity.stock;
	data['limit'] = entity.limit;
	data['restaurant'] = entity.restaurant?.toJson();
	data['region'] = entity.region?.toJson();
	data['category'] = entity.category?.toJson();
	data['tags'] = entity.tags?.toJson();
	data['score'] = entity.score?.toJson();
	data['updatedAt'] = entity.updatedAt;
	data['zscore'] = entity.zscore;
	data['originalPrice'] = entity.originalPrice;
	return data;
}

RestBundleOptsPopularBundleItemsName $RestBundleOptsPopularBundleItemsNameFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularBundleItemsName restBundleOptsPopularBundleItemsName = RestBundleOptsPopularBundleItemsName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restBundleOptsPopularBundleItemsName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restBundleOptsPopularBundleItemsName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restBundleOptsPopularBundleItemsName.zhHk = zhHk;
	}
	return restBundleOptsPopularBundleItemsName;
}

Map<String, dynamic> $RestBundleOptsPopularBundleItemsNameToJson(RestBundleOptsPopularBundleItemsName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestBundleOptsPopularBundleItemsImage $RestBundleOptsPopularBundleItemsImageFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularBundleItemsImage restBundleOptsPopularBundleItemsImage = RestBundleOptsPopularBundleItemsImage();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundleOptsPopularBundleItemsImage.sId = sId;
	}
	final String? url = jsonConvert.convert<String>(json['url']);
	if (url != null) {
		restBundleOptsPopularBundleItemsImage.url = url;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		restBundleOptsPopularBundleItemsImage.createdAt = createdAt;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restBundleOptsPopularBundleItemsImage.updatedAt = updatedAt;
	}
	return restBundleOptsPopularBundleItemsImage;
}

Map<String, dynamic> $RestBundleOptsPopularBundleItemsImageToJson(RestBundleOptsPopularBundleItemsImage entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['url'] = entity.url;
	data['createdAt'] = entity.createdAt;
	data['updatedAt'] = entity.updatedAt;
	return data;
}

RestBundleOptsPopularBundleItemsRestaurant $RestBundleOptsPopularBundleItemsRestaurantFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularBundleItemsRestaurant restBundleOptsPopularBundleItemsRestaurant = RestBundleOptsPopularBundleItemsRestaurant();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundleOptsPopularBundleItemsRestaurant.sId = sId;
	}
	final RestBundleOptsPopularBundleItemsRestaurantName? name = jsonConvert.convert<RestBundleOptsPopularBundleItemsRestaurantName>(json['name']);
	if (name != null) {
		restBundleOptsPopularBundleItemsRestaurant.name = name;
	}
	return restBundleOptsPopularBundleItemsRestaurant;
}

Map<String, dynamic> $RestBundleOptsPopularBundleItemsRestaurantToJson(RestBundleOptsPopularBundleItemsRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name?.toJson();
	return data;
}

RestBundleOptsPopularBundleItemsRestaurantName $RestBundleOptsPopularBundleItemsRestaurantNameFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularBundleItemsRestaurantName restBundleOptsPopularBundleItemsRestaurantName = RestBundleOptsPopularBundleItemsRestaurantName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restBundleOptsPopularBundleItemsRestaurantName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restBundleOptsPopularBundleItemsRestaurantName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restBundleOptsPopularBundleItemsRestaurantName.zhHk = zhHk;
	}
	return restBundleOptsPopularBundleItemsRestaurantName;
}

Map<String, dynamic> $RestBundleOptsPopularBundleItemsRestaurantNameToJson(RestBundleOptsPopularBundleItemsRestaurantName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestBundleOptsPopularBundleItemsRegion $RestBundleOptsPopularBundleItemsRegionFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularBundleItemsRegion restBundleOptsPopularBundleItemsRegion = RestBundleOptsPopularBundleItemsRegion();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundleOptsPopularBundleItemsRegion.sId = sId;
	}
	return restBundleOptsPopularBundleItemsRegion;
}

Map<String, dynamic> $RestBundleOptsPopularBundleItemsRegionToJson(RestBundleOptsPopularBundleItemsRegion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

RestBundleOptsPopularBundleItemsCategory $RestBundleOptsPopularBundleItemsCategoryFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularBundleItemsCategory restBundleOptsPopularBundleItemsCategory = RestBundleOptsPopularBundleItemsCategory();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundleOptsPopularBundleItemsCategory.sId = sId;
	}
	return restBundleOptsPopularBundleItemsCategory;
}

Map<String, dynamic> $RestBundleOptsPopularBundleItemsCategoryToJson(RestBundleOptsPopularBundleItemsCategory entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

RestBundleOptsPopularBundleItemsTags $RestBundleOptsPopularBundleItemsTagsFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularBundleItemsTags restBundleOptsPopularBundleItemsTags = RestBundleOptsPopularBundleItemsTags();
	final int? vegetable = jsonConvert.convert<int>(json['vegetable']);
	if (vegetable != null) {
		restBundleOptsPopularBundleItemsTags.vegetable = vegetable;
	}
	return restBundleOptsPopularBundleItemsTags;
}

Map<String, dynamic> $RestBundleOptsPopularBundleItemsTagsToJson(RestBundleOptsPopularBundleItemsTags entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['vegetable'] = entity.vegetable;
	return data;
}

RestBundleOptsPopularBundleItemsScore $RestBundleOptsPopularBundleItemsScoreFromJson(Map<String, dynamic> json) {
	final RestBundleOptsPopularBundleItemsScore restBundleOptsPopularBundleItemsScore = RestBundleOptsPopularBundleItemsScore();
	final double? region = jsonConvert.convert<double>(json['region']);
	if (region != null) {
		restBundleOptsPopularBundleItemsScore.region = region;
	}
	final int? restaurant = jsonConvert.convert<int>(json['restaurant']);
	if (restaurant != null) {
		restBundleOptsPopularBundleItemsScore.restaurant = restaurant;
	}
	return restBundleOptsPopularBundleItemsScore;
}

Map<String, dynamic> $RestBundleOptsPopularBundleItemsScoreToJson(RestBundleOptsPopularBundleItemsScore entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['region'] = entity.region;
	data['restaurant'] = entity.restaurant;
	return data;
}

RestServiceFee $RestServiceFeeFromJson(Map<String, dynamic> json) {
	final RestServiceFee restServiceFee = RestServiceFee();
	final double? factor = jsonConvert.convert<double>(json['factor']);
	if (factor != null) {
		restServiceFee.factor = factor;
	}
	final int? flat = jsonConvert.convert<int>(json['flat']);
	if (flat != null) {
		restServiceFee.flat = flat;
	}
	return restServiceFee;
}

Map<String, dynamic> $RestServiceFeeToJson(RestServiceFee entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['factor'] = entity.factor;
	data['flat'] = entity.flat;
	return data;
}

RestMotd $RestMotdFromJson(Map<String, dynamic> json) {
	final RestMotd restMotd = RestMotd();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restMotd.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restMotd.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restMotd.zhHk = zhHk;
	}
	return restMotd;
}

Map<String, dynamic> $RestMotdToJson(RestMotd entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestPromotion $RestPromotionFromJson(Map<String, dynamic> json) {
	final RestPromotion restPromotion = RestPromotion();
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restPromotion.enUs = enUs;
	}
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restPromotion.zhCn = zhCn;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restPromotion.zhHk = zhHk;
	}
	return restPromotion;
}

Map<String, dynamic> $RestPromotionToJson(RestPromotion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['en-US'] = entity.enUs;
	data['zh-CN'] = entity.zhCn;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestExpireProp $RestExpirePropFromJson(Map<String, dynamic> json) {
	final RestExpireProp restExpireProp = RestExpireProp();
	return restExpireProp;
}

Map<String, dynamic> $RestExpirePropToJson(RestExpireProp entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	return data;
}

RestExtraFees $RestExtraFeesFromJson(Map<String, dynamic> json) {
	final RestExtraFees restExtraFees = RestExtraFees();
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		restExtraFees.name = name;
	}
	final int? price = jsonConvert.convert<int>(json['price']);
	if (price != null) {
		restExtraFees.price = price;
	}
	final int? cost = jsonConvert.convert<int>(json['cost']);
	if (cost != null) {
		restExtraFees.cost = cost;
	}
	final bool? extraFee = jsonConvert.convert<bool>(json['extraFee']);
	if (extraFee != null) {
		restExtraFees.extraFee = extraFee;
	}
	return restExtraFees;
}

Map<String, dynamic> $RestExtraFeesToJson(RestExtraFees entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['name'] = entity.name?.toJson();
	data['price'] = entity.price;
	data['cost'] = entity.cost;
	data['extraFee'] = entity.extraFee;
	return data;
}

RestExtraFeesName $RestExtraFeesNameFromJson(Map<String, dynamic> json) {
	final RestExtraFeesName restExtraFeesName = RestExtraFeesName();
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restExtraFeesName.enUs = enUs;
	}
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restExtraFeesName.zhCn = zhCn;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restExtraFeesName.zhHk = zhHk;
	}
	return restExtraFeesName;
}

Map<String, dynamic> $RestExtraFeesNameToJson(RestExtraFeesName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['en-US'] = entity.enUs;
	data['zh-CN'] = entity.zhCn;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestPrepare $RestPrepareFromJson(Map<String, dynamic> json) {
	final RestPrepare restPrepare = RestPrepare();
	final int? avg = jsonConvert.convert<int>(json['avg']);
	if (avg != null) {
		restPrepare.avg = avg;
	}
	final int? std = jsonConvert.convert<int>(json['std']);
	if (std != null) {
		restPrepare.std = std;
	}
	final RestPrepareFood? food = jsonConvert.convert<RestPrepareFood>(json['food']);
	if (food != null) {
		restPrepare.food = food;
	}
	final double? reliability = jsonConvert.convert<double>(json['reliability']);
	if (reliability != null) {
		restPrepare.reliability = reliability;
	}
	return restPrepare;
}

Map<String, dynamic> $RestPrepareToJson(RestPrepare entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['avg'] = entity.avg;
	data['std'] = entity.std;
	data['food'] = entity.food?.toJson();
	data['reliability'] = entity.reliability;
	return data;
}

RestPrepareFood $RestPrepareFoodFromJson(Map<String, dynamic> json) {
	final RestPrepareFood restPrepareFood = RestPrepareFood();
	final int? foodDhrwufv77 = jsonConvert.convert<int>(json['food_DHrWUfv77']);
	if (foodDhrwufv77 != null) {
		restPrepareFood.foodDhrwufv77 = foodDhrwufv77;
	}
	final int? foodKzcarOark = jsonConvert.convert<int>(json['food_kZcar_oark']);
	if (foodKzcarOark != null) {
		restPrepareFood.foodKzcarOark = foodKzcarOark;
	}
	final int? foodTkonswtzu = jsonConvert.convert<int>(json['food_tKONSwtzU']);
	if (foodTkonswtzu != null) {
		restPrepareFood.foodTkonswtzu = foodTkonswtzu;
	}
	final int? foodV5ctwgy0w = jsonConvert.convert<int>(json['food_v5CtwGY0w']);
	if (foodV5ctwgy0w != null) {
		restPrepareFood.foodV5ctwgy0w = foodV5ctwgy0w;
	}
	return restPrepareFood;
}

Map<String, dynamic> $RestPrepareFoodToJson(RestPrepareFood entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['food_DHrWUfv77'] = entity.foodDhrwufv77;
	data['food_kZcar_oark'] = entity.foodKzcarOark;
	data['food_tKONSwtzU'] = entity.foodTkonswtzu;
	data['food_v5CtwGY0w'] = entity.foodV5ctwgy0w;
	return data;
}

RestBundlePopular $RestBundlePopularFromJson(Map<String, dynamic> json) {
	final RestBundlePopular restBundlePopular = RestBundlePopular();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundlePopular.sId = sId;
	}
	final RestBundlePopularName? name = jsonConvert.convert<RestBundlePopularName>(json['name']);
	if (name != null) {
		restBundlePopular.name = name;
	}
	final int? index = jsonConvert.convert<int>(json['index']);
	if (index != null) {
		restBundlePopular.index = index;
	}
	final List<RestBundlePopularBundleItems?>? bundleItems = jsonConvert.convertList<RestBundlePopularBundleItems>(json['bundleItems']);
	if (bundleItems != null) {
		restBundlePopular.bundleItems = bundleItems;
	}
	return restBundlePopular;
}

Map<String, dynamic> $RestBundlePopularToJson(RestBundlePopular entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['name'] = entity.name?.toJson();
	data['index'] = entity.index;
	data['bundleItems'] =  entity.bundleItems?.map((v) => v?.toJson()).toList();
	return data;
}

RestBundlePopularName $RestBundlePopularNameFromJson(Map<String, dynamic> json) {
	final RestBundlePopularName restBundlePopularName = RestBundlePopularName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restBundlePopularName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restBundlePopularName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restBundlePopularName.zhHk = zhHk;
	}
	return restBundlePopularName;
}

Map<String, dynamic> $RestBundlePopularNameToJson(RestBundlePopularName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestBundlePopularBundleItems $RestBundlePopularBundleItemsFromJson(Map<String, dynamic> json) {
	final RestBundlePopularBundleItems restBundlePopularBundleItems = RestBundlePopularBundleItems();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundlePopularBundleItems.sId = sId;
	}
	final dynamic? code = jsonConvert.convert<dynamic>(json['code']);
	if (code != null) {
		restBundlePopularBundleItems.code = code;
	}
	final dynamic? description = jsonConvert.convert<dynamic>(json['description']);
	if (description != null) {
		restBundlePopularBundleItems.description = description;
	}
	final dynamic? label = jsonConvert.convert<dynamic>(json['label']);
	if (label != null) {
		restBundlePopularBundleItems.label = label;
	}
	final bool? available = jsonConvert.convert<bool>(json['available']);
	if (available != null) {
		restBundlePopularBundleItems.available = available;
	}
	final List<dynamic>? options = jsonConvert.convertListNotNull<dynamic>(json['options']);
	if (options != null) {
		restBundlePopularBundleItems.options = options;
	}
	final RestBundlePopularBundleItemsName? name = jsonConvert.convert<RestBundlePopularBundleItemsName>(json['name']);
	if (name != null) {
		restBundlePopularBundleItems.name = name;
	}
	final int? price = jsonConvert.convert<int>(json['price']);
	if (price != null) {
		restBundlePopularBundleItems.price = price;
	}
	final int? cost = jsonConvert.convert<int>(json['cost']);
	if (cost != null) {
		restBundlePopularBundleItems.cost = cost;
	}
	final int? originalPrice = jsonConvert.convert<int>(json['originalPrice']);
	if (originalPrice != null) {
		restBundlePopularBundleItems.originalPrice = originalPrice;
	}
	final int? index = jsonConvert.convert<int>(json['index']);
	if (index != null) {
		restBundlePopularBundleItems.index = index;
	}
	final List<dynamic>? date = jsonConvert.convertListNotNull<dynamic>(json['date']);
	if (date != null) {
		restBundlePopularBundleItems.date = date;
	}
	final int? point = jsonConvert.convert<int>(json['point']);
	if (point != null) {
		restBundlePopularBundleItems.point = point;
	}
	final dynamic? hours = jsonConvert.convert<dynamic>(json['hours']);
	if (hours != null) {
		restBundlePopularBundleItems.hours = hours;
	}
	final RestBundlePopularBundleItemsImage? image = jsonConvert.convert<RestBundlePopularBundleItemsImage>(json['image']);
	if (image != null) {
		restBundlePopularBundleItems.image = image;
	}
	final dynamic? stock = jsonConvert.convert<dynamic>(json['stock']);
	if (stock != null) {
		restBundlePopularBundleItems.stock = stock;
	}
	final dynamic? limit = jsonConvert.convert<dynamic>(json['limit']);
	if (limit != null) {
		restBundlePopularBundleItems.limit = limit;
	}
	final RestBundlePopularBundleItemsRestaurant? restaurant = jsonConvert.convert<RestBundlePopularBundleItemsRestaurant>(json['restaurant']);
	if (restaurant != null) {
		restBundlePopularBundleItems.restaurant = restaurant;
	}
	final RestBundlePopularBundleItemsRegion? region = jsonConvert.convert<RestBundlePopularBundleItemsRegion>(json['region']);
	if (region != null) {
		restBundlePopularBundleItems.region = region;
	}
	final RestBundlePopularBundleItemsCategory? category = jsonConvert.convert<RestBundlePopularBundleItemsCategory>(json['category']);
	if (category != null) {
		restBundlePopularBundleItems.category = category;
	}
	final RestBundlePopularBundleItemsTags? tags = jsonConvert.convert<RestBundlePopularBundleItemsTags>(json['tags']);
	if (tags != null) {
		restBundlePopularBundleItems.tags = tags;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restBundlePopularBundleItems.updatedAt = updatedAt;
	}
	final RestBundlePopularBundleItemsScore? score = jsonConvert.convert<RestBundlePopularBundleItemsScore>(json['score']);
	if (score != null) {
		restBundlePopularBundleItems.score = score;
	}
	final int? zscore = jsonConvert.convert<int>(json['zscore']);
	if (zscore != null) {
		restBundlePopularBundleItems.zscore = zscore;
	}
	final int? last2WeekCount = jsonConvert.convert<int>(json['last2WeekCount']);
	if (last2WeekCount != null) {
		restBundlePopularBundleItems.last2WeekCount = last2WeekCount;
	}
	final String? fullChar = jsonConvert.convert<String>(json['fullChar']);
	if (fullChar != null) {
		restBundlePopularBundleItems.fullChar = fullChar;
	}
	final List<String?>? pinyin = jsonConvert.convertList<String>(json['pinyin']);
	if (pinyin != null) {
		restBundlePopularBundleItems.pinyin = pinyin;
	}
	return restBundlePopularBundleItems;
}

Map<String, dynamic> $RestBundlePopularBundleItemsToJson(RestBundlePopularBundleItems entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['code'] = entity.code;
	data['description'] = entity.description;
	data['label'] = entity.label;
	data['available'] = entity.available;
	data['options'] =  entity.options;
	data['name'] = entity.name?.toJson();
	data['price'] = entity.price;
	data['cost'] = entity.cost;
	data['originalPrice'] = entity.originalPrice;
	data['index'] = entity.index;
	data['date'] =  entity.date;
	data['point'] = entity.point;
	data['hours'] = entity.hours;
	data['image'] = entity.image?.toJson();
	data['stock'] = entity.stock;
	data['limit'] = entity.limit;
	data['restaurant'] = entity.restaurant?.toJson();
	data['region'] = entity.region?.toJson();
	data['category'] = entity.category?.toJson();
	data['tags'] = entity.tags?.toJson();
	data['updatedAt'] = entity.updatedAt;
	data['score'] = entity.score?.toJson();
	data['zscore'] = entity.zscore;
	data['last2WeekCount'] = entity.last2WeekCount;
	data['fullChar'] = entity.fullChar;
	data['pinyin'] =  entity.pinyin;
	return data;
}

RestBundlePopularBundleItemsName $RestBundlePopularBundleItemsNameFromJson(Map<String, dynamic> json) {
	final RestBundlePopularBundleItemsName restBundlePopularBundleItemsName = RestBundlePopularBundleItemsName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restBundlePopularBundleItemsName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restBundlePopularBundleItemsName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restBundlePopularBundleItemsName.zhHk = zhHk;
	}
	return restBundlePopularBundleItemsName;
}

Map<String, dynamic> $RestBundlePopularBundleItemsNameToJson(RestBundlePopularBundleItemsName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestBundlePopularBundleItemsImage $RestBundlePopularBundleItemsImageFromJson(Map<String, dynamic> json) {
	final RestBundlePopularBundleItemsImage restBundlePopularBundleItemsImage = RestBundlePopularBundleItemsImage();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundlePopularBundleItemsImage.sId = sId;
	}
	final String? url = jsonConvert.convert<String>(json['url']);
	if (url != null) {
		restBundlePopularBundleItemsImage.url = url;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		restBundlePopularBundleItemsImage.createdAt = createdAt;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restBundlePopularBundleItemsImage.updatedAt = updatedAt;
	}
	return restBundlePopularBundleItemsImage;
}

Map<String, dynamic> $RestBundlePopularBundleItemsImageToJson(RestBundlePopularBundleItemsImage entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['url'] = entity.url;
	data['createdAt'] = entity.createdAt;
	data['updatedAt'] = entity.updatedAt;
	return data;
}

RestBundlePopularBundleItemsRestaurant $RestBundlePopularBundleItemsRestaurantFromJson(Map<String, dynamic> json) {
	final RestBundlePopularBundleItemsRestaurant restBundlePopularBundleItemsRestaurant = RestBundlePopularBundleItemsRestaurant();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundlePopularBundleItemsRestaurant.sId = sId;
	}
	return restBundlePopularBundleItemsRestaurant;
}

Map<String, dynamic> $RestBundlePopularBundleItemsRestaurantToJson(RestBundlePopularBundleItemsRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

RestBundlePopularBundleItemsRegion $RestBundlePopularBundleItemsRegionFromJson(Map<String, dynamic> json) {
	final RestBundlePopularBundleItemsRegion restBundlePopularBundleItemsRegion = RestBundlePopularBundleItemsRegion();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundlePopularBundleItemsRegion.sId = sId;
	}
	return restBundlePopularBundleItemsRegion;
}

Map<String, dynamic> $RestBundlePopularBundleItemsRegionToJson(RestBundlePopularBundleItemsRegion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

RestBundlePopularBundleItemsCategory $RestBundlePopularBundleItemsCategoryFromJson(Map<String, dynamic> json) {
	final RestBundlePopularBundleItemsCategory restBundlePopularBundleItemsCategory = RestBundlePopularBundleItemsCategory();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restBundlePopularBundleItemsCategory.sId = sId;
	}
	return restBundlePopularBundleItemsCategory;
}

Map<String, dynamic> $RestBundlePopularBundleItemsCategoryToJson(RestBundlePopularBundleItemsCategory entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

RestBundlePopularBundleItemsTags $RestBundlePopularBundleItemsTagsFromJson(Map<String, dynamic> json) {
	final RestBundlePopularBundleItemsTags restBundlePopularBundleItemsTags = RestBundlePopularBundleItemsTags();
	final int? bbq = jsonConvert.convert<int>(json['bbq']);
	if (bbq != null) {
		restBundlePopularBundleItemsTags.bbq = bbq;
	}
	return restBundlePopularBundleItemsTags;
}

Map<String, dynamic> $RestBundlePopularBundleItemsTagsToJson(RestBundlePopularBundleItemsTags entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['bbq'] = entity.bbq;
	return data;
}

RestBundlePopularBundleItemsScore $RestBundlePopularBundleItemsScoreFromJson(Map<String, dynamic> json) {
	final RestBundlePopularBundleItemsScore restBundlePopularBundleItemsScore = RestBundlePopularBundleItemsScore();
	final double? region = jsonConvert.convert<double>(json['region']);
	if (region != null) {
		restBundlePopularBundleItemsScore.region = region;
	}
	final int? restaurant = jsonConvert.convert<int>(json['restaurant']);
	if (restaurant != null) {
		restBundlePopularBundleItemsScore.restaurant = restaurant;
	}
	return restBundlePopularBundleItemsScore;
}

Map<String, dynamic> $RestBundlePopularBundleItemsScoreToJson(RestBundlePopularBundleItemsScore entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['region'] = entity.region;
	data['restaurant'] = entity.restaurant;
	return data;
}

RestPromoItems $RestPromoItemsFromJson(Map<String, dynamic> json) {
	final RestPromoItems restPromoItems = RestPromoItems();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restPromoItems.sId = sId;
	}
	final bool? available = jsonConvert.convert<bool>(json['available']);
	if (available != null) {
		restPromoItems.available = available;
	}
	final List<dynamic>? options = jsonConvert.convertListNotNull<dynamic>(json['options']);
	if (options != null) {
		restPromoItems.options = options;
	}
	final RestPromoItemsName? name = jsonConvert.convert<RestPromoItemsName>(json['name']);
	if (name != null) {
		restPromoItems.name = name;
	}
	final int? price = jsonConvert.convert<int>(json['price']);
	if (price != null) {
		restPromoItems.price = price;
	}
	final int? originalPrice = jsonConvert.convert<int>(json['originalPrice']);
	if (originalPrice != null) {
		restPromoItems.originalPrice = originalPrice;
	}
	final RestPromoItemsImage? image = jsonConvert.convert<RestPromoItemsImage>(json['image']);
	if (image != null) {
		restPromoItems.image = image;
	}
	final double? zscore = jsonConvert.convert<double>(json['zscore']);
	if (zscore != null) {
		restPromoItems.zscore = zscore;
	}
	final int? last2WeekCount = jsonConvert.convert<int>(json['last2WeekCount']);
	if (last2WeekCount != null) {
		restPromoItems.last2WeekCount = last2WeekCount;
	}
	return restPromoItems;
}

Map<String, dynamic> $RestPromoItemsToJson(RestPromoItems entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['available'] = entity.available;
	data['options'] =  entity.options;
	data['name'] = entity.name?.toJson();
	data['price'] = entity.price;
	data['originalPrice'] = entity.originalPrice;
	data['image'] = entity.image?.toJson();
	data['zscore'] = entity.zscore;
	data['last2WeekCount'] = entity.last2WeekCount;
	return data;
}

RestPromoItemsName $RestPromoItemsNameFromJson(Map<String, dynamic> json) {
	final RestPromoItemsName restPromoItemsName = RestPromoItemsName();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restPromoItemsName.zhCn = zhCn;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restPromoItemsName.enUs = enUs;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restPromoItemsName.zhHk = zhHk;
	}
	return restPromoItemsName;
}

Map<String, dynamic> $RestPromoItemsNameToJson(RestPromoItemsName entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['en-US'] = entity.enUs;
	data['zh-HK'] = entity.zhHk;
	return data;
}

RestPromoItemsImage $RestPromoItemsImageFromJson(Map<String, dynamic> json) {
	final RestPromoItemsImage restPromoItemsImage = RestPromoItemsImage();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		restPromoItemsImage.sId = sId;
	}
	final String? url = jsonConvert.convert<String>(json['url']);
	if (url != null) {
		restPromoItemsImage.url = url;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		restPromoItemsImage.createdAt = createdAt;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		restPromoItemsImage.updatedAt = updatedAt;
	}
	return restPromoItemsImage;
}

Map<String, dynamic> $RestPromoItemsImageToJson(RestPromoItemsImage entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	data['url'] = entity.url;
	data['createdAt'] = entity.createdAt;
	data['updatedAt'] = entity.updatedAt;
	return data;
}

RestClosed $RestClosedFromJson(Map<String, dynamic> json) {
	final RestClosed restClosed = RestClosed();
	final String? zhCn = jsonConvert.convert<String>(json['zh-CN']);
	if (zhCn != null) {
		restClosed.zhCn = zhCn;
	}
	final String? zhHk = jsonConvert.convert<String>(json['zh-HK']);
	if (zhHk != null) {
		restClosed.zhHk = zhHk;
	}
	final String? enUs = jsonConvert.convert<String>(json['en-US']);
	if (enUs != null) {
		restClosed.enUs = enUs;
	}
	final String? user = jsonConvert.convert<String>(json['user']);
	if (user != null) {
		restClosed.user = user;
	}
	final String? reason = jsonConvert.convert<String>(json['reason']);
	if (reason != null) {
		restClosed.reason = reason;
	}
	return restClosed;
}

Map<String, dynamic> $RestClosedToJson(RestClosed entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['zh-CN'] = entity.zhCn;
	data['zh-HK'] = entity.zhHk;
	data['en-US'] = entity.enUs;
	data['user'] = entity.user;
	data['reason'] = entity.reason;
	return data;
}