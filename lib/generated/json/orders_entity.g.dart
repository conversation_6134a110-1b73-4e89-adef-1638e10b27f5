import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/data/orders_entity.dart';
import 'package:connect/data/multi_name_entity.dart';

import 'package:connect/ui/home/<USER>/order_status_manager.dart';

import 'package:connect/utils/date_util.dart';

import 'package:timezone/timezone.dart' as tz;


OrdersEntity $OrdersEntityFromJson(Map<String, dynamic> json) {
	final OrdersEntity ordersEntity = OrdersEntity();
	final String? orderId = jsonConvert.convert<String>(json['_id']);
	if (orderId != null) {
		ordersEntity.orderId = orderId;
	}
	final String? comments = jsonConvert.convert<String>(json['comments']);
	if (comments != null) {
		ordersEntity.comments = comments;
	}
	final num? count = jsonConvert.convert<num>(json['count']);
	if (count != null) {
		ordersEntity.count = count;
	}
	final String? passcode = jsonConvert.convert<String>(json['passcode']);
	if (passcode != null) {
		ordersEntity.passcode = passcode;
	}
	final String? passcodeExt = jsonConvert.convert<String>(json['passcodeExt']);
	if (passcodeExt != null) {
		ordersEntity.passcodeExt = passcodeExt;
	}
	final List<OrdersItem?>? items = jsonConvert.convertList<OrdersItem>(json['items']);
	if (items != null) {
		ordersEntity.items = items;
	}
	final OrdersFees? fees = jsonConvert.convert<OrdersFees>(json['fees']);
	if (fees != null) {
		ordersEntity.fees = fees;
	}
	final OrdersRegion? region = jsonConvert.convert<OrdersRegion>(json['region']);
	if (region != null) {
		ordersEntity.region = region;
	}
	final OrdersCustomer? customer = jsonConvert.convert<OrdersCustomer>(json['customer']);
	if (customer != null) {
		ordersEntity.customer = customer;
	}
	final OrdersRestaurant? restaurant = jsonConvert.convert<OrdersRestaurant>(json['restaurant']);
	if (restaurant != null) {
		ordersEntity.restaurant = restaurant;
	}
	final OrdersDelivery? delivery = jsonConvert.convert<OrdersDelivery>(json['delivery']);
	if (delivery != null) {
		ordersEntity.delivery = delivery;
	}
	final OrdersDistribution? distribution = jsonConvert.convert<OrdersDistribution>(json['distribution']);
	if (distribution != null) {
		ordersEntity.distribution = distribution;
	}
	final List<OrdersAdj?>? adj = jsonConvert.convertList<OrdersAdj>(json['adj']);
	if (adj != null) {
		ordersEntity.adj = adj;
	}
	final OrdersAdjustments? adjustments = jsonConvert.convert<OrdersAdjustments>(json['adjustments']);
	if (adjustments != null) {
		ordersEntity.adjustments = adjustments;
	}
	final String? status = jsonConvert.convert<String>(json['status']);
	if (status != null) {
		ordersEntity.status = status;
	}
	final String? createdAt = jsonConvert.convert<String>(json['createdAt']);
	if (createdAt != null) {
		ordersEntity.createdAt = createdAt;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		ordersEntity.updatedAt = updatedAt;
	}
	final String? confirmedAt = jsonConvert.convert<String>(json['confirmedAt']);
	if (confirmedAt != null) {
		ordersEntity.confirmedAt = confirmedAt;
	}
	final num? subtotal = jsonConvert.convert<num>(json['subtotal']);
	if (subtotal != null) {
		ordersEntity.subtotal = subtotal;
	}
	final bool? doubt = jsonConvert.convert<bool>(json['doubt']);
	if (doubt != null) {
		ordersEntity.doubt = doubt;
	}
	final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
	if (isSelected != null) {
		ordersEntity.isSelected = isSelected;
	}
	return ordersEntity;
}

Map<String, dynamic> $OrdersEntityToJson(OrdersEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.orderId;
	data['comments'] = entity.comments;
	data['count'] = entity.count;
	data['passcode'] = entity.passcode;
	data['passcodeExt'] = entity.passcodeExt;
	data['items'] =  entity.items?.map((v) => v?.toJson()).toList();
	data['fees'] = entity.fees?.toJson();
	data['region'] = entity.region?.toJson();
	data['customer'] = entity.customer?.toJson();
	data['restaurant'] = entity.restaurant?.toJson();
	data['delivery'] = entity.delivery?.toJson();
	data['distribution'] = entity.distribution?.toJson();
	data['adj'] =  entity.adj?.map((v) => v?.toJson()).toList();
	data['adjustments'] = entity.adjustments?.toJson();
	data['status'] = entity.status;
	data['createdAt'] = entity.createdAt;
	data['updatedAt'] = entity.updatedAt;
	data['confirmedAt'] = entity.confirmedAt;
	data['subtotal'] = entity.subtotal;
	data['doubt'] = entity.doubt;
	data['isSelected'] = entity.isSelected;
	return data;
}

OrdersItem $OrdersItemFromJson(Map<String, dynamic> json) {
	final OrdersItem ordersItem = OrdersItem();
	final String? foodId = jsonConvert.convert<String>(json['_id']);
	if (foodId != null) {
		ordersItem.foodId = foodId;
	}
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		ordersItem.name = name;
	}
	final List<OrdersItemOptions?>? options = jsonConvert.convertList<OrdersItemOptions>(json['options']);
	if (options != null) {
		ordersItem.options = options;
	}
	final num? price = jsonConvert.convert<num>(json['price']);
	if (price != null) {
		ordersItem.price = price;
	}
	final String? code = jsonConvert.convert<String>(json['code']);
	if (code != null) {
		ordersItem.code = code;
	}
	return ordersItem;
}

Map<String, dynamic> $OrdersItemToJson(OrdersItem entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.foodId;
	data['name'] = entity.name?.toJson();
	data['options'] =  entity.options?.map((v) => v?.toJson()).toList();
	data['price'] = entity.price;
	data['code'] = entity.code;
	return data;
}

OrdersDistribution $OrdersDistributionFromJson(Map<String, dynamic> json) {
	final OrdersDistribution ordersDistribution = OrdersDistribution();
	final num? restaurant = jsonConvert.convert<num>(json['restaurant']);
	if (restaurant != null) {
		ordersDistribution.restaurant = restaurant;
	}
	return ordersDistribution;
}

Map<String, dynamic> $OrdersDistributionToJson(OrdersDistribution entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['restaurant'] = entity.restaurant;
	return data;
}

OrdersItemOptions $OrdersItemOptionsFromJson(Map<String, dynamic> json) {
	final OrdersItemOptions ordersItemOptions = OrdersItemOptions();
	final String? optionId = jsonConvert.convert<String>(json['_id']);
	if (optionId != null) {
		ordersItemOptions.optionId = optionId;
	}
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		ordersItemOptions.name = name;
	}
	final num? price = jsonConvert.convert<num>(json['price']);
	if (price != null) {
		ordersItemOptions.price = price;
	}
	return ordersItemOptions;
}

Map<String, dynamic> $OrdersItemOptionsToJson(OrdersItemOptions entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.optionId;
	data['name'] = entity.name?.toJson();
	data['price'] = entity.price;
	return data;
}

OrdersFees $OrdersFeesFromJson(Map<String, dynamic> json) {
	final OrdersFees ordersFees = OrdersFees();
	final OrdersFeesTip? tip = jsonConvert.convert<OrdersFeesTip>(json['tip']);
	if (tip != null) {
		ordersFees.tip = tip;
	}
	final num? delta = jsonConvert.convert<num>(json['delta']);
	if (delta != null) {
		ordersFees.delta = delta;
	}
	final num? tax = jsonConvert.convert<num>(json['tax']);
	if (tax != null) {
		ordersFees.tax = tax;
	}
	final num? service = jsonConvert.convert<num>(json['service']);
	if (service != null) {
		ordersFees.service = service;
	}
	final num? delivery = jsonConvert.convert<num>(json['delivery']);
	if (delivery != null) {
		ordersFees.delivery = delivery;
	}
	final num? credit = jsonConvert.convert<num>(json['credit']);
	if (credit != null) {
		ordersFees.credit = credit;
	}
	return ordersFees;
}

Map<String, dynamic> $OrdersFeesToJson(OrdersFees entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['tip'] = entity.tip?.toJson();
	data['delta'] = entity.delta;
	data['tax'] = entity.tax;
	data['service'] = entity.service;
	data['delivery'] = entity.delivery;
	data['credit'] = entity.credit;
	return data;
}

OrdersFeesTip $OrdersFeesTipFromJson(Map<String, dynamic> json) {
	final OrdersFeesTip ordersFeesTip = OrdersFeesTip();
	final bool? cash = jsonConvert.convert<bool>(json['cash']);
	if (cash != null) {
		ordersFeesTip.cash = cash;
	}
	final num? amount = jsonConvert.convert<num>(json['amount']);
	if (amount != null) {
		ordersFeesTip.amount = amount;
	}
	return ordersFeesTip;
}

Map<String, dynamic> $OrdersFeesTipToJson(OrdersFeesTip entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['cash'] = entity.cash;
	data['amount'] = entity.amount;
	return data;
}

OrdersRegion $OrdersRegionFromJson(Map<String, dynamic> json) {
	final OrdersRegion ordersRegion = OrdersRegion();
	final String? sId = jsonConvert.convert<String>(json['_id']);
	if (sId != null) {
		ordersRegion.sId = sId;
	}
	return ordersRegion;
}

Map<String, dynamic> $OrdersRegionToJson(OrdersRegion entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.sId;
	return data;
}

OrdersCustomer $OrdersCustomerFromJson(Map<String, dynamic> json) {
	final OrdersCustomer ordersCustomer = OrdersCustomer();
	final String? phone = jsonConvert.convert<String>(json['phone']);
	if (phone != null) {
		ordersCustomer.phone = phone;
	}
	final num? orderCount = jsonConvert.convert<num>(json['orderCount']);
	if (orderCount != null) {
		ordersCustomer.orderCount = orderCount;
	}
	final String? language = jsonConvert.convert<String>(json['language']);
	if (language != null) {
		ordersCustomer.language = language;
	}
	return ordersCustomer;
}

Map<String, dynamic> $OrdersCustomerToJson(OrdersCustomer entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['phone'] = entity.phone;
	data['orderCount'] = entity.orderCount;
	data['language'] = entity.language;
	return data;
}

OrdersRestaurant $OrdersRestaurantFromJson(Map<String, dynamic> json) {
	final OrdersRestaurant ordersRestaurant = OrdersRestaurant();
	final String? restaurantId = jsonConvert.convert<String>(json['_id']);
	if (restaurantId != null) {
		ordersRestaurant.restaurantId = restaurantId;
	}
	final MultiNameEntity? name = jsonConvert.convert<MultiNameEntity>(json['name']);
	if (name != null) {
		ordersRestaurant.name = name;
	}
	final bool? manual = jsonConvert.convert<bool>(json['manual']);
	if (manual != null) {
		ordersRestaurant.manual = manual;
	}
	final String? timezone = jsonConvert.convert<String>(json['timezone']);
	if (timezone != null) {
		ordersRestaurant.timezone = timezone;
	}
	final OrdersRestaurantAddress? address = jsonConvert.convert<OrdersRestaurantAddress>(json['address']);
	if (address != null) {
		ordersRestaurant.address = address;
	}
	final OrdersRestaurantDelivery? delivery = jsonConvert.convert<OrdersRestaurantDelivery>(json['delivery']);
	if (delivery != null) {
		ordersRestaurant.delivery = delivery;
	}
	return ordersRestaurant;
}

Map<String, dynamic> $OrdersRestaurantToJson(OrdersRestaurant entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.restaurantId;
	data['name'] = entity.name?.toJson();
	data['manual'] = entity.manual;
	data['timezone'] = entity.timezone;
	data['address'] = entity.address?.toJson();
	data['delivery'] = entity.delivery?.toJson();
	return data;
}

OrdersRestaurantAddress $OrdersRestaurantAddressFromJson(Map<String, dynamic> json) {
	final OrdersRestaurantAddress ordersRestaurantAddress = OrdersRestaurantAddress();
	final String? number = jsonConvert.convert<String>(json['number']);
	if (number != null) {
		ordersRestaurantAddress.number = number;
	}
	final String? street = jsonConvert.convert<String>(json['street']);
	if (street != null) {
		ordersRestaurantAddress.street = street;
	}
	final String? unit = jsonConvert.convert<String>(json['unit']);
	if (unit != null) {
		ordersRestaurantAddress.unit = unit;
	}
	final String? city = jsonConvert.convert<String>(json['city']);
	if (city != null) {
		ordersRestaurantAddress.city = city;
	}
	final String? state = jsonConvert.convert<String>(json['state']);
	if (state != null) {
		ordersRestaurantAddress.state = state;
	}
	final String? country = jsonConvert.convert<String>(json['country']);
	if (country != null) {
		ordersRestaurantAddress.country = country;
	}
	final String? zipcode = jsonConvert.convert<String>(json['zipcode']);
	if (zipcode != null) {
		ordersRestaurantAddress.zipcode = zipcode;
	}
	final String? formatted = jsonConvert.convert<String>(json['formatted']);
	if (formatted != null) {
		ordersRestaurantAddress.formatted = formatted;
	}
	final OrdersRestaurantAddressLocation? location = jsonConvert.convert<OrdersRestaurantAddressLocation>(json['location']);
	if (location != null) {
		ordersRestaurantAddress.location = location;
	}
	final String? note = jsonConvert.convert<String>(json['note']);
	if (note != null) {
		ordersRestaurantAddress.note = note;
	}
	return ordersRestaurantAddress;
}

Map<String, dynamic> $OrdersRestaurantAddressToJson(OrdersRestaurantAddress entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['number'] = entity.number;
	data['street'] = entity.street;
	data['unit'] = entity.unit;
	data['city'] = entity.city;
	data['state'] = entity.state;
	data['country'] = entity.country;
	data['zipcode'] = entity.zipcode;
	data['formatted'] = entity.formatted;
	data['location'] = entity.location?.toJson();
	data['note'] = entity.note;
	return data;
}

OrdersRestaurantAddressLocation $OrdersRestaurantAddressLocationFromJson(Map<String, dynamic> json) {
	final OrdersRestaurantAddressLocation ordersRestaurantAddressLocation = OrdersRestaurantAddressLocation();
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		ordersRestaurantAddressLocation.type = type;
	}
	final List<double>? coordinates = jsonConvert.convertListNotNull<double>(json['coordinates']);
	if (coordinates != null) {
		ordersRestaurantAddressLocation.coordinates = coordinates;
	}
	return ordersRestaurantAddressLocation;
}

Map<String, dynamic> $OrdersRestaurantAddressLocationToJson(OrdersRestaurantAddressLocation entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['type'] = entity.type;
	data['coordinates'] =  entity.coordinates;
	return data;
}

OrdersRestaurantDelivery $OrdersRestaurantDeliveryFromJson(Map<String, dynamic> json) {
	final OrdersRestaurantDelivery ordersRestaurantDelivery = OrdersRestaurantDelivery();
	final String? provider = jsonConvert.convert<String>(json['provider']);
	if (provider != null) {
		ordersRestaurantDelivery.provider = provider;
	}
	final num? prepare = jsonConvert.convert<num>(json['prepare']);
	if (prepare != null) {
		ordersRestaurantDelivery.prepare = prepare;
	}
	final OrdersRestaurantDeliveryFee? fee = jsonConvert.convert<OrdersRestaurantDeliveryFee>(json['fee']);
	if (fee != null) {
		ordersRestaurantDelivery.fee = fee;
	}
	final List<OrdersRestaurantDeliveryFee?>? fees = jsonConvert.convertList<OrdersRestaurantDeliveryFee>(json['fees']);
	if (fees != null) {
		ordersRestaurantDelivery.fees = fees;
	}
	final num? minimum = jsonConvert.convert<num>(json['minimum']);
	if (minimum != null) {
		ordersRestaurantDelivery.minimum = minimum;
	}
	final num? prepareTime = jsonConvert.convert<num>(json['prepareTime']);
	if (prepareTime != null) {
		ordersRestaurantDelivery.prepareTime = prepareTime;
	}
	final OrdersRestaurantDeliveryEstimate? estimate = jsonConvert.convert<OrdersRestaurantDeliveryEstimate>(json['estimate']);
	if (estimate != null) {
		ordersRestaurantDelivery.estimate = estimate;
	}
	final OrdersRestaurantDeliveryWindow? window = jsonConvert.convert<OrdersRestaurantDeliveryWindow>(json['window']);
	if (window != null) {
		ordersRestaurantDelivery.window = window;
	}
	return ordersRestaurantDelivery;
}

Map<String, dynamic> $OrdersRestaurantDeliveryToJson(OrdersRestaurantDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['provider'] = entity.provider;
	data['prepare'] = entity.prepare;
	data['fee'] = entity.fee?.toJson();
	data['fees'] =  entity.fees?.map((v) => v?.toJson()).toList();
	data['minimum'] = entity.minimum;
	data['prepareTime'] = entity.prepareTime;
	data['estimate'] = entity.estimate?.toJson();
	data['window'] = entity.window?.toJson();
	return data;
}

OrdersRestaurantDeliveryWindow $OrdersRestaurantDeliveryWindowFromJson(Map<String, dynamic> json) {
	final OrdersRestaurantDeliveryWindow ordersRestaurantDeliveryWindow = OrdersRestaurantDeliveryWindow();
	final String? end = jsonConvert.convert<String>(json['end']);
	if (end != null) {
		ordersRestaurantDeliveryWindow.end = end;
	}
	final MultiNameEntity? formatted = jsonConvert.convert<MultiNameEntity>(json['formatted']);
	if (formatted != null) {
		ordersRestaurantDeliveryWindow.formatted = formatted;
	}
	final String? start = jsonConvert.convert<String>(json['start']);
	if (start != null) {
		ordersRestaurantDeliveryWindow.start = start;
	}
	return ordersRestaurantDeliveryWindow;
}

Map<String, dynamic> $OrdersRestaurantDeliveryWindowToJson(OrdersRestaurantDeliveryWindow entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['end'] = entity.end;
	data['formatted'] = entity.formatted?.toJson();
	data['start'] = entity.start;
	return data;
}

OrdersRestaurantDeliveryFee $OrdersRestaurantDeliveryFeeFromJson(Map<String, dynamic> json) {
	final OrdersRestaurantDeliveryFee ordersRestaurantDeliveryFee = OrdersRestaurantDeliveryFee();
	final num? factor = jsonConvert.convert<num>(json['factor']);
	if (factor != null) {
		ordersRestaurantDeliveryFee.factor = factor;
	}
	final num? flat = jsonConvert.convert<num>(json['flat']);
	if (flat != null) {
		ordersRestaurantDeliveryFee.flat = flat;
	}
	return ordersRestaurantDeliveryFee;
}

Map<String, dynamic> $OrdersRestaurantDeliveryFeeToJson(OrdersRestaurantDeliveryFee entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['factor'] = entity.factor;
	data['flat'] = entity.flat;
	return data;
}

OrdersRestaurantDeliveryFeesAdjustments $OrdersRestaurantDeliveryFeesAdjustmentsFromJson(Map<String, dynamic> json) {
	final OrdersRestaurantDeliveryFeesAdjustments ordersRestaurantDeliveryFeesAdjustments = OrdersRestaurantDeliveryFeesAdjustments();
	final num? customer = jsonConvert.convert<num>(json['customer']);
	if (customer != null) {
		ordersRestaurantDeliveryFeesAdjustments.customer = customer;
	}
	final String? reason = jsonConvert.convert<String>(json['reason']);
	if (reason != null) {
		ordersRestaurantDeliveryFeesAdjustments.reason = reason;
	}
	final num? restaurant = jsonConvert.convert<num>(json['restaurant']);
	if (restaurant != null) {
		ordersRestaurantDeliveryFeesAdjustments.restaurant = restaurant;
	}
	final num? ricepo = jsonConvert.convert<num>(json['ricepo']);
	if (ricepo != null) {
		ordersRestaurantDeliveryFeesAdjustments.ricepo = ricepo;
	}
	return ordersRestaurantDeliveryFeesAdjustments;
}

Map<String, dynamic> $OrdersRestaurantDeliveryFeesAdjustmentsToJson(OrdersRestaurantDeliveryFeesAdjustments entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['customer'] = entity.customer;
	data['reason'] = entity.reason;
	data['restaurant'] = entity.restaurant;
	data['ricepo'] = entity.ricepo;
	return data;
}

OrdersRestaurantDeliveryEstimate $OrdersRestaurantDeliveryEstimateFromJson(Map<String, dynamic> json) {
	final OrdersRestaurantDeliveryEstimate ordersRestaurantDeliveryEstimate = OrdersRestaurantDeliveryEstimate();
	final String? deadline = jsonConvert.convert<String>(json['deadline']);
	if (deadline != null) {
		ordersRestaurantDeliveryEstimate.deadline = deadline;
	}
	final num? max = jsonConvert.convert<num>(json['max']);
	if (max != null) {
		ordersRestaurantDeliveryEstimate.max = max;
	}
	final num? min = jsonConvert.convert<num>(json['min']);
	if (min != null) {
		ordersRestaurantDeliveryEstimate.min = min;
	}
	return ordersRestaurantDeliveryEstimate;
}

Map<String, dynamic> $OrdersRestaurantDeliveryEstimateToJson(OrdersRestaurantDeliveryEstimate entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['deadline'] = entity.deadline;
	data['max'] = entity.max;
	data['min'] = entity.min;
	return data;
}

OrdersDelivery $OrdersDeliveryFromJson(Map<String, dynamic> json) {
	final OrdersDelivery ordersDelivery = OrdersDelivery();
	final dynamic? nId = jsonConvert.convert<dynamic>(json['_id']);
	if (nId != null) {
		ordersDelivery.nId = nId;
	}
	final String? provider = jsonConvert.convert<String>(json['provider']);
	if (provider != null) {
		ordersDelivery.provider = provider;
	}
	final OrdersDeliveryCourier? courier = jsonConvert.convert<OrdersDeliveryCourier>(json['courier']);
	if (courier != null) {
		ordersDelivery.courier = courier;
	}
	final String? status = jsonConvert.convert<String>(json['status']);
	if (status != null) {
		ordersDelivery.status = status;
	}
	final OrdersDeliveryStats? stats = jsonConvert.convert<OrdersDeliveryStats>(json['stats']);
	if (stats != null) {
		ordersDelivery.stats = stats;
	}
	final num? time = jsonConvert.convert<num>(json['time']);
	if (time != null) {
		ordersDelivery.time = time;
	}
	final String? finishAt = jsonConvert.convert<String>(json['finishAt']);
	if (finishAt != null) {
		ordersDelivery.finishAt = finishAt;
	}
	final OrdersDeliveryEstimate? estimate = jsonConvert.convert<OrdersDeliveryEstimate>(json['estimate']);
	if (estimate != null) {
		ordersDelivery.estimate = estimate;
	}
	final OrdersDeliveryAddress? address = jsonConvert.convert<OrdersDeliveryAddress>(json['address']);
	if (address != null) {
		ordersDelivery.address = address;
	}
	return ordersDelivery;
}

Map<String, dynamic> $OrdersDeliveryToJson(OrdersDelivery entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.nId;
	data['provider'] = entity.provider;
	data['courier'] = entity.courier?.toJson();
	data['status'] = entity.status;
	data['stats'] = entity.stats?.toJson();
	data['time'] = entity.time;
	data['finishAt'] = entity.finishAt;
	data['estimate'] = entity.estimate?.toJson();
	data['address'] = entity.address?.toJson();
	return data;
}

OrdersDeliveryStats $OrdersDeliveryStatsFromJson(Map<String, dynamic> json) {
	final OrdersDeliveryStats ordersDeliveryStats = OrdersDeliveryStats();
	final num? enRouteToPickup = jsonConvert.convert<num>(json['en-route-to-pickup']);
	if (enRouteToPickup != null) {
		ordersDeliveryStats.enRouteToPickup = enRouteToPickup;
	}
	final num? atPickup = jsonConvert.convert<num>(json['at-pickup']);
	if (atPickup != null) {
		ordersDeliveryStats.atPickup = atPickup;
	}
	final num? minPickupCompleted = jsonConvert.convert<num>(json['min-pickup-completed']);
	if (minPickupCompleted != null) {
		ordersDeliveryStats.minPickupCompleted = minPickupCompleted;
	}
	final num? pickupCompleted = jsonConvert.convert<num>(json['pickup-completed']);
	if (pickupCompleted != null) {
		ordersDeliveryStats.pickupCompleted = pickupCompleted;
	}
	final num? enRouteToDropoff = jsonConvert.convert<num>(json['en-route-to-dropoff']);
	if (enRouteToDropoff != null) {
		ordersDeliveryStats.enRouteToDropoff = enRouteToDropoff;
	}
	return ordersDeliveryStats;
}

Map<String, dynamic> $OrdersDeliveryStatsToJson(OrdersDeliveryStats entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['en-route-to-pickup'] = entity.enRouteToPickup;
	data['at-pickup'] = entity.atPickup;
	data['min-pickup-completed'] = entity.minPickupCompleted;
	data['pickup-completed'] = entity.pickupCompleted;
	data['en-route-to-dropoff'] = entity.enRouteToDropoff;
	return data;
}

OrdersDeliveryAddress $OrdersDeliveryAddressFromJson(Map<String, dynamic> json) {
	final OrdersDeliveryAddress ordersDeliveryAddress = OrdersDeliveryAddress();
	final String? city = jsonConvert.convert<String>(json['city']);
	if (city != null) {
		ordersDeliveryAddress.city = city;
	}
	final String? country = jsonConvert.convert<String>(json['country']);
	if (country != null) {
		ordersDeliveryAddress.country = country;
	}
	final String? formatted = jsonConvert.convert<String>(json['formatted']);
	if (formatted != null) {
		ordersDeliveryAddress.formatted = formatted;
	}
	final String? unit = jsonConvert.convert<String>(json['unit']);
	if (unit != null) {
		ordersDeliveryAddress.unit = unit;
	}
	final OrdersDeliveryAddressLocation? location = jsonConvert.convert<OrdersDeliveryAddressLocation>(json['location']);
	if (location != null) {
		ordersDeliveryAddress.location = location;
	}
	return ordersDeliveryAddress;
}

Map<String, dynamic> $OrdersDeliveryAddressToJson(OrdersDeliveryAddress entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['city'] = entity.city;
	data['country'] = entity.country;
	data['formatted'] = entity.formatted;
	data['unit'] = entity.unit;
	data['location'] = entity.location?.toJson();
	return data;
}

OrdersDeliveryAddressLocation $OrdersDeliveryAddressLocationFromJson(Map<String, dynamic> json) {
	final OrdersDeliveryAddressLocation ordersDeliveryAddressLocation = OrdersDeliveryAddressLocation();
	final List<double>? coordinates = jsonConvert.convertListNotNull<double>(json['coordinates']);
	if (coordinates != null) {
		ordersDeliveryAddressLocation.coordinates = coordinates;
	}
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		ordersDeliveryAddressLocation.type = type;
	}
	return ordersDeliveryAddressLocation;
}

Map<String, dynamic> $OrdersDeliveryAddressLocationToJson(OrdersDeliveryAddressLocation entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['coordinates'] =  entity.coordinates;
	data['type'] = entity.type;
	return data;
}

OrdersDeliveryCourier $OrdersDeliveryCourierFromJson(Map<String, dynamic> json) {
	final OrdersDeliveryCourier ordersDeliveryCourier = OrdersDeliveryCourier();
	final String? cid = jsonConvert.convert<String>(json['_id']);
	if (cid != null) {
		ordersDeliveryCourier.cid = cid;
	}
	final String? email = jsonConvert.convert<String>(json['email']);
	if (email != null) {
		ordersDeliveryCourier.email = email;
	}
	final String? phone = jsonConvert.convert<String>(json['phone']);
	if (phone != null) {
		ordersDeliveryCourier.phone = phone;
	}
	return ordersDeliveryCourier;
}

Map<String, dynamic> $OrdersDeliveryCourierToJson(OrdersDeliveryCourier entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.cid;
	data['email'] = entity.email;
	data['phone'] = entity.phone;
	return data;
}

OrdersDeliveryEstimate $OrdersDeliveryEstimateFromJson(Map<String, dynamic> json) {
	final OrdersDeliveryEstimate ordersDeliveryEstimate = OrdersDeliveryEstimate();
	final num? max = jsonConvert.convert<num>(json['max']);
	if (max != null) {
		ordersDeliveryEstimate.max = max;
	}
	final num? min = jsonConvert.convert<num>(json['min']);
	if (min != null) {
		ordersDeliveryEstimate.min = min;
	}
	return ordersDeliveryEstimate;
}

Map<String, dynamic> $OrdersDeliveryEstimateToJson(OrdersDeliveryEstimate entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['max'] = entity.max;
	data['min'] = entity.min;
	return data;
}

OrdersAdj $OrdersAdjFromJson(Map<String, dynamic> json) {
	final OrdersAdj ordersAdj = OrdersAdj();
	final num? customer = jsonConvert.convert<num>(json['customer']);
	if (customer != null) {
		ordersAdj.customer = customer;
	}
	final String? reason = jsonConvert.convert<String>(json['reason']);
	if (reason != null) {
		ordersAdj.reason = reason;
	}
	final num? restaurant = jsonConvert.convert<num>(json['restaurant']);
	if (restaurant != null) {
		ordersAdj.restaurant = restaurant;
	}
	final num? ricepo = jsonConvert.convert<num>(json['ricepo']);
	if (ricepo != null) {
		ordersAdj.ricepo = ricepo;
	}
	return ordersAdj;
}

Map<String, dynamic> $OrdersAdjToJson(OrdersAdj entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['customer'] = entity.customer;
	data['reason'] = entity.reason;
	data['restaurant'] = entity.restaurant;
	data['ricepo'] = entity.ricepo;
	return data;
}

OrdersAdjustments $OrdersAdjustmentsFromJson(Map<String, dynamic> json) {
	final OrdersAdjustments ordersAdjustments = OrdersAdjustments();
	final num? customer = jsonConvert.convert<num>(json['customer']);
	if (customer != null) {
		ordersAdjustments.customer = customer;
	}
	final String? reason = jsonConvert.convert<String>(json['reason']);
	if (reason != null) {
		ordersAdjustments.reason = reason;
	}
	final num? restaurant = jsonConvert.convert<num>(json['restaurant']);
	if (restaurant != null) {
		ordersAdjustments.restaurant = restaurant;
	}
	final num? ricepo = jsonConvert.convert<num>(json['ricepo']);
	if (ricepo != null) {
		ordersAdjustments.ricepo = ricepo;
	}
	return ordersAdjustments;
}

Map<String, dynamic> $OrdersAdjustmentsToJson(OrdersAdjustments entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['customer'] = entity.customer;
	data['reason'] = entity.reason;
	data['restaurant'] = entity.restaurant;
	data['ricepo'] = entity.ricepo;
	return data;
}