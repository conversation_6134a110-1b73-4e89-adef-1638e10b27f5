import 'package:flutter/material.dart';

class RColors {
  static const Color app_main = Color(0xFF666666);

  static const Color transparent_80 = Color(0x80000000);

  static const Color text_dark = Color(0xFF333333);
  static const Color text_normal = Color(0xFF666666);
  static const Color text_gray = Color(0xFF999999);

  static const Color divider = Color(0xffe5e5e5);
  static const Color divider_e1 = Color(0xffe1e1e1);

  static const Color gray_33 = Color(0xFF333333);
  static const Color gray_66 = Color(0xFF666666);
  static const Color gray_99 = Color(0xFF999999);
  static const Color common_orange = Color(0XFFFC9153);
  static const Color gray_ef = Color(0XFFEFEFEF);

  static const Color gray_f0 = Color(0xfff0f0f0);
  static const Color gray_f5 = Color(0xfff5f5f5);
  static const Color gray_f6 = Color(0xfff6f6f6);
  static const Color gray_cc = Color(0xffcccccc);
  static const Color gray_ce = Color(0xffcecece);
  static const Color green_1 = Color(0xff009688);
  static const Color green_62 = Color(0xff626262);
  static const Color green_e5 = Color(0xffe5e5e5);
  static const Color r_orange = Color(0xfff08670);
  static const Color r_orange_alpha = Color(0x80f08670);
  static const Color r_blue_alpha = Color(0x802196F3);
  static const Color red = Color(0xffD81B60);

  static const Color mr = Color(0xffEB5B55);

  static const Color ink = Color(0xffFFF9F9);
  
  static const Color yellow = Color(0xffFFB800);
  static const Color green  = Color(0xff42E3A9);
  static const Color light_yellow  = Color(0xffFFFAEE);
}

Map<String, Color> circleAvatarMap = {
  'A': Colors.blueAccent,
  'B': Colors.blue,
  'C': Colors.cyan,
  'D': Colors.deepPurple,
  'E': Colors.deepPurpleAccent,
  'F': Colors.blue,
  'G': Colors.green,
  'H': Colors.lightBlue,
  'I': Colors.indigo,
  'J': Colors.blue,
  'K': Colors.blue,
  'L': Colors.lightGreen,
  'M': Colors.blue,
  'N': Colors.brown,
  'O': Colors.orange,
  'P': Colors.purple,
  'Q': Colors.black,
  'R': Colors.red,
  'S': Colors.blue,
  'T': Colors.teal,
  'U': Colors.purpleAccent,
  'V': Colors.black,
  'W': Colors.brown,
  'X': Colors.blue,
  'Y': Colors.yellow,
  'Z': Colors.grey,
  '#': Colors.blue,
};

Map<String, Color> themeColorMap = {
  'gray': RColors.gray_33,
  'blue': Colors.blue,
  'blueAccent': Colors.blueAccent,
  'cyan': Colors.cyan,
  'deepPurple': Colors.deepPurple,
  'deepPurpleAccent': Colors.deepPurpleAccent,
  'deepOrange': Colors.deepOrange,
  'green': Colors.green,
  'indigo': Colors.indigo,
  'indigoAccent': Colors.indigoAccent,
  'orange': Colors.orange,
  'purple': Colors.purple,
  'pink': Colors.pink,
  'red': Colors.red,
  'teal': Colors.teal,
  'black': Colors.black,
};
