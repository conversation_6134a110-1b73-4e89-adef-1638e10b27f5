class Strings {
  static const String app_name = 'Connect';
  static const String title_orders = 'Orders';
  static const String title_report = 'Report';
  static const String title_restaurant = 'Restaurant';
  static const String title_more = 'More';
  static const String ricepo_connect = 'RICE Connect';
  static const String log_in = 'Log in';
  static const String log_out = 'Log out';
  static const String connect_help =
      'If you are a restaurant owner or courier and need a RICE account please email us at ';
  static const String connect_help_email = '<EMAIL>.';
  static const String login_password = 'Login with password';
  static const String login_phone = 'Login with phone';
  static const String phone = 'Phone';
  static const String sms_code = 'SMS Code';
  static const String enter_code = 'Enter code';
  static const String phone_num_hint = '**********';
  static const String password = 'Password';
  static const String email = 'Email';
  static const String send_code = 'Send Code';
  static const String vc_try_again = 'Try again %ss';
  static const String empty_tips =
      'You currently have no order, \nplease wait.';
  static const String ready_meal = 'Ready 完成备餐';
  static const String confirm_order = 'Confirm 确认订单';
  static const String region_china = 'China +86';
  static const String region_usa = 'United States +1';
  static const String region_france = 'France +33';
  static const String region_spain = 'Spain +34';
  static const String region_uk = 'UK +44';
  static const String network_check = 'Please check your network';
  static const String manual_handle = 'Manual Handle';
  static const String auto_confirm = 'Auto-Confirm';
  static const String search_order = 'Search Order';
  static const String process_order = 'In Hand';
  static const String history_order = 'History';
  static const String pre_order = 'Pre-order'; //预订单
  static const String label_all = 'All';
  static const String setting = 'Setting';
  static const String search_label = 'Search';
  static const String no_history_orders = 'No history orders';
  static const String expand_orders = 'Unfold';
  static const String collapse_orders = 'Fold';
  static const String no_pending_orders = 'No pending orders';
  static const String choose_action = 'Choose Action';
  static const String copy_number = 'Copy Number';
  static const String call_by_phone = 'Call By Phone';
  static const String cancel = 'Cancel';
  static const String ok = 'Ok';
  static const String no = 'No';
  static const String confirm = 'Confirm'; //确定
  static const String immediately_delivery =
      'Delivery immediately | before %s'; //立即送达 | 12:50前送达
  static const String delivery_completed = 'Completed | %s'; //已经送达 | xx
  static const String pre_order_time = 'Pre-order | %s Completed'; //预订单 | xx送达
  static const String wait_confirm_receiving = 'News'; //待确认接单
  static const String wait_chucan_confirmed = 'In Progress'; //待确认出餐
  static const String wait_courier_come = 'Ready'; //待信使取餐
  static const String courier_at_pickup = 'at-pickup'; //信使已到店
  static const String courier_pickup_completed = 'pickup-completed'; //信使已取餐
  static const String courier_at_dropoff = 'Delivering'; //信使送餐中
  static const String courier_completed = 'Completed'; //订单送达
  static const String order_cancel = 'Order cancelled'; //订单已取消
  static const String pickup_by_myself = 'Pick up in store'; //店内自取
  static const String tail_number = 'Tail number'; //尾号
  static const String confirm_receiving = 'Please confirm'; //请确认接单
  static const String manual_confirm_receiving =
      'Please confirm the order manually'; //请手动确认接单
  static const String meal_preparation_time =
      'Meal preparation time : %s'; //备餐用时
  static const String meal_preparation_time_timeout =
      'Meal preparation time : %s 超时: %s'; //备餐用时，超时
  static const String prepare_time = 'Meal preparation time'; //备餐用时
  static const String prepare_time_const = 'Prepare Time 备餐时间'; //备餐时间
  static const String promise_prepare_time =
      'Commitment to average meal preparation time(10~80Minutes)'; //承诺平均备餐用时
  static const String recommended_time = 'Please Ready before %s'; //建议11：55前出餐
  static const String courier_picked_up =
      'Already picked up at %s'; //11：56信使已经取餐
  static const String restaurant_completed = '出餐完成'; //出餐完成
  static const String match_courier = 'Matched driver'; //已匹配信使
  static const String sub_total = 'Subtotal'; //小计
  static const String tax = 'Tax'; //税
  static const String tip = 'tip'; //小费
  static const String reach_minimum = 'Reach-Minimum'; //补差价
  static const String delivery_fee = 'Delivery Fees'; //送费
  static const String order_number = 'Order number'; //订单号
  static const String order_time = 'Order time'; //下单时间
  static const String order_print = 'Print'; //打印
  static const String copy_to_clipboard = 'Copied to pasteboard'; //已复制到粘贴板
  static const String product = '%s products'; //件商品
  static const String note_product = 'Note: %s'; //备注
  static const String minutes = 'Minutes'; //分钟
  static const String pre_income = 'Estimated Revenue '; //预计收入
  static const String confirmed_before_print =
      'Please confirm the order before printing'; //请先确认接单后打印
  static const String auto_confirm_devices =
      'This device automatically takes orders'; //本设备自动接单
  static const String suggest_one_devices_auto_confirm =
      'It is recommended to leave only 1 device/service provider to automatically take orders, more than open the automatic order may encounter unexpected circumstances.'; //建议只留1台设备/服务商来自动接单，多出开启自动接单可能遇到意外情况。
  static const String pre_total = '已完成%s单，预计收入%s'; //已完成%s单，预计收入%s
  static const String call_customer = '拨打客服'; //拨打客服
  static const String united_states = 'United States: +1**********';
  static const String spain = 'Spain: +34930343777';
  static const String france = 'France: +33644646648';
  static const String current_version = 'Current Version %s';
  static const String ringtone = 'Ringtone';
  static const String copy_print = '2 copy print';
  static const String update_password = 'Update Password';
  static const String call_support = 'Call Support';
  static const String select_language = 'Select language';
  static const String log_out_confirmed = 'Are you sure to log out?'; //确认退出登录吗
  static const String zh_CN = '简体中文'; //简体中文
  static const String zh_HK = '繁体中文'; //繁体中文
  static const String en = 'English';
  static const String language = 'Language';
  static const String update_success = 'Update success';
  static const String done = 'done'; //完成
}
