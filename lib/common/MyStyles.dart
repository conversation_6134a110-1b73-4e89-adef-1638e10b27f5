import 'package:flutter/material.dart';

class MyStyles {
  static const TextStyle bh1 =
      TextStyle(color: Colors.black, fontSize: 29, fontWeight: FontWeight.w700);

  static const TextStyle sh2 =
      TextStyle(color: Colors.black, fontSize: 28, fontWeight: FontWeight.w600);

  static const TextStyle sh3 =
      TextStyle(color: Colors.black, fontSize: 22, fontWeight: FontWeight.w600);

  static const TextStyle sh4 =
      TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.w600);

  static const TextStyle mh4 =
      TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.w500);

  static const TextStyle mh5 =
      TextStyle(color: Colors.black, fontSize: 16, fontWeight: FontWeight.w500);

  static const TextStyle mh6 =
      TextStyle(color: Colors.black, fontSize: 17, fontWeight: FontWeight.w500);

  static const TextStyle mh7 =
      TextStyle(color: Colors.black, fontSize: 12, fontWeight: FontWeight.w500);

  static const TextStyle m20 =
      TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.w500);
  static const TextStyle m16 =
      TextStyle(color: Colors.black, fontSize: 16, fontWeight: FontWeight.w500);
  static const TextStyle m15 =
      TextStyle(color: Colors.black, fontSize: 15, fontWeight: FontWeight.w500);
  static const TextStyle r15 =
      TextStyle(color: Colors.black, fontSize: 15, fontWeight: FontWeight.w400);
  static const TextStyle m13 =
      TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.w500);
  static const TextStyle r13 =
      TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.w400);
  static const TextStyle m12 =
      TextStyle(color: Colors.black, fontSize: 12, fontWeight: FontWeight.w500);
  static const TextStyle r12 =
      TextStyle(color: Colors.black, fontSize: 12, fontWeight: FontWeight.w400);
  static const TextStyle m10 =
      TextStyle(color: Colors.black, fontSize: 10, fontWeight: FontWeight.w500);
}

class MyColors {
  static const Color lightN1 = Color(0xFFF6F6F6);
  static const Color lightN2 = Color(0xFFFAFAFA);
  static const Color lightN3 = Color(0xFFD7D7D7);
  static const Color lightN4 = Color(0xFFCCCCCC);
  static const Color lightN5 = Color(0xFF8D9093);
  static const Color lightN6 = Color(0xFF333333);
  static const Color lightMR = Color(0xFFFFF5F5);
  static const Color MR = Color(0xFFEB5B55);
  static const Color B = Color(0xFF000000);
  static const Color W = Color(0xFFFFFFFF);
  static const Color MG = Color(0xFF40CFC7);
  static const Color MY = Color(0xFFEECA5E);
  static const Color darkD1 = Color(0xFFB7B9C1);
  static const Color darkD2 = Color(0xFF51535F);
  static const Color darkD3 = Color(0xFF373943);
  static const Color darkD4 = Color(0xFF2A2B33);
  static const Color darkD5 = Color(0xFF1C1D23);
  static const Color lightN7 = Color(0xFFE1E1E1);
  static const Color darkD6 = Color(0xFF454555);

  static const Color color333 = Color(0xFF333333);
  static const Color portal = Color(0xFFF85656);
  static const Color color666 = Color(0xFF666666);
  static const Color color999 = Color(0xFF999999);
  static const Color line = Color(0xFFEAEAEA);
  static const Color bg = Color(0xFFF8F8F8);
  static const Color ccc = Color(0xFFCCCCCC);
  static const Color f4 = Color(0xFFF4F4F4);
  static const Color FFF1F1 = Color(0xFFFFF1F1);
  static const Color hint = Color(0xFFCCCCCC);
}
