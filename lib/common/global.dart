import 'package:connect/utils/connect_cache.dart';
import 'package:connect/utils/log_util.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:event_bus/event_bus.dart';
import 'package:firebase_app_installations/firebase_app_installations.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';

import 'package:package_info_plus/package_info_plus.dart';
import 'package:uuid/uuid.dart';
import '../firebase_options.dart';
import '../utils/push_permission.dart';

class GlobalConfig {
  static const bool isWeb = kIsWeb;
  static const bool isRelease = const bool.fromEnvironment("dart.vm.product");
  // static const bool isRelease = true;
  static GlobalKey<NavigatorState> navigatorKey =
      new GlobalKey<NavigatorState>();
  static EventBus eventBus = EventBus();
  static bool isBackground = false;
  static var autoConfirmedMapId = [];
  static var currentTabIndex =
      0; //0,in progress tab;1,history tab;2,preOrder tab
  static var currentLabelIndex = 0; //0,all;1,new;2,in progress;3,ready
  static var isSunmi = false;
  static var paymentRefresh =
      false; //invoke auth api when tap setup payment if paymentRefresh is true

  static BuildContext context() {
    return navigatorKey.currentContext!;
  }

  ///android push init
  static initFirebase() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    FirebaseMessaging.instance.onTokenRefresh.listen((fcmToken) {
      LogUtil.d("fcmToken: $fcmToken");
      pushToken(fcmToken);
    }).onError((err) {
      // Error getting token.
      LogUtil.e("fcmToken error: $err");
    });
    String? fcmToken = await FirebaseMessaging.instance.getToken().catchError((e) {});
    if (fcmToken != null) pushToken(fcmToken);
    // request push permission when launch app.
    await pushPermission();
  }

  static deviceId() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    if (Platform.isAndroid) {
      // var androidInfo = await deviceInfoPlugin.androidInfo;
      String installationId = await getFirebaseInstallationId();
      ConnectCache.saveDeviceId("${installationId}");
      ConnectCache.saveVersion("${packageInfo.version}");
    } else if (Platform.isIOS) {
      DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      var iosInfo = await deviceInfoPlugin.iosInfo;
      ConnectCache.saveDeviceId("${iosInfo.identifierForVendor}");
      ConnectCache.saveVersion("${packageInfo.version}");
    }
    ConnectCache.saveBuildNumber("${packageInfo.buildNumber}");
    ConnectCache.savePackageName("${packageInfo.packageName}");
  }

  static Future<String> getFirebaseInstallationId() async {
    final fid = await FirebaseInstallations.instance.getId() ?? Uuid().v4();
    return fid;
  }

}
