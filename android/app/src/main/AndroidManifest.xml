<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:amazon="http://schemas.amazon.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="rocks.rice.connect">
    <!-- io.flutter.app.FlutterApplication is an android.app.Application that
         calls FlutterMain.startInitialization(this); in its onCreate method.
         In most cases you can leave this as-is, but you if you want to provide
         additional functionality it is fine to subclass or reimplement
         FlutterApplication and put your custom class here. -->

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>


    <uses-sdk tools:overrideLibrary="xyz.luan.audioplayers" />

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />

    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- amazon -->
    <uses-permission android:name="com.amazon.device.messaging.permission.RECEIVE" />
    <permission
        android:name="rocks.rice.connect.permission.RECEIVE_ADM_MESSAGE"
        android:protectionLevel="signature" />
    <uses-permission android:name="rocks.rice.connect.permission.RECEIVE_ADM_MESSAGE" />
    <!-- amazon -->

    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <queries>
        <package android:name="com.google.android.apps.maps" />
    </queries>
    <queries>
        <package android:name="woyou.aidlservice.jiuiv5" />
    </queries>

    <application
        android:name="android.app.Application"
        android:icon="@mipmap/ic_launcher"
        android:label="RICE Portal"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <!-- Displays an Android View that continues showing the launch screen
                 Drawable until Flutter paints its first frame, then this splash
                 screen fades out. A splash screen is useful to avoid any visual
                 gap between the end of Android's launch screen and the painting of
                 Flutter's first frame. -->
            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <amazon:enable-feature
            android:name="com.amazon.device.messaging"
            android:required="false" />

        <service
            android:name="com.onesignal.ADMMessageHandler"
            android:exported="false" />
        <service
            android:name="com.onesignal.ADMMessageHandlerJob"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <receiver
            android:exported="true"
            android:name="com.onesignal.ADMMessageHandler$Receiver"
            android:permission="com.amazon.device.messaging.permission.SEND">
            <intent-filter>
                <action android:name="com.amazon.device.messaging.intent.REGISTRATION" />
                <action android:name="com.amazon.device.messaging.intent.RECEIVE" />

                <category android:name="rocks.rice.connect" />
            </intent-filter>
        </receiver>

        <meta-data android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyBWD0m5J2dmbyY44wIwhig_waHG0xOu-oI"/>
    </application>
</manifest>
