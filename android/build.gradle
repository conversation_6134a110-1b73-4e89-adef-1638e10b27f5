//buildscript {
//    ext.kotlin_version = '1.9.0'
//    repositories {
//        google()
//        // oneSignal
//        maven { url 'https://plugins.gradle.org/m2/' } // Gradle Plugin Portal
//        mavenCentral()
//        maven {
//            url = uri("https://storage.googleapis.com/r8-releases/raw")
//        }
//        maven { url 'https://jitpack.io' }
//    }
//
//    dependencies {
//        classpath 'com.android.tools.build:gradle:8.9.0'
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//        classpath 'com.google.gms:google-services:4.3.5'
//        classpath 'com.google.firebase:perf-plugin:1.4.1'
//        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.2.0'
//        // OneSignal-Gradle-Plugin
//        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:0.13.4'
//        classpath "com.android.tools:r8:8.3.37"
//
//    }
//}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}

//configurations.all {
//    resolveStrategy {
//        force 'com.github.umair13adil:RxLogs:v1.0.17'
//    }
//}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
