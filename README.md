# Connect

RICE Portal flutter project.

## 环境
Flutter 2.10.4 • channel stable • https://github.com/flutter/flutter.git
Framework • revision c860cba910 (3 weeks ago) • 2022-03-25 00:23:12 -0500
Engine • revision 57d3bac3dd
Tools • Dart 2.16.2 • DevTools 2.9.2

## Attention
   In this project, the source code of the thrid-party library has been modified in three places. When you download and compile the project for the first time, you may need to manually modify it according to the documentation.

   1. Modify to determine whether the device is a tablet, in line 72 in the flutter_device_type library, modify if (diagonalSizeInches >= 7) to if (diagonalSizeInches >= 6.959614918106986)
   2. In the location library, modify 
      ActivityCompat.requestPermissions(it, arrayOf(
                            Manifest.permission.ACCESS_FINE_LOCATION,
                            Manifest.permission.ACCESS_BACKGROUND_LOCATION),
                            REQUEST_PERMISSIONS_REQUEST_CODE)
      to
      if (ActivityCompat.shouldShowRequestPermissionRationale(it, android.Manifest.permission.ACCESS_BACKGROUND_LOCATION)) {
                    result?.success(0)
                } else {
                    ActivityCompat.requestPermissions(it, arrayOf(
                            Manifest.permission.ACCESS_FINE_LOCATION,
                            Manifest.permission.ACCESS_BACKGROUND_LOCATION),
                            REQUEST_PERMISSIONS_REQUEST_CODE)
                }
   3. In the google_maps_flutter_heatmap library, modify #import <Google-Maps-iOS-Utils/GMUHeatmapTileLayer.h> to
   #Import <GoogleMapsUtils/GMUHeatmapTileLayer.h>, otherwise your project will report an error during the compilation phase

## Components
   You can visit this web url https://flutter.dev/docs/development/ui/widgets

## Third-Party Libraries

   common_utils: It contains common tool classes, easy to develop

   - [common_utils](https://github.com/Sky24n/flustars)

   pull_to_refresh: A widget provided to the flutter scroll component drop-down refresh and pull up load.support android and ios.

   - [pull_to_refresh](https://pub.dev/packages/pull_to_refresh)
  
   dio: A powerful Http client for Dart, which supports Interceptors, Global configuration, FormData, Request Cancellation, File downloading, Timeout etc.
   - [dio](https://github.com/flutterchina/dio)

   flutter_screenutil: A flutter plugin for adapting screen and font size.Let your UI display a reasonable layout on different screen sizes!
   - [flutter_screenutil](https://pub.dev/packages/flutter_screenutil)
   
   device_info: Get current device information from within the Flutter application.
   - [device_info](https://pub.dev/packages/device_info)

   fluttertoast: This toast library supports two kinds of toast messages one which requires BuildContext other with No BuildContext
   - [fluttertoast](https://pub.dev/packages/fluttertoast)
    
   connectivity: This plugin allows Flutter apps to discover network connectivity and configure themselves accordingly. It can distinguish between cellular vs WiFi connection. This plugin works for iOS and Android.
   - [connectivity](https://pub.dev/packages/connectivity)
    
   fluintl: This package provides internationalization and localization facilities.
   - [fluintl](https://github.com/Sky24n/fluintl)
  
   provider: A wrapper around InheritedWidget to make them easier to use and more reusable.
   - [provider](https://pub.dev/packages/provider)

## Tools
   launcher icons: A command-line tool which simplifies the task of updating your Flutter app's launcher icon
   - [flutter_launcher_icons](https://pub.dev/packages/flutter_launcher_icons#flutter-launcher-icons)

## JSON
1. 首先去[json2dart](https://caijinglong.github.io/json2dart/index_ch.html)生成model
2. 执行命令`fvm flutter packages pub run build_runner build `

## Android target 34

1. geolocator 11.1.0

modify the LocationServiceImplHandler onListen ContextCompat.registerReceiver


