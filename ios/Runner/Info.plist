<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>RICE Portal</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>itms-beta</string>
		<string>itms</string>
		<string>comgooglemaps</string>
		<string>baidumap</string>
		<string>iosamap</string>
		<string>waze</string>
		<string>yandexmaps</string>
		<string>yandexnavi</string>
		<string>citymapper</string>
		<string>mapswithme</string>
		<string>osmandmaps</string>
		<string>dgis</string>
		<string>qqmap</string>
		<string>here-location</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>RICE Portal needs your permission to upload image proof for the delivery.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>RICE Portal collects location data to distribute the best orders and delivery routes to you.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>RICE Portal collects location data to distribute the best orders and delivery routes to you.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>RICE Portal collects location data to distribute the best orders and delivery routes to you.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>RICE Portal needs your permission to record a voice for the delivery.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>RICE Portal needs your permission to upload image proof for the delivery.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>location</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
</dict>
</plist>