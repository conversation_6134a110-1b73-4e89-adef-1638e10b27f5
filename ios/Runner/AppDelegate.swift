import UIKit
import Flutter
import GoogleMaps

@main
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication
            .LaunchOptionsKey: Any]?
    ) -> Bool {
        GMSServices.provideAPIKey("AIzaSyBWD0m5J2dmbyY44wIwhig_waHG0xOu-oI")
        GeneratedPluginRegistrant.register(with: self)

        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController;
        let batteryChannel = FlutterMethodChannel.init(name: "com.connect/print",
                                                       binaryMessenger: controller.binaryMessenger);
        batteryChannel.setMethodCallHandler({
            (call: FlutterMethodCall, result: FlutterResult) -> Void in
            if ("appleMap" == call.method) {
                if let dict = call.arguments as? Dictionary<String, String> {
                    let lat:String = dict["lat"] ?? ""
                    let log:String = dict["log"] ?? ""
                    let address:String = dict["address"] ?? ""
                    let from:String = dict["from"] ?? ""
                    let formatName:String = address.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
                    var appleURL = "";
                    if("shifts" == from){
                        appleURL = "http://maps.apple.com/?q=\(formatName)"
                    }else{
                        appleURL = "http://maps.apple.com/?ll=\(lat),\(log)&z=12&q=\(formatName)"
                    }
                    if let url = URL(string: appleURL) {
                        UIApplication.shared.open(url)
                        result(appleURL)
                    }
                }

            } else if("googleMap"==call.method){
                if let dict = call.arguments as? Dictionary<String, String> {
                    let address:String = dict["address"] ?? ""
                    let formatName:String = address.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
                    let appleURL = "https://www.google.com/maps/search/?api=1&query=\(formatName)"
                    if let url = URL(string: appleURL) {
                        UIApplication.shared.open(url)
                        result(appleURL)
                    }
                }

            }else if("persistentLog"==call.method){
                if let dict = call.arguments as? Dictionary<String, String> {
                    let tag:String = dict["tag"] ?? ""
                    let subTag:String = dict["subTag"] ?? ""
                    let message:String = dict["message"] ?? ""
                    Log(.Connect, desc:"tag:\(tag), subTag:\(subTag), message:\(message)")
                }
            }else if("logPath"==call.method){
                result(logPath.path)
            }else {
                result(FlutterMethodNotImplemented);
            }
        });

        return super
            .application(application,
                         didFinishLaunchingWithOptions: launchOptions)
    }

}
