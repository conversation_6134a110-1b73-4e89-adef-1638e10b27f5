//
//  Logger.swift
//  rohan
//
//  Created by <PERSON> on 2021/4/23.
//  Copyright © 2021 RICEPO. All rights reserved.
//

import Foundation
import XCGLogger

extension XCGLogger {
    /// 此方法只为兼容老版本日志库代码，新日志不要使用
    public func debug(_ desc: String? = nil,
                      functionName: StaticString = #function,
                      fileName: StaticString = #file,
                      lineNumber: Int = #line,
                      userInfo: [String: Any] = [:])
    {
        Log(desc: desc,
            functionName: functionName,
            fileName: fileName,
            lineNumber: lineNumber,
            userInfo: userInfo)
    }
}

public enum LogModule: String {
    case Unknown
    case AppLifeCycle
    case Network
    case URL
    case WebView
    case DB
    case UserAction
    case DataHandle
    case PushToken
    case IAP
    case Toast
    case Alert
    case PostNote
    case LogEvent
    case Timestamp
    
    case Connect
    case Deinit
    
    var desc: String {
        return "Module \(self): "
    }
    
    func desc(_ detail: String?) -> String {
        return self.desc + (detail ?? "")
    }
}

/// 只想在 debug 模式记录的日志，使用此方法
public func LogDebug(_ desc: String? = nil,
                     functionName: StaticString = #function,
                     fileName: StaticString = #file,
                     lineNumber: Int = #line,
                     userInfo: [String: Any] = [:])
{
    #if DEBUG
    log.info(desc,
             functionName: functionName,
             fileName: fileName,
             lineNumber: lineNumber,
             userInfo: userInfo)
    #endif
}

public func LogDebugFunc(_ desc: String,
                         functionName: StaticString = #function,
                         fileName: StaticString = #file,
                         lineNumber: Int = #line,
                         userInfo: [String: Any] = [:])
{
    LogDebug(.DataHandle, desc: desc, functionName: functionName, fileName: fileName, lineNumber: lineNumber, userInfo: userInfo)
}

/// 只想在 debug 模式记录的日志，使用此方法
public func LogDebug(_ module: LogModule = .Unknown,
                desc: String? = nil,
                functionName: StaticString = #function,
                fileName: StaticString = #file,
                lineNumber: Int = #line,
                userInfo: [String: Any] = [:])
{
    #if DEBUG
    log.info(module.desc(desc),
             functionName: functionName,
             fileName: fileName,
             lineNumber: lineNumber,
             userInfo: userInfo)
    #endif
}

public func Log(_ module: LogModule = .Unknown,
                desc: String? = nil, // 字符串
                functionName: StaticString = #function,
                fileName: StaticString = #file,
                lineNumber: Int = #line,
                userInfo: [String: Any] = [:])
{
    log.info(module.desc(desc),
             functionName: functionName,
             fileName: fileName,
             lineNumber: lineNumber,
             userInfo: userInfo)
}

public func Log(_ module: LogModule = .Unknown,
                obj: Any? = nil, // 对象
                functionName: StaticString = #function,
                fileName: StaticString = #file,
                lineNumber: Int = #line,
                userInfo: [String: Any] = [:])
{
    log.info(module.desc(String(describing: obj)),
             functionName: functionName,
             fileName: fileName,
             lineNumber: lineNumber,
             userInfo: userInfo)
}

//全局变量默认lazy，且只调用一次
public let log: XCGLogger = {
    let log = XCGLogger.default
    let path = logPath
    #if DEBUG
    log.setup(level: .debug, showThreadName: true, showLevel: false, showFileNames: true, showLineNumbers: true)
    #else
    log.setup(level: .debug, showThreadName: true, showLevel: false, showFileNames: true, showLineNumbers: true)
    #endif

    log.add(destination: fileDestination)
    log.logAppDetails()
    
    return log
}()

private var fileDestination: FileDestination = {
    let fileDestination = AutoRotatingFileDestination(
        writeToFile: logPath,
        identifier: "advancedLogger.fileDestination",
        shouldAppend: true,
        appendMarker: "\n\n-------------------- Relauched App --------------------\n\n",
        maxFileSize: 10 * 1024 * 1024, // 10MB，实测大概在30M左右
        maxTimeInterval: 0 // 不限时长
    )

    fileDestination.outputLevel = .debug
    fileDestination.showLogIdentifier = false
    fileDestination.showFunctionName = true
    fileDestination.showThreadName = true
    fileDestination.showLevel = false
    fileDestination.showFileName = true
    fileDestination.showLineNumber = true
    fileDestination.showDate = true
    
    fileDestination.targetMaxLogFiles = 2 // 几个归档文件

    // Process this destination in the background
    fileDestination.logQueue = XCGLogger.logQueue
    
    return fileDestination
}()

public let logPath: URL = cacheDirectory.appendingPathComponent("connect.log")

private var documentsDirectory: URL {
    let dir: String? = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first
    let url = URL.init(fileURLWithPath: dir!)
    
    return url
}

private var cacheDirectory: URL {
    let dir: String? = NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true).first
    
    let fileManager = FileManager.default
    
    let filePath = dir! as String + "/ricepo"
    
    let exist = fileManager.fileExists(atPath: filePath)
    
    if !exist { try! fileManager.createDirectory(atPath: filePath, withIntermediateDirectories: true, attributes: nil) }

    let url = URL.init(fileURLWithPath: filePath)
    
    return url
}
