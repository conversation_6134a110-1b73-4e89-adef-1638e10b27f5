# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Push a new release build to the App Store"
  lane :release do

    app_store_connect_api_key(
      key_id: "BFC9JV4J9D",
      issuer_id: "69a6de7a-56d4-47e3-e053-5b8c7c11a4d1",
      in_house:false,
*******************************************************************************************************************************************************************************************************************************************************************************************
)
    # Get profile
    match(type: "appstore", readonly: true)

    match(
       app_identifier: ["rocks.rice.connect","rocks.rice.connect.OneSignalNotificationServiceExtension"],
       type: "appstore",
       )

    # Get testFlight version number
    latest_build_number = latest_testflight_build_number(
      username: "<EMAIL>",
      app_identifier: "rocks.rice.connect"
    )

    current_build_number = get_build_number(xcodeproj: "Runner.xcodeproj")

    # campare build number
    current_values = current_build_number.split(".")

    latest_values = latest_build_number.split(".")

    if current_values[1].to_i > latest_values[1].to_i || current_values[2].to_i > latest_values[2].to_i
      new_build_number = current_build_number
     else
      # Add 1
       update_number =  ((latest_build_number.is_a? String) ? latest_build_number[-1].to_i : latest_build_number) + 1

       new_build_number = latest_build_number[0...-1] + update_number.to_s
    end

    # Increase testFlight version number
    increment_build_number(
      build_number: new_build_number,
      xcodeproj: "./Runner.xcodeproj"
    )
    # run_tests(scheme: "rohanTests",xcargs: "-UseModernBuildSystem=NO",)
    # run_tests(scheme: "rohanUITests",xcargs: "-UseModernBuildSystem=NO",)
    #  Build App
    gym(
      scheme: "Runner"
    )
    # Upload to testFlight
    upload_to_testflight(
      username: "<EMAIL>",
      app_identifier: "rocks.rice.connect",
      skip_waiting_for_build_processing:true,
      # wait_for_uploaded_build: true
    )

    # Upload dsym file to Crashlytics
       upload_symbols_to_crashlytics()
  end
end
