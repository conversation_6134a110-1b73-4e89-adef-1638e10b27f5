git_url("**************:ricepo/ios-sign.git")

storage_mode("git")

app_identifier(["rocks.rice.connect"])

username("<EMAIL>")

ENV["FASTLANE_PASSWORD"] = "Qopqas-jegso9-vevqot"

# username("<EMAIL>")

# ENV["FASTLANE_PASSWORD"] = "Ricepo123"

ENV["MATCH_PASSWORD"] = "ricepo" #a

# type("development") # The default type, can be: appstore, adhoc, enterprise or development

# For all available options run `fastlane match --help`
# Remove the # in the beginning of the line to enable the other options

# The docs are available on https://docs.fastlane.tools/actions/match


# fastlane match development --readonly
